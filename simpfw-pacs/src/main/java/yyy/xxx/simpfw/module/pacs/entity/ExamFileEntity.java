package yyy.xxx.simpfw.module.pacs.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.module.pacs.constants.OCRTaskStatus;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
public class ExamFileEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.id
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.exam_uid
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private String examUid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.exam_item_code
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private String examItemCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.file_name
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private String fileName;

    private String filePath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.file_md5
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private String fileMd5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.upload_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.file_size
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private Long fileSize;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.create_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.update_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column d_exam_file.status
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    private Integer status;

    private String deviceNo;

    private Long dataSourceDictCode;

    @Builder.Default
    private OCRTaskStatus ocrDiagTaskStatus;

    private String ocrFileLabel;

    private Long replacedFileId;

    private String replacedInitFileName;



    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.id
     *
     * @return the value of d_exam_file.id
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.id
     *
     * @param id the value for d_exam_file.id
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.exam_uid
     *
     * @return the value of d_exam_file.exam_uid
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public String getExamUid() {
        return examUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.exam_uid
     *
     * @param examUid the value for d_exam_file.exam_uid
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setExamUid(String examUid) {
        this.examUid = examUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.exam_item_code
     *
     * @return the value of d_exam_file.exam_item_code
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public String getExamItemCode() {
        return examItemCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.exam_item_code
     *
     * @param examItemCode the value for d_exam_file.exam_item_code
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setExamItemCode(String examItemCode) {
        this.examItemCode = examItemCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.file_name
     *
     * @return the value of d_exam_file.file_name
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.file_name
     *
     * @param fileName the value for d_exam_file.file_name
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.file_md5
     *
     * @return the value of d_exam_file.file_md5
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public String getFileMd5() {
        return fileMd5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.file_md5
     *
     * @param fileMd5 the value for d_exam_file.file_md5
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setFileMd5(String fileMd5) {
        this.fileMd5 = fileMd5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.upload_time
     *
     * @return the value of d_exam_file.upload_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.upload_time
     *
     * @param uploadTime the value for d_exam_file.upload_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.file_size
     *
     * @return the value of d_exam_file.file_size
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public Long getFileSize() {
        return fileSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.file_size
     *
     * @param fileSize the value for d_exam_file.file_size
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.create_time
     *
     * @return the value of d_exam_file.create_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.create_time
     *
     * @param createTime the value for d_exam_file.create_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.update_time
     *
     * @return the value of d_exam_file.update_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.update_time
     *
     * @param updateTime the value for d_exam_file.update_time
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column d_exam_file.status
     *
     * @return the value of d_exam_file.status
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column d_exam_file.status
     *
     * @param status the value for d_exam_file.status
     *
     * @mbg.generated Wed Nov 20 12:12:26 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }
}