package yyy.xxx.simpfw.module.pacs.ocr;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import yyy.xxx.simpfw.module.pacs.constants.OCRTaskType;

/**
 * <AUTHOR>
 * @since 2024/11/2 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FunctionDefinition {

    private FunctionType functionType;

    //private OCRTaskType taskType;

    private FunctionParam functionParam;

}
