<template>
<div class="user-file-wrap">
  <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
    <div class="avatar-wrapper">
      <img :src="avatar" class="user-avatar">
       <strong>{{currentUser && currentUser.nickName? currentUser.nickName : name}}</strong>
     <!-- <i class="el-icon-caret-bottom" /> -->
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item><router-link to="/user/profile">
        个人中心
      </router-link></el-dropdown-item>
      <el-dropdown-item divided @click.native="logout">
        <span>退出登录</span>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</div>
</template>

<script>
import { mapGetters } from 'vuex'
//获取当前用户信息
import { getUserProfile } from "@/api/system/user";

export default {
  data() {
    return {
      currentUser: null
    };
  },

  computed: {
    ...mapGetters([
      'avatar',
      'name'
    ])
  },

  methods: {
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => {});
    }
  },

  created() {
    getUserProfile().then(res => {
      this.currentUser = res.data;
    });
  }
}
</script>

<style lang="scss" scoped>
.user-file-wrap {
  text-align: right;

  .avatar-container {
    margin-right: 30px;

    .avatar-wrapper {
      cursor: pointer;

      .user-avatar {
        margin: 2px 0;
        width: 36px;
        height: 36px;
        border-radius: 10px;
        vertical-align: middle;
      }

      strong {
        margin-left: 8px;
        line-height: 40px;
        color: #EFEFEF;
        font-size: 18px;
        vertical-align: middle;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }
    }
  }
}
</style>
