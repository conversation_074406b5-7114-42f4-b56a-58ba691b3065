/*<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <item :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>*/


import path from 'path'
import { isExternal } from '@/utils/validate'
//import Item from './Item'
import AppLink from '../Sidebar/Link'
//import FixiOSBug from './FixiOSBug'

export default {
  name: 'MenuItem',
  components: { AppLink },//Item, 
//  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = [];
      }
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath, routeQuery) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      if (routeQuery) {
        let query = JSON.parse(routeQuery);
        return { path: path.resolve(this.basePath, routePath), query: query }
      }
      return path.resolve(this.basePath, routePath)
    }
  },

  render(h) {
    let vm = this, item = this.item;
    
    if(item.hidden) {
      return null;
    }
    //无子菜单
    if(vm.hasOneShowingChild(item.children, item) 
      && (!vm.onlyOneChild.children || vm.onlyOneChild.noShowingChildren) 
      && !item.alwaysShow) {
      if(vm.onlyOneChild.meta) {
        let attrs = {props: {index: vm.resolvePath(vm.onlyOneChild.path)}};
        let buttonAttrs = { slot: "title", props: {to: vm.resolvePath(vm.onlyOneChild.path, vm.onlyOneChild.query)}, class: {"topbar-menu-button": true}};
        let childnodes = [h("AppLink", buttonAttrs, vm.onlyOneChild.meta.title)];
        return h("el-menu-item", attrs, childnodes);
      }
      return null;
    }
    //
    let attrs = {props: {index: this.resolvePath(item.path)}, class: {"topbar-menu-pop": true}};
    let childnodes = [];
    //
    if(item.meta) {
      childnodes.push(h("template", { slot: "title" }, item.meta.title));
    }
    //
    item.children.forEach(r => {
      let mattrs = {props: {key: r.path, item: r, basePath: this.resolvePath(r.path) }};
      childnodes.push(h("MenuItem", mattrs));
    });
    //
    let wattrs = {class: {"el-submenu-wrap": true}};
    let wnode = h("li", wattrs, [h('el-submenu', attrs, childnodes)]);
    
    return wnode;
  }
}
