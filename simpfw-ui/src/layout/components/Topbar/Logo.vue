<template>
  <div class="topbar-logo">
    <router-link to="/">
      <img v-if="logo" :src="logo" class="sidebar-logo" />
      {{ appTitle }}
    </router-link>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import logoImg from '@/assets/logo/logo.png'
import variables from '@/assets/styles/variables.scss'

export default {
  name: 'TopbarLogo',

  data() {
    return {
      logo: null//logoImg
    }
  },
  
  computed: {
    ...mapGetters([
      'appTitle'
    ])
  }
}
</script>

<style scoped>
.topbar-logo a{
  padding-left: 16px;
  display: inline-block;
  line-height: 40px;
  font-size: 20px;
  font-family: "Microsoft YaHei";
  color: #EFEFEF;
}
</style>
