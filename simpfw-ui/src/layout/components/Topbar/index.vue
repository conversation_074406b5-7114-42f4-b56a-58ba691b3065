<template>
<div class="topbar" :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
  <el-row>
    <el-col :span="4">
      <Logo />    
    </el-col>

    <el-col :span="17">
        <el-menu mode="horizontal"
            :default-active="activeMenu"
            :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
            :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
            :unique-opened="true"
            :active-text-color="settings.theme"
            :collapse-transition="false"
            class="topbar-menu"
        >
            <MenuItem v-for="(route, index) in sidebarRouters"
             :key="route.path + index" 
             :item="route"
             :base-path="route.path" />
        </el-menu>
    </el-col>

    <el-col :span="3">
      <UserFile />
    </el-col>
  </el-row>
</div>
</template>

<style>
.topbar{
    height: 40px;
    overflow: hidden;
    box-shadow: 0px 2px 8px #999;
}
.topbar-menu.el-menu.el-menu--horizontal{
    border-bottom-width: 0;
}
.topbar-menu .el-submenu__icon-arrow{
    position: static;
    margin-left: 4px;
}
body > .el-menu--horizontal > .el-menu--popup-bottom-start{
    margin-top: 0;
}
.topbar-menu .el-menu--popup-bottom-start{
    margin-top: 0;
}
.topbar-menu-pop .el-menu--popup-right-start{
    margin-left: 0;
}
.topbar-menu > .el-menu-item, .topbar-menu .el-submenu__title {
    height: 40px;
    line-height: 40px;
}
.topbar-menu > .el-submenu-wrap{
    float: left;
}
a.topbar-menu-button {
    display: inline-block;
    width: 100%;
    height: inherit;
}
</style>
<script>
import { mapGetters, mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";

import Logo from "./Logo";
import MenuItem from "./MenuItem";
import UserFile from "./UserFile";

export default {
    components: { Logo, MenuItem, UserFile },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebarRouters", "sidebar"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        variables() {
            return variables;
        }
    }
};
</script>
