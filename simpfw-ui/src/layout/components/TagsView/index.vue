<template>
  <div id="tags-view-container" class="tags-view-container">
    <!-- <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll"> -->
    <div>
      <!-- 导航收缩 -->
      <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
        @toggleClick="toggleSideBar" />
      <!-- 系统标题 -->
      <logo />
      <!-- 页面/组件Tabs -->
      <span v-if="!compactLayout">
        <router-link v-for="tag in visitedViews" ref="tag" :key="tag.path" :class="isActive(tag) ? 'active' : ''"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }" tag="span" class="tags-view-item"
          @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
          @contextmenu.prevent.native="openMenu(tag, $event)">
          {{ tag.title }}
          <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
        </router-link>
      </span>
      <span v-if="examInfoEdited" class="msg-container">
            {{examInfoEdited.patientInfo && examInfoEdited.patientInfo.name}}
            ({{examInfoEdited.patientInfo.gender.dictLabel}},{{formattedExamAge(examInfoEdited)}})
            检查号: {{examInfoEdited.examNo}}
            登记号: {{examInfoEdited.patientInfo && examInfoEdited.patientInfo.registNo}}
            <span v-if="examInfoEdited.outpNo">  卡号: {{examInfoEdited.outpNo}}</span>
            <span v-if="examInfoEdited.inpNo">  住院号: {{examInfoEdited.inpNo}}</span>
            <span v-if="examInfoEdited.bedNo">  床号: {{examInfoEdited.bedNo}}</span>
            <span v-if="examInfoEdited.examTime">  检查日期: {{!!examInfoEdited.examTime? examInfoEdited.examTime.split(" ")[0] : null}}</span>
            <span v-if="examInfoEdited.examParts_names">  检查部位: {{examInfoEdited.examParts_names}}</span>
            <span v-if="examInfoEdited.reqDept">申请科室: {{examInfoEdited.reqDept? examInfoEdited.reqDept.deptName : ''}}</span>
          </span>
      <!-- 搜索、退出 -->
      <div class="tags-view-right">
        <el-dropdown class="tags-view-right-dropdown" trigger="click">
          <span class="el-dropdown-link">
            <svg-icon class="tags-view-svg" icon-class="system"/>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item >
              <el-link class="tags-view-right-item" @click="logout">退出系统</el-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click="toggleCompactLayout" class="tags-view-right-item">精简布局</span>
            <el-switch
            v-model="compactLayout"
            @change="handleCompactLayout"
            inactive-color="#d3d3d3"
            active-color="#13ce66"
            >
          </el-switch>
            </el-dropdown-item>
            <!-- <el-dropdown-item>
              <screenfull id="screenfull" class="right-menu-item hover-effect" />
            </el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- <div class="tags-view-right">
        <el-link class="tags-view-right-item" @click="logout">退出系统</el-link>
        
            <span>精简布局</span>
            <el-switch
            v-model="compactLayout"
            @change="handleCompactLayout"
            active-color="#13ce66"
            inactive-color="#ff4949">
          </el-switch>
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
      </div> -->
    </div>
    <!-- </scroll-pane> -->
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)"><i class="el-icon-refresh-right"></i> 刷新页面</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)"><i class="el-icon-close"></i> 关闭当前</li>
      <li @click="closeOthersTags"><i class="el-icon-circle-close"></i> 关闭其他</li>
      <li v-if="!isFirstView()" @click="closeLeftTags"><i class="el-icon-back"></i> 关闭左侧</li>
      <li v-if="!isLastView()" @click="closeRightTags"><i class="el-icon-right"></i> 关闭右侧</li>
      <li @click="closeAllTags(selectedTag)"><i class="el-icon-circle-close"></i> 全部关闭</li>
    </ul>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import ScrollPane from './ScrollPane'
import path from 'path'
import Hamburger from '@/components/Hamburger'
import {mergeWithNotNull, undefinedOrNull,fmt_exam_age} from "@/utils/common";
import Logo from './Logo'
//检查信息接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
import { UIS_MODALITIES } from "@/assets/scripts/pacs/modalities";
import Screenfull from '@/components/Screenfull'
export default {
  components: { ScrollPane, Hamburger, Logo,Screenfull },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      compactLayout: this.$store.state.settings.compactLayout, //精简布局
      unAudit: {
        total: 0,
        data: [],
      }

    }
  },



  computed: {
    ...mapGetters([
      'sidebar',
      'currentEquipRoom'
    ]),
    currentEquipRoomLabel() {
      let room = this.currentEquipRoom, label = room && (room.roomName || room.roomCode) || null;
      return label;
    },
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    },
    theme() {
      return this.$store.state.settings.theme;
    },
    examInfoEdited() {
      console.log("examInfoEdited", this.$store.state.exam);
      return this.$store.state.exam.examInfoEdited;
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
    let storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || {};
    if(undefined==storageSetting.compactLayout) {
      this.updateCompactLayout(this.compactLayout);
    }else{
      this.compactLayout = storageSetting.compactLayout
      this.$store.dispatch('settings/changeSetting', {
        key: 'compactLayout',
        value: this.compactLayout
      })
    }
  },
  methods: {
    toggleCompactLayout() {
      // 切换 compactLayout 的值
      this.compactLayout = !this.compactLayout;
      // 调用 handleCompactLayout 方法
      this.handleCompactLayout(this.compactLayout);
    },
    handleCompactLayout(val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'compactLayout',
        value: val
      })
      this.compactLayout = val;
      this.updateCompactLayout(val);
    },
    updateCompactLayout(compactLayout) {
      // 从localStorage中读取当前的设置
      let storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || {};
      storageSetting['compactLayout'] = compactLayout;
      // 将更新后的设置保存到localStorage中
      localStorage.setItem('layout-setting', JSON.stringify(storageSetting));
    },

    formattedExamAge(exma) {
      if (!exma || !exma.examAge) return '';
      let age = fmt_exam_age(exma);
      return age;
    },

    isActive(route) {
      return route.path === this.$route.path
    },
    activeStyle(tag) {
      if (!this.isActive(tag)) return {};
      return {
        "background-color": this.theme,
        "border-color": this.theme
      };
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    isFirstView() {
      try {
        return this.selectedTag.fullPath === this.visitedViews[1].fullPath || this.selectedTag.fullPath === '/index'
      } catch (err) {
        return false
      }
    },
    isLastView() {
      try {
        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath
      } catch (err) {
        return false
      }
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = this.affixTags = this.filterAffixTags(this.routes)
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    moveToCurrentTag() {
      return;
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
      })
    },
    refreshSelectedTag(view) {
      this.$tab.refreshPage(view);
    },
    closeSelectedTag(view) {
      this.$tab.closePage(view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeRightTags() {
      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {
        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews)
        }
      })
    },
    closeLeftTags() {
      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {
        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag).catch(() => { });
      this.$tab.closeOtherPage(this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$tab.closeAllPage().then(({ visitedViews }) => {
        if (this.affixTags.some(tag => tag.path === this.$route.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Dashboard') {
          // to reload home page
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    /**
     * 在本机房的患者信息
     */
    async getListForCheckAudit() {
      const room = this.currentEquipRoom;
      if (!room || !room.roomCode) {
        console.warn("未选择房间。");
        return;
      }
      let params = {
        pageSize: 1000, pageNum: 0,
        resultStatusValues: [1, 2],
        datesCreated: 1,
        datesExamed: 1
      };
      //超声
      params.examModalitiesCodes = UIS_MODALITIES;
      //读取无需诊前检查或诊前检查已就绪的
      params.examPrerequireExclude = true;
      //读取未删除数据
      params.status = 0;
      //本机
      params.callInfo = { callRoom: { roomCode: room.roomCode } };
      //
      //偏好查询设置

      //this.grid.pager.total = 0;
      //this.grid.data = [];
      await eiapi.find(params).then(res => {
        //this.loading = false;
        this.unAudit.total = res.total;
        this.unAudit.data = res.rows;
        return this.unAudit
      }).catch(err => { });
      //  console.log("11", this.unAudit)
      return this.unAudit
    },
    async logout() {


      let unAudit = await this.getListForCheckAudit()
      //console.log("22", unAudit)
      // debugger
      if (!!unAudit && unAudit.total > 0) {
        let msg = "您还有 <span style='color: red;'> " + unAudit.total + '份报告未审核 </span>的报告，请确认是否退出'
        this.$confirm(msg, {
          dangerouslyUseHTMLString: true,
        }).then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/index';
          })
        }).catch(() => {


        })

      } else {
        this.$confirm('确定注销并退出系统吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/index';
          })
        }).catch(() => { });
      }




    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 42px;
  width: 100%;

  /*background: #fff;*/
  /*border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);*/
  .tags-view-item {
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 40px;
    min-width: 80px;
    line-height: 40px;
    text-align: center;
    /*border: 1px solid #d8dce5;*/
    /*color: #495060;
    background: #fff;*/
    color: #FFFFFF;
    padding: 0 8px;
    font-size: 14px;
    margin-left: 5px;
    margin-top: 2px;

    &:first-of-type {
      margin-left: 8px;
    }

    &:last-of-type {
      margin-right: 8px;
    }

    &.active {
      /*background-color: #42b983;
      color: #fff;
      border-color: #42b983;*/
      /*&::before {
        content: '';
        background: #fff;
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: relative;
        margin-right: 2px;
      }*/
      background-color: #FBFCFD;
      color: #018FD7;
      border-radius: 8px 8px 0 0;
    }
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  .hamburger-container {
    display: inline-block;
    line-height: 42px;

    .hamburger {
      fill: #FFF;
    }
  }

  .msg-container{
    margin-left: 30px;
    line-height: 42px;
    color: white;
    font-weight: 600;
    
  }

  .tags-view-right {
    float: right;
    margin-right: 8px;
    display: flex;
    align-items: center;
    height: 42px;

    .tags-view-right-item {
      line-height: 42px;
      color: white;
      font-weight: 600;
      font-size: large;
    }
  }

  .tags-view-right::after {
    clear: both;
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}

.tags-view-svg {
  width: 20px !important;
  height: 20px !important;
  margin-right: 15px;
  vertical-align: middle;
  display: inline-block;
}
</style>
