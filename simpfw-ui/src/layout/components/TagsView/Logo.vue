<template>
  <div class="tagview-logo-container">
    <h1><img data-v-e1da8340="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGWSURBVHgB7ZnvTcNADMVfKgboCGEC2g0yAhs0G9AV2AAmYITCBA0TtBskGzQbPHy6i8gHJO6fUU7kJ1mXSo7y5MY++wIIJGuxs9iNy+QkVhutlbu4iG2xbEaxvRF8kotHlEFnBBPlMJYmGBsURnGC75AHk8GdW2t8V5zRmfndIAdMoxdrAp51ZGKtTxHc0xXzwAA1TCBFcItI5N4XRhIr+IQE5P4tI1+NWME1EqF9n/9E8BsyQZsHqoJ7ZojuTHBwAoYKbpEZ2rbWm5BeYqiq6h6Zof3Hel//kK35GQpIEAZZXn39QwS/Q4/O13Epzc/o6xgiuIUeB2/PgAS9MWNJmz3/EKAhauMwE2zSDEi7NbcMLGmG1BGpE/t06ygZf/1JHGw/vBN7gO2Ld4ic0rVmusGtNTKzDqHaFCnYu2gvASP4inL4KO4wcOO6pT10m5sUpjOPvdO6svJ/oP0WcmE+eiq0rHPBZ+bnDA2YeIj3Cw1yQ53oTuSNMnWjO9H4aPHt1p6gz9HHqbj20gvactZTD1Mqax8tX9cxeAW2UIjqAAAAAElFTkSuQmCC" alt="" style="width: 18px; height: 18px;">{{ appTitle }} </h1>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import logoImg from '@/assets/logo/logo.png'

export default {
  name: 'TagViewLogo',

  computed: {
    ...mapGetters([
      'appTitle'
    ])
  }
}
</script>

<style lang="scss" scoped>
.tagview-logo-container {
  display: inline-block;
  margin: 0 4px;

  h1{
    margin: 0;
    padding: 0;
    line-height: 42px;
    font-size: 14px;
    color: #FFF;

    img{
      margin-right: 2px;
      vertical-align: middle;
    }
  }
}
</style>
