<template>
  <div
    class="siderbar-user"
    :class="{ compact: compactLayout }"
    ref="sidebarUser"
  >
    <el-dropdown class="avatar-container" trigger="click">
      <div class="avatar-wrapper" v-show="!isCollapse">
        <img :src="avatar" class="user-avatar">
        <div v-if="compactLayout" class="user-info">
          <div class="user-name">{{ name }}</div>
          <div class="user-nickname">{{ nickName }}</div>
          <!-- <div class="dept-name" v-if="truncatedDeptNameLabel">{{ truncatedDeptNameLabel }}</div> -->
        </div>
        <span v-else class="user-prof" v-show="!isCollapse">
          <span class="user-name">{{ name }} {{ nickName }}</span>
          <span class="dept-name">{{ truncatedDeptNameLabel }}</span>
        </span>
      </div>
      <el-dropdown-menu slot="dropdown">
        <router-link to="/user/profile">
          <el-dropdown-item>个人中心</el-dropdown-item>
        </router-link>
        <el-dropdown-item divided @click.native="logout">
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {isCollapse: {type: Boolean}},

  data() {
    return {
      sidebarWidth: 0
    };
  },

  computed: {
    ...mapGetters([
      'name',
      'nickName',                 
      'avatar',
      'currentEquipRoom',
      'deptName'
    ]),

    deptNameLabel() {
      return this.deptName||null;
    },

    truncatedDeptNameLabel() {
      console.log('sidebarWidth:', this.sidebarWidth);
      const maxLength = Math.floor(this.sidebarWidth / 23);//8; // 根据需要调整最大长度
      const deptName = this.deptNameLabel;
      if (deptName && deptName.length > maxLength) {
        const start = deptName.slice(0, Math.ceil(maxLength / 2));
        const end = deptName.slice(-Math.floor(maxLength / 2));
        return `${start}...${end}`;
      }
      return deptName;
    },

    currentEquipRoomLabel() {
      let room = this.currentEquipRoom, label = room && (room.roomName || room.roomCode) ||null;
      return label;
    },
    compactLayout() {
        return this.$store.state.settings.compactLayout
    },
  },

  mounted() {
    this.sidebarWidth = this.$refs.sidebarUser.getBoundingClientRect().width;
  },

  methods: {
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>

.siderbar-user {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background-color: rgba(1, 143, 215, 0.05);
  overflow: hidden;
  cursor: default;
}

.siderbar-user .user-avatar {
  margin-top: 12px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  vertical-align: top;
}

.siderbar-user .user-prof {
  display: inline-block;
  margin: 8px 0 4px 8px;
  vertical-align: top;
}

.siderbar-user .user-prof > span {
  display: block;
  line-height: 22px;
  text-align: left;
}

.siderbar-user .dept-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.siderbar-user.compact {
  margin-left: 4px;
  height: 50px;
}

.siderbar-user.compact .user-avatar {
  margin-top: 8px;
  width: 30px;
  height: 30px;
}

.siderbar-user.compact .user-prof {
  margin: 4px 0 2px 4px;
}

.siderbar-user.compact .user-prof > span {
  line-height: 18px;
}

.siderbar-user.compact .dept-name {
  max-width: 100px;
}
</style>