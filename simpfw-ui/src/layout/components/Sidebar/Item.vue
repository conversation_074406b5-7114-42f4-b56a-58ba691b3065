
<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    compactLayout: {
      type: Boolean,
      default: false
    }
  },
  render(h, context) {
    const { icon, title,compactLayout } = context.props
    const vnodes = []
    if(!!compactLayout){
      if (icon) {
      vnodes.push(<div class="menu-item-wrapper"><svg-icon icon-class={icon}/></div>)
    }

    if (title) {
      if (title.length > 5) {
        vnodes.push(<span slot='title' title={(title)} class="menu-item-title">{(title)}</span>)
      } else {
        vnodes.push(<span slot='title' class="menu-item-title">{(title)}</span>)
      }
    }
    }else{
      if (icon) {
        vnodes.push(<svg-icon icon-class={icon}/>)
      }

      if (title) {
        if (title.length > 5) {
          vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)
        } else {
          vnodes.push(<span slot='title'>{(title)}</span>)
        }
      }
    }
    

    
    return vnodes
  }
}
</script>

<style scoped>
.menu-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}

.menu-item-wrapper svg {
  width: 30px !important;
  height: 30px !important;
  margin-right: 0 !important;
}

.menu-item-title {
  display: block;
  margin-top: 0;
  line-height: 1;
}
</style>