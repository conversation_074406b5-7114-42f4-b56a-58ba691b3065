<template>
    <div :class="{'has-logo':showLogo}">
        <el-scrollbar wrap-class="scrollbar-wrapper" :style="compactLayout? {height: '89%'}:{}">
            <el-menu
                :default-active="activeMenu"
                :collapse="isCollapse"
                :unique-opened="true"
                :active-text-color="settings.theme"
                :collapse-transition="false"
                mode="vertical">
                <sidebar-item :style="compactLayout? {'padding-left': '0px'}:{}"
                    v-for="(route, index) in sidebarRouters"
                    :key="route.path  + index"
                    :item="route"
                    :base-path="route.path"
                />
            </el-menu>
        </el-scrollbar>
        <User :isCollapse="isCollapse" :style="compactLayout? {height: '11%'}:{}"/>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import User from "./User";
import variables from "@/assets/styles/variables.scss";

export default {
    components: { SidebarItem, Logo, User },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebarRouters", "sidebar", "name"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            //
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo;
        },
        variables() {
            return variables;
        },
        isCollapse() {
            return !this.sidebar.opened;
        },
        compactLayout() {
            return this.$store.state.settings.compactLayout
        },
    }
};
</script>
