<template>
  <section class="app-main">
    <!-- <transition name="fade-transform" mode="out-in"> -->
      <keep-alive :include="cachedViews">
        <router-view :key="key" v-if="$route.path !== '/MTReportWriting' && $route.path !== '/OtolReportWriting'"/>
      </keep-alive>

      <!-- 图像浏览, 切换路由显示隐藏, 不重新加载iframe -->
      <vReportWriting ref="OtolReportWriting"
                v-show="$route.path === '/OtolReportWriting'"
            ></vReportWriting>

            <MTReportWriting ref="MTReportWriting"
                v-show="$route.path === '/MTReportWriting'"
            ></MTReportWriting>
    <!-- </transition> -->
  </section>
</template>

<script>
import '@/assets/styles/pacs/common.css';
import ReportWriting from "@/views/otol/report/ReportWriting";
import MTReportWriting from "@/views/otol/report/MTReportWriting";

export default {
  name: 'AppMain',

  components: {
    vReportWriting: ReportWriting,
    MTReportWriting,MTReportWriting
  },

  computed: {
    cachedViews() {
      //console.log(this.$store.state.tagsView.cachedViews);
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      //console.log(this.$route.path);
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  /*min-height: calc(100vh - 30px);*/
  margin: 8px 8px 0 8px;
  height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #FFF;
  border-radius: 8px 8px 0 0;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    /*min-height: calc(100vh - 84px);*/
    /*height: calc(100vh - 64px);*/
    border-width: 0;
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
