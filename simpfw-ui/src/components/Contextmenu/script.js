export default {
  props: {
    appendToBody: {type: Boolean, default: true},
    items: {type: Array, default: () => []}
  },
  data() {
    return {
      isShow: false,
      left: -9999,
      top: -999,
      //
      data: null
    }
  },
  methods: {
    show(evt, data) {
      if(!this.available) { return; }

      const vm = this, wrap = document.body, menu = vm.$refs.contextmenu;
      if(vm.appendToBody && !!menu && wrap != menu.parentNode) {
        //console.log(menu);
        wrap.append(menu);
      }

      vm.isShow = true;
      vm.data = data;

      //显示位置
      //菜单坐标
      let menuLeft = evt.clientX + 8, menuTop = evt.clientY + 1;
      //显示菜单，获取菜单尺寸
      vm.left = menuLeft
      vm.top = menuTop;
      //修正
      vm.$nextTick(() => {
        //菜单尺寸
        const menuWidth = menu.clientWidth, menuHeight = menu.clientHeight;
        //可视区域
        const wrapWidth = wrap.scrollLeft + wrap.clientWidth
          , wrapHeight = wrap.scrollTop + wrap.clientHeight;

        if(menuLeft + menuWidth > wrapWidth) {
          menuLeft = wrapWidth - menuWidth - 13;
        }

        if(menuTop + menuHeight > wrapHeight) {
          menuTop = wrapHeight - menuHeight - 13;
        }

        vm.left = menuLeft
        vm.top = menuTop;
        //
        this.items.forEach(li => {
          this.assertMenuItem(li);
        });
      });
    },
    handleClick(item, evt) {
      evt.stopPropagation();
      if(item.disabled) {
        return;
      }
      //console.log(item);
      //
      /*const fx = this.$listeners["select"];
      if(fx) {
        fx(item, this.data);
      }*/
      let dat = this.data;
      this.hide();
      this.$nextTick(() => {
        this.triggerBind("select", item, dat);
      });
    },

    hide() {
      this.isShow = false;
      this.data = null;
    },

    //菜单状态
    assertMenuItem(item) {
      if("function" !== (typeof item.assert)) {
        return ;
      }
      item.assert(item, this.data);
    }
  },
  mounted() {
    document.addEventListener("click", this.hide, false);
    const m = this.$refs.contextmenu;
    if(m) {
      m.addEventListener("contextmenu", (evt) => { evt.preventDefault(); evt.stopPropagation(); }, false)
    }
  },
  deactivated() {
    //console.log("contextmenu deactivated");
    this.hide();
  },
  beforeDestroy() {
    //console.log("beforeDestroy contextmenu");
    try {
      this.hide();
      let m = this.$refs.contextmenu;
      m.parentNode.removeChild(m);
    } catch (err) { console.error(err); }
  },
  computed: {
    available() {
      return this.items && this.items.length;
    }
  },
  watch: {
    isShow(nv, ov) {
      if(!nv) {
        this.left = this.top = -9999;
      }
    }
  }
}