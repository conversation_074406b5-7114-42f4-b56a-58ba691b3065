<template>
  <div ref="contextmenu" class="contextmenu-pane" v-show="isShow"
   :style="{left: left + 'px',top: top + 'px'}"><!--  v-if="available" -->
    <ul>
      <li v-for="(itm, idx) in items" :key="idx"
      :class="{'contextmenu-item-disabled': itm.disabled}"
       @click="handleClick(itm, $event)"><el-button type="text" size="medium">{{itm.name}}</el-button></li>
    </ul>
  </div>
</template>

<script>
import model from "./script";
export default model;
</script>

