import request from '@/utils/request'

const baseUrl = 'http://192.168.2.107:21985';

const currBrowserName = browserName();

export function browserName() {
  const ua = navigator.userAgent.toLowerCase();
  //Microsoft Edge
  if(-1 !== ua.indexOf("edge") || -1 !== ua.indexOf("edg")) {
    return "msedge";
  }
  //Chrome
  if(-1 !== ua.indexOf("chrome")) {
    return "chrome";
  }
  //Firefox
  if(-1 !== ua.indexOf("firefox")) {
    return "firefox";
  }
  //
  return navigator.appName;
}

/**
 * 获取屏幕信息
 * {"Code":200,"Msg": "", "Data": [{"DeviceName":"\\.\DISPLAY1","Primary":True,"Bounds":{"X":0,"Y":0,"Width":1536,"Height":864},"WorkingArea":{"X":0,"Y":0,"Width":1536,"Height":816}},{"DeviceName":"\\.\DISPLAY2","Primary":False,"Bounds":{"X":-1920,"Y":0,"Width":1920,"Height":1080},"WorkingArea":{"X":-1920,"Y":0,"Width":1920,"Height":1032}}]}
 */
export function screenInfo() {
  return request.get(baseUrl + '/monitor')
}
/**
 * 访问网址
 * @Param url 要访问你的网址
 * @Param isExternal 是否外链
 */
export function splitScreen(url, isExternal = false) {
  if(!url) {
    return Promise.resolve(-1);
  }

  return screenInfo().then(res => {
    const info = res.Data;
    //不是多个屏幕，返回1标识无操作
    if(!info || info.length <= 1) {
      return Promise.resolve(1);
    }
    //
    if(!isExternal) {
      url = location.protocol + '//' + location.host + url;
    }
    console.log(url);
    //
    let req = baseUrl + '/browse?browser=' + currBrowserName + '&url=' + encodeURIComponent(url);
    return request.get(req)
  }).then(res => {
    if(!!res && 200 == res.Code) {
      return Promise.resolve(0);
    }

    return Promise.resolve(res.Code);
  });
}