import {cloneDeep} from "lodash";
//可使用工具
const AvailTools = [
        {name: "ZoomMouseWheel", title: "移动", icon: "arrows", hidden: true},
        {name: "<PERSON>", title: "移动", icon: "arrows"},
        {name: "Magnify", title: "放大", icon: "search-plus"},
        {name: "Wwwc", title: "窗宽位", icon: "level"},
        {name: "Length", title: "长度", icon: "measure-temp"},
        {name: "<PERSON><PERSON>", title: "角度", icon: "angle-left"},
        {name: "ArrowAnnotate", title: "标注", icon: "measure-non-target"},
        {name: "Rotate", title: "旋转", icon: "rotate"},
        {name: "EllipticalRoi", title: "圆框", icon: "circle-o"},
        {name: "RectangleRoi", title: "方框", icon: "edit"},
        //{name: "FreehandRoi", title: "线框", icon: "inline-edit"},
        {name: "Invert", title: "反相", icon: "adjust"},
        {name: "Reset", title: "重置", icon: "reset"},
        {name: "Eraser", title: "擦除", icon: "eraser"},
        {name: "Clear", title: "清除", icon: "trash"},
      ];
//防止修改源
export function tools(names = null) {
  let tools = AvailTools;
  if(!!names && !!names.length) {
    return tools.map(e => names.includes(e.name));
  }
  return cloneDeep(tools);
}
/**
 * 获得工具
 */
export function getTool(toolName) {
  const tool = cornerstoneTools[`${toolName}Tool`];

  return tool;
};

/**
 * 元素使用工具
 */
export function addToolForElement(ele, toolName, toolOpts, active = false) {
  const tool = getTool(toolName);
  if(!tool) {
    console.warn("未支持工具%s", toolName);
    return;
  }
  toolOpts = toolOpts || {};
  cornerstoneTools.addToolForElement(ele, tool, toolOpts);
  if(!!active) {
    activeToolForElement(ele, toolName);
  }
}

/**
 * 元素激活工具
 * return 0-执行命令当前激活工具继续使用；1-激活工具；2-由用户执行；
 */
export function activeToolForElement(ele, toolName) {
  //
  disable_Eraser(ele);
  //反相/反色
  if("Invert" === toolName) {
    exec_Invert(ele);
    return 0;
  }
  //
  if("Reset" === toolName) {
    exec_Reset(ele);
    return 0;
  }
  //
  if("Eraser" === toolName) {
    enable_Eraser(ele);
    return 2;
  }
  //清除
  if("Clear" === toolName) {
    exec_Clear(ele);
    return 0;
  }

  cornerstoneTools.setToolActiveForElement(ele, toolName, {mouseButtonMask: 1});

  return 1;
}
export function passiveToolForElement(ele, toolName) {
  cornerstoneTools.setToolPassiveForElement(ele, toolName);
  //cornerstoneTools.setToolDisabledForElement(ele, toolName);
}
/**
 * 反相/反色
 */
export function exec_Invert(ele) {
  let viewport = cornerstone.getViewport(ele);
  viewport.invert = !viewport.invert;
  cornerstone.setViewport(ele, viewport);
  cornerstone.updateImage(ele);
}
/**
 * 重置
 */
export function exec_Reset(ele) {
  cornerstone.reset(ele);
}

function getToolStateForElement(ele) {
  //return cornerstoneTools.getElementToolStateManager(ele);
  const eele = cornerstone.getEnabledElement(ele);
  if (!eele || !eele.image) {
    return null;
  }
  //
  const { toolState } = cornerstoneTools.globalImageIdSpecificToolStateManager;
  if (!toolState || !toolState.hasOwnProperty(eele.image.imageId)) {
    return null;
  }

  return toolState[eele.image.imageId];
}
/**
 * 清除
 */
export function exec_Clear(ele) {
  const imageToolState = getToolStateForElement(ele);
  for(let toolType in imageToolState) {
    imageToolState[toolType].data.length = 0;
  }

  cornerstone.updateImage(ele);
}
/**
 * 擦除
 */
export function enable_Eraser(ele) {
  ele.addEventListener("mouseup", exec_Eraser, false);
}
export function disable_Eraser(ele) {
  ele.removeEventListener("mouseup", exec_Eraser);
}
export function exec_Eraser(evt) {
  const ele = evt.currentTarget || evt.target || evt.srcElement;
  if(!ele) {
    return;
  }
  const imageToolState = getToolStateForElement(ele);
  for(let toolType in imageToolState) {
    let toolStateData = imageToolState[toolType].data;
    for(let i = 0; i < toolStateData.length; i ++) {
      let anno = toolStateData[i];
      //console.log(anno);
      if(anno.active) {
        toolStateData.splice(i, 1);
        cornerstone.updateImage(ele);
        return;
      }
    }
  }

}
