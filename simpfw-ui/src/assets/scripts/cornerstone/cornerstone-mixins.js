import * as csh from "@/assets/scripts/cornerstone/cornerstone-helper";

export default {
  props: {
    scaleZoom: {
      type: Object,
      default () {
          return {
              max: 5, 
              min: 0.2
          }
      }
    }
  },
  data() {
    return {
      tools: csh.tools(),
      toolActive: "Pan"
    }
  },
  methods: {

    enableCornerstone(ele) {
      try { 
        cornerstone.getEnabledElement(ele); 
        return 0;
      } catch (err) { 
        cornerstone.enable(ele);
        return 1;
      }
    },

    enableCornerstoneTools(ele) {
      const ZoomMouseWheelToolName = "ZoomMouseWheel";
      this.tools.forEach(t => {
        const name = t.name;
        let isActive = name === this.toolActive || name === ZoomMouseWheelToolName;
        let opts;
        if(name === ZoomMouseWheelToolName) {
          const scaleZoom = this.scaleZoom;
          opts = {
            //
            configuration: {
              invert: false,
              preventZoomOutsideImage: false,
              minScale: scaleZoom.min,
              maxScale: scaleZoom.max,
            }
          };
        }
        csh.addToolForElement(ele, name, opts, isActive);
      });

    },

    activeCornerstoneTool(ele, nam) {
      const res = csh.activeToolForElement(ele, nam);
      //
      const toolActive = this.toolActive;
      if(0 !== res) {
        this.toolActive = nam;        
      }
      //
      if(1 !== res) {
        try {
          csh.passiveToolForElement(ele, toolActive);
        } catch(err) { console.error(err); }
      }
    }
  }
}
