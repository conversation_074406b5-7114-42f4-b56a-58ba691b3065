//影像
import cornerstone from 'cornerstone-core';
import cornerstoneWADOImageLoader from 'cornerstone-wado-image-loader';
import cornerstoneWebImageLoader from 'cornerstone-web-image-loader';
import dicomParser from 'dicom-parser';
//
import cornerstoneMath from 'cornerstone-math';
import Hammer from 'hammerjs';
//工具
import cornerstoneTools from 'cornerstone-tools';
//全局调用
const cornerstoneGlobal = "cornerstone", cornerstoneToolsGlobal = "cornerstoneTools", cornerstoneWADOImageLoaderGlobal = "cornerstoneWADOImageLoader";
//console.log(cornerstoneGlobal, cornerstoneToolsGlobal);
if(!(cornerstoneGlobal in window)) {
  //console.log(cornerstoneGlobal);
  //DICOM文件
  cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
  cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
  //JPG文件
  cornerstoneWebImageLoader.external.cornerstone = cornerstone;
  cornerstoneWebImageLoader.external.dicomParser = dicomParser;
  const cornerstoneConfig = {
    webWorkerPath: "./image-loader/image-loader/cornerstoneWADOImageLoaderWebWorker.js",
    taskConfiguration: {
      decodeTask: {
        codecsPath: "./image-loader/cornerstoneWADOImageLoaderCodecs.js",
      },
    },
  };
  /*cornerstoneWADOImageLoader.webWorkerManager.initialize(cornerstoneConfig);
  cornerstoneWADOImageLoader.configure({
    beforeSend: function(xhr) {
      xhr.setRequestHeader("Access-Control-Allow-Origin", location.origin);
    }
  });
  cornerstoneWebImageLoader.configure({
    beforeSend: function(xhr) {
      xhr.setRequestHeader("Access-Control-Allow-Origin", location.origin);
    }
  });*/

  window[cornerstoneGlobal] = cornerstone;

  //图像操作工具
  cornerstoneTools.external.cornerstone = cornerstone;
  cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
  cornerstoneTools.external.Hammer = Hammer;
  cornerstoneTools.init();

  window[cornerstoneToolsGlobal] = cornerstoneTools;
  //
  window[cornerstoneWADOImageLoaderGlobal] = cornerstoneWADOImageLoader;
}

//export default cornerstone;

