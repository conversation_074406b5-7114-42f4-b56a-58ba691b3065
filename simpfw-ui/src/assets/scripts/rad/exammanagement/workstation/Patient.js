import store from "@/store/index";

import {cloneDeep} from "lodash";
import {mergeWith as mergeWithDeep} from "lodash";
import {emptyRegist} from "@/assets/scripts/uis/exammanagement/patient/Regist";
import {mergeWithNotNull} from "@/utils/common";

import {undefinedOrNull, currDate} from "@/utils/common";
//科室信息
import { treeselect as deptTreeselect } from "@/api/system/dept";
//检查信息
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//设备信息
import {findDevices} from "@/assets/scripts/pacs/equiproom/dicominfo/api";
//机房信息
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';

import {EditModel, TransModel, UndoModel} from "@/assets/scripts/uis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//检查单
import ExamViewer from '@/views/rad/exammanagement/workstation/ExamViewer';
//import ExamEquipRoom from '@/views/uis/exammanagement/patient/comp/ExamEquipRoom';

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth"

//表单标识
const formSimp = "simp";
//右键菜单
const TableContexmenuItems = [
        {cmd: 'exam::copy', name: '复制'},
        {cmd: 'exam::edit', name: '更改信息'}
        , {cmd: 'exam::del', name: '删除检查', permissions: ['exam-info:delete'], assert: (ctx, exam) => {if(!!exam && 2 === exam.status) {ctx.cmd = 'exam::undel';ctx.name = '删除恢复';} else {ctx.cmd = 'exam::del';ctx.name = '删除检查';}}}
        //, {cmd: 'exam::equip-room-change', name: '更改机房'}
        //, {cmd: 'exam::precond', name: '诊前准备'}
        , {cmd: 'exam::at-pm', name: '延迟检查'}//下午检查
        , {cmd: 'report::write', name: '打开报告'}
      ];
//export {TableContexmenuItems};
function tableContextmenuItems() {
  const userPermissions = store.state.user.permissions, superPerm = "*:*:*";
  if(userPermissions.includes(superPerm)) {
    return TableContexmenuItems;
  }

  return TableContexmenuItems.filter(e => !e.permissions || e.permissions.length === 0 || userPermissions.includes(e.permissions[0]));
};
export {tableContextmenuItems};

//逻辑
export default {
  name: "PatientList",

  extends: BaseGridModel,

  mixins: [EditModel, TransModel, UndoModel],

  components: { Contextmenu, ExamViewer, OperationAuth },//, ExamEquipRoom

  dicts: ["uis_exam_item", "uis_inp_type", "uis_exam_modality", "uis_gender_type", "uis_exam_result_status"],

  data() {
    return {

      searchFormVisible: false,
      /**
       * 搜索框
       */
      searchForm:{
        id: null,
        examNo: null,
        examItemCodes: [],
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModality: {dictCode: null},
        examDoctor: {nockName: null},
        reportDoctor: {nockName: null},
        auditDoctor: {nockName: null},

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        },

        inpTypeValues: []
      },

      /**
       * 当前
       */
      currentTableRow: null,
      currentTableRowStr: null,

      tableAction: tableContextmenuItems(),

      deptTreeData: [],

      searchFormSimp: {
        textFieldName: "patientInfo.name",
        textField: null,

        dateFieldName: "createTime",
        dateFieldGe: currDate(),
        dateFieldLt: currDate(),
        
        inpTypeValues: [],
        examDevicesCode: [],
        resultStatusValues: [],
        equipRoomsCode: []
      },
      //当前查询表单
      lastSearchForm: formSimp,
      //
      combo: {
        searchTextFields: [
          {name: "examNo", label: "检查号"},
          {name: "patientInfo.registNo", label: "登记号"},
          {name: "patientInfo.name", label: "姓名"},
          {name: "inpNo", label: "住院号"},
          {name: "patientInfo.healthCardId", label: "就诊卡号"},
        ],
        searchDateFields: [
          {name: "examTime", label: "检查日期"},
          {name: "createTime", label: "登记日期"},
          {name: "auditTime", label: "审核日期"},
          {name: "applyTime", label: "预约日期"},
        ],
        //设备型号列表
        devices: [],
        //房间列表
        equipRooms: []
      }
    };
  },

  methods: {

    /**
     * 搜索
     */
    getList(mix) {
      let params;
      //
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      if(formSimp ===  this.lastSearchForm) {
        //
        params = cloneDeep(this.searchFormSimp);
        //
        if(!!params.textField) {
          params[params.textFieldName] = params.textField;
        }
        delete params.textFieldName;
        delete params.textField;
        //
        if(!!params.dateFieldGe || !!params.dateFieldLt) {
          params[params.dateFieldName + 'Ge'] = params.dateFieldGe || null;
          params[params.dateFieldName + 'Lt'] = params.dateFieldLt || null;
        }
        delete params.dateFieldName;
        delete params.dateFieldGe;
        delete params.dateFieldLt;
      } else {
        params = cloneDeep(this.searchForm);
      }

      if(params.examItemCodes) {
        params.examItemCodes.forEach((c, i) => {
          params["examItemCodes[" + i + "]"] = c;
        });
        delete params["examItemCodes"];
      }
      //
      //if(!(mix instanceof Event) && (mix instanceof Object)) {
      //  if(mix.page) { params.pageNum = mix.page; }
      //  if(mix.limit) { params.pageSize = mix.limit; }
      //}
      //已删除状态
      let pos;
      if(!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
          params.resultStatusValues.splice(pos, 1);
          //params.resultStatusAsStatus = "2";
          params.status = 2;
      } else {
        params.status = 0;
      }

      this.loading = true;
      eiapi.find(params).then(res => {
        this.loading = false;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    }, 

    /**
     * 查看检查记录
     * @param item 选择的检查记录
     */
    selectTableRow(item) {
      var vm = this;
      vm.currentTableRow = item;
      if (item.id != undefined){
        eiapi.get(item.id).then(res => {
          vm.currentTableRow = res.data;
          
          vm.currentTableRowStr = "姓名：" + (vm.currentTableRow.patientInfo.name || "-")
          + " 年龄：" + (vm.currentTableRow.patientInfo.age || "-")
          + " 影像号：" + (vm.currentTableRow.patientInfo.patientId || "-")
          + " 电话：" + (vm.currentTableRow.patientInfo.homePhone || "-")
          + " 地址：" + (vm.currentTableRow.patientInfo.address || "-")
          + "\r\n临床诊断：" + (vm.currentTableRow.clinicDiagnosis || "-")
           this.$emit("changeCurrentExam", vm.currentTableRow)
        })
      }
      else{
        console.log("err: item.id undefined")
      }

    },

    //查询
    search(opt) {
      this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号/检查房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }
      //登记房间
      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },
    //显示检查详情
    handleDetail(row) {
      //this.$modal.msg("未实现");
      if(row == undefined){
        this.$modal.msg("请选择一个患者");
        return;
      }
      this.$refs.examViewer.view(row);
    },

    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    // handleUndoDelete(row) {
    //   eiapi.undoDel(row.id).then(res => {
    //     if(res && 200 === res.code) { 
    //       this.$modal.msgSuccess("执行成功。");
    //       this.getList(); 
    //     } else {
    //       this.$modal.msgError(res && res.msg || "执行失败。");
    //     }
    //   }); 
    // },
    //表格行右键菜单
    showTableAction(row, col, evt) {
      evt.preventDefault();
      this.$refs["tableContextmenu"].show(evt, row);
    },
    //右键菜单
    handleTableAction(item, row) {
      switch(item.cmd) {
        case 'exam::edit':
          this.handleUpdate(row);
          break;
        case 'exam::del':
          //this.handleDelete(row);
          this.verifyForDelete(row);
          break;
        case 'exam::undel':
          //this.undoDelete(row);
          this.callUndoDelete(row);
          break;
        //case 'exam::equip-room-change':
        //  this.$refs.examEquipRoom.change(row);
        //  break;
        case 'exam::precond':
          this.handlePrecond(row, {examPrerequire: 0});
          break;
        case 'exam::at-pm':
          this.handlePrecond(row, {examAtPm: 1});
          break;
        case 'report::write':
          this.$router.push({name: "ReportWriting", params: {report: row}});
          break;
        case 'exam::copy':
            document.execCommand('Copy','false',null);
           break;
        default:
          this.$modal.msg("未实现");
      }
    },
    /**
     * 诊前准备或下午检查
     * @param props {'examPrerequire':0}-诊前准备状态码, {'examAtPm':1}-下午检查状态码
     */
    handlePrecond(row, props) {
      this.handleTrans(row, props).then(res => {
        if(res && 200 === res.code) {
          this.getList();
        }
      });
    },

    callUndoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if(res && 200 === res.code) {
          this.getList();
        }
      });
    },

    toggleSearchForm() {
      this.searchFormVisible = !this.searchFormVisible;
    },
    //读取部门树信息
    buildDeptTree() {
      deptTreeselect().then(res => {
        this.deptTreeData = res.data;
      });
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //读取设备型号列表
    findDevices() {
      findDevices().then(res => {
        this.combo.devices = res.data;
      });
    },
    //
    findEquipRooms() {
      findRoom({pageNo: 1, pageSize: 9999}).then(res => {
        this.combo.equipRooms = res.rows;
      });
    }
  },

  created() {
    this.buildDeptTree();

    this.findDevices();

    this.findEquipRooms();
  }
};
