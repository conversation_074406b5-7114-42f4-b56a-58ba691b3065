import request,{postForm} from '@/utils/request'

import * as piapi from "@/assets/scripts/pacs/image/api";
console.log(piapi);
//export piapi;

const ctx = '/exammanagement/workStation';

//读取已采集的影像list
export function findStudyList(query) {
  console.log(query)
  return postForm({
    url: ctx + '/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

//读取已采集的影像
export function workStationGet(query) {
  return request.get(ctx + "/workStationGet", {params: query});
}