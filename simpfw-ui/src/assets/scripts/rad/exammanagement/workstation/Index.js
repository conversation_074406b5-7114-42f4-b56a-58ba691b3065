
import Patient from '@/views/rad/exammanagement/workstation/Patient';
import Study from '@/views/rad/exammanagement/workstation/DCMStudy';
import ExamView from '@/views/rad/exammanagement/workstation/ExamViewer';
import ReportView from '@/views/rad/exammanagement/workstation/ReportViewer';
import DcmView from '@/views/rad/exammanagement/workstation/DCMViewer';
import DCMStudyViewer from '@/views/rad/exammanagement/workstation/DCMStudyViewer';
export default {
  name: "WorkStation",

  components: {
    Patient,
    Study,
    "StudyNone": Study,
    ReportView: ReportView,
    ExamView,
    DcmView,
    DCMStudyViewer,
  },

  data() {
    return {
      tabsName: {
        Study: "Study",
        StudyNone: "StudyNone",
        ReportView: "ReportView",
        ExamView: "ExamView",
      },
      currentTab: "Study",

      currentExam: null,
      currentStudy: null,
    };
  },

  methods: {
    handleAction(cmd, row) {

    },

    handleTabClick(tab){
      if(tab.name == "ExamView"){
        this.$refs.ExamView.view(this.currentExam)
      }
      if(tab.name == "ReportView"){
        this.$refs.ReportView.view(this.currentExam)
      }
      if(tab.name == "StudyNone"){
        this.$refs.none.setNoType(true)
      }
    },

    changeCurrentExam(row){
      this.currentExam = row;
      this.$refs.ExamView.view(this.currentExam)
      this.$refs.ReportView.view(this.currentExam)
      this.$refs.DcmView.write(this.currentExam);
    },
    
    changeCurrentStudy(row){
      this.currentStudy = row;
      this.$refs.DcmStudyView.write(this.currentStudy)
    },
    
  }
};
