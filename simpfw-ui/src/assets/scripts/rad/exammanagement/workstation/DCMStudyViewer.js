import { mapGetters } from 'vuex';
//工具
import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";
//cornerstone
import "@/assets/scripts/cornerstone/cornerstone-setup";

import {mergeWithNotNull, undefinedOrNull} from "@/utils/common";
//获取当前用户信息
import { getUserProfile } from "@/api/system/user";
//检查信息接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//影像相关工具
import {FakeUidPrefix, FileTypes, assertJpeg, loadAndDisplay} from "@/assets/scripts/pacs/image/util";
//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";
import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel"
//
import ReportWriterImageComm from "@/assets/scripts/uis/report/comp/ReportWriterImageComm";
//import ReportPreviewer from "@/views/uis/report/comp/ReportPreviewer";
//放大影像
import {default as ImageBubble1} from "@/views/uis/report/comp/ImageBubble"
//
import VideoView from "@/views/uis/report/comp/VideoView";
//检查状态变更
import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//
import ReportWriterDetail from "@/views/uis/report/comp/ReportWriterDetail";

import imageView from '@/views/pacs/image/imageView'
//选择用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//操作权限
import { default as PermiCheck} from "@/directive/permission/mixins";
//影像转移
import {ImageTransfer} from "@/assets/scripts/pacs/report/mixins/fun.imageTransfer";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//
import ExamPicker from "@/views/uis/exammanagement/patient/comp/ExamPicker";
//
import {workStationGet} from "@/assets/scripts/rad/exammanagement/workstation/api"

/**
 * 空白表单
 */
function emptyForm() {
  return {
    
    patientInfo: {
      id: null,
      medicalRecordNo: null,
      name: null, 

      gender: {}, 
      age: null,
      ageUnit: {},
    },

    examModality: {},
    examItem: {},
    examParts: [],
    resultStatus: {dictValue: null},
    //examResultProp: {dictValue: "0"},
    examResultProp: {},

    examDoctor: {},
    reportDoctor: {},

    examDoctorsName: null,
    examDoctorsCode: null,
    consultantsName: null,
    consultantsCode: null,
    recordersName: null,
    recordersCode: null,

    examDesc: null,
    examDiagnosis: null,
    operationSuggestion: null,
    //报告的影像
    images: [],
    //采集的影像
    dicomStudy: {seriesSet: null},
    dicomStudies: []
  }
}

export { emptyForm };

const model = {

  name: "DCMStudyViewer",
  
  components: {LinksignPopup, ImageBubble1, ReportWriterDetail, imageView, UserPicker, VideoView, Contextmenu, ExamPicker},

  dicts: ["uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "uis_exam_result_prop"],

  mixins: [PermiCheck, ReportWriterImageComm, ImageTransfer],

  data() {
    return {
      //当前用户
      currentUser: null,
      //报告内容
      reportForm: emptyForm(),
      //
      qr: null,
      //
      editable: false,
      //双击图像显示大图
      dialogVisible:false,
      imageURL:'',
      currentItemIdx:null,

      isFistfocus: true,

      d_examResultProp:false,
      his_opt:{},

      myStudy: null,
    };
  },

  methods: {
    //初始
    onPageLoad() {

    },

    /**
     * 写报告
     */
    write(study, opt = {}) {
      this.isFistfocus = true;
      //
      this.clearAuditForPrint();
      //
      this.editable = false;
      this.currentItemIdx = null;
      //
      if(!study || !study.id) {
        return;
      }
      workStationGet({id: study.id}).then(res => {
        this.myStudy = res && res.data;
        this.loadReportImages();
        
      });
    },

    //读取已采集的图片
    loadReportImages() {
        let seriesSet = this.myStudy.seriesSet;

        if(!seriesSet || seriesSet.length <= 0) { return true; }
console.log(seriesSet)
        seriesSet.forEach(ser => {
          let imagesSet = ser.imagesSet;
          if(imagesSet && imagesSet.length) {
            imagesSet.forEach((img, i) => {
              let {studyInstanceUid, fileType} = img;
              img.studyInstanceUid0 = ((FileTypes.jpeg.name === fileType || FileTypes.jpeg.code === fileType)? FakeUidPrefix : "") + studyInstanceUid;
              this.structExamImage(img, imagesSet, i);
            });
          }
            // fm.dicomStudies = studies;
            this.loadExamImages();
        });
    },

    // 设置图片类型，来源，选中状态/是否作为报告影像
    structExamImage(img, imagesSet, index) {
      console.log(img)
      if(!img.id) { return; }

      const {studyInstanceUid, sopInstanceUid, fileType} = img;
      let sopUri; 
      if(this.typeofJpg(img) || this.typeofVideo(img)) {
        console.log("9999999")
        sopUri = imapi.imageLocate(img); 
        //
        
        console.log("9999999" + sopUri)
        let selectedImages = this.reportForm.images;//已选影像
        let selectedImage = !!selectedImages? selectedImages.find(i => i.sopInstanceUid === sopInstanceUid) : null;

        img.fileType = img.fileType || FileTypes.jpeg.code;//assertJpeg(sopUri)? FileTypes.jpeg.name : FileTypes.dicom.name;
        img.uri = sopUri;
        img.selected = !!selectedImage;
        //为倒叙
        //selectedImages.forEach((ser, i) => {
        //    ser.selectNum = 1 + i;
        //})

        if(selectedImage) { selectedImage.uri = img.uri; }
      } else if(FileTypes.dicom.code == fileType || FileTypes.dicom.name == fileType) {
        //获取浏览地址，获取影像DICOM参数
        imapi.dcmViewer(null, studyInstanceUid).then(res => {
          const dicomStudy = res.dicomStudy || {};
          if(!dicomStudy) {
            this.$modal.msgWarning("该检查没有影像信息。");
            return true;
          }
          const suid = dicomStudy.dicomStudyInstanceUid || dicomStudy.studyInstanceUid;
          return imapi.indexImages(suid);
        }).then(res => {
          const items = res.data;
          if(!!items && !!items.length) {
            items.forEach(ig => {
              //if(!ig.fileUrl.endsWith("/2")) {return true;}
              if(-1 != this.studyImagesSet.findIndex(e => e.sopInstanceUid === ig.sopInstanceUid)) { return true; }
              ig.uri = `wadors:${ig.fileUrl}`;
              imagesSet.push(ig);
              cornerstoneWADOImageLoader.wadors.metaDataManager.add(ig.uri, JSON.parse(ig.noteInfo));
            });
            this.$nextTick(this.loadExamImages);
          }
        });
      }
    },
    //当前编辑的报告
    get() {
      return this.reportForm;
    },
    
    //cornerstone加载图片
    loadExamImages(opts = {}) {
      //
      //加载图片前，根据选择重新排序显示
      console.log(this.studyImagesSet)
      // this.reportForm.images.forEach((ser, i) => {
      //     ser.selectNum = 1 + i;
      //     this.studyImagesSet.forEach(img=>{
      //         if(img.sopInstanceUid==ser.sopInstanceUid){
      //             img.selectNum=ser.selectNum
      //         }
      //     })
          
      // })
      this.$nextTick(() => {
        document.querySelectorAll(".report-writer-image2 div.cornerstone-element").forEach((c, i) => {
          console.log(this.studyImagesSet);
          const image = this.studyImagesSet[i];
          if(this.typeofVideo(image)) {
            cornerstone.disable(c);
          } else {
            try {
              //是否已
              cornerstone.getEnabledElement(c);
            } catch (err) {
              cornerstone.enable(c);
            }
            let idx = parseInt(c.parentNode.dataset.index);
            if(isNaN(idx)) {
              idx = i;
            }
            this.loadExamImage(c, idx);
          }
        });
        //滚动条到最近
        if(opts.focusLast) {
          this.$nextTick(() => {document.querySelector(".cornerstone-elements-pane").scrollLeft = 0;});
        }
      });
    },

    //获取>显示影像
    loadExamImage(element, idx) {
      const imagesSet = this.studyImagesSet;
      //console.log(imagesSet);
      let image = imagesSet[idx];
      if(!this.validateExamImage(image)) { return; }
      //console.log(image);
      let imageId = image.uri;//wadoForImage(image.uri);
      //console.log(imageId);
      try {
        loadAndDisplay(element, imageId);
      } catch (err) { console.error(err); }
    },
    //选择影像
    selectExamImage(state, item) {
      //
      item.selectNum = null;
      delete item.selectNum;

      let imgId = item.sopInstanceUid, selectedImages = this.reportForm.images;
      const idx = selectedImages.findIndex(si => si.sopInstanceUid === imgId);
      if(!state && -1 !== idx) {
        selectedImages.splice(idx, 1);
        selectedImages.forEach((ser, i) => {
            ser.selectNum = 1 + i;
            this.studyImagesSet.forEach(img=>{
                if(img.sopInstanceUid==ser.sopInstanceUid){
                    img.selectNum=ser.selectNum
                }
            })
        })
      } else if(state && -1 === idx) {
        const count = selectedImages.length
        item.selectNum = count + 1;

        selectedImages.push(item);
      }
    },

    //删除影像
    deleteExamImage(item) {
      
      console.log(item);
      this.$modal.confirm("是否确定?").then(() => {
        const dicomStudy = this.myStudy;
        console.log(item);
        console.log(dicomStudy);

        if(!dicomStudy.seriesSet) { return ; }

        const series = dicomStudy.seriesSet.find(ser => ser.studyInstanceUid === item.studyInstanceUid && ser.seriesInstanceUid === item.seriesInstanceUid);
        
        let imagesSet = series? series.imagesSet : null;
        if(!imagesSet) { return ; }

        const idx = imagesSet.findIndex(i => i.sopInstanceUid === item.sopInstanceUid);
        if(-1 === idx) { return ; }
        // const img = imagesSet[idx];
        // const pos = selectedImages.findIndex(si => si.sopInstanceUid === img.sopInstanceUid);
        // if(-1 != pos) {
        //   selectedImages.splice(pos, 1);
        // }
        imagesSet.splice(idx, 1);
        item.status = 2;
        //
        // selectedImages.forEach((ser, i) => {
        //     ser.selectNum = 1 + i;
        // })
        //
        //this.$nextTick(() => {
          imapi.delImage(item.id).then(this.loadExamImages);
        //});
          //
        if(this.dialogVisible) {
          const imagesLength=this.studyImagesSet.length-1
          //const delFlag=this.deleteExamImage(this.studyImagesSet[this.currentItemIdx])
          if(this.currentItemIdx>imagesLength){
              this.nextView(-1)
          }else if(imagesLength<0){
              this.dialogVisible=false
          }else{
              this.nextView(1)
          }
        }
    });
    },
    //是否图片已删除
    isExamImageDeleted(img) {
      return 2 === img.status;
    },
    validateExamImage(img) {
      return !this.isExamImageDeleted(img) && !!img.uri;
    },

    //预览
    handlePreview(mix) {
      //选择的图像情况
      if(!this.checkSelectedImagesCount()) {
        return;
      }
      //工作状态为检查中和已报告的，保存报告
      /*const exam = this.reportForm, resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
      if(!undefinedOrNull(resultStatusCode) && StatusDict.exam === resultStatusCode || StatusDict.report === resultStatusCode) {
        this.save({firmed: true, silence: true});
      }*/
      //是否指定操作
      let cmd = ("string" === (typeof mix)) && mix.startsWith("report::")? mix : "report::preview";
      this.triggerBind("dispatchAction", cmd, this.reportForm);
    },

    //放大影像
    zoomImage(evt) {
      console.log(evt)
      let ele = !!evt? (evt.currentTarget || evt.target) : null, idx = !!ele? parseInt(ele.parentNode.dataset.index) : -1;
      console.log(idx)
      if(-1 >= idx) { 
        // this.$refs.imageBubble1.close();
        return ; 
      }
      const imagesSet = this.studyImagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      //视频不预览
      if(this.typeofVideo(img)) {
        return;
      }
      //
      console.log(img)
      evt["workStationSetPosition"] = "sticky";
      evt["workStationSetLeft"] = "10%"
      evt["workStationSetBottom"] = "0px"
      this.$refs.imageBubble1.view(img, evt);
    },
    //预览影像
    /*viewImage(idx) {
      const imagesSet = this.studyImagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      this.$refs.imageView.view(img);
    },*/
    //聚焦最新采集的图像
    scrollImage(dir) {
      const imageScrollView = this.$refs.imageScrollView;
      //console.log(imageScrollView);
      let scollLeft = imageScrollView.scrollLeft;
      const scrollStep = 12 + imageScrollView.querySelector(".cornerstone-element-container").clientWidth;
      scollLeft += dir * scrollStep;
      imageScrollView.scrollLeft = Math.max(0, scollLeft);
    },
    //检查勾选的图像数是否合理
    checkSelectedImagesCount() {
      //图象数只能是0, 1, 2, 3, 4, 6
      const images = this.reportForm.images, numSupported = [0, 1, 2, 3, 4, 6];
      const numImages = !!images? images.length : 0;
      if(!numSupported.includes(numImages)) {
        this.$modal.alert("影像张数应为: " + numSupported.join(", "));
        return false;
      }

      return true;
    },

    viewImage(evt){
        this.dialogVisible=true
        
        let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
        //console.log(this.studyImagesSet)
        this.imageURL=this.studyImagesSet[idx]
        this.currentItemIdx=idx
        //console.log( this.currentItemIdx)
    },
    nextView(val){
        
        const imagesLength=this.studyImagesSet.length-1
        if((-1===val&&this.currentItemIdx+val<0) || (1===val&&this.currentItemIdx+val>imagesLength)){
            return
        }
        this.currentItemIdx=this.currentItemIdx+val
        this.imageURL=this.studyImagesSet[this.currentItemIdx]
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param opts {}
     */
    toPickUser(opts) {
      const fm = this.reportForm;
      if(!fm.id) { return; }

      let tar = opts.target;
      let names, codes;
      if(tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }
      let selectedUsers = [];
      if(!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({userName: e, nickName: names[i]});
        });
      }
      opts.selectedUsers = selectedUsers;

      this.$refs.userPicker.showPicker(opts);
    },
    pickUser(tar, users) {
      let fm = this.reportForm, names = [], codes = [];
      if(!!users.length) {
        users.forEach(e => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }
      names = names.join(",");
      codes = codes.join(",");

      if(tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }
    },
    //
    cleanExam(){ 
      this.reportForm.examDesc=""
      this.reportForm.examDiagnosis=""
    },
    //清除打印审核请求
    clearAuditForPrint() {
      this.$store.dispatch("auditForPrint", null);
    },
    switchTab(val){
        if(this.isFistfocus){
          this.triggerBind('switchTab',val)
          this.isFistfocus=false
        }
    },
    //图像类型
    typeofVideo(image) {
      console.log(image)
      return FileTypes.video.name === image.fileType || FileTypes.video.code === image.fileType;
    },
    //图像类型
    typeofJpg(image) {
      return FileTypes.jpeg.name === image.fileType || FileTypes.jpeg.code === image.fileType;
    },
    //播放动态采集
    viewVideo(evt) {
      let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
      const image = this.studyImagesSet[idx];
      if(!!image && !!image.uri && this.typeofVideo(image)) {
        this.$refs.videoView.view(image);
      }
    },
    //选择影像转入的检查
    toPickExam(image) {
      this.$refs.examPicker.show(image);
    },
    //影像转移成功后调用
    afterTransferImage() {
      this.loadReportImages();
    }
  },
  //加载完成执行
  mounted() {
    this.onPageLoad();
  },
  //退出组件
  activated() {
    this.onPageLoad();
  },

  computed: {
    //当前机房，当前编辑的检查信息
    ...mapGetters(['studyImagesSet']),
    //采集的影像
    studyImagesSet() {
      console.log("=============")
      let dicomStudy = this.myStudy
      const seriesSet = !!dicomStudy && !!dicomStudy? dicomStudy.seriesSet : null;
      if(!seriesSet) {
        return;
      }

      let studyImagesSet = [];
      seriesSet.forEach(ser => {
          let images = ser.imagesSet;
          if (!images) {
              return true;
          }
          //刷新页面时,选中序号更新
          //var count = 1
          images.forEach(img => {
            console.log(img)
              if (img.status !== 2 && !!img.uri) {
                studyImagesSet.push(img);
              }
              //if (img.selected) {
              //    img.selectNum = count++
              //}
          });
      });

      console.log(studyImagesSet);
      return studyImagesSet.reverse();
    },
    //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
    reportable() {
      return true;
    },
  }
};

export default model;