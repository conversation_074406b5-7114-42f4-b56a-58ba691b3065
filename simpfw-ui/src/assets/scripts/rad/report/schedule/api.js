import request,{postForm} from '@/utils/request'

const ctx = '/schedule';

// 用户查询列表
export function findScheduleUser(query) {
  return postForm({
    url: ctx + '/scheduleUser/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 用户查询详细
export function getScheduleUser(id) {
  return request({
    url: ctx + '/scheduleUser/get?id=' + id,
    method: 'get'
  })
}

// 用户插入
export function insertScheduleUser(query) {
  return request.post(ctx + '/scheduleUser/insert', query);
  // return postForm({
  //   url: ctx + '/scheduleUser/insert',
  //   data: query,
  //   headers: {repeatSubmit: false}
  // })
}

// 用户删除
export function delScheduleUser(id) {
  return request({
    url: ctx + '/scheduleUser/del/' + id,
    method: 'delete'
  })
}


// 分组查询列表
export function findScheduleGroup(query) {
  return postForm({
    url: ctx + '/scheduleGroup/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 分组插入
export function insertScheduleGoup(query) {
  return request.post(ctx + '/scheduleGroup/insert', query);
  // return postForm({
  //   url: ctx + '/scheduleUser/insert',
  //   data: query,
  //   headers: {repeatSubmit: false}
  // })
}

// 分组删除
export function delScheduleGoup(id) {
  return request({
    url: ctx + '/scheduleGroup/del/' + id,
    method: 'delete'
  })
}

// 班次查询列表
export function findWorkShift(query) {
  return postForm({
    url: ctx + '/workShift/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 班次查询详细
export function getWorkShift(id) {
  return request({
    url: ctx + '/workShift/get?id=' + id,
    method: 'get'
  })
}

//班次插入
export function insertWorkShift(query) {
  return request.post(ctx + '/workShift/insert', query);
  // return postForm({
  //   url: ctx + '/scheduleUser/insert',
  //   data: query,
  //   headers: {repeatSubmit: false}
  // })
}

// 班次删除
export function delWorkShift(id) {
  return request({
    url: ctx + '/workShift/del/' + id,
    method: 'delete'
  })
}


// 排班查询列表
export function findAlloteWork(query) {
  return postForm({
    url: ctx + '/allotedWork/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 排班查询详细
export function getAlloteWork(id) {
  return request({
    url: ctx + '/allotedWork/get?id=' + id,
    method: 'get'
  })
}

//排班修改 添加
export function insertAlloteWork(query) {
  return request.post(ctx + '/allotedWork/insert', query);
  // return postForm({
  //   url: ctx + '/scheduleUser/insert',
  //   data: query,
  //   headers: {repeatSubmit: false}
  // })
}

// 排班删除
export function delAlloteWork(id) {
  return request({
    url: ctx + '/allotedWork/del/' + id,
    method: 'delete'
  })
}

// 排班停止
export function updateStatusStop(id) {
  return request({
    url: ctx + '/allotedWork/updateStatusStop/' + id,
    method: 'post'
  })
}

// 自动释放
export function autoAllocate(query) {
  return postForm({
    url: ctx + '/autoAllocate',
    data: query,
    headers: {repeatSubmit: false}
  })
}
