import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel.js";

//逻辑
export default {

  extends: BaseDialogModel,

  components: {},//, ExamEquipRoom

  data() {
    return {
      rowList: [],
      spanArr: [],
      position: 0,
      listData: []
      
    };
  },
  methods: {
    //显示窗口
    showAllotedWorkEdit(row,column) {
      this.open();
    },

    queryData(){//查询操作
  		this.listData = [
  			{
        	id:1,
          type:1,
          sheetType: "事件单",
          taskKey: "shijian_01",
          templateUrl: "/shijian_01"
        },
        {
        	id:2,
          type:1,
          sheetType: "事件单",
          taskKey: "shijian_02",
          templateUrl: "/shijian_02"
        },
        {
        	id:3,
          type:1,
          sheetType: "事件单",
          taskKey: "shijian_03",
          templateUrl: "/shijian_04"
        },
        {
        	id:4,
          type:2,
          sheetType: "问题单",
          taskKey: "wenti_01",
          templateUrl: "/wenti_01"
        },
        {
        	id:5,
          type:2,
          sheetType: "问题单",
          taskKey: "wenti_02",
          templateUrl: "/wenti_02"
        },
        {
        	id:6,
          type:2,
          sheetType: "问题单",
          taskKey: "wenti_03",
          templateUrl: "/wenti_03"
        }
  		];
		  this.rowspan()
  	},
  	rowspan() {
      let vm = this;
  		vm.listData.forEach((item,index) => {
	    	if( index === 0){
	    		vm.spanArr.push(1);
	    		vm.position = 0;
	    	}else{
	    		if(vm.listData[index].type === vm.listData[index-1].type ){
	    			vm.spanArr[vm.position] += 1;
	    			vm.spanArr.push(0);
	    		}else{
	    			vm.spanArr.push(1);
	    			vm.position = index;
	    		}
	    	}
	    })
  	},
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {  //表格合并行
    	
    	if(columnIndex === 1){
    		const _row = this.spanArr[rowIndex];
    		const _col = _row>0 ? 1 : 0;
    		return {
    			rowspan: _row,
    			colspan: _col
    		}
    	}
    }
  },

  mounted() {
    this.queryData();
  },

  created() {
    
  }
};
