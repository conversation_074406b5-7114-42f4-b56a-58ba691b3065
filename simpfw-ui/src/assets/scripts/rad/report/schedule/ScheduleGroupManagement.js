import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";
import {undefinedOrNull, currDate} from "@/utils/common";
import {cloneDeep} from "lodash";

//检查信息
import * as exeiapi from "@/assets/scripts/rad/exammanagement/examinfo/api";

//表单标识
const formSimp = "simp";

//逻辑
export default {

  extends: BaseGridModel,

  components: {},//, ExamEquipRoom

  dicts: ["uis_exam_result_status"],

  data() {
    return {
      searchReportType:"1",
      checked1: true,
        checked2: true,
      editAllotedWork:{
        user:{
          selectUser:null,
          userOptions: [],
        },
      },
      editUserGroup:{
        title:null,
        isAdd:false,
        isVisible:false,
        selectUser:"",
        userOptions: [],

        selectGroup:"",
        groupOptions: [],
        tableSelectUser:{
          userName:null,
        }, 
      },

      /**
       * 搜索框
       */
      searchForm:{
        id: null,
        examNo: null,
        examItemCodes: [],
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModality: {dictCode: null},
        examDoctor: {nockName: null},
        reportDoctor: {nockName: null},
        auditDoctor: {nockName: null},

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        },

        inpTypeValues: [],
        pageSize:17, //100%情况下，17条刚刚好满屏
      },

      searchFormSimp: {
        textFieldName: 'patientInfo.name',
        textField: null,

        dateFieldName: "createTime",
        dateFieldGe: null,
        dateFieldLt: currDate(),
        
        inpTypeValues: [],
        examDevicesCode: [],
        resultStatusValues: [],
        equipRoomsCode: [],
        reportDoctor:{
            nickName:null,
        },
      },

      //当前查询表单
      lastSearchForm: formSimp,

      groupGrid:{

        editGroupDialog:{
          title:null,
          isAdd:false,
          isVisible:false,
          groupName:null,
          groupCode:null,
        },

        groupMenu: {
          label: '全部',
          value:'all',
          children: []
        },

        groupMenuSelection:null,
      },

      

      inputGroupName:"",
      value: '',
    };
  },

    methods: {

    reset() {
      this.editUserGroup = {
        title:null,
        isAdd:false,
        isVisible:false,
        selectUser:"",
        userOptions: [],

        selectGroup:"",
        groupOptions: [],
        tableSelectUser:{
          userName:null,
        }, 
      }
    }, 

    handleNodeClick(data) {
      console.log(data);
    },


    getScheduleGroupList(mix) {
      let vm = this;
      let params;
      vm.groupGrid.groupMenu.children.length = 0;
      eiapi.findScheduleGroup(params).then(res => {
        for(var i=0;i<res.rows.length;i++){
          vm.groupGrid.groupMenu.children.push({label:res.rows[i].groupName,value:res.rows[i].groupCode});
        }
      });
    }, 

    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号/检查房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }
      //登记房间
      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },
    //显示检查详情
    handleDetail(row) {
      //this.$modal.msg("未实现");
      this.$refs.examViewer.view(row);
    },

    /**
     * 搜索
     */
    getList(mix) {
      let vm = this;
      let params;
      //
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      if(formSimp ===  this.lastSearchForm) {
        //
        params = cloneDeep(this.searchFormSimp);
        //
        if(!!params.textField) {
          params[params.textFieldName] = params.textField;
        }
        delete params.textFieldName;
        delete params.textField;
        //
        if(!!params.dateFieldGe || !!params.dateFieldLt) {
          params[params.dateFieldName + 'Ge'] = params.dateFieldGe || null;
          params[params.dateFieldName + 'Lt'] = params.dateFieldLt || null;
        }
        delete params.dateFieldName;
        delete params.dateFieldGe;
        delete params.dateFieldLt;
      } else {
        params = cloneDeep(this.searchForm);
      }

      if("1"==vm.searchReportType){
        params["reportDoctorCodeAllocate"] = this.editAllotedWork.user.selectUser;
      }else if("2"==vm.searchReportType){
        params["auditDoctorCodeAllocate"] = this.editAllotedWork.user.selectUser;
      }
      

      if(params.examItemCodes) {
        params.examItemCodes.forEach((c, i) => {
          params["examItemCodes[" + i + "]"] = c;
        });
        delete params["examItemCodes"];
      }
      //
      //if(!(mix instanceof Event) && (mix instanceof Object)) {
      //  if(mix.page) { params.pageNum = mix.page; }
      //  if(mix.limit) { params.pageSize = mix.limit; }
      //}
      //已删除状态
      let pos;
      if(!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
          params.resultStatusValues.splice(pos, 1);
          //params.resultStatusAsStatus = "2";
          params.status = 2;
      } else {
        params.status = 0;
      }

      this.loading = true;
      exeiapi.find(params).then(res => {
        this.loading = false;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    }, 

    //查询
    search(opt) {
      if(!!opt && (opt instanceof Event)) {
        let el = 13 === opt.which? (opt.target || opt.currentTarget) : null;
        if(!!el && "INPUT" !== el.nodeName) {
          return;
        }
      }

      this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    addGroup(){
      this.groupGrid.editGroupDialog.title = "添加";
      this.groupGrid.editGroupDialog.isAdd=true,
      this.groupGrid.editGroupDialog.isVisible = true;
      this.groupGrid.editGroupDialog.groupName = null;
      this.groupGrid.editGroupDialog.groupCode = null;
    },

    modifyGroup(){
      if(this.groupGrid.groupMenuSelection=="all"||undefined==this.groupGrid.groupMenuSelection){
        this.$modal.msg("请选择一个组别");
        return;
      }
      for(var i=0;i<this.groupGrid.groupMenu.children.length;i++){
        if(this.groupGrid.groupMenuSelection==this.groupGrid.groupMenu.children[i].value){
          this.groupGrid.editGroupDialog.groupName = this.groupGrid.groupMenu.children[i].label;
        }
      }
      this.groupGrid.editGroupDialog.title = "修改";
      this.groupGrid.editGroupDialog.isAdd=false,
      this.groupGrid.editGroupDialog.groupCode = this.groupGrid.groupMenuSelection;
      this.groupGrid.editGroupDialog.isVisible = true;
    },

    autoAllocate(){
      let vm = this;
      let params = {};
      if(vm.checked1){
        params["reportDoctorCodeAllocate"] = vm.editAllotedWork.user.selectUser;
      }
      if(vm.checked2){
        params["auditDoctorCodeAllocate"] = vm.editAllotedWork.user.selectUser;
      }

      eiapi.autoAllocate(params).then(res => {
        
      });
    },

    delGroup(){
      const vm = this;
      if(this.groupGrid.groupMenuSelection=="all"||undefined==this.groupGrid.groupMenuSelection){
        this.$modal.msg("请选择一个组别");
        return;
      }

      
      vm.$modal.confirm('是否确认删除？').then(() => {
        let params = [];
        params.push(this.groupGrid.groupMenuSelection);

        return eiapi.delScheduleGoup(params);
      }).then(() => {
        vm.getGroupOptions();
        vm.$modal.msgSuccess("删除成功");
      }).catch((err) => {if("cancel" !== err) {console.error(err);vm.$modal.msgError(err);}});

    },

    etUserOptions(){
      let params;
      eiapi.findScheduleUser(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editAllotedWork.user.userOptions.push({label:res.rows[i].userName,value:res.rows[i].userCode});
          if(undefined!=res.rows[i].group) this.editAllotedWork.group.groupOptions.push({label:res.rows[i].group.groupName,value:res.rows[i].userCode});
        }
      });
    },

    getGroupOptions(){
      let params;
      this.editUserGroup.userOptions = [];
      eiapi.findScheduleGroup(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editUserGroup.groupOptions.push({label:res.rows[i].groupName,id:res.rows[i].groupCode});
        }
      });
    },

    userGroupConfirm(){
      if(!this.editUserGroup.isAdd){
        this.editUserGroup.selectUser = this.editUserGroup.tableSelectUser.id;
      }

      if(this.editUserGroup.selectUser == undefined){
        this.$modal.msg("请选择一个医生");
        return;
      }


      if(this.editUserGroup.selectGroup == undefined){
        this.$modal.msg("请选择一个分组");
        return;
      }

      this.editUserGroup.isVisible = false;

      let params =[];
      params.push({id:this.editUserGroup.selectUser,groupCode:this.editUserGroup.selectGroup});
      eiapi.insertScheduleUser(params).then(res => {
        this.getList();
        this.getUserOptions();
      });
    },

    addUserGroup(){
      this.editUserGroup.isVisible = true;
      this.editUserGroup.selectGroup = null;
      this.editUserGroup.selectUser = null;
      this.editUserGroup.isAdd =true;
    },

    updateUserGroup(row){
      this.editUserGroup.tableSelectUser = row;
      this.editUserGroup.isVisible = true;
      if(null!=row.group) this.editUserGroup.selectGroup = row.group.groupName;
      this.editUserGroup.isAdd =false;
    },

    deleteUserGroup(row){
      let params =[];
      params.push({id:row.id,deleteGroupCode:true});
      eiapi.insertScheduleUser(params).then(res => {
        this.getList();
        this.getUserOptions();
      });
    },

    groupMenuSelection(key, keyPath) {
      this.groupGrid.groupMenuSelection = key;
      this.getList();
    },

    userSelectChanged(){
      
    },

    getUserOptions(){
      let params;
      eiapi.findScheduleUser(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editAllotedWork.user.userOptions.push({label:res.rows[i].userName,value:res.rows[i].userCode});
        }
      });
    },

    editGroupDialogConfirm(){
      if(undefined==this.groupGrid.editGroupDialog.groupName){
        this.$modal.msg("请填写组别名称");
        return;
      }
      if(undefined==this.groupGrid.editGroupDialog.groupCode){
        this.$modal.msg("请填写组别代码");
        return;
      }

      let groupObj = {groupCode:this.groupGrid.editGroupDialog.groupCode,
        groupName:this.groupGrid.editGroupDialog.groupName};
      eiapi.insertScheduleGoup(groupObj).then(res => {
        this.$modal.msg("成功");
        this.getGroupOptions();
        this.groupGrid.editGroupDialog.isVisible = false;
      }).catch((err) => {
        if("cancel" !== err) {
          console.error(err);vm.$modal.msgError(err);
        }
        this.groupGrid.editGroupDialog.isVisible = false;
      });
      
    },
  },

  created() {
    this.getList();
    this.getScheduleGroupList();
    this.getUserOptions();
    this.getGroupOptions();
  }
};
