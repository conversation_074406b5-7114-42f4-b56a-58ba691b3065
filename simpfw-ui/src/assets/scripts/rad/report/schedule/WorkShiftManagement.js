import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";
import * as ExPapi from "@/assets/scripts/pacs/comcfg/examparts/api";

//逻辑
export default {

  extends: BaseGridModel,

  components: {},//, ExamEquipRoom

  dicts: ['uis_exam_parts_type'],

  data() {
    return {
      centerDialogVisible: false,
      editWorkShiftDialog:{
        title:null,
        shiftCode:null,
        shiftName:null,
        beginTime:null,
        endTime:null,
        noteInfo:null
      },

      groupGrid:{

        editGroupDialog:{
          id:null,
          title:null,
          isVisible:false,
          groupCode:null,
          groupOptions: [],
        },

        total: 0,
        //
        pager: {
          total: 0,
          pageNum: 1,
          pageSize: 10
        },
        // 表格数据
        data: []
      },

      
    };
  },
  methods: {
    /**
         * 搜索
         */
    getList(mix) {
      let params;
      this.load = false;
      eiapi.findWorkShift(params).then(res => {
        this.load = true;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    }, 

    //查询
    search(opt) {
      // this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    addGroup(){
      this.groupGrid.editGroupDialog.title = "添加";
      this.groupGrid.editGroupDialog.id=null;
      this.groupGrid.editGroupDialog.isAdd=true,
      this.groupGrid.editGroupDialog.isVisible = true;
      this.groupGrid.editGroupDialog.groupCode = null;
    },

    getScheduleGroupList(mix) {
      let vm = this;
      let params;
      eiapi.findScheduleGroup(params).then(res => {
        this.groupGrid.total = res.total;
        this.groupGrid.data = res.rows;
      });
    }, 

    editGroupDialogConfirm(){
      let vm = this;
      // if(undefined==this.groupGrid.editGroupDialog.groupCode){
      //   this.$modal.msg("请选择组别");
      //   return;
      // }
      let params;
      eiapi.findScheduleGroup(params).then(res => {
        let data = res.rows;
        var group = data.find(d=>d.groupCode==vm.groupGrid.editGroupDialog.groupCode)
        //已经存在该分组
        if(undefined!=group){
          //新增
          if(undefined==vm.groupGrid.editGroupDialog.id){
            vm.$modal.msg("组别已存在，无法重复添加");
            return;
          }
          //修改
          else{
            if(vm.groupGrid.editGroupDialog.id!=group.id){
              vm.$modal.msg("组别已存在，无法修改");
              return;
            }
          }
          
        }

        var groupName =vm.groupGrid.editGroupDialog.groupOptions.find(d=>d.value==this.groupGrid.editGroupDialog.groupCode).label;
  
        let groupObj = {id:vm.groupGrid.editGroupDialog.id,
          groupCode:vm.groupGrid.editGroupDialog.groupCode,
          groupName:groupName};
        eiapi.insertScheduleGoup(groupObj).then(res => {
          vm.$modal.msg("成功");
          vm.groupGrid.editGroupDialog.isVisible = false;
          vm.getScheduleGroupList();
        }).catch((err) => {
          if("cancel" !== err) {
            console.error(err);vm.$modal.msgError(err);
          }
          vm.groupGrid.editGroupDialog.isVisible = false;
        });

        vm.groupGrid.editGroupDialog.isVisible = false
      });
   
    },

    addWorkShift(){
      this.centerDialogVisible = true;
      for (var key in this.editWorkShiftDialog) { 
        if (this.editWorkShiftDialog.hasOwnProperty(key)) { 
          this.editWorkShiftDialog[key] = null; 
        } 
      } 
      this.editWorkShiftDialog.title = "新增";
    },

    confirm(){
      let vm = this;
      vm.centerDialogVisible = false
      eiapi.insertWorkShift(this.editWorkShiftDialog).then(res => {
        vm.$modal.msgSuccess("修改成功");
        vm.getList();
      });
    },

    updateWorkShift(row){
      this.editWorkShiftDialog = row;
      this.centerDialogVisible = true;
      this.editWorkShiftDialog.title = "修改";
    },

    deleteWorkShift(row){
      const vm = this;
      vm.$modal.confirm('是否确认删除？').then(() => {
        return eiapi.delWorkShift(row.id);
      }).then(() => {
        vm.getList();
        vm.$modal.msgSuccess("删除成功");
      }).catch((err) => {if("cancel" !== err) {console.error(err);vm.$modal.msgError(err);}});
      // eiapi.delWorkShift(row.shiftCode).then(res => {
       
      // });
      // vm.$modal.confirm('是否确认删除？').then(
      //   eiapi.delWorkShift(row.shiftCode).then(res => {
      //     vm.$modal.msgSuccess("删除成功");
      //   })
      // );
    },

    modifyGroup(row){
      this.groupGrid.editGroupDialog.title = "修改";
      this.groupGrid.editGroupDialog.id=row.id;
      this.groupGrid.editGroupDialog.isAdd=false,
      this.groupGrid.editGroupDialog.groupCode = eval(row.groupCode);
      console.log(this.groupGrid.editGroupDialog.groupCode);
      console.log(this.groupGrid.editGroupDialog.groupOptions);
      this.groupGrid.editGroupDialog.isVisible = true;
    },

    delGroup(row){
      const vm = this;
      
      vm.$modal.confirm('是否确认删除？').then(() => {
        let params = [];
        params.push(row.id);

        return eiapi.delScheduleGoup(params);
      }).then(() => {
        this.getScheduleGroupList();
        vm.$modal.msgSuccess("删除成功");
      }).catch((err) => {if("cancel" !== err) {console.error(err);vm.$modal.msgError(err);}});

    },

    getGroupOptions(){
      let vm = this;
      ExPapi.treeselect().then(res => {
        let data = res.data;
        //
        data.forEach(e => {
          vm.groupGrid.editGroupDialog.groupOptions.push({label:e.label,value:e.id});
        });
        var aa = 1;
      });
    },
  },

  created() {
    this.getList();
    this.getScheduleGroupList();
    this.getGroupOptions();
  }
};
