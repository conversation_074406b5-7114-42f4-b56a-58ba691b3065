import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";
import {undefinedOrNull, currDate} from "@/utils/common";
import AllotedWorkEdit from '@/views/rad/report/schedule/AllotedWorkEdit';
import GroupAllotedWork from '@/views/rad/report/schedule/GroupAllotedWork';
import Test from '@/views/rad/report/schedule/Test';

//逻辑
export default {

  extends: BaseGridModel,

  components: {AllotedWorkEdit,GroupAllotedWork,Test},//, ExamEquipRoom

  data() {
    return {
      editUserGroup:{
        selectGroup:null,
        groupOptions: [],
      },

      editAllotedWorkDialog:{

      },

      /**
       * 搜索框
       */
      searchForm:{
        dateRange: [currDate(),currDate()],
        groupOptions: [],
      },

      tableColumns:[],
        
      GroupCode:"",
      
      value: '',
      openPreview:false,
    };
  },
  methods: {
    /**
         * 搜索
         */
    getList(mix) {
      let params = {};
      if(undefined!=this.editUserGroup.selectGroup&&"all"!=this.editUserGroup.selectGroup){
        params["groupCode"] = this.editUserGroup.selectGroup;
      }
      eiapi.findScheduleUser(params).then(res => {
        for(var i=0;i<res.rows.length;i++){
          //医生班次按日期合并
          const map=new Map();
          for(var j=0;j<res.rows[i].allotedWorkList.length;j++){
            const date = res.rows[i].allotedWorkList[j].workDate;
            const shiftName = res.rows[i].allotedWorkList[j].workShift.shiftName
            map.set(date,map.get(date)==undefined?shiftName:map.get(date)+shiftName);
          }

          map.forEach((val,key)=>res.rows[i][String(key)] = val);
          
        }

        this.grid.total = res.total;
        this.grid.data = res.rows;
        
      });
      //this.grid.data.push({doctorName:1,shiftName:[2,3,4,5,6]});
    }, 

    //查询
    search(opt) {
      // this.lastSearchForm = formSimp === opt? opt : null;
      var beginDate = this.searchForm.dateRange[0];
      
      var dateNum = (this.searchForm.dateRange[1]-this.searchForm.dateRange[0])/(3600 * 1000 * 24) + 1;
      this.tableColumns.splice(0,this.tableColumns.length);
      for(var i=0;i<dateNum;i++){
        var dateTime = new Date(beginDate);
        dateTime.setDate(dateTime.getDate()+i);

        var day = (dateTime.getDate()).toString();
        if(day.length==1){
          day = "0"+day;
        }

        var month = (dateTime.getMonth()+1).toString();
        if(month.length==1){
          month = "0"+month;
        }

        var date = String(dateTime.getFullYear()+"-" + month+"-" +day);//+"("+beginDate.getDay()+")";
        this.tableColumns.push({label:date,prop:date});
      }

      this.getList();
    },

    getGroupOptions(){
      let params;
      this.editUserGroup.groupOptions = [];
      eiapi.findScheduleGroup(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editUserGroup.groupOptions.push({label:res.rows[i].groupName,id:res.rows[i].groupCode});
        }
      });
    },

    test(){
      this.$refs.test.showAllotedWorkEdit();
    },

    repeated(){
      this.$refs.groupAllotedWork.showAllotedWorkEdit();
    },

    preview(){
      this.openPreview = true;
    },

    
    cellDbClick(row, column, cell, event){
      if(row.allotedWorkList.length<1) return;
      this.$refs.allotedWorkEdit.showAllotedWorkEdit(row, column, cell, event);
    },

    toAllotedWorkEdit(){
      this.$refs.allotedWorkEdit.showAllotedWorkEdit();
    },

    
    fetchData(){
      let vm = this;
      const tableToExcel = () => {
      let data=[];
      var fields = [];
      for(var i=0;i<vm.tableColumns.length;i++){
        fields.push(vm.tableColumns[i].label);
      }
      for (let index = 0; index < vm.grid.data.length; index++) {
        const element = vm.grid.data[index];
        let tmp = {};
        tmp["医生"] = vm.grid.data[index].userName;
        for(var i=0;i<fields.length;i++){
          tmp[fields[i]] =  undefined==vm.grid.data[index][fields[i]]?"":vm.grid.data[index][fields[i]];
        }
       
        data[index] = tmp;
      }
      
        // 要导出的json数据
        const jsonData = data;

        // var fieldsN = 0;
        // for(var key in vm.fields){
        //   fieldsN++;
        // }
        
        // let str = '<tr><td align="center" colspan='+ fieldsN+'><b>'+vm.tabsName[vm.timeType]+'</b></td></tr>';
        let str = '<tr><td><b>'+ "医生" + '</b></td>';
        for(var i=0;i<fields.length;i++){
          // 增加\t为了不让表格显示科学计数法或者其他格式
          str+=`<td><b>${fields[i] + '\t'}</b></td>`;     
        }
        str+=`</tr>`;     
        //let str = '<tr><td>姓名</td><td>电话</td><td>邮箱</td></tr>';
        // 循环遍历，每行加入tr标签，每个单元格加td标签
        for(let i = 0 ; i < jsonData.length ; i++ ){
            str+='<tr>';
            for(const key in jsonData[i]){
                // 增加\t为了不让表格显示科学计数法或者其他格式
                str+=`<td>${ jsonData[i][key] + '\t'}</td>`;     
            }
            str+='</tr>';
        }
        // Worksheet名
        const worksheet = 'Sheet1'
        const uri = 'data:application/vnd.ms-excel;base64,';
 
        // 下载的表格模板数据
        const template = `<html xmlns:o="urn:schemas-microsoft-com:office:office" 
        xmlns:x="urn:schemas-microsoft-com:office:excel" 
        xmlns="http://www.w3.org/TR/REC-html40">
        <head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
        <x:Name>${worksheet}</x:Name>
        <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
        </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
        </head><body><table>${str}</table></body></html>`;
        // 下载模板
        //window.location.href = uri + base64(template);
		    const link = document.createElement("a");
        link.href = uri + base64(template);
        // 对下载的文件命名
        link.download =  "排班表" + ".xls";
        link.click();
		

      };
    // 输出base64编码
    const base64 = s => window.btoa(unescape(encodeURIComponent(s)));

    tableToExcel();
    },
  },

  created() {
    this.searchForm.dateRange[1] = new Date().setDate(new Date().getDate()+7);
    this.search();
    this.getGroupOptions();
    
  }
};
