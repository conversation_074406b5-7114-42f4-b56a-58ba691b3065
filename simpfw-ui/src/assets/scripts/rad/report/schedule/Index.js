import ScheduleUserManagement from '@/views/rad/report/schedule/ScheduleUserManagement';
import ScheduleGroupManagement from '@/views/rad/report/schedule/ScheduleGroupManagement';
import WorkShiftManagement from '@/views/rad/report/schedule/WorkShiftManagement';
import AllotedWorkManagement from '@/views/rad/report/schedule/AllotedWorkManagement';

//逻辑
export default {
  name: "Schedule",


  components: {
    ScheduleUserManagement,
    ScheduleGroupManagement,
    WorkShiftManagement,
    AllotedWorkManagement
  },


  data() {
    return {
      activeComponentName:"groupManagement",
      slideMsgTabs: [
        {componentName: "scheduleUserManagement",label: "人员管理"},
        {componentName: "workShiftManagement", label: "班次管理"},
        {componentName: "allotedWorkManagement", label: "排班管理"},
        {componentName: "scheduleGroupManagement", label: "排班查询"},
      ],
    };
  },

  methods: {
    
    handleSlideClick(vm) {
      this.activeComponentName = vm.$attrs["componentName"]
    },
  }
};
