import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel.js";
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";

//逻辑
export default {

  extends: BaseDialogModel,

  components: {},//, ExamEquipRoom

  data() {
    return {
      rowList: [],
      spanArr: [],
      position: 0,
      listData: [],
      tableColumns:[],
    };
  },
  methods: {
    //显示窗口
    showAllotedWorkEdit(row,column) {
      this.queryData();
      this.open();
    },

    queryData(){//查询操作
      
      let params;
      
      eiapi.findAlloteWork(params).then(res => {
        let vm = this;
        vm.listData=[];
        vm.tableColumns = [];
        const mapGroup=new Map();
        for(var i=0;i<res.rows.length;i++){
          //医生班次按日期合并
          var mapDate;
          var groupName = res.rows[i].userVo.group==undefined?"全部":res.rows[i].userVo.group.groupName;
          if(undefined==(mapDate=mapGroup.get(groupName))){
            mapDate=new Map();
          }
          var date = res.rows[i].workDate;
          var userName = res.rows[i].userVo.userName;

          var userNameSet = new Set();
          if(undefined==(userNameSet=mapDate.get(date))){
            userNameSet = new Set();
          }

          userNameSet.add(userName);

          mapDate.set(date,userNameSet);
          mapGroup.set(groupName,mapDate);

          //var isExit = false;
          
          // // mapDate.forEach((val,key)=>{
          // //   if(val==userName){
          // //     isExit = true;
          // //   };});
          // const s = new Set();
          // s.add(1);
          // s.add(1);
          
          // mapDate.set(date,mapDate.get(date)==undefined?userName:mapDate.get(date)+userName);
          
          
          
          // mapGroup.set(groupName,mapDate);
          // //map.forEach((val,key)=>res.rows[i][String(key)] = val);
          
        }

        var dateSet = new Set();
        mapGroup.forEach((val,key)=>{
          var temp = {};
          temp["groupName"] = key;
          var mapDate = val;
          mapDate.forEach((val,key)=>{
            var userNameSet = val;
            var temp2 = new String();
            var i =0;
            userNameSet.forEach((value, key) => {
              temp2+=(" "+ value.toString());
              if(i++%2) temp2+="\n";
            });
            temp[key] = temp2;
            dateSet.add(key);
          });
          vm.listData.push(temp);
        });

        dateSet.forEach((val,key)=>{
          vm.tableColumns.push({label:val,prop:val});
        });

        
        var aa = 0;
        //this.grid.total = res.total;
        // this.listData = res.rows;
        //vm.rowspan()
      });
		  
  	},
  	rowspan() {
  		this.listData.forEach((item,index) => {
	    	if( index === 0){
	    		this.spanArr.push(1);
	    		this.position = 0;
	    	}else{
	    		if(this.listData[index].shiftCode === this.listData[index-1].shiftCode ){
	    			this.spanArr[this.position] += 1;
	    			this.spanArr.push(0);
	    		}else{
	    			this.spanArr.push(1);
	    			this.position = index;
	    		}
	    	}
	    })
  	},
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {  //表格合并行
    	
    	if(columnIndex === 1){
    		const _row = this.spanArr[rowIndex];
    		const _col = _row>0 ? 1 : 0;
    		return {
    			rowspan: _row,
    			colspan: _col
    		}
    	}
    }
  },

  mounted() {
    this.queryData();
  },

  created() {
    this.queryData();
  }
};
