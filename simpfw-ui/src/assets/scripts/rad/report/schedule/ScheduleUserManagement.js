import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";
import { listUser } from "@/api/system/user";

//逻辑
export default {

  extends: BaseGridModel,

  components: {},//, ExamEquipRoom

 data() {
    return {
      test:1,
      editUserGroup:{
        title:null,
        isAdd:false,
        isVisible:false,
        selectUser:"",
        userOptions: [],

        selectGroup:null,
        groupOptions: [],
        tableSelectUser:{
          userName:null,
        }, 
      },

      searchSelectGroup:null,

      multipleSelection:[],
      allUserGrid: {
        multipleSelection:[],
        
        total: 0,
        //
        pager: {
          total: 0,
          pageNum: 1,
          pageSize: 10
        },
        // 表格数据
        data: []
      },
    };
  },

 
  methods: {

    getAllUserList(){
      listUser().then(response => {
        this.allUserGrid.data = response.rows;
        this.allUserGrid.total = response.total;
      }
    );
    },

    /**
         * 搜索
         */
    getList(mix) {
      let params = {};
      if(undefined!=this.editUserGroup.searchSelectGroup){
        params["groupCode"] = this.editUserGroup.searchSelectGroup;
      }
      
      eiapi.findScheduleUser(params).then(res => {
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    }, 

    //查询
    search(opt) {
      // this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    handleAllUserSelectionChange(val) {
      this.allUserGrid.multipleSelection = val;
    },

    handleUserSelectionChange(val) {
      this.multipleSelection = val;
    },

    updateUserGroup(row){
      this.editUserGroup.tableSelectUser = row;
      this.editUserGroup.isVisible = true;
      if(null!=row.group) this.editUserGroup.selectGroup = row.group.groupCode;
      this.editUserGroup.isAdd =false;
    },

    addScheduleUser(row){
      let vm = this;
      let params = [];
      var aa = this.test;
      if(undefined==row){
        for(var i=0;i<vm.allUserGrid.multipleSelection.length;i++){
          var user = {
            userCode:vm.allUserGrid.multipleSelection[i].userName,
            userName:vm.allUserGrid.multipleSelection[i].nickName,
            groupCode:vm.editUserGroup.searchSelectGroup,
            noteInfo:vm.allUserGrid.multipleSelection[i].remark};
          params.push(user);
        }
      }else{
        var user = {
          userCode:row.userName,
          userName:row.nickName,
          groupCode:vm.editUserGroup.searchSelectGroup,
          noteInfo:row.remark};
        params.push(user);
      }
      
      
      eiapi.insertScheduleUser(params).then(res => {
        this.getList();
      });
      
    },

    getGroupOptions(){
      let vm = this;
      let params;
      vm.editUserGroup.groupOptions = [];
      eiapi.findScheduleGroup(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          vm.editUserGroup.groupOptions.push({label:res.rows[i].groupName,value:res.rows[i].groupCode});
        }
        var aa = 0;
      });
    },
    

    userGroupConfirm(){
      if(!this.editUserGroup.isAdd){
        this.editUserGroup.selectUser = this.editUserGroup.tableSelectUser.id;
      }

      if(this.editUserGroup.selectUser == undefined){
        this.$modal.msg("请选择一个医生");
        return;
      }


      if(this.editUserGroup.selectGroup == undefined){
        this.$modal.msg("请选择一个分组");
        return;
      }

      this.editUserGroup.isVisible = false;

      let params =[];
      params.push({id:this.editUserGroup.selectUser,groupCode:this.editUserGroup.selectGroup});
      eiapi.insertScheduleUser(params).then(res => {
        this.getList();
        //this.getUserOptions();
      });
    },

    delScheduleUser(row){
      let params = [];
      if(undefined==row){
        for(var i=0;i<this.multipleSelection.length;i++){
          params.push(this.multipleSelection[i].userCode);
        }
      }else{
        params.push(row.userCode);
      }
      
      
      eiapi.delScheduleUser(params).then(res => {
        this.getList();
      });
      
    },
  },

  created() {
    this.getList();
    this.getAllUserList();
    this.getGroupOptions();
  },

  watch: {
    test(newVal,oldVal) {
      console.log(newVal);
      console.log(oldVal);
    } 
  }
};
