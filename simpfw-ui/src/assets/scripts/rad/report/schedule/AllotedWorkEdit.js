
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel.js";
import * as eiapi from "@/assets/scripts/rad/report/schedule/api";
//设备信息
import {findDevices} from "@/assets/scripts/pacs/equiproom/dicominfo/api";

export default {
  name: "AllotedWorkEdit",
  extends: BaseDialogModel,
  dicts: ["uis_inp_type", "uis_exam_modality","uis_job_content"],
  data() {
    return {
      // 遮罩层
      loading: false,
      grid: {
        total: 0,
        //
        pager: {
          total: 0,
          pageNum: 1,
          pageSize: 10
        },
        // 表格数据
        data: []
      },

      editAllotedWork:{
        user:{
          selectUser:null,
          userOptions: [],
        },
        group:{
          selectGroup:null,
          groupOptions: [],
        },

        workShift:{
          selectWorkShift:null,
          workShiftOptions: [],
        },
        titel:null,
        isAdd:false,
        id:null,
        workType:null,
        inpTypeValues:null,
        modalityType:null,
        beginTime:null,
        endTime:null,
        row:null,
        column:null,
        examDevicesCode:null,
        endTimeAvailable:false,
      },

      combo: {
        //设备型号列表
        devices: [],
      },

      multipleSelection:[],
    };
  },
  watch: {
    
  },
  created() {
    this.findDevices();
    this.getUserOptions();
    //this.getGroupOptions();
    this.getWorkShiftOptions();
  },
  methods: {

    refresh(){
      let vm = this;
      vm.editAllotedWork.id = null;
      vm.editAllotedWork.isAdd = false;
      vm.editAllotedWork.user.selectUser = null;
      vm.editAllotedWork.group.selectGroup = null;
      vm.editAllotedWork.workShift.selectWorkShift = null;
      vm.editAllotedWork.beginTime = null;//allotedWork.workDate.split(',')
      vm.editAllotedWork.inpTypeValues = null;
      vm.editAllotedWork.modalityType = null;
      vm.editAllotedWork.examDevicesCode = null;
      vm.editAllotedWork.workType = null;
    },

    //读取设备型号列表
    findDevices() {
      findDevices().then(res => {
        this.combo.devices = res.data;
      });
    },

    //显示窗口
    showAllotedWorkEdit(row,column) {
      
      let vm = this;
      vm.refresh();
      vm.editAllotedWork.endTime = null;
      vm.editAllotedWork.beginTime = null;
      if(undefined==row||undefined==column) {
        vm.editAllotedWork.titel = "添加";
        vm.editAllotedWork.isAdd = true;
        this.open();
        return;
      }
      
      vm.editAllotedWork.titel = "修改";
      vm.editAllotedWork.isAdd = false;
      vm.editAllotedWork.user.selectUser = row.userCode;
      vm.editAllotedWork.group.selectGroup = row.userCode;
      console.log(vm.editAllotedWork.group.selectGroup);
      console.log(vm.editAllotedWork.group.groupOptions);
      
      let workDate = column.property;
      let allotedWork;
      for(let i=0;i<row.allotedWorkList.length;i++){
        if(workDate==row.allotedWorkList[i].workDate){
          allotedWork = row.allotedWorkList[i];
        }
      }
      vm.editAllotedWork.id = allotedWork.id;
      vm.editAllotedWork.workShift.selectWorkShift = allotedWork.shiftCode.split(',');
      vm.editAllotedWork.beginTime = new Date(allotedWork.workDate);//allotedWork.workDate.split(',')
      vm.editAllotedWork.inpTypeValues = allotedWork.diagnosisType.split(',');
      vm.editAllotedWork.modalityType = allotedWork.modalityType.split(',');
      vm.editAllotedWork.examDevicesCode = allotedWork.devicesCode.split(',');
      vm.editAllotedWork.workType = allotedWork.workType.split(',');
      this.getList();
      this.open();
    },

    userSelectChanged(){
      let vm = this;
      ///如果用户没有组别
      vm.editAllotedWork.group.selectGroup = vm.editAllotedWork.user.selectUser;
    },

    getDictName(code,dict){
      if(undefined==code||undefined==dict) return null;
      let strRes = null;
      let codes = code.split(",");
      let dictMap = new Map();
      for(let i=0;i<dict.length;i++){
        dictMap.set(dict[i].value,dict[i].label);
      }
      for(let i=0;i<codes.length;i++){
        if(undefined==strRes) strRes = dictMap.get(codes[i]);
        else strRes = strRes + "," + dictMap.get(codes[i]);
      }

      return strRes;
    },

    getdevicesName(code,dict){
      if(undefined==code||undefined==dict) return null;
      let strRes = null;
      let codes = code.split(",");
      let dictMap = new Map();
      for(let i=0;i<dict.length;i++){
        dictMap.set(dict[i].deviceCode,dict[i].device);
      }
      for(let i=0;i<codes.length;i++){
        if(undefined==strRes) strRes = dictMap.get(codes[i]);
        else strRes = strRes + "," + dictMap.get(codes[i]);
      }

      return strRes;
    },

    /**
         * 搜索
         */
    getList(mix) {
      let vm = this;
      if(undefined==vm.editAllotedWork.user.selectUser){
        vm.$modal.msg("请选择医生");
        return;
      }
      //获取时间
      var day = (vm.editAllotedWork.beginTime.getDate()).toString();
      if(day.length==1){
        day = "0"+day;
      }

      var month = (vm.editAllotedWork.beginTime.getMonth()+1).toString();
      if(month.length==1){
        month = "0"+month;
      }

      var date = String(vm.editAllotedWork.beginTime.getFullYear()+"-" + month+"-" +day);

      let params ={userCode:vm.editAllotedWork.user.selectUser,
                   workDate:date,             
      };
      
      eiapi.findAlloteWork(params).then(res => {
        vm.grid.total = res.total;
        vm.grid.data = res.rows;
        for(let i=0;i<vm.grid.data.length;i++){
          
          vm.grid.data[i].workType = vm.getDictName(vm.grid.data[i].workType,vm.dict.type.uis_job_content);
          vm.grid.data[i].diagnosisType = vm.getDictName(vm.grid.data[i].diagnosisType,vm.dict.type.uis_inp_type);
          vm.grid.data[i].modalityType = vm.getDictName(vm.grid.data[i].modalityType,vm.dict.type.uis_exam_modality);
          vm.grid.data[i].examDevicesCode = vm.getdevicesName(vm.grid.data[i].devicesCode,vm.combo.devices);
        }
        var aa = 0;
      });
      //this.grid.data.push({doctorName:1,shiftName:[2,3,4,5,6]});
    }, 

    //查询
    search(opt) {
      this.getList();
    },

    getUserOptions(){
      let params;
      eiapi.findScheduleUser(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editAllotedWork.user.userOptions.push({label:res.rows[i].userName,value:res.rows[i].userCode});
          if(undefined!=res.rows[i].group) this.editAllotedWork.group.groupOptions.push({label:res.rows[i].group.groupName,value:res.rows[i].userCode});
        }
      });
    },

    // getGroupOptions(){
    //   let params;
    //   eiapi.findScheduleGroup(params).then(res => {
    //     ///要取消分页
    //     for(var i=0;i<res.rows.length;i++){
    //       this.editAllotedWork.group.groupOptions.push({label:res.rows[i].groupName,value:res.rows[i].groupCode});
    //     }
    //   });
    // },

    getWorkShiftOptions(){
      let params;
      eiapi.findWorkShift(params).then(res => {
        ///要取消分页
        for(var i=0;i<res.rows.length;i++){
          this.editAllotedWork.workShift.workShiftOptions.push({label:res.rows[i].shiftName,value:res.rows[i].shiftCode});
        }
      });
    },

    getArString(array){
      var str = null;
      for(let i=0;i<array.length;i++){
        if(undefined==str) str = array[i];
        else str= str + "," + array[i];
      }
      return str;
    },

    confirm(){
      let vm = this;
      var alloteWorkList = [];
      var beginTime = this.editAllotedWork.beginTime;
      var dateNum = 1;
      if(undefined!=this.editAllotedWork.endTime){
        var endTime = this.editAllotedWork.endTime;
        dateNum = (endTime-beginTime)/(3600 * 1000 * 24)+1;
      }

      var inpTypeValues = vm.getArString(this.editAllotedWork.inpTypeValues);
      let modalityType = vm.getArString(this.editAllotedWork.modalityType);
      let examDevicesCode = vm.getArString(this.editAllotedWork.examDevicesCode);
      let workType = vm.getArString(this.editAllotedWork.workType);


      for(let i=0;i<vm.editAllotedWork.workShift.selectWorkShift.length;i++){
        let selectWorkShift = vm.editAllotedWork.workShift.selectWorkShift[i];
        for(var j=0;j<dateNum;j++){
          var dateTime = new Date(beginTime);
          dateTime.setDate(dateTime.getDate()+j);
          console.log("day" + dateTime.getDay());
          //周末跳过
          if(6==dateTime.getDay()||0==dateTime.getDay()) continue;
          var id = null;
          if(0==j) id = vm.editAllotedWork.id;
          var allotedWork={
            id:id,
            workDate:new Date(dateTime),
            userCode:vm.editAllotedWork.user.selectUser,
            shiftCode:selectWorkShift,
            diagnosisType:inpTypeValues,
            modalityType:modalityType,
            devicesCode:examDevicesCode,
            workType:workType,
          };
          alloteWorkList.push(allotedWork);
        }
      }
      
      eiapi.insertAlloteWork(alloteWorkList).then(res => {
        this.$emit("search");
      });

      this.close();
    },

    delAllotedWork(row){
      var a = row;
      let params = [];
      if(undefined==row){
        for(var i=0;i<this.multipleSelection.length;i++){
          params.push(this.multipleSelection[i].id);
        }
      }else{
        params.push(row.id);
      }
      
      
      eiapi.updateStatusStop(params).then(res => {
        this.getList();
        this.$emit("search");
      });
    },

    handleAlloteWorkSelectionChange(val){
      this.multipleSelection = val;
    }

  }
};