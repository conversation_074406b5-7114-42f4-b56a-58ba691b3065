import {cloneDeep, mergeWith} from "lodash";

import {mergeWithNotNull} from "@/utils/common";
//接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api.js";
//
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
//编辑
import {EditModel, UndoModel, TransModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth"
//定时刷新列表
let timer_getList;

export default {

  extends: BaseGridModel,
  mixins: [EditModel, UndoModel, TransModel],
  dicts: ["uis_exam_result_status",'uis_exam_item'],

  components: { Contextmenu, OperationAuth },

  props: {
    refresh: {type: Number},
    actions: {type: Array, default: () => []},
    filter: {type: Object}
  },

  data() {
    return {
      searchForm: {
        combo_props: [{value: "patientInfo.name", label: "姓名"}  
          , {value: "examNo", label: "检查号"}
          , {value: 'patientInfo.registNo',label:'登记号'}
          , {value: 'encounter.encounterMRN',label:'住院号'}
          , {value: 'callInfo.callNo',label:'排队号'}]

        , propName: "patientInfo.name"
        , propValue: null
        ,resultStatusValues:null
        ,examItemCodes:null
      },
      //查询次数
      numGetList: 0
    }
  },

  methods: {

    /**
     * 搜索
     */
    getList: function(opts) {
      clearTimeout(timer_getList);
      //
      this.handleCurrentChange(null);
      //查询参数
      let sfm = this.searchForm, params = {pageSize: sfm.pageSize, pageNum: sfm.pageNum};
      //父组件指定参数
      if(this.filter) {
        mergeWith(params, this.filter, true, mergeWithNotNull);
      }
      //
      this.triggerBind("dataChange", false);return;
      //点击按钮触发
      if(!timer_getList || opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }
      //
      eiapi.find(params).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;

        this.delayGetList();
        //首次查询，是否显示我的工作
        if(this.numGetList == 0) {
          const hasData = this.grid.pager.total > 0
          this.triggerBind("dataChange", hasData);
        }
        this.numGetList ++;
      }).catch(this.delayGetList);
    },
    //轮询
    delayGetList() {
      if(this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },

    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if(1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    //删除
    undoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if(res && 200 === res.code) { this.getList(); }
      });
    },
    //延迟检查
    handlePostpone(row) {
        let props = {examAtPm: 1};
        this.handleTrans(row, props).then(res => this.getList());
    },
    //
    focusPropValue() {
      try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    }
  },

  activated() {
    this.delayGetList();
  },

  deactivated() {
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  }
};
