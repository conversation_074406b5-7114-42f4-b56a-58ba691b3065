import { mapGetters } from 'vuex';
//工具
import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";
//cornerstone
import "@/assets/scripts/cornerstone/cornerstone-setup";

import {mergeWithNotNull, undefinedOrNull,fmt_exam_age} from "@/utils/common";
//获取当前用户信息
import { getUserProfile } from "@/api/system/user";
//检查信息接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//影像相关工具
import {FakeUidPrefix, FileTypes, assertJpeg, loadAndDisplay,isCompressed} from "@/assets/scripts/pacs/image/util";
//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";
import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel"
//
import ReportWriterImageComm from "@/assets/scripts/gis/report/comp/ReportWriterImageComm";
//import ReportPreviewer from "@/views/gis/report/comp/ReportPreviewer";
//放大影像
import ImageBubble from "@/views/pacs/report/comp/ImageBubble"
//
import VideoView from "@/views/pacs/report/comp/VideoView";
//检查状态变更
import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import {ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//表单
import ReportWriterForm from "@/assets/scripts/uis/report/comp/ReportWriterForm";
//
import ReportWriterDetail from "@/views/gis/report/comp/ReportWriterDetail";

import imageView from '@/views/pacs/image/imageView'
//选择用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//操作权限
import { default as PermiCheck} from "@/directive/permission/mixins";
//影像转移
import {ImageTransfer} from "@/assets/scripts/pacs/report/mixins/fun.imageTransfer";
//影像
import {ImageOperator} from "@/assets/scripts/pacs/report/mixins/fun.imageOperator";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//
import ExamPicker from "@/views/gis/exammanagement/patient/comp/ExamPicker";
//报告采集影像和报告影像
import { ReportImageFun } from "@/assets/scripts/pacs/image/fun.image";

import {resback as resbackImage, getPng as getReportPng,saveDesignReport as saveDesignReport} from "@/assets/scripts/pacs/image/api.imageTranster";

import {batchReport as batchReport} from "@/assets/scripts/uis/exammanagement/examinfo/api";
//
const ErrM_Unwrite = "请选择要书写的报告。", PERMI_AUDIT = "exam-report:audit";

import html2canvas from 'html2canvas';

/**
 * 空白表单
 */
function emptyForm() {
  return {

    patientInfo: {
      id: null,
      medicalRecordNo: null,
      name: null,

      gender: {},
      age: null,
      ageUnit: {},
    },

    examModality: {},
    examItem: {},
    examParts: [],
    resultStatus: {dictValue: null},
    //examResultProp: {dictValue: "0"},
    examResultProp: {},

    examDoctor: {},
    reportDoctor: {},

    examDoctorsName: null,
    examDoctorsCode: null,
    consultantsName: null,
    consultantsCode: null,
    recordersName: null,
    recordersCode: null,
    operDoctor: {userName: null, nickName: null},

    examDesc: null,
    examDiagnosis: null,
    operationSuggestion: null,
    //报告的影像
    images: [],
    //采集的影像
    dicomStudy: {seriesSet: null},
    dicomStudies: [],
    currentExamItemCode:null,
  }
}

export { emptyForm };

const model = {

  name: "otolReportWriter",

  components: {LinksignPopup, ImageBubble, ReportWriterDetail, imageView, UserPicker, VideoView, Contextmenu, ExamPicker},

  dicts: ["uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "uis_exam_result_prop"],

  mixins: [ResultStatus, ReportWriterForm, PermiCheck, ReportWriterImageComm, ImageTransfer, ImageOperator, ReportImageFun],

  data() {
    return {
      //当前用户
      currentUser: null,
      //报告内容
      reportForm: emptyForm(),
      //
      reportOrig: null,
      //
      qr: null,
      //
      editable: false,
      //双击图像显示大图
      dialogVisible:false,
      imageURL:'',
      currentItemIdx:null,

      isFistfocus: true,

      d_examResultProp:false,
      his_opt:{},
      eventM:null,
      imageNum:0,
      reportDSrc:null,
      blankReportDSrc:null,
      reportModelPath:{},
      ikey: new Date().getTime(),
      saveLoading: false,
      showG:false,
      exameTemp:null,
    };
  },

  methods: {
    pasteClick(event){
      this.handlePasteTest();
    },
    handleFocus(){
      //this.$refs.uploadImage.blur()
    },

    async handlePasteTest() {
      // var value = null;
      const clipboard = navigator.clipboard;
      if(undefined==clipboard) return;
      const clipboardItems = await clipboard.read();

      for (const clipboardItem of clipboardItems) {
        for (const type of clipboardItem.types) {
          // 筛选图片类型的文件
          if (type.indexOf('image') > -1) {
            let vm = this;
            const blob = await clipboardItem.getType(type);
            // 将Blob转换成File
            let file = new File([blob], `image-${Date.now()}`, { type: type })
            // fileList.push(file)
            // // 将Blob转换成url，方便前端展示预览图
            // const url = URL.createObjectURL(blob)
            // urlList.push(file)


          //去除粘贴到div事件
          // event.preventDefault();
          // event.returnValue=false;
          // let file = null;
          // if (!items || items.length === 0) {
          //   this.$message.error("当前不支持本地图片上传");
          //   return;
          // }
          // // 搜索剪切板items
          // for (let i = 0; i < items.length; i++) {
          //   if (items[i].type.includes('image')) {
          //     file = items[i].getAsFile();
          //     break;
          //   }
          // }
          if (!file) {
            vm.$message.error("粘贴内容非图片");
            return;
          }

          var form = new FormData()

          form.append("file", file);
          //

          form.append("examInfoId", vm.reportForm.id);

          resbackImage(form).then(r => {
            vm.afterResbackImage();
          }).catch(err => vm.processing = false);
              }
            }
          }


    },

    handlePaste(event) {
      if(undefined==event) return ;
      let vm = this;
      const items = (event.clipboardData || window.clipboardData).items;
      //去除粘贴到div事件
      event.preventDefault();
      event.returnValue=false;
      let file = null;
      if (!items || items.length === 0) {
        this.$message.error("当前不支持本地图片上传");
        return;
      }
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.includes('image')) {
          file = items[i].getAsFile();
          break;
        }
      }
      if (!file) {
        this.$message.error("粘贴内容非图片");
        return;
      }

      var form = new FormData()

      form.append("file", file);
      //

      form.append("examInfoId", this.reportForm.id);

      resbackImage(form).then(r => {
        vm.afterResbackImage();
      }).catch(err => this.processing = false);
    },
    //初始
    onPageLoad() {
      //当前用户信息
      if(!this.currentUser) {
        getUserProfile().then(res => {
          this.currentUser = res.data;
        });
      }
      //加载历史表单数据，获取记录人员，检查医生，会诊医生
      if(!!this.reportForm_his){
          this.reportForm.recordersName=this.reportForm_his.recordersName
          this.reportForm.examDoctorsName=this.reportForm_his.examDoctorsName
          this.reportForm.consultantsName=this.reportForm_his.consultantsName
      }

      //表单元素焦点
      const fme =this.$refs.reportForm.$el;
      //触发的源：检查所见，检查诊断，术后医嘱
      const eles = [fme.examDesc, fme.examDiagnosis, fme.operationSuggestion];
      eles.forEach(ele => {
        if(!ele) { return true; }
        ele.addEventListener('focus', evt => {
          const ths = (evt.target || evt.srcElement).name;
          this.triggerBind('focusFormField', ths);
        });
        /*ele.addEventListener('blur', evt => {
          this.triggerBind('focusFormField', null);
        });*/
      });
    },

    //表单
    fillReportForm(dat) {

      if(dat) {
        let fm = emptyForm();
        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //
        if(fm.examParts) {
          let examParts_names = [];
          fm.examParts.forEach(p => {
            examParts_names.push(p.partsName);
          });
          fm.examParts_names = examParts_names.join(",");
        }
        //
        /*if(!fm.examResultProp || !fm.examResultProp.dictCode) {
          fm.examResultProp = {dictValue: "0"};
        }*/
        //
        //console.log(this.currentUser);
        if((!fm.reportDoctor || !fm.reportDoctor.nickName) && this.currentUser) {
          fm.reportDoctor = this.currentUser;
        }
        //
        //if(fm.resultStatus && fm.resultStatus.dictValue === StatusDict.report
        //   && (!fm.auditDoctor || !fm.auditDoctor.userName) && this.currentUser) {
        //  fm.auditDoctor = this.currentUser;
        //}

        //当报告为未审核状态时，同时记录人员，检查医生，会诊医生为空，将历史保存填入
        //console.log(this.reportable,this.auditable,this.withdrawable,)
        if((!fm.recordersName) && !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && this.reportForm_his) {
            fm.recordersName = this.reportForm_his.recordersName;
        }
        if((!fm.examDoctorsName)&& !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && !this.reauditable && this.reportForm_his) {
            fm.examDoctorsName = this.reportForm_his.examDoctorsName;
        }
        if((!fm.consultantsName)&& !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && !this.reauditable && this.reportForm_his) {
            fm.consultantsName = this.reportForm_his.consultantsName;
        }

        this.reportForm = fm;
        this.currentExamItemCode = this.reportForm.examItem.dictValue;
        //Object.assign(this.reportForm, fm);
        console.log("this.currentExamItemCode",this.currentExamItemCode);
        this.reportOrig = cloneDeep(fm);
      }
    },

    clearReport(){
      const vm = this;
      let exam = vm.reportForm;
      vm.$modal.confirm('是否确认清空报告数据？').then(() => {
        vm.loadReport(vm.reportModelPath[vm.reportForm.examItem.dictValue],vm.reportForm,undefined);
        eiapi.reportDataClear(exam).then(() => {
          vm.$modal.msgSuccess("清空成功");
        });
      })



    },

    /**
     * 回退报告
     */
    resultStatusRollback() {
      const vm = this;
      let exam = vm.reportForm;
      let reportModelPath = vm.reportModelPath[exam.examItem.dictValue];

      if (matchAnyStatus(exam, StatusDict.report)) {
        vm.$modal
          .confirm(
            "当前报告为已报告状态，是否确认清空报告数据并回退会已检查状态？"
          )
          .then(() => {
            // vm.loadReport(reportModelPath,exam,undefined);
            eiapi.resultStatusRollback(exam).then(() => {
              vm.loadReport(vm.reportModelPath[vm.reportForm.examItem.dictValue],vm.reportForm,undefined);
              vm.$modal.msgSuccess("回退成功");
            });
          });
      } else {
        vm.$modal
          .confirm(
            "当前报告为已检查状态，是否确认清空检查数据并回退会已检查状态？"
          )
          .then(() => {
            eiapi.resultStatusRollback(exam).then(() => {
              vm.loadReport(vm.reportModelPath[vm.reportForm.examItem.dictValue],vm.reportForm,undefined);
              vm.$modal.msgSuccess("回退成功");
            });
          });
      }
    },


    /**
     * 写报告
     */
    write(exam, opt = {},save,sendPicture) {
      this.exameTemp = exam;
      console.log("write-------------");
      let vm = this;
      let prom;
      //未保存检查
      const modi = this.checkModi();
      if(modi) {
        modi.unshift("当前报告有以下未保存修改，是否打开新报告？");
        prom = this.$modal.confirm(modi.join("<br/>- "), {dangerouslyUseHTMLString: true});
      } else {
        prom = Promise.resolve(1);
      }
      prom.then(() => {
        //
        this.isFistfocus = true;
        //
        this.clearAuditForPrint();
        //
        this.setTransferAction(null);
        //
        this.editable = false;
        this.reportForm = emptyForm();
        this.currentItemIdx = null;
        this.reportOrig = null;
        //
        if(!exam || !exam.id) {
          return;
        }
        eiapi.get(exam.id).then(res => {
          const dat = res && res.data;
          if(dat) {
            if(!!vm.reportModelPath[exam.examItem.dictValue]){
              this.saveLoading = true;
              //工作状态为登记或检查，使用模板
              const resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
              if(sendPicture){
                eiapi.viewUploadOrgReportPng(exam, 1).then(r => {
                  if(undefined!=r.data) {
                      setTimeout(() =>{
                        this.loadReport(vm.reportModelPath[exam.examItem.dictValue],exam,exam.id,"data:image/png;base64," + r.data[0]).then(res=>{
                          this.saveReport(exam.id,this.reportModelPath[exam.examItem.dictValue]).then(e=>{
                            this.saveLoading = false;
                          });
                        });
                      }, 1000);
                  }
                }, err => {
                  this.$modal.msgWarning("获取报告错误！");
                })
              }else{
                  this.loadReport(vm.reportModelPath[exam.examItem.dictValue],exam,exam.id).then(res=>{
                      this.saveLoading = false;
                  }).catch(e=>{
                    this.$modal.msgWarning("加载结构化报告错误！");
                    this.saveLoading = false;
                  });
              }
            }else{
              this.loadReport(vm.blankReportDSrc);
            }

            //
            let images = dat.images;
            if(images) {
              images.forEach(e => {
                const parts = e.path? e.path.split("/") : null;
                if(parts && parts.length == 3) {
                  const studyInstanceUid = parts[0], seriesInstanceUid = parts[1], sopInstanceUid = parts[2];
                  e["studyInstanceUid"] = studyInstanceUid;
                  e["seriesInstanceUid"] = seriesInstanceUid;
                  e["sopInstanceUid"] = sopInstanceUid;
                  /*let studyInstanceUid0 = studyInstanceUid;
                  if(FileTypes.jpeg === e.fileType && -1 === studyInstanceUid0.indexOf(FakeUidPrefix)) {
                    studyInstanceUid0 = FakeUidPrefix + studyInstanceUid0;
                  }
                  //e["uri"] = imapi.loadImage(studyInstanceUid0, seriesInstanceUid, sopInstanceUid);*/
                }
              });
            }
            this.fillReportForm(dat);
            //获取检查类型，放射检查时，是否使用多屏
            if(!this.splitScreen(dat)) {
              this.loadReportImages();
            }
            //审核打印，审核后生成报告文档：jpg、pdf
            const {nextTick} =  opt;
            if(!!nextTick && (nextTick.includes("print") || nextTick.includes("exportAsDoc"))) {
              //this.$nextTick(() => {
                this.handlePreview('report::' + nextTick.join(","));
              //});
            }
            //是否允许直接采集
            const fm = this.reportForm;
            if("/ReportWriting" === this.$route.path && matchAnyStatus(fm, StatusDict.regist, StatusDict.exam, StatusDict.report)) {
              this.triggerBind("dispatchAction", "report::activeCollectImage", fm);
            }
          }
        });
      });
    },

    // saveTemp(cmd){

    //   // this.saveReport(this.reportForm.id,this.reportModelPath[this.reportForm.examItem.dictValue],cmd.cmd);
    //   this.save({cmd:this.reportForm.id.cmd,firmed: true});
    // },

    batchAudit(){

      let form = new FormData();
      form.append("examInfos", this.reportForm.id);

      fun = eiapi.batchAuditPdf;
      const cup = (data) => {
        fun(form).then(res => {
          if('qrnoauth' === res.errC) {
            if(null == vm.qr) {
              vm.qr = new QRSignRel(() => {
                this.qr.activated = false;
                this.qr.openDig = false;
                ///opt.firmed = true;
                cup(data);
              });
            }
            this.qr.openDig = true;
            return;
          }

          this.write(fm, null,"save");
          this.triggerBind("refreshExamInfo");
        }).catch();
      };
      cup(form);
    },

    /**
     * 保存/审核报告
     * @param opt {cmd: null, firmed: false, silence: false}, 审核时传入cmd='audit'
     */
    save(opt = {},tmp) {
      this.saveLoading = true;
      let vm = this;
      document.addEventListener("keydown",function(e){
        // if(e.code===90){
          console.log("您按下的按钮的keyCode为：",e)
        // }
      })

      if(this.transferActive) {
        this.$modal.msgWarning("请取消转移采集影像。");
        return;
      }

      var fm = this.reportForm;
      if(!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      //须选择阴/阳
      // let erpc = !!fm.examResultProp? fm.examResultProp.dictValue : null;
      // if("0" !== erpc && "1" !== erpc && "2" !== erpc&&2!==fm.reportDataSource) {
      //   this.d_examResultProp=true
      //   this.his_opt=opt
      //   //this.$modal.alert("请明确该检查为阴性或阳性。");
      //   return;
      // }
      //
      if(!this.checkSelectedImagesCount()) {
        return;
      }
      //保存不执行未保存检查
      this.reportOrig = null;
      //审核
      const auditReport = "audit" === opt.cmd;
      //
      let confirmMsg = auditReport? "是否审核该报告？<br>" : "是否保存报告？";
      //是否弹出确认框
      let firmed = opt.firmed;

      let prom = firmed? Promise.resolve(true) : this.$modal.confirm(confirmMsg, {dangerouslyUseHTMLString: true});

      let promFun = () => {prom.then(() => {
        let postData = cloneDeep(fm);
        //dicom影像路径
        //const re = new RegExp("^[^/]+//[^/]+(/ext/dicom-web/rs/.+)$");
        //报告图片
        let images = postData.images.map(e => {

          return this.convImgToAtta(e);
        }).filter(e => !!e);
        postData.images = images;

        //保存当前form表单
        this.$store.dispatch("saveReportForm", cloneDeep(fm));

        if(auditReport) {
          return eiapi.auditReport(postData);
        } else {
          //更新呼叫机房未当前机房
          postData.equipRoom = this.currentEquipRoom;
          return eiapi.saveReport(postData);
        }
      }).then(res => {
        //console.log(res);
        //if(res && 200 == res.code) {
          //已知错误：获取扫码认证失败
          if('qrnoauth' === res.errC) {
            vm.saveLoading = false;
            if(null == this.qr) {
              this.qr = new QRSignRel(() => {
                this.qr.activated = false;
                this.qr.openDig = false;
                opt.firmed = true;
                this.save(opt);
              });
            }
            this.qr.openDig = true;
            return;
          }
          // if(!opt.silence) {
          //   this.$modal.msgSuccess("操作完成。");
          // }

          const reId = this.reportForm.id;
          const modelPath = this.reportModelPath[this.reportForm.examItem.dictValue];
            //重新读取修改的报告
            let rewriteOpt;
            //是否打印请求的审核
            if(auditReport) {
              //
              eiapi.get(fm.id).then(res => {
                let examInfoRes = res.data;
                //vm.saveReport(reId,modelPath).then(e=>{// console.log("saveIt",tmp);
                  //res.baseImg
                //iV
                let imgBa64 = "data:image/png;base64," + examInfoRes.signImage;
                // const contentValue = {auditDoctorSign:{src:imgBa64}};
                if(!!modelPath){
                  vm.saveReport(reId,modelPath,imgBa64,examInfoRes).then(e=>{// console.log("saveIt",tmp);
                    let reportDesignImg = [];
                    reportDesignImg.push(e.res.baseImg);
                    examInfoRes["reportDesignImg"] = reportDesignImg;
                    eiapi.uploadReportDoc(examInfoRes).catch(err=>{
                      this.$modal.msgWarning("报告上传异常！请重试！");
                      eiapi.updateResultStatus(fm);
                    });

                    //
                    let nextTick = [];//"exportAsDoc"
                    if(!!this.reportToAuditForPrint) {
                      nextTick.push("print");
                    }
                    rewriteOpt = {nextTick};

                    //清除打印审核请求
                    this.clearAuditForPrint();
                    //
                    this.write(examInfoRes, rewriteOpt,"save");
                    this.triggerBind("refreshExamInfo");

                    vm.saveLoading = false;
                  }).catch(err=>{
                    this.$modal.msgWarning("结构化报告保存异常！请重试！");
                    eiapi.updateResultStatus(fm);
                    vm.saveLoading = false;
                  });
                }else{
                  eiapi.uploadReportDoc(examInfoRes).catch(err=>{
                    this.$modal.msgWarning("报告上传异常！请重试！");
                    eiapi.updateResultStatus(fm);
                  });

                  //
                  let nextTick = [];//"exportAsDoc"
                  if(!!this.reportToAuditForPrint) {
                    nextTick.push("print");
                  }
                  rewriteOpt = {nextTick};

                  //清除打印审核请求
                  this.clearAuditForPrint();
                  //
                  this.write(examInfoRes, rewriteOpt,"save");
                  this.triggerBind("refreshExamInfo");
                  vm.saveLoading = false;
                }

                //});



              } ).catch(err=>{
                this.$modal.msgWarning("获取检查异常！审核失败，请重试！");
                eiapi.updateResultStatus(fm);
                vm.saveLoading = false;
              });

            }else{
              vm.saveReport(reId,modelPath,this.reportForm).then(e=>{// console.log("saveIt",tmp);
                vm.saveLoading = false;

                //清除打印审核请求
                this.clearAuditForPrint();
                //
                this.write(fm, rewriteOpt,"save");
                this.triggerBind("refreshExamInfo");
              });
            }
        //}
      }).catch(err => {
        console.error("执行出错, %s", err);
        //清除打印审核请求
        this.clearAuditForPrint();
      })
    }

      if(auditReport) {
        promFun();
      }else{
        promFun();
      }
    },
    //读取已采集的图片
    loadReportImages() {
      //this.collectImages();
      let fm = this.reportForm;
      imapi.findStudies({examInfo: {id: fm.id}, pageSize: 9999})
        .then(res => {
          this.imageNum = 0;
          const studies = res.rows;
          if(!studies || studies.length <= 0) { return; }

          studies.forEach(study => {
            if(!study) { return true; }
            let seriesSet = study.seriesSet;

            if(!seriesSet || seriesSet.length <= 0) { return true; }

            seriesSet.forEach(ser => {
              let imagesSet = ser.imagesSet;
              if(imagesSet && imagesSet.length) {
                imagesSet.forEach((img, i) => {
                  let {studyInstanceUid, fileType} = img;
                  img.studyInstanceUid0 = ((FileTypes.jpeg.name === fileType || FileTypes.jpeg.code === fileType)? FakeUidPrefix : "") + studyInstanceUid;
                  this.structExamImage(img, imagesSet, i);
                });
              }
            });

          });
          //console.log(study)
          fm.dicomStudies = studies;
          //console.log(fm);
          this.loadExamImages();
        });
    },

    //当前编辑的报告
    get() {
      return this.reportForm;
    },
    //更新指定属性“检查所见/检查诊断/术后医嘱”
    //@replace true-替换, false-末尾追加
    updateProp(nam, val, replace = true) {
      if(!this.reportable || !val) {
        return;
      }

      let fm = this.reportForm;
      if(!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      //console.log("name=%s, value=%s", nam, val);
      if(!replace) {
        val = (this.reportForm[nam] ? this.reportForm[nam]+'\n' : '')  + val;
      }
      //
      this.$set(this.reportForm, nam, val);
      //this.reportForm[nam] = val;
      //Object.assign(this.reportForm, this.reportForm);
    },

    // 设置图片类型，来源，选中状态/是否作为报告影像
    structExamImage(img, imagesSet, index) {
      if(!img.id) { return; }

      const {studyInstanceUid, sopInstanceUid, fileType} = img;
      let sopUri;
      //jpeg和动态
      if(this.typeofJpg(img) || this.typeofVideo(img)) {
        sopUri = imapi.imageLocate(img);
        //
        let selectedImages = this.reportForm.images;//已选影像
        let selectedImage = !!selectedImages? selectedImages.find(i => i.sopInstanceUid === sopInstanceUid) : null;

        img.fileType = img.fileType || FileTypes.jpeg.code;//assertJpeg(sopUri)? FileTypes.jpeg.name : FileTypes.dicom.name;
        img.uri = sopUri;
        img.selected = !!selectedImage;
        img.transferSelected = false;
        //为倒叙
        //selectedImages.forEach((ser, i) => {
        //    ser.selectNum = 1 + i;
        //})

        if(selectedImage) { selectedImage.uri = img.uri; }
      } else if(FileTypes.dicom.code == fileType || FileTypes.dicom.name == fileType) {
        if(0==index){
          this.imageNum += imagesSet.length;
        }

        if(0==index){
        //dicom
        //获取浏览地址，获取影像DICOM参数
        imapi.dcmViewer(this.reportForm.id, studyInstanceUid).then(res => {
          const dicomStudy = res.dicomStudy || {};
          if(!dicomStudy) {
            this.$modal.msgWarning("该检查没有影像信息。");
            return true;
          }
          const suid = dicomStudy.dicomStudyInstanceUid || dicomStudy.studyInstanceUid;
          return imapi.indexImages(suid);
        }).then(res => {
          const items = res.data;
          if(!!items && !!items.length) {
            items.forEach(ig => {
              //if(!ig.fileUrl.endsWith("/2")) {return true;}
              if(-1 != this.imagesSet.findIndex(e => e.sopInstanceUid === ig.sopInstanceUid)) { return true; }
              //
              const meta = JSON.parse(ig.noteInfo);
              const isCred = isCompressed(meta);
              //
              let fileUrl = ig.fileUrl, imageRend = 'wadors';
              if(isCred) {
                imageRend = 'wadouri';
                fileUrl = fileUrl.replace('/frames/', '/slice/');
              }
              //提交时保留的路径
              let selectedImages = this.reportForm.images;//已选影像
              let selectedImage = !!selectedImages? selectedImages.find(i => fileUrl.endsWith(i.path)) : null;
              //
              ig.uri = `${imageRend}:${fileUrl}`;
              ig.selected = !!selectedImage;
              imagesSet.push(ig);
              //元信息
              cornerstoneWADOImageLoader.wadors.metaDataManager.add(ig.uri, meta);
            });
            //this.loadExamImages();//this.$nextTick();
          }


        });

      }
      }
    },
    //cornerstone加载图片
    loadExamImages(opts = {}) {
      //
      //加载图片前，根据选择重新排序显示
    //   console.log(this.reportForm.images,this.imagesSet)
      this.reportForm.images.forEach((ser, i) => {
          ser.selectNum = 1 + i;
          this.imagesSet.forEach(img=>{
              if(this.isSameImage(ser, img)){
                  img.selectNum=ser.selectNum
              }
          })

      });
      //读取图像数据
      this.$nextTick(() => {
        document.querySelectorAll(".report-writer-image div.cornerstone-element").forEach((c, i) => {
          //console.log(i, c);
          const image = this.imagesSet[i];
          if(this.typeofVideo(image)) {
            cornerstone.disable(c);
          } else {
            try {
              //是否已
              cornerstone.getEnabledElement(c);
            } catch (err) {
              cornerstone.enable(c);
            }
            let idx = parseInt(c.parentNode.dataset.index);
            if(isNaN(idx)) {
              idx = i;
            }
            this.loadExamImage(c, idx);
          }
        });
        //滚动条到最近
        if(opts.focusLast) {
          this.$nextTick(() => {document.querySelector(".cornerstone-elements-pane").scrollLeft = 0;});
        }
      });
    },

    //获取>显示影像
    loadExamImage(element, idx) {
      const imagesSet = this.imagesSet;
      //console.log(imagesSet);
      let image = imagesSet[idx];
      if(!this.validateExamImage(image)) { return; }
      //console.log(image);
      let imageId = image.uri;//wadoForImage(image.uri);
      //console.log(imageId);
      try {
        loadAndDisplay(element, imageId);
      } catch (err) { console.error(err); }
    },
    //选择影像
    selectExamImage(state, item) {
      //
      item.selectNum = null;
      delete item.selectNum;
      //对比报告图像和采集图像
      let fimg = this.isSameImage;

      let selectedImages = this.reportForm.images;
      let idx = selectedImages.findIndex(si => fimg(si, item));

      //取消勾选
      if(!state && -1 !== idx) {
        selectedImages.splice(idx, 1);
        selectedImages.forEach((ser, i) => {
            ser.selectNum = 1 + i;
            this.imagesSet.forEach(img => {
                if(fimg(ser, img)){
                    img.selectNum=ser.selectNum
                }
            })
        })
      } else if(state && -1 === idx) {
        const count = selectedImages.length
        item.selectNum = count + 1;

        selectedImages.push(item);
      }
    },

    //删除影像
    deleteExamImage(item) {
      this.$modal.confirm("是否确定?").then(() => {
        const fm = this.reportForm, dicomStudy = fm.dicomStudies[0];

        if(!dicomStudy.seriesSet) { return ; }

        const series = dicomStudy.seriesSet.find(ser => ser.studyInstanceUid === item.studyInstanceUid && ser.seriesInstanceUid === item.seriesInstanceUid);

        let imagesSet = series? series.imagesSet : null;
        if(!imagesSet) { return ; }

        const idx = imagesSet.findIndex(i => i.sopInstanceUid === item.sopInstanceUid);
        if(-1 === idx) { return ; }
        const img = imagesSet[idx], selectedImages = fm.images;
        const pos = selectedImages.findIndex(si => si.sopInstanceUid === img.sopInstanceUid);
        if(-1 != pos) {
          selectedImages.splice(pos, 1);
        }
        imagesSet.splice(idx, 1);
        item.status = 2;
        //
        selectedImages.forEach((ser, i) => {
            ser.selectNum = 1 + i;
        })
        //
        //this.$nextTick(() => {
          imapi.delImage(item.id).then(this.loadExamImages);
        //});
          //
        if(this.dialogVisible) {
          const imagesLength=this.imagesSet.length-1
          //const delFlag=this.deleteExamImage(this.imagesSet[this.currentItemIdx])
          if(this.currentItemIdx>imagesLength){
              this.nextView(-1)
          }else if(imagesLength<0){
              this.dialogVisible=false
          }else{
              this.nextView(1)
          }
        }
    });
    },
    //是否图片已删除
    isExamImageDeleted(img) {
      return 2 === img.status;
    },
    validateExamImage(img) {
      return !this.isExamImageDeleted(img) && !!img.uri;
    },
    //上报危急值
    reportCriticalValues() {
      this.triggerBind("dispatchAction", "report::critical", this.reportForm);
    },
    //取消检查
    handleCancel() {
      this.confirmToUpdateResultStatus(this.reportForm, StatusDict.cancel).then(res => {
        this.$modal.msgSuccess("操作成功");
        this.triggerBind("refreshExamInfo");
      });
    },

    getNowTime() {
      var date = new Date();
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.date = date.getDate();
      this.hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      this.minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      this.second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      this.milliSeconds = date.getMilliseconds();
      var currentTime = this.year+'-'+this.month + '-' + this.date + ' ' + this.hour + ':' + this.minute + ':' + this.second + '.' + this.milliSeconds;
      return currentTime;
  },

    //预览
    handlePreview(mix) {
      let vm = this;
      //选择的图像情况
      if(!this.checkSelectedImagesCount()) {
        return;
      }
      //工作状态为检查中和已报告的，保存报告
      /*const exam = this.reportForm, resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
      if(!undefinedOrNull(resultStatusCode) && StatusDict.exam === resultStatusCode || StatusDict.report === resultStatusCode) {
        this.save({firmed: true, silence: true});
      }*/

      vm.getReportPng().then(e=>{
        let cmd = ("string" === (typeof mix)) && mix.startsWith("report::")? mix : "report::preview";
        let reportDesignImg = [];
        reportDesignImg.push(e.res);
        vm.reportForm["reportDesignImg"] = reportDesignImg;
        vm.triggerBind("dispatchAction", cmd, vm.reportForm);
      });

      // let cmd = ("string" === (typeof mix)) && mix.startsWith("report::")? mix : "report::preview";
      // vm.triggerBind("dispatchAction", cmd, vm.reportForm);
    },

    //审核
    handleAudit(mix) {
      if(!this.hasPermi(PERMI_AUDIT)) {
        this.$modal.alert("您没有审核报告的权限。");
        return;
      }
      let opt;
      //点击按钮审核
      if(mix instanceof Event) {
        this.clearAuditForPrint();

        opt = {};
      } else {
        opt = mix;;
      }
      opt.cmd = "audit";
      opt.firmed = true
      opt.silence = true;
      this.save(opt);
    },
    //复核
    handleReaudit() {
      const fm = this.reportForm;
      eiapi.reauditReport(fm).then(res => {
        this.$modal.msgSuccess("报告复核完成。");
        this.write(fm);
        this.triggerBind("refreshExamInfo");
      });
    },
    //采集调用
    addCollectImage(image) {
      const rp = this.reportForm;
      if(!this.reportable) {
        const rs = rp.resultStatus, statusLabel = !!rs? rs.dictLabel : "未知";
        this.$modal.alert("无法采集影像，原因：该报告状态为" + statusLabel);
        return ;
      }
      this.structExamImage(image);
      //
      let seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      let imagesSet = seriesSet && seriesSet.length? seriesSet[0].imagesSet : null;
      if(!imagesSet) {
        imagesSet = [];
        this.reportForm.dicomStudies[0].seriesSet = [{imagesSet}];
        seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      }
      seriesSet[0].studyInstanceUid = image.studyInstanceUid;
      seriesSet[0].seriesInstanceUid = image.seriesInstanceUid;
      imagesSet.push(image);
      //console.log(this.reportForm, seriesSet);
      this.loadExamImages({focusLast: true});
      //
      rp.resultStatus = Object.assign({}, rp.resultStatus, {dictValue: StatusDict.exam});
    },
    //放大影像
    zoomImage(evt) {
      let ele = !!evt? (evt.currentTarget || evt.target) : null, idx = !!ele? parseInt(ele.parentNode.dataset.index) : -1;
      if(-1 >= idx) {
        this.$refs.imageBubble.close();
        return ;
      }
      const imagesSet = this.imagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      //视频不预览
      if(this.typeofVideo(img)) {
        return;
      }
      //
      this.$refs.imageBubble.view(img, evt);
    },
    //预览影像
    /*viewImage(idx) {
      const imagesSet = this.imagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      this.$refs.imageView.view(img);
    },*/
    //聚焦最新采集的图像
    scrollImage(dir) {
      let imageScrollView = this.$refs.imageScrollView, cele;
      if(imageScrollView.clientWidth === imageScrollView.scrollWidth || !(cele = imageScrollView.querySelector(".cornerstone-element-container"))) { return; }

      let scollLeft = imageScrollView.scrollLeft;
      const scrollStep = 12 + cele.clientWidth;
      scollLeft += dir * scrollStep;
      imageScrollView.scrollLeft = Math.max(0, scollLeft);
    },
    //检查勾选的图像数是否合理
    checkSelectedImagesCount() {
      //图象数只能是0, 1, 2, 3, 4, 6
      const images = this.reportForm.images, numSupported = [0, 1, 2, 3, 4, 6];
      const numImages = !!images? images.length : 0;
      if(!numSupported.includes(numImages)) {
        this.$modal.alert("影像张数应为: " + numSupported.join(", "));
        return false;
      }

      return true;
    },
    //召回报告
    handleWithdrawAuditReport() {
      if(!this.hasPermi(PERMI_AUDIT)) {
        this.$modal.alert("您没有召回报告的权限。");
        return;
      }
      //this.$modal.confirm("是否确定召回报告？").then(res => {
          this.triggerBind("dispatchAction", "report::withdrawAudit", this.reportForm);
      //});
    },

    afterWithdrawReport(exam) {
      const contentValue = {sign:{src:""}};
      return this.saveReport(this.reportForm.id,this.reportModelPath[this.reportForm.examItem.dictValue],contentValue)
    },
    viewImage(evt){
        this.dialogVisible=true

        let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
        //console.log(this.imagesSet)
        this.imageURL=this.imagesSet[idx]
        this.currentItemIdx=idx
        //console.log( this.currentItemIdx)
    },
    nextView(val){

        const imagesLength=this.imagesSet.length-1
        if((-1===val&&this.currentItemIdx+val<0) || (1===val&&this.currentItemIdx+val>imagesLength)){
            return
        }
        this.currentItemIdx=this.currentItemIdx+val
        this.imageURL=this.imagesSet[this.currentItemIdx]
    },
    //播放动态采集
    viewVideo(evt) {
      let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
      const image = this.imagesSet[idx];
      if(!!image && !!image.uri && this.typeofVideo(image)) {
        this.$refs.videoView.view(image);
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param opts {}
     */
    toPickUser(opts) {
      const fm = this.reportForm;
      if(!fm.id) { return; }

      let tar = opts.target;
      let names, codes;
      if(tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }
      let selectedUsers = [];
      if(!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({userName: e, nickName: names[i]});
        });
      }
      opts.selectedUsers = selectedUsers;

      this.$refs.userPicker.showPicker(opts);
    },
    pickUser(tar, users) {
      let fm = this.reportForm, names = [], codes = [];
      if(!!users.length) {
        users.forEach(e => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }
      names = names.join(",");
      codes = codes.join(",");

      if(tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }
    },
    //
    cleanExam(){
      this.reportForm.examDesc=""
      this.reportForm.examDiagnosis=""
    },
    //清除打印审核请求
    clearAuditForPrint() {
      this.$store.dispatch("auditForPrint", null);
    },
    switchTab(val){
        if(this.isFistfocus){
          this.triggerBind('switchTab',val)
          this.isFistfocus=false
        }
    },
    //是否有未保存的修改
    checkModi() {
      let fields = [];

      const orig = this.reportOrig, fm = this.reportForm;
      if(orig && fm) {
        if(orig.examDesc != fm.examDesc) { fields.push("检查所见"); }
        if(orig.examDiagnosis != fm.examDiagnosis) { fields.push("检查诊断"); }
      }

      return fields.length > 0? fields : null;
    },
    //报告图像是否采集的图像
    isSameImage(rimg, cimg) {
      if(this.typeofDcm(cimg)) {
        return cimg.fileUrl === rimg.fileUrl || cimg.fileUrl.endsWith(rimg.path);
      } else {
        return rimg.sopInstanceUid == cimg.sopInstanceUid;
      }
    },


    loadReport(modelPath,exam,id,base64){
      // let modelPathAr = modelPath.split(';');
      // modelPath = modelPathAr[0];
      if(!modelPath) return Promise.resolve('未知结构化报告路径');
      let modelPathAr = modelPath.split(';');
      if(this.showG&&modelPathAr.length>1){
        modelPath = modelPathAr[1];
      }else{
        modelPath = modelPathAr[0];
      }

      let call = "_ctx.services.preview.loadReportOrTemplate";
      let args = [];
      // modelPath = this.reportModelPath[this.reportForm.examItem.dictValue]
      args.push(modelPath);
      // if(!!id)
      args.push(id+'');

      let argObj={};
      if(undefined!=exam&&matchAnyStatus(exam, StatusDict.regist, StatusDict.exam, StatusDict.report)){
        argObj["name"] = {text:exam.patientInfo.name};
        argObj["age"] = {text:exam.patientInfo.age + exam.patientInfo.ageUnit.dictLabel};
        argObj["sex"] = {text:exam.patientInfo.gender.dictLabel};
        argObj["examNo"] = {text:exam.examNo};
        argObj["bedNo"] = {text:exam.bedNo};
        argObj["registNo"] = {text:exam.patientInfo.registNo};
        argObj["birthday"] = {text:exam.patientInfo.birthday.substr(0, 10)};
        if(undefined!=exam.inpWard) argObj["inpWardName"] = {text:exam.inpWard.dictLabel};
      }
      // argObj["report"] = {src:base64}
      if(!!base64) {
        argObj["report"] = {src:base64}
      };
      args.push(argObj);

      //已审核报告禁止编辑
      if(undefined!=exam&&matchAnyStatus(exam, StatusDict.audit)){
        console.log("editable:false");
        let reportOption = {editable:false}
        args.push(reportOption);
      }

      console.log("loadReport",args,call);
      return sendMessageAsync({
        type: 'report-system-call-fn',
        args: args,
        call: call,
        id: undefined==id?'write':id
      })
    },

    loadAndSaveReportOrTemplate(modelPath,exam,id,base64){
      // let modelPathAr = modelPath.split(';');
      // modelPath = modelPathAr[0];
      if(!modelPath) return Promise.resolve('未知结构化报告路径');
      let modelPathAr = modelPath.split(';');
      if(this.showG&&modelPathAr.length>1){
        modelPath = modelPathAr[1];
      }else{
        modelPath = modelPathAr[0];
      }

      let call = "_ctx.services.preview.loadAndSaveReportOrTemplate";
      let args = [];
      // modelPath = this.reportModelPath[this.reportForm.examItem.dictValue]
      args.push(modelPath);
      if(!!id) args.push(id+'');

      let argObj={};
      if(undefined!=exam){
        argObj["name"] = {text:exam.patientInfo.name};
        argObj["age"] = {text:exam.patientInfo.age + exam.patientInfo.ageUnit.dictLabel};
        argObj["sex"] = {text:exam.patientInfo.gender.dictLabel};
        argObj["registNo"] = {text:exam.patientInfo.registNo};
        argObj["examNo"] = {text:exam.examNo};
        argObj["birthday"] = {text:exam.patientInfo.birthday.substr(0, 10)};
      }

      // argObj["report"] = {src:base64}
      if(!!base64) {
        argObj["report"] = {src:base64}
      };
      args.push(argObj);

      let reportOptione={};
      reportOptione["writeAfterLoad"] = true;

      if(!!id) args.push(reportOptione);

      console.log("loadAndSaveReportOrTemplate",args,call,id);
      return sendMessageAsync({
        type: 'report-system-call-fn',
        args: args,
        call: call,
        id: undefined==id?'write':id
      })
    },

    getReportPng(){
      let call = "_ctx.services.preview.getReportPng";
      console.log("getReportPng:",call)
      return sendMessageAsync({
        type: 'report-system-call-fn',
        args: [],
        call: call,
        id: 'getReportPng'
      })
    },

    postPicture(base64){
      let call = "_ctx.services.preview.reportContentRef.value.setWidgetContentValue";
      console.log("postPicture:",call)
      return sendMessageAsync({
        type: 'report-system-call-fn',
        args: ["signature",{src:base64}],
        call: call,
        id: 'postPicture'
      });
    },

    saveReport(id,modelPath,imgBa64,exam){
      if(!modelPath) return Promise.resolve('未知结构化报告路径');
      let modelPathAr = modelPath.split(';');
      if(this.showG&&modelPathAr.length>1){
        modelPath = modelPathAr[1];
      }else{
        modelPath = modelPathAr[0];
      }


      let vm = this;
      let call = "_ctx.services.preview.writeReport";
      let args = [];
      // modelPath = vm.reportModelPath[vm.reportForm.examItem.dictValue]
      args.push(modelPath);
      args.push(id+'');
      args.push(true);
      const contentValue = {};

      if(undefined!=exam&&StatusDict.audit==exam.resultStatus.dictValue&&undefined!=exam.auditTime) {
        contentValue["auditTime"] = {text:exam.auditTime.substr(0, 10)}
      }else
        contentValue["auditTime"] = {text:''};

      if(undefined!=imgBa64) {
        contentValue['auditDoctorSign'] = {src:imgBa64};
      }
      args.push(contentValue);
      console.log("saveReport:",args)
      return sendMessageAsync({
        type: 'report-system-call-fn',
        args: args,
        call: call,
        id: {id:id}
      })
    },

    base64ToFile(base64Data, filename) {
      const parts = base64Data.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);

      // 将原始数据转换为Uint8Array
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }

      // 使用Blob对象创建File对象
      const blob = new Blob([uInt8Array], {type: contentType});
      blob.lastModifiedDate = new Date();
      blob.name = filename;

      return new File([blob], filename, {type: contentType});
    },

    addListener() {
      let vm = this;
      (function (win, doc) {
          var ifr = doc.getElementById('reportTable').contentWindow;
          var cb = function (json) {
              eval(json);
          };
          console.log("addListener-------------");
          var sendMessageAsync = function (data) {
              return new Promise((resolve, reject) => {
                  if (win.postMessage) {
                      var listener = function (e) {
                          console.log('报表系统回调了report-call', e);
                          if (e.data && e.data.type && e.data.type === 'report-call-response') {
                            resolve(e.data); // 根据需要解析和返回数据
                            //   if ("_ctx.services.preview.loadReportOrTemplate" === e.data.call) {
                            //       console.log("report-call-responsei-id:", e.data.id);
                            //       // 处理加载模板的逻辑
                            //       resolve(e.data); // 根据需要解析和返回数据
                            //   } else if ("_ctx.services.preview.writeReport" === e.data.call && undefined !== e.data.res.baseImg) {
                            //       if (e.data.id.cmd) {
                            //           // 处理保存报告的逻辑
                            //           resolve(e.data); // 根据需要解析和返回数据
                            //       }
                            //   }else if ("_ctx.services.preview.getReportPng" === e.data.call && undefined !== e.data.res) {
                            //     if (e.data.id) {
                            //         // 处理保存报告的逻辑
                            //         resolve(e.data); // 根据需要解析和返回数据
                            //     }
                            // }
                          }
                      };
                      if (win.addEventListener) {
                          win.addEventListener("message", listener, false);
                      } else if (win.attachEvent) {
                          win.attachEvent("onmessage", listener);
                      }

                      // 发送消息
                      ifr.postMessage(data, '*');

                      // 清理监听器
                      // setTimeout(() => {
                      //     if (win.removeEventListener) {
                      //         win.removeEventListener("message", listener, false);
                      //     } else if (win.detachEvent) {
                      //         win.detachEvent("onmessage", listener);
                      //     }
                      // }, 5000); // 假设5秒后不再需要监听，可以根据实际情况调整
                  } else {
                      // 如果不支持postMessage，可以在这里处理备选方案
                      reject(new Error("postMessage not supported"));
                  }
              });
          };

          win.sendMessageAsync = sendMessageAsync;
      })(window, document);
  },
  },
  //加载完成执行
  mounted() {
    this.onPageLoad();
    let vm = this;

    // this.getConfigKey("reportDesignUrlTemp").then(response => {
    //   if(!response || !response.msg) { return; }
    //   let urlConfig = JSON.parse(response.msg);
    //   // for (let key in urlConfig) {
    //   //   if(undefined!= urlConfig[key]["model"]) urlConfig[key]["model"] = urlConfig[key]["baseUrl"]+ decodeURIComponent(urlConfig[key]["model"]);
    //   //   if(undefined!= urlConfig[key]["save"]) urlConfig[key]["save"] = urlConfig[key]["baseUrl"]+ decodeURIComponent(urlConfig[key]["save"]);
    //   //   if(undefined!= urlConfig[key]["rewrite"]) urlConfig[key]["rewrite"] = urlConfig[key]["baseUrl"]+ decodeURIComponent(urlConfig[key]["rewrite"]);
    //   //   if(undefined!= urlConfig[key]["getReportPng"]) {
    //   //     urlConfig[key]["getReportPng"] = urlConfig[key]["baseUrl"]+ decodeURIComponent(urlConfig[key]["getReportPng"]);
    //   //     vm.reportDSrc = urlConfig[key]["baseUrl"];
    //   //   }

    //   // }
    //   console.log("88888888888",this.reportForm)
    //   for(let key in urlConfig){
    //     vm.reportModelPath[key] = urlConfig[key].structTemplate
    //   }
    //   // vm.reportModelPath = urlConfig;
    //   vm.blankReportDSrc = urlConfig["blank"].structTemplate;
    //   vm.reportDSrc =  decodeURIComponent(urlConfig["baseUrl"]);
    //   //vm.reportDSrc = "http://localhost:8080/#/report/preview?path=/%E7%9C%BC%E7%A7%91/%E7%BA%AF%E9%9F%B3%E6%B5%8B%E5%90%AC.rtpl&op=write";
    //   this.addListener();
    //   console.log("mounted",vm.reportDSrc,urlConfig);
    // });

    this.getConfigKey("reportDesignTemplateConfig").then((response) => {
      if (!response || !response.msg) {
        return;
      }
      let urlConfig = JSON.parse(response.msg);

      vm.reportDSrc = decodeURIComponent(urlConfig["baseUrl"]);
      // vm.reportDSrc =decodeURIComponent('http://localhost:5173/#/report/preview?path=/%E7%A9%BA%E7%99%BD.rtpl&op=write&showOpButton=true');
      console.log("reportDSrc:",vm.reportDSrc)

    });

    this.getConfigKey("examIemConfigKey").then((response) => {
      if (!response || !response.msg) {
        return;
      }
      let reportDesignUrlKey = "reportDesignTemplateConfig";
      let urlConfig = JSON.parse(response.msg);
      for (let key in urlConfig) {
        if (!!urlConfig[key] && !!urlConfig[key][reportDesignUrlKey])
          vm.reportModelPath[key] =
            urlConfig[key][reportDesignUrlKey].structTemplate;
      }
      vm.blankReportDSrc = urlConfig["blank"][reportDesignUrlKey].structTemplate;
      this.addListener();
    });


  },
  //组件
  activated() {
    this.onPageLoad();
  },

  computed: {
    //当前机房，当前编辑的检查信息
    ...mapGetters(['currentEquipRoom', 'examInfoEdited', 'reportToAuditForPrint','reportToAudit','reportForm_his']),
    
    
    formattedExamAge() {
      if (!this.reportForm || !this.reportForm.examAge) return '';
      let age = fmt_exam_age(this.reportForm);
      return age;
    },

    //采集的影像
    imagesSet() {
      const fm = this.reportForm, dicomStudies = fm.dicomStudies;
      if(!dicomStudies || dicomStudies.length <= 0) { return; }

      let imagesSet = [];

      dicomStudies.forEach(dicomStudy => {
        const seriesSet = !!dicomStudy && !!dicomStudy? dicomStudy.seriesSet : null;
        if(!seriesSet) {
          return;
        }

        seriesSet.forEach(ser => {
            let images = ser.imagesSet;
            if (!images) {
                return true;
            }
            //刷新页面时,选中序号更新
            //var count = 1
            images.forEach(img => {
                if (img.status !== 2 && !!img.uri) {
                    imagesSet.push(img);
                }
                //if (img.selected) {
                //    img.selectNum = count++
                //}
            });
        });
      });
      //console.log(imagesSet);
      return imagesSet.reverse();
    },

    rollbackable() {
      const fm = this.reportForm;

      return (
        fm.id && //this.editable &&
        !fm.status &&
        matchAnyStatus(this.reportForm, StatusDict.exam, StatusDict.report)
      ); //(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
    },

    //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
    reportable() {
      const fm = this.reportForm;

      return fm.id //this.editable &&
      && (!fm.status)
      && matchAnyStatus(this.reportForm, StatusDict.regist, StatusDict.exam, StatusDict.report);//(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
    },
    //是否可审核
    auditable() {
      return matchAnyStatus(this.reportForm, StatusDict.report);//!!this.reportForm.resultStatus && '2' === this.reportForm.resultStatus.dictValue;
    },
    //是否可复审
    reauditable() {
      return matchAnyStatus(this.reportForm, StatusDict.audit);//!!this.reportForm.resultStatus && '3' === this.reportForm.resultStatus.dictValue;
    },
    //
    withdrawable() {
      return matchAnyStatus(this.reportForm, StatusDict.audit, StatusDict.reaudit);//!!this.reportForm.resultStatus && /^[34]$/.test(this.reportForm.resultStatus.dictValue);
    },
    //是否可打印
    printable() {
      //已报告、已审核、已复核、已打印、已归档
      return matchAnyStatus(this.reportForm, StatusDict.report, StatusDict.audit, StatusDict.reaudit, StatusDict.archive);
    },
    //提交按钮说明
    editButtonLabel() {
      const fm = this.reportForm;
      return matchAnyStatus(fm, StatusDict.regist, StatusDict.exam)? "提交"  : "保存";
    },
    //
    numImagesSelected() {
      const imagesSet = this.imagesSet;
      if(!imagesSet) { return 0; }

      return imagesSet.filter(i => i.selected).length;
    }
  },

  watch: {
    //当前报告检查信息是否有修改
    examInfoEdited: {
      deep: true,
      handler(nval, oval) {
        //console.log(nval, oval);
        let fm = this.reportForm;
        if(!!nval && !!nval.id && !!fm && nval.id === fm.id) {
          const props = ["inpNo", "bedNo"];
          props.forEach(p => {
            fm[p] = nval[p];
          })
        }
      }
    },

    //打印时要审核的报告
    reportToAuditForPrint: {
      deep: true,
      handler(nval, oval) {
        if(!nval) {
          return;
        }
        this.handleAudit({firmed: true});
      }
    },

    //打印时要审核
    reportToAudit: {
        deep: true,
        handler(nval, oval) {
          if(!nval) {
            return;
          }
          this.handleAudit({firmed: true});
        }
      }
  },

  activated(){
    //this.reportDSrc+="?aa = " +new Date().getTime()
    this.ikey = new Date().getTime();
    console.log("this.reportDSrc",this.reportDSrc);
  },

  deactivated(){
    console.log("this.reportDSrc",this.reportDSrc);
  },

  created() {
  },

};

export default model;
