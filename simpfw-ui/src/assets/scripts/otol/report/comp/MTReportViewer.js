import { cloneDeep } from "lodash";
//
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorkerSrc from 'pdfjs-dist/build/pdf.worker.entry';
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorkerSrc;
//
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
//
import * as api from "@/assets/scripts/uis/exammanagement/examinfo/api";
//报告采集影像和报告影像
import { ReportImageFun } from "@/assets/scripts/pacs/image/fun.image";
//
import { PageFitFun } from "@/assets/scripts/pacs/report/mixins/fun.PageFit";
//报告状态
import { StatusDict, matchAny as matchAnyResultStatus } from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//
const OPER_EXPORTASDOC = "exportAsDoc", OPER_PRINT = "print";
//
const MIMETYPES = {
  JPG: 'jpg',
  PDF: 'pdf'
};

//
const model = {
  extends: BaseDialogModel,

  mixins: [ReportImageFun, PageFitFun],

  props: {
    mimeType: { type: String, default: MIMETYPES.PDF },
    //预览上传的报告
    viewLoadReport: { type: Boolean, default: false }
  },

  data() {
    return {
      report: null,

      pdfDoc: null,
      numPdfPages: 0,

      extOperExportAsDoc: false,
      orgPdfReport: false,
      imageRef: null,
      imagesDoc: null
    }
  },

  methods: {

    switchPdf() {
      let vm = this;
      vm.orgPdfReport = !vm.orgPdfReport;
      this.loadPdfFile();
    },
    /**
     * 浏览
     * @param {*} report
     * @param {*} opt 'print'-打印
     */
    view(report, opt, url) {
      this.options = !!opt ? opt.split(",") : null;

      this.report = cloneDeep(report);

      this.open();
      this.loadPdfFile()
      // this.
      //报告影像地址
      // let images = report.images;
      // if(!!images) {
      //   images = images.map(img => this.convImgToAtta(img));
      //   this.report.images = images.filter(img => !!img);
      // }
      // //浏览报告文件类型
      // const mimeType = this.mimeType;
      // if(MIMETYPES.PDF === mimeType) {
      //   this.loadPdfFile();
      // } else if(MIMETYPES.JPG === mimeType) {
      //   this.loadJpgFile();
      // }
    },
    //制作模板
    asReportTemplate() {
      this.triggerBind("makeTplDoc", this.report);

      this.close();
    },
    //审核报告
    audit() {
      const report = this.report;
      //请求审核报告
      try {
        this.$store.dispatch("audit", report);
      } catch (err) { console.error(err); }
      this.close();
    },
    //打印
    plint() {
      let vm = this;
      const report = this.report;
      // if(!matchAnyResultStatus(report, StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive)) {
      //   //const resultStatus = report.resultStatus;
      //   this.$modal.confirm("打印报告须审核，是否进行审核？").then(() => {
      //     //请求审核报告
      //     try {
      //       this.$store.dispatch("auditForPrint", cloneDeep(report));

      //     } catch (err) { console.error(err); }
      //     this.close();
      //   });
      //   return;
      // }
      //防止未审核右键或浏览器菜单打印
      const dialogWrap = this.dialogRef, printingdialog = "printingdialog";
      let clz = dialogWrap.className.split(" ");
      if (!clz.includes(printingdialog)) { dialogWrap.className += " " + printingdialog; }

      window.print();

      if (!clz.includes(printingdialog)) { dialogWrap.className = clz.join(" "); }
      vm.close();
      // setTimeout(function(){vm.close();},50);
    },
    //却换页面视图
    switchPageSize() {
      if (this.pageFitted) {
        this.actualSize();
      } else {
        this.fitPage();
      }

      const wrap = this.dialogRef;
      const pageView = wrap.querySelector(".report-previewer-wrap");
      //
      const dia = wrap.querySelector(".el-dialog");
      //
      let zoom = this.scaleValue(pageView.style.transform);
      const dialogWidth = pageView.offsetWidth * zoom;
      //
      dia.style.width = (dialogWidth + (16 * 2)) + "px";
      //
      let dialogBodyHeight = "unset";
      if (this.pageFitted) {
        dialogBodyHeight = pageView.offsetHeight * zoom;
        dialogBodyHeight = (dialogBodyHeight + (4 * 2)) + "px";
      }
      dia.querySelector(".el-dialog__body").style.height = dialogBodyHeight;
    },
    /**
     * 读取PDF文件
     */
    loadPdfFile() {
      this.pdfDoc = null;
      this.numPdfPages = 0;
      //报告影像地址
      // let report = this.report, images = report.images;
      // if(!!images) {
      //   images = images.map(img => this.convImgToAtta(img));
      //   report.images = images.filter(img => !!img);
      // }
      //
      this.extOperExportAsDoc = true;

      let fun;
      // fun = api.viewUploadOrgReportDoc;
      //
      console.log("到这里了")

      var self = this;
      if (this.pdfDoc) {
        URL.revokeObjectURL(this.pdfDoc);
      }

      let fnc =  api.viewUploadReportDoc;
      if (matchAnyResultStatus(this.report, StatusDict.regist,
        StatusDict.exam,
        StatusDict.report)) {
        fnc = api.viewUploadOrgReportDoc
      }

      fnc(this.report).then(r => {
        if (!r.data) {
          vm.close();
        }
        const binaryData = atob(r.data);
        console.log("获取到pdf文件")
        const arrayBuffer = new ArrayBuffer(binaryData.length);
        const uint8Array = new Uint8Array(arrayBuffer);
        for (let i = 0; i < binaryData.length; i++) {
          uint8Array[i] = binaryData.charCodeAt(i);
        }
        // 创建一个 Blob 对象
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        // 创建一个 URL
        const url = URL.createObjectURL(blob);
        // const url = URL.createObjectURL(dat);
        self.pdfDoc = url
        // return pdfjsLib.getDocument({ data: dat}).promise;//, cMapPacked: true
      }).catch(e => {
        self.close();
      })

    },
    //生成指定PDF页
    renderPdfPage(num) {
      //
      this.pdfDoc.getPage(num).then(page => {
        //
        let canvas = this.dialogRef.querySelector(`div[data-page='report-previewer-page-${num}'] canvas`)
        let ctx = canvas.getContext("2d");
        ctx.mozImageSmoothingEnabled = false;
        ctx.webkitImageSmoothingEnabled = false;
        ctx.msImageSmoothingEnabled = false;
        ctx.imageSmoothingEnabled = false;
        //scale避免模糊
        let viewport = page.getViewport({ scale: 1.6 });
        //let scale = Math.min(canvas.clientWidth / viewport.width, canvas.clientHeight / viewport.height);
        //viewport.scale = scale;
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        //viewport.width = canvas.clientWidth
        //viewport.height = canvas.clientHeight;
        let renderContext = {
          canvasContext: ctx,
          viewport: viewport,
          //transform: [scale, 0, 0, scale, 0, 0]
        };
        page.render(renderContext).promise.then(() => {
          //page.getTextContent()
          //继续直到最后1页
          if (this.numPdfPages > num) {
            this.renderPdfPage(num + 1)
          } else {
            if (this.extOperPrint) {
              this.plint();
            }
          }
        });
      });
    },
    //JPG格式
    loadJpgFile() {
      this.numPdfPages = 0;
      //
      this.extOperExportAsDoc = true;
      //
      api.viewReportDoc(this.report, 1).then(r => {
        this.extOperExportAsDoc = false;

        const images = r.data;

        this.numPdfPages = images ? images.length : 0;
        //生成第1页
        this.$nextTick(() => {
          this.imagesDoc = images;
          //this.renderJpgPage();
          if (this.options) {
            const delay = 1000;
            if (this.extOperExportAsDoc) {
              setTimeout(this.exportAsDoc, delay);
            } else if (this.extOperPrint) {
              setTimeout(this.plint, delay);
            }
          }
        })
      }, err => {
        this.extOperExportAsDoc = false;
        this.$model.msgWarning("生成报告失败。")
      })
    },

    renderJpgPage() {
      const images = this.imagesDoc;
      for (let i = 0; i < images.length; i++) {
        let imgSrc = "data:image/jpeg;base64," + images[i];
        let img = this.imageRef;
        if (!img) {
          this.imageRef = img = new Image();
        }
        img.src = imgSrc;
        img.onload = () => {
          const canvas = this.dialogRef.querySelector(`div[data-page='report-previewer-page-${1 + i}'] canvas`)
          const ctx = canvas.getContext("2d");
          const imgWidth = img.width, imgHeight = img.height;
          let width = canvas.clientWidth, height = imgHeight * width / imgWidth;
          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(img, 0, 0, width, height);
        };
      }
    }
  },

  computed: {
    mime() {
      const mimeType = this.mimeType;
      return {
        isPDF: MIMETYPES.PDF === mimeType,
        isJPG: MIMETYPES.JPG === mimeType
      }
    },

    //
    extOperPrint() {
      const opt = this.options;
      return !!opt && opt.includes(OPER_PRINT);
    },
  }
};
export default model;
