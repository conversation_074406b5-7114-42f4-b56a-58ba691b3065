import {mapGetters} from "vuex";
//工具
import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";
//cornerstone
import "@/assets/scripts/cornerstone/cornerstone-setup";

import {mergeWithNotNull, undefinedOrNull, fmt_exam_age, isSmbFtpLocURL} from "@/utils/common";
//获取当前用户信息
import {getUserProfile} from "@/api/system/user";
//检查信息接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
import  createTimeTracker from '@/utils/TimeTracker';
//影像相关工具
import {
  FakeUidPrefix,
  FileTypes,
  assertJpeg,
  loadAndDisplay,
  isCompressed,
} from "@/assets/scripts/pacs/image/util";
//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";
import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel";
//
import ReportWriterImageComm from "@/assets/scripts/gis/report/comp/ReportWriterImageComm";
//import ReportPreviewer from "@/views/gis/report/comp/ReportPreviewer";
//放大影像
import ImageBubble from "@/views/pacs/report/comp/ImageBubble";
//
import VideoView from "@/views/pacs/report/comp/VideoView";
//检查状态变更
import {
  StatusDict,
  matchAny as matchAnyStatus,
  isFinalAuditStatus
} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import {ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//表单
import ReportWriterForm from "@/assets/scripts/uis/report/comp/ReportWriterForm";
//
import ReportWriterDetail from "@/views/gis/report/comp/ReportWriterDetail";

import imageView from "@/views/pacs/image/imageView";
//选择用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//操作权限
import {default as PermiCheck} from "@/directive/permission/mixins";
import {PERMI_AUDIT, PERMI_SECOND_AUDIT, PERMI_THIRD_AUDIT, PERMI_WRITE} from "@/directive/permission/mixins";
//影像转移
import {ImageTransfer} from "@/assets/scripts/pacs/report/mixins/fun.imageTransfer";
//影像
import {ImageOperator} from "@/assets/scripts/pacs/report/mixins/fun.imageOperator";
//右键菜单
import Contextmenu from "@/components/Contextmenu";
//
import ExamPicker from "@/views/gis/exammanagement/patient/comp/ExamPicker";
//  报表控件
import ReportView from "@/views/report/widgets/Report";
//报告采集影像和报告影像
import {ReportImageFun} from "@/assets/scripts/pacs/image/fun.image";
// 获取用户权限
import store from '@/store'

import {
  resback as resbackImage,
  getPng as getReportPng,
  saveDesignReport as saveDesignReport,
} from "@/assets/scripts/pacs/image/api.imageTranster";

import DesUtil from "@/utils/DesUtil";

import {batchReport as batchReport} from "@/assets/scripts/uis/exammanagement/examinfo/api";
//
const ErrM_Unwrite = "请选择要书写的报告。";

import QRSign from "@/assets/scripts/pacs/linksign/QRSign";

/**
 * 空白表单
 */
function emptyForm() {
  return {
    patientInfo: {
      id: null,
      medicalRecordNo: null,
      name: null,

      gender: {},
      age: null,
      ageUnit: {},
    },

    examModality: {},
    examItem: {},
    examParts: [],
    resultStatus: {dictValue: null},
    //examResultProp: {dictValue: "0"},
    examResultProp: {},

    examDoctor: {},
    reportDoctor: {},

    examDoctorsName: null,
    examDoctorsCode: null,
    consultantsName: null,
    consultantsCode: null,
    recordersName: null,
    recordersCode: null,
    operDoctor: {userName: null, nickName: null},

    examDesc: null,
    examDiagnosis: null,
    operationSuggestion: null,
    //报告的影像
    images: [],
    //采集的影像
    dicomStudy: {seriesSet: null},
    dicomStudies: [],
    currentExamItemCode: null,
  };
}

export {emptyForm};

const model = {
  name: "mTReportWriter",

  components: {
    LinksignPopup,
    ImageBubble,
    ReportWriterDetail,
    imageView,
    UserPicker,
    VideoView,
    Contextmenu,
    ExamPicker,
    ReportView,
  },

  dicts: [
    "uis_exam_modality",
    "uis_gender_type",
    "uis_age_unit",
    "uis_exam_item",
    "uis_exam_result_prop",
  ],

  mixins: [
    ResultStatus,
    ReportWriterForm,
    PermiCheck,
    ReportWriterImageComm,
    ImageTransfer,
    ImageOperator,
    ReportImageFun,
  ],

  data() {
    return {
      //当前用户
      currentUser: null,
      //报告内容
      reportForm: emptyForm(),
      //
      reportOrig: null,
      //
      qr: null,
      //
      editable: false,
      //双击图像显示大图
      dialogVisible: false,
      imageURL: "",
      currentItemIdx: null,

      isFistfocus: true,

      d_examResultProp: false,
      his_opt: {},
      eventM: null,
      imageNum: 0,
      // 结构化报告地址
      reportDSrc: null,
      // 空白报告地址
      blankReportDSrc: null,
      // 报告模板地址
      auditReportViewTemplate: null,
      reportModelPath: {},
      ikey: new Date().getTime(),
      saveLoading: false,
      showG: false,
      exameTemp: null,

      qrSign: new QRSign(() => {
      }),
      examIemConfigKey: [],
    };
  },

  methods: {
    pasteClick(event) {
      this.handlePasteTest();
    },
    handleFocus() {
      //this.$refs.uploadImage.blur()
    },

    async handlePasteTest() {
      // var value = null;
      const clipboard = navigator.clipboard;
      if (undefined == clipboard) return;
      const clipboardItems = await clipboard.read();

      for (const clipboardItem of clipboardItems) {
        for (const type of clipboardItem.types) {
          // 筛选图片类型的文件
          if (type.indexOf("image") > -1) {
            let vm = this;
            const blob = await clipboardItem.getType(type);
            // 将Blob转换成File
            let file = new File([blob], `image-${Date.now()}`, {type: type});
            // fileList.push(file)
            // // 将Blob转换成url，方便前端展示预览图
            // const url = URL.createObjectURL(blob)
            // urlList.push(file)

            //去除粘贴到div事件
            // event.preventDefault();
            // event.returnValue=false;
            // let file = null;
            // if (!items || items.length === 0) {
            //   this.$message.error("当前不支持本地图片上传");
            //   return;
            // }
            // // 搜索剪切板items
            // for (let i = 0; i < items.length; i++) {
            //   if (items[i].type.includes('image')) {
            //     file = items[i].getAsFile();
            //     break;
            //   }
            // }
            if (!file) {
              vm.$message.error("粘贴内容非图片");
              return;
            }

            var form = new FormData();

            form.append("file", file);
            //

            form.append("examInfoId", vm.reportForm.id);

            resbackImage(form)
              .then((r) => {
                vm.afterResbackImage();
              })
              .catch((err) => (vm.processing = false));
          }
        }
      }
    },

    handlePaste(event) {
      if (undefined == event) return;
      let vm = this;
      const items = (event.clipboardData || window.clipboardData).items;
      //去除粘贴到div事件
      event.preventDefault();
      event.returnValue = false;
      let file = null;
      if (!items || items.length === 0) {
        this.$message.error("当前不支持本地图片上传");
        return;
      }
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.includes("image")) {
          file = items[i].getAsFile();
          break;
        }
      }
      if (!file) {
        this.$message.error("粘贴内容非图片");
        return;
      }

      var form = new FormData();

      form.append("file", file);
      //

      form.append("examInfoId", this.reportForm.id);

      resbackImage(form)
        .then((r) => {
          vm.afterResbackImage();
        })
        .catch((err) => (this.processing = false));
    },
    //初始
    onPageLoad() {
      //当前用户信息
      if (!this.currentUser) {
        getUserProfile().then((res) => {
          this.currentUser = res.data;
        });
      }
      //加载历史表单数据，获取记录人员，检查医生，会诊医生
      if (!!this.reportForm_his) {
        this.reportForm.recordersName = this.reportForm_his.recordersName;
        this.reportForm.examDoctorsName = this.reportForm_his.examDoctorsName;
        this.reportForm.consultantsName = this.reportForm_his.consultantsName;
      }

      //表单元素焦点
      const fme = this.$refs.reportForm.$el;
      //触发的源：检查所见，检查诊断，术后医嘱
      const eles = [fme.examDesc, fme.examDiagnosis, fme.operationSuggestion];
      eles.forEach((ele) => {
        if (!ele) {
          return true;
        }
        ele.addEventListener("focus", (evt) => {
          const ths = (evt.target || evt.srcElement).name;
          this.triggerBind("focusFormField", ths);
        });
        /*ele.addEventListener('blur', evt => {
          this.triggerBind('focusFormField', null);
        });*/
      });
    },

    //表单
    fillReportForm(dat) {
      console.log("当前记录重新赋值", dat)
      if (dat) {
        let fm = emptyForm();
        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //
        if (fm.examParts) {
          let examParts_names = [];
          fm.examParts.forEach((p) => {
            examParts_names.push(p.partsName);
          });
          fm.examParts_names = examParts_names.join(",");
        }
        //
        /*if(!fm.examResultProp || !fm.examResultProp.dictCode) {
          fm.examResultProp = {dictValue: "0"};
        }*/
        //
        //console.log(this.currentUser);
        if (
          (!fm.reportDoctor || !fm.reportDoctor.nickName) &&
          this.currentUser
        ) {
          fm.reportDoctor = this.currentUser;
        }
        //
        //if(fm.resultStatus && fm.resultStatus.dictValue === StatusDict.report
        //   && (!fm.auditDoctor || !fm.auditDoctor.userName) && this.currentUser) {
        //  fm.auditDoctor = this.currentUser;
        //}

        //当报告为未审核状态时，同时记录人员，检查医生，会诊医生为空，将历史保存填入
        //console.log(this.reportable,this.auditable,this.withdrawable,)
        if (
          !fm.recordersName &&
          !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) &&
          this.reportForm_his
        ) {
          fm.recordersName = this.reportForm_his.recordersName;
        }
        if (
          !fm.examDoctorsName &&
          !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) &&
          !this.reauditable &&
          this.reportForm_his
        ) {
          fm.examDoctorsName = this.reportForm_his.examDoctorsName;
        }
        if (
          !fm.consultantsName &&
          !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) &&
          !this.reauditable &&
          this.reportForm_his
        ) {
          fm.consultantsName = this.reportForm_his.consultantsName;
        }

        this.reportForm = fm;
        this.currentExamItemCode = this.reportForm.examItem.dictValue;
        //Object.assign(this.reportForm, fm);
        console.log("this.currentExamItemCode", this.currentExamItemCode);
        this.reportOrig = cloneDeep(fm);
      }
    },

    /**
     * 清空报告
     */
    clearReport() {
      const vm = this;
      let exam = vm.reportForm;
      vm.$modal
        .confirm(
          "是否确认清空报告数据？"
        )
        .then(() => {
          let view = this.getReportViewWidget();
          let tp = vm.getReportTemplatePath(exam);
          vm.isLoadingReport(true);
          view
            .loadReport(
              tp,
              undefined,
              vm.buildReportInitValue(exam),
              vm.buildReportOpenOption(exam)
            ).then(() => {
            vm.isLoadingReport(false);
          });
          eiapi.reportDataClear(exam).then(() => {
            vm.isSaveLoading(false);
            vm.loadCurrentReport(exam);
            vm.refreshModified(exam);
            vm.$modal.msgSuccess("清空成功");
          });
        });
    },

    //刷新患者列表
    refreshPatientSheet() {
      this.triggerBind("refreshExamInfo");
    },
    //更相关数据
    refreshModified(exam, rewriteOpt) {
      console.log("refreshModified----------");
      this.refreshPatientSheet();
      this.write(exam, rewriteOpt);
    },

    /**
     * 回退报告
     */
    resultStatusRollback() {
      const vm = this;
      let exam = vm.reportForm;

      if (matchAnyStatus(exam, StatusDict.report)) {
        vm.$modal
          .confirm(
            "当前报告为已报告状态，是否确认回退回已检查状态？"
          )
          .then(() => {
            // vm.loadReport(reportModelPath,exam,undefined);
            eiapi.resultStatusRollback(exam).then(() => {
              vm.isSaveLoading(false);
              vm.loadCurrentReport(exam);
              vm.refreshModified(exam);
              vm.$modal.msgSuccess("回退成功");
            });
          });
      } else {
        vm.$modal
          .confirm(
            "当前报告为已检查状态，是否确认回退回已登记状态？"
          )
          .then(() => {
            eiapi.resultStatusRollback(exam).then(() => {
              vm.isSaveLoading(false);
              vm.loadCurrentReport(exam);
              vm.refreshModified(exam);
              vm.$modal.msgSuccess("回退成功");
            });
          });
      }
    },
    /**
     * 获取检查类型配置的报告模版路径
     */
    getReportTemplatePath(exam) {
      if (isFinalAuditStatus(exam)) {
        // 审核之后统一用审核模版打开
        return this.auditReportViewTemplate;
      }
      if (exam && exam.examItem) {
        return this.reportModelPath[exam.examItem.dictValue];
      }
      return null;
    },
    /**
     * 报表初始化值
     */
    buildReportInitValue(exam) {
      let argObj = {};
      // 姓名
      argObj["name"] = {text: exam.patientInfo.name};
      // 年龄
      argObj["age"] = {
        text: fmt_exam_age(exam),
      };
      // 性别
      argObj["sex"] = {text: exam.patientInfo.gender.dictLabel};
      argObj["examNo"] = {text: exam.examNo};
      argObj["inpNo"] = {text: exam.inpNo}; //住院号
      argObj["medicalRecordNo"] = {text: exam.patientInfo.medicalRecordNo}; //病历号
      argObj["bedNo"] = {text: exam.bedNo};
      argObj["registNo"] = {text: exam.patientInfo.registNo};
      argObj["birthday"] = {text: exam.patientInfo.birthday.substr(0, 10)};
      // 病区
      if (!!exam.inpWard)
        argObj["inpWardName"] = {text: exam.inpWard.dictLabel};
      // 检查科室/接收科室
      if (!!exam.examDept) {
        argObj["examDeptName"] = {text: exam.examDept.deptName};
      }
      //收费类型
      if (!!exam.examCostType) {
        argObj["examCostTypeLabel"] = {text: exam.examCostType.dictLabel};
      }
      //就诊类别
      if (!!exam.inpType) {
        argObj["inpTypeLabel"] = {text: exam.inpType.dictLabel};
      }
      //检查医生
      if (!!exam.examDoctor) {
        argObj["examDoctorName"] = {text: exam.examDoctor.nickName};
      }
      console.log("exam.auditDoctor", exam.auditDoctor);
      if (!!exam.auditDoctor) {
        argObj["auditDoctorName"] = {text: exam.auditDoctor.nickName};
      }
      // 检查部位
      if (!!exam.examParts && exam.examParts.length > 0) {
        let examParts = exam.examParts.map(e => e.partsName).join('、');
        argObj["examParts"] = {text: examParts};
      }

      // 审核状态放审核报告后的pdf，其它的都放原始报告的pdf
      if (isFinalAuditStatus(exam)) {
        // 统一用审核报告的的模版
        let url = exam.reportUrlPdf;
        // if(!isSmbFtpLocURL(url)) url = DesUtil.decode(url, config.securityKey)
        argObj["reportUrlPdf"] = {src: url};
      } else {
        let url = exam.reportUrlOrgPdf;
        if (matchAnyStatus(exam, StatusDict.audit, StatusDict.second_audit)) {
          url = exam.reportUrlPdf
        }
        // if(!isSmbFtpLocURL(url)) url = DesUtil.decode(url, config.securityKey)
        argObj["reportUrlOrgPdf"] = {src: url};
        // 非审核状态签名统一清除
        argObj["auditDoctorSign"] = {src: ""};
      }
      return argObj;
    },
    /**
     * 报表初始化控制参数
     */
    buildReportOpenOption(exam) {
      // 审核状态统一不能编辑
      let op = {};
      if (matchAnyStatus(exam, StatusDict.audit)) {
        op["editable"] = false;
      }
      return op;
    },
    getReportViewWidget() {
      let view = this.$refs["reportView"];
      if (!view) {
        console.error("当前报告查看组件没有初始化，请注意");
        return;
      }
      return view;
    },
    /**
     * 双击，查看预览当前报告
     */
    loadCurrentReport(exam) {
      const vm = this;
      console.log("报表加载，加载当前记录: ", exam,new Date().toString())
      eiapi.get(exam.id).then((res) => {
        const dat = res && res.data;
        if (!dat) {
          console.log("根据ID没有查询到检查记录", exam.id)
          return;
        }
        let tp = vm.getReportTemplatePath(dat);
        if (!tp) {
          console.log(
            "未找到报告模版路径",
            exam.examItem.dictLabel,
            exam.examItem.dictValue
          );
          return;
        }
        let view = this.getReportViewWidget();
        if (!view) {
          return;
        }
        vm.isLoadingReport(true);
        view
          .loadReport(
            tp,
            exam.id,
            vm.buildReportInitValue(dat),
            vm.buildReportOpenOption(dat)
          )
          .then(() => {
            console.log("报表加载，切换患者报告完成: ", new Date().toString())
            // console.log("切换患者报告完成");
            vm.isLoadingReport(false);
          });
        let images = dat.images;
        if (images) {
          images.forEach(e => {
            const parts = e.path ? e.path.split("/") : null;
            if (parts && parts.length == 3) {
              const studyInstanceUid = parts[0], seriesInstanceUid = parts[1], sopInstanceUid = parts[2];
              e["studyInstanceUid"] = studyInstanceUid;
              e["seriesInstanceUid"] = seriesInstanceUid;
              e["sopInstanceUid"] = sopInstanceUid;
              /*let studyInstanceUid0 = studyInstanceUid;
              if(FileTypes.jpeg === e.fileType && -1 === studyInstanceUid0.indexOf(FakeUidPrefix)) {
                studyInstanceUid0 = FakeUidPrefix + studyInstanceUid0;
              }
              //e["uri"] = imapi.loadImage(studyInstanceUid0, seriesInstanceUid, sopInstanceUid);*/
            }
          });
        }
        this.fillReportForm(dat);
        //获取检查类型，放射检查时，是否使用多屏
        this.loadReportImages();
      });
    },

    /**
     * 写报告
     */
    write(exam, opt = {}, save, sendPicture) {
      this.sendReportImage(exam);
      this.exameTemp = exam;
      // console.log("write-------------");
      let vm = this;
      let prom;
      //未保存检查
      const modi = this.checkModi();
      if (modi) {
        modi.unshift("当前报告有以下未保存修改，是否打开新报告？");
        this.$modal
          .confirm(modi.join("<br/>- "), {
            dangerouslyUseHTMLString: true,
          })
          .then(() => {
            vm.switchReport(exam);
          });
      } else {
        vm.switchReport(exam);
      }
    },

    /**
     * 切换报告
     * @param {*} exam
     */
    switchReport(exam) {
      let vm = this;
      //
      this.isFistfocus = true;
      //
      this.clearAuditForPrint();
      //
      this.setTransferAction(null);
      //
      this.editable = false;
      //this.reportForm = emptyForm(); //vm.loadCurrentReport(exam)方法里已经有这行代码逻辑
      this.currentItemIdx = null;
      this.reportOrig = null;
      //
      if (!exam || !exam.id) {
        return;
      }
      if(!!exam.examItem&&!!this.examIemConfigKey){
        this.splitScreenUsed = this.examIemConfigKey[exam.examItem.dictValue]['splitScreenUsed'];;
      }
      this.$store.dispatch("lastEdited", cloneDeep(exam));
      return vm.loadCurrentReport(exam);
    },

    batchAudit() {
      let form = new FormData();
      form.append("examInfos", this.reportForm.id);

      let fun = eiapi.batchAuditPdf;
      const cup = (data) => {
        fun(form)
          .then((res) => {
            if ("qrnoauth" === res.errC) {
              if (null == vm.qr) {
                vm.qr = new QRSignRel(() => {
                  this.qr.activated = false;
                  this.qr.openDig = false;
                  ///opt.firmed = true;
                  cup(data);
                });
              }
              this.qr.openDig = true;
              return;
            }

            this.write(fm, null, "save");
            this.triggerBind("refreshExamInfo");
          })
          .catch();
      };
      cup(form);
    },

    handleSignFailed(exam) {
      /*let request = {
        id: null,
        content: null
      };
      request.id = exam.id;
      request.content = "CA签署失败";
      eiapi.withdrawAuditReport(request).then(
        (res) => {
          // 刷新
          this.$modal.msgSuccess(res.msg);
          this.isSaveLoading(false);
          this.afterWithdrawReport(exam);
        }
      )*/
      this.reportPostProcess(exam);
    },

    doGetSignInfo(signNum, exam, file) {
       console.log("获取签名信息开始",new Date().toString());
      this.qrSign.getSignInfo(signNum).then((gSRes) => {
          console.log("获取签名信息完成",new Date().toString());
        let signInfo = gSRes.data;
        // 处理不同的签署状态
        if (signInfo.signStatus !== 1) {
          let currentExam = exam
          let message = '';
          let buttonText = '';
          let callback = null;

          switch (signInfo.signStatus) {
            case 0:
              message = `请前往易签助手小程序进行签署，signNum: ${signNum}`;
              buttonText = '确认已签署';
              callback = () => {
                this.doGetSignInfo(signNum, currentExam, file)
              };
              break;
            case 2:
              message = `签署失败，signNum: ${signNum}，请重新审核签署或联系CA服务商解决`;
              buttonText = '重新审核';
              callback = () => {
                this.handleSignFailed(currentExam)
                //location.reload();
              };
              break;
            case 3:
              message = `签署已作废，signNum: ${signNum}，请重新审核签署`;
              buttonText = '重新审核';
              callback = () => {
                this.handleSignFailed(currentExam)
                //location.reload();
              };
              break;
          }

          this.$confirm(message, '提示', {
            confirmButtonText: buttonText,
            showCancelButton: false,
            type: 'warning',
            center: true
          }).then(() => {
            callback && callback();
          });
        } else {
            console.log("MT审核MT后端保存审核文件开始",new Date().toString());
            eiapi.saveAuditFile(file, exam).then((safRes) => {
            console.log("MT审核MT后端保存审核文件完成",new Date().toString());
            exam.reportUrlPdf = safRes.data;
            console.log("MT审核MT后端保存签名",new Date().toString());
            this.qrSign.saveSign(exam, safRes.data).then((sSRes) => {
              console.log("MT审核MT后端保存签名完成",new Date().toString());
              this.$modal.msgSuccess(sSRes.msg);
              this.doAuditAndSave(exam);
            }).catch(() => {
              this.handleSignFailed(exam);
            });
          }).catch(() => {
            this.handleSignFailed(exam);
          });
        }
      }).catch(() => {
        this.handleSignFailed(exam);
      });
    },

    doAuditAndSave(exam) {
        console.log("MT审核MT后端开始审核",new Date().toString());
      return eiapi
        .doAudit(exam)
        .finally(() => {
            console.log("MT审核MT后端审核完成",new Date().toString());
          this.reportPostProcess(exam);
        });
    },

    doAuditReport(useObj, exam) {
      console.log("MT审核在这里真正处理审核逻辑", new Date().toString());
      let vm = this;
      let view = this.getReportViewWidget();
      if (!view) {
        return;
      }
      vm.isSaveLoading(true);
      //  let tracker  = createTimeTracker()
      // tracker.start("MT审核耗时")
      view.sign(useObj, exam).then(() => {
        console.log("MT审核已经合成了签名pdf",new Date().toString());
        // tracker.pause('已经合成了签名pdf')
        let tp = vm.getReportTemplatePath(exam);
        view.save(tp, exam.id).then((res) => {
          console.log("MT审核报表已经保存完成了",new Date().toString());
          // tracker.pause('报表已经保存完成了')
          if (this.qrSign.enableTest || this.qrSign.useGXCA) {
             console.log("MT审核QR签名PDF开始",new Date().toString());
            this.qrSign.createSign(res.pdf).then((cSRes) => {
              //  tracker.pause('QR签名')
               console.log("MT审核QR签名PDF完成",new Date().toString());
              let signNum = cSRes.data;
              setTimeout(() => {
                this.doGetSignInfo(signNum, exam, res.pdf);
              }, 2000)
            }).catch((err) => {
              this.handleSignFailed(exam)
            })
          } else {
             console.log("MT审核MT后端审核开始",new Date().toString());
            eiapi.saveAuditFile(res.pdf, exam).then((safRes) => {
              exam.reportUrlPdf = safRes.data;
               console.log("MT审核MT后端审核结束",new Date().toString());
              // tracker.pause('MT保存审核文件完成')
              let c = this.doAuditAndSave(exam);
              // console.log(c)
              // tracker.end()
            })
          }
        });
      });
    },

    reportPostProcess(exam) {
      // 刷新
      this.isSaveLoading(false);
      this.loadCurrentReport(exam);
      this.refreshModified(exam);
    },

    /**
     * 是否正在保存报告
     */
    isSaveLoading(state) {
      this.saveLoading = state;
    },
    /**
     * 是否正在加载报告
     */
    isLoadingReport(state) {
      let self = this;
      self.loadingReport = state;
    },
    /**
     * 审核报告
     */
    auditReport() {
      this.saveLoading = true;
      let vm = this;
      var fm = this.reportForm;
      if (!vm.checkCurrentReport()) {
        this.saveLoading = false;
        return;
      }
      //  console.log("确认审核")
      let postData = cloneDeep(fm);

      eiapi.getUserSignInfo(postData).then((res) => {
        // 没有签名需要重新签名
        if ("qrnoauth" === res.errC) {
          vm.isSaveLoading(false);
          if (null == this.qr) {
            this.qr = new QRSignRel(() => {
              this.qr.activated = false;
              this.qr.openDig = false;
              // opt.firmed = true;
              // this.save(opt);
              vm.auditReport();
            });
          }
          this.qr.openDig = true;
          return;
        }
        vm.doAuditReport(res.data, fm);
      });

      // let confirmMsg = "是否审核该报告？<br>";
      // //是否弹出确认框
      // this.$modal
      //   .confirm(confirmMsg, { dangerouslyUseHTMLString: true })
      //   .then(() => {

      //   })
      //   .catch(() => {
      //     console.log("取消审核");
      //     vm.isSaveLoading(false);
      //   });
    },
    /**
     * 真正触发保存报告
     * @param {*} exam
     */
    doSave(exam) {
      let vm = this;
      let view = this.getReportViewWidget();
      if (!view) {
        return;
      }
      vm.isSaveLoading(true);
      let tp = vm.getReportTemplatePath(exam);
      view.save(tp, exam.id).then((res) => {
        console.log("报表已经保存完成了", res.pdf);

        let postData = cloneDeep(exam);
        //dicom影像路径
        //const re = new RegExp("^[^/]+//[^/]+(/ext/dicom-web/rs/.+)$");
        //报告图片
        let images = postData.images.map(e => {

          return this.convImgToAtta(e);
        }).filter(e => !!e);
        postData.images = images;

        eiapi.saveReport(postData).then((res) => {
          this.refreshModified(postData);
        });
        console.log("MT已经保存完成了", res.pdf);
        vm.isSaveLoading(false);
      });
    },
    /**
     * 保存报告
     * @param opt {cmd: null, firmed: false, silence: false}, 审核时传入cmd='audit'
     */
    save(opt = {}, tmp) {
      console.log("保存报告");
      let vm = this;
      vm.isSaveLoading(true);

      var fm = this.reportForm;
      if (!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      if (!this.checkSelectedImagesCount()) {
        return;
      }
      //保存不执行未保存检查
      this.reportOrig = null;
      //审核
      const auditReport = "audit" === opt.cmd;
      //
      let confirmMsg = "是否保存报告？";
      this.$modal
        .confirm(confirmMsg, {dangerouslyUseHTMLString: true})
        .then(() => {
          vm.doSave(fm);
        })
        .catch(() => {
          vm.isSaveLoading(false);
        });
    },
    //读取已采集的图片
    loadReportImages() {
      //this.collectImages();
      let fm = this.reportForm;
      imapi
        .findStudies({examInfo: {id: fm.id}, pageSize: 9999})
        .then((res) => {
          this.imageNum = 0;
          const studies = res.rows;
          if (!studies || studies.length <= 0) {
            return;
          }

          studies.forEach((study) => {
            if (!study) {
              return true;
            }
            let seriesSet = study.seriesSet;

            if (!seriesSet || seriesSet.length <= 0) {
              return true;
            }

            seriesSet.forEach((ser) => {
              let imagesSet = ser.imagesSet;
              if (imagesSet && imagesSet.length) {
                imagesSet.forEach((img, i) => {
                  let {studyInstanceUid, fileType} = img;
                  img.studyInstanceUid0 =
                    (FileTypes.jpeg.name === fileType ||
                    FileTypes.jpeg.code === fileType
                      ? FakeUidPrefix
                      : "") + studyInstanceUid;
                  this.structExamImage(img, imagesSet, i);
                });
              }
            });
          });
          //console.log(study)
          fm.dicomStudies = studies;
          //console.log(fm);
          this.loadExamImages();
        });
    },

    //当前编辑的报告
    get() {
      return this.reportForm;
    },
    //更新指定属性“检查所见/检查诊断/术后医嘱”
    //@replace true-替换, false-末尾追加
    updateProp(nam, val, replace = true) {
      if (!this.reportable || !val) {
        return;
      }

      let fm = this.reportForm;
      if (!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      //console.log("name=%s, value=%s", nam, val);
      if (!replace) {
        val = (this.reportForm[nam] ? this.reportForm[nam] + "\n" : "") + val;
      }
      //
      this.$set(this.reportForm, nam, val);
      //this.reportForm[nam] = val;
      //Object.assign(this.reportForm, this.reportForm);
    },

    // 设置图片类型，来源，选中状态/是否作为报告影像
    structExamImage(img, imagesSet, index) {
      if (!img.id) {
        return;
      }

      const {studyInstanceUid, sopInstanceUid, fileType} = img;
      let sopUri;
      //jpeg和动态
      if (this.typeofJpg(img) || this.typeofVideo(img)) {
        sopUri = imapi.imageLocate(img);
        //
        let selectedImages = this.reportForm.images; //已选影像
        let selectedImage = !!selectedImages
          ? selectedImages.find((i) => i.sopInstanceUid === sopInstanceUid)
          : null;

        img.fileType = img.fileType || FileTypes.jpeg.code; //assertJpeg(sopUri)? FileTypes.jpeg.name : FileTypes.dicom.name;
        img.uri = sopUri;
        img.selected = !!selectedImage;
        img.transferSelected = false;
        //为倒叙
        //selectedImages.forEach((ser, i) => {
        //    ser.selectNum = 1 + i;
        //})

        if (selectedImage) {
          selectedImage.uri = img.uri;
        }
      } else if (
        FileTypes.dicom.code == fileType ||
        FileTypes.dicom.name == fileType
      ) {
        if (0 == index) {
          this.imageNum += imagesSet.length;
        }

        if (0 == index) {
          //dicom
          //获取浏览地址，获取影像DICOM参数
          imapi
            .dcmViewer(this.reportForm.id, studyInstanceUid)
            .then((res) => {
              const dicomStudy = res.dicomStudy || {};
              if (!dicomStudy) {
                this.$modal.msgWarning("该检查没有影像信息。");
                return true;
              }
              const suid =
                dicomStudy.dicomStudyInstanceUid || dicomStudy.studyInstanceUid;
              return imapi.indexImages(suid);
            })
            .then((res) => {
              const items = res.data;
              if (!!items && !!items.length) {
                items.forEach((ig) => {
                  //if(!ig.fileUrl.endsWith("/2")) {return true;}
                  if (
                    -1 !=
                    this.imagesSet.findIndex(
                      (e) => e.sopInstanceUid === ig.sopInstanceUid
                    )
                  ) {
                    return true;
                  }
                  //
                  const meta = JSON.parse(ig.noteInfo);
                  const isCred = isCompressed(meta);
                  //
                  let fileUrl = ig.fileUrl,
                    imageRend = "wadors";
                  if (isCred) {
                    imageRend = "wadouri";
                    fileUrl = fileUrl.replace("/frames/", "/slice/");
                  }
                  //提交时保留的路径
                  let selectedImages = this.reportForm.images; //已选影像
                  let selectedImage = !!selectedImages
                    ? selectedImages.find((i) => fileUrl.endsWith(i.path))
                    : null;
                  //
                  ig.uri = `${imageRend}:${fileUrl}`;
                  ig.selected = !!selectedImage;
                  imagesSet.push(ig);
                  //元信息
                  cornerstoneWADOImageLoader.wadors.metaDataManager.add(
                    ig.uri,
                    meta
                  );
                });
                //this.loadExamImages();//this.$nextTick();
              }
            });
        }
      }
    },
    //cornerstone加载图片
    loadExamImages(opts = {}) {
      //
      //加载图片前，根据选择重新排序显示
      //   console.log(this.reportForm.images,this.imagesSet)
      this.reportForm.images.forEach((ser, i) => {
        ser.selectNum = 1 + i;
        this.imagesSet.forEach((img) => {
          if (this.isSameImage(ser, img)) {
            img.selectNum = ser.selectNum;
          }
        });
      });
      //读取图像数据
      this.$nextTick(() => {
        document
          .querySelectorAll(".report-writer-image div.cornerstone-element")
          .forEach((c, i) => {
            //console.log(i, c);
            const image = this.imagesSet[i];
            if (this.typeofVideo(image)) {
              cornerstone.disable(c);
            } else {
              try {
                //是否已
                cornerstone.getEnabledElement(c);
              } catch (err) {
                cornerstone.enable(c);
              }
              let idx = parseInt(c.parentNode.dataset.index);
              if (isNaN(idx)) {
                idx = i;
              }
              this.loadExamImage(c, idx);
            }
          });
        //滚动条到最近
        if (opts.focusLast) {
          this.$nextTick(() => {
            document.querySelector(".cornerstone-elements-pane").scrollLeft = 0;
          });
        }
      });
    },

    //获取>显示影像
    loadExamImage(element, idx) {
      const imagesSet = this.imagesSet;
      //console.log(imagesSet);
      let image = imagesSet[idx];
      if (!this.validateExamImage(image)) {
        return;
      }
      //console.log(image);
      let imageId = image.uri; //wadoForImage(image.uri);
      //console.log(imageId);
      try {
        loadAndDisplay(element, imageId);
      } catch (err) {
        console.error(err);
      }
    },
    //选择影像
    selectExamImage(state, item) {
      //
      item.selectNum = null;
      delete item.selectNum;
      //对比报告图像和采集图像
      let fimg = this.isSameImage;

      let selectedImages = this.reportForm.images;
      let idx = selectedImages.findIndex((si) => fimg(si, item));

      //取消勾选
      if (!state && -1 !== idx) {
        selectedImages.splice(idx, 1);
        selectedImages.forEach((ser, i) => {
          ser.selectNum = 1 + i;
          this.imagesSet.forEach((img) => {
            if (fimg(ser, img)) {
              img.selectNum = ser.selectNum;
            }
          });
        });
      } else if (state && -1 === idx) {
        const count = selectedImages.length;
        item.selectNum = count + 1;

        selectedImages.push(item);
      }

      let postData = cloneDeep(this.reportForm);
      //dicom影像路径
      //const re = new RegExp("^[^/]+//[^/]+(/ext/dicom-web/rs/.+)$");
      //报告图片
      let images = postData.images.map(e => {

        return this.convImgToAtta(e);
      }).filter(e => !!e);
      postData.images = images;

      this.sendReportImage(postData);
    },

    sendReportImage(exam) {
      eiapi.getReportImageData(exam).then(res => {
        let aa = 0;
      });
    },

    //删除影像
    deleteExamImage(item) {
      this.$modal.confirm("是否确定?").then(() => {
        const fm = this.reportForm,
          dicomStudy = fm.dicomStudies[0];

        if (!dicomStudy.seriesSet) {
          return;
        }

        const series = dicomStudy.seriesSet.find(
          (ser) =>
            ser.studyInstanceUid === item.studyInstanceUid &&
            ser.seriesInstanceUid === item.seriesInstanceUid
        );

        let imagesSet = series ? series.imagesSet : null;
        if (!imagesSet) {
          return;
        }

        const idx = imagesSet.findIndex(
          (i) => i.sopInstanceUid === item.sopInstanceUid
        );
        if (-1 === idx) {
          return;
        }
        const img = imagesSet[idx],
          selectedImages = fm.images;
        const pos = selectedImages.findIndex(
          (si) => si.sopInstanceUid === img.sopInstanceUid
        );
        if (-1 != pos) {
          selectedImages.splice(pos, 1);
        }
        imagesSet.splice(idx, 1);
        item.status = 2;
        //
        selectedImages.forEach((ser, i) => {
          ser.selectNum = 1 + i;
        });
        //
        //this.$nextTick(() => {
        imapi.delImage(item.id).then(this.loadExamImages);
        //});
        //
        if (this.dialogVisible) {
          const imagesLength = this.imagesSet.length - 1;
          //const delFlag=this.deleteExamImage(this.imagesSet[this.currentItemIdx])
          if (this.currentItemIdx > imagesLength) {
            this.nextView(-1);
          } else if (imagesLength < 0) {
            this.dialogVisible = false;
          } else {
            this.nextView(1);
          }
        }
      });
    },
    //是否图片已删除
    isExamImageDeleted(img) {
      return 2 === img.status;
    },
    validateExamImage(img) {
      return !this.isExamImageDeleted(img) && !!img.uri;
    },
    //上报危急值
    reportCriticalValues() {
      this.triggerBind("dispatchAction", "report::critical", this.reportForm);
    },
    //取消检查
    handleCancel() {
      this.confirmToUpdateResultStatus(this.reportForm, StatusDict.cancel).then(
        (res) => {
          this.$modal.msgSuccess("操作成功");
          this.triggerBind("refreshExamInfo");
        }
      );
    },

    getNowTime() {
      var date = new Date();
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.date = date.getDate();
      this.hour =
        date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      this.minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      this.second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      this.milliSeconds = date.getMilliseconds();
      var currentTime =
        this.year +
        "-" +
        this.month +
        "-" +
        this.date +
        " " +
        this.hour +
        ":" +
        this.minute +
        ":" +
        this.second +
        "." +
        this.milliSeconds;
      return currentTime;
    },

    handlePreviewReport() {
      let vm = this;
      let cmd = ("string" === (typeof mix)) && mix.startsWith("report::") ? mix : "report::preview";
      vm.triggerBind("dispatchAction", cmd, vm.reportForm);

    },

    //预览 | 打印预览报告
    handlePreview(mix) {
      let vm = this;
      let viewer = vm.getReportViewWidget();
      viewer && viewer.print(); //打印


      console.log('点击了打印',mix)
      var fm = this.reportForm;
      //  console.log("确认审核")
      let postData = cloneDeep(fm);

      eiapi.printReport(postData)

    },

    //审核
    handleAudit(mix) {
      console.log("点击了审核");
      if (!(this.hasPermi(PERMI_AUDIT) ||
        this.hasPermi(PERMI_SECOND_AUDIT) ||
        this.hasPermi(PERMI_THIRD_AUDIT))) {
        this.$modal.alert("您没有审核报告的权限。");
        return;
      }
      this.auditReport();
    },
    checkCurrentReport() {
      var fm = this.reportForm;
      if (!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return false;
      }
      return true;
    },

    //复核
    handleReaudit() {
      const fm = this.reportForm;
      eiapi.reauditReport(fm).then((res) => {
        this.$modal.msgSuccess("报告复核完成。");
        this.refreshModified(fm);
      });
    },
    //采集调用
    addCollectImage(image) {
      const rp = this.reportForm;
      if (!this.reportable) {
        const rs = rp.resultStatus,
          statusLabel = !!rs ? rs.dictLabel : "未知";
        this.$modal.alert("无法采集影像，原因：该报告状态为" + statusLabel);
        return;
      }
      this.structExamImage(image);
      //
      let seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      let imagesSet =
        seriesSet && seriesSet.length ? seriesSet[0].imagesSet : null;
      if (!imagesSet) {
        imagesSet = [];
        this.reportForm.dicomStudies[0].seriesSet = [{imagesSet}];
        seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      }
      seriesSet[0].studyInstanceUid = image.studyInstanceUid;
      seriesSet[0].seriesInstanceUid = image.seriesInstanceUid;
      imagesSet.push(image);
      //console.log(this.reportForm, seriesSet);
      this.loadExamImages({focusLast: true});
      //
      rp.resultStatus = Object.assign({}, rp.resultStatus, {
        dictValue: StatusDict.exam,
      });
    },
    //放大影像
    zoomImage(evt) {
      let ele = !!evt ? evt.currentTarget || evt.target : null,
        idx = !!ele ? parseInt(ele.parentNode.dataset.index) : -1;
      if (-1 >= idx) {
        this.$refs.imageBubble.close();
        return;
      }
      const imagesSet = this.imagesSet;
      if (!imagesSet || idx >= imagesSet.length) {
        return;
      }
      const img = imagesSet[idx];
      //视频不预览
      if (this.typeofVideo(img)) {
        return;
      }
      //
      this.$refs.imageBubble.view(img, evt);
    },
    //预览影像
    /*viewImage(idx) {
      const imagesSet = this.imagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      this.$refs.imageView.view(img);
    },*/
    //聚焦最新采集的图像
    scrollImage(dir) {
      let imageScrollView = this.$refs.imageScrollView,
        cele;
      if (
        imageScrollView.clientWidth === imageScrollView.scrollWidth ||
        !(cele = imageScrollView.querySelector(
          ".cornerstone-element-container"
        ))
      ) {
        return;
      }

      let scollLeft = imageScrollView.scrollLeft;
      const scrollStep = 12 + cele.clientWidth;
      scollLeft += dir * scrollStep;
      imageScrollView.scrollLeft = Math.max(0, scollLeft);
    },
    //检查勾选的图像数是否合理
    checkSelectedImagesCount() {
      //图象数只能是0, 1, 2, 3, 4, 6
      const images = this.reportForm.images,
        numSupported = [0, 1, 2, 3, 4, 6];
      const numImages = !!images ? images.length : 0;
      if (!numSupported.includes(numImages)) {
        this.$modal.alert("影像张数应为: " + numSupported.join(", "));
        return false;
      }

      return true;
    },
    //召回报告
    handleWithdrawAuditReport() {
      if (!(this.hasPermi(PERMI_AUDIT) ||
        this.hasPermi(PERMI_SECOND_AUDIT) ||
        this.hasPermi(PERMI_THIRD_AUDIT))) {
        this.$modal.alert("您没有召回报告的权限。");
        return;
      }
      //this.$modal.confirm("是否确定召回报告？").then(res => {
      this.triggerBind(
        "dispatchAction",
        "report::withdrawAudit",
        this.reportForm
      );
      //});
    },
    /**
     * 召回之后
     * 需要清理之前保存的报告内容
     */
    afterWithdrawReport(exam) {
      this.loadCurrentReport(exam);
    },
    viewImage(evt) {
      this.dialogVisible = true;

      let ele = evt.currentTarget || evt.target,
        idx = parseInt(ele.parentNode.dataset.index);
      //console.log(this.imagesSet)
      this.imageURL = this.imagesSet[idx];
      this.currentItemIdx = idx;
      //console.log( this.currentItemIdx)
    },
    nextView(val) {
      const imagesLength = this.imagesSet.length - 1;
      if (
        (-1 === val && this.currentItemIdx + val < 0) ||
        (1 === val && this.currentItemIdx + val > imagesLength)
      ) {
        return;
      }
      this.currentItemIdx = this.currentItemIdx + val;
      this.imageURL = this.imagesSet[this.currentItemIdx];
    },
    //播放动态采集
    viewVideo(evt) {
      let ele = evt.currentTarget || evt.target,
        idx = parseInt(ele.parentNode.dataset.index);
      const image = this.imagesSet[idx];
      if (!!image && !!image.uri && this.typeofVideo(image)) {
        this.$refs.videoView.view(image);
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param opts {}
     */
    toPickUser(opts) {
      const fm = this.reportForm;
      if (!fm.id) {
        return;
      }

      let tar = opts.target;
      let names, codes;
      if (tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }
      let selectedUsers = [];
      if (!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({userName: e, nickName: names[i]});
        });
      }
      opts.selectedUsers = selectedUsers;

      this.$refs.userPicker.showPicker(opts);
    },
    pickUser(tar, users) {
      let fm = this.reportForm,
        names = [],
        codes = [];
      if (!!users.length) {
        users.forEach((e) => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }
      names = names.join(",");
      codes = codes.join(",");

      if (tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }
    },
    //
    cleanExam() {
      this.reportForm.examDesc = "";
      this.reportForm.examDiagnosis = "";
    },
    //清除打印审核请求
    clearAuditForPrint() {
      this.$store.dispatch("auditForPrint", null);
    },
    switchTab(val) {
      if (this.isFistfocus) {
        this.triggerBind("switchTab", val);
        this.isFistfocus = false;
      }
    },
    //是否有未保存的修改
    checkModi() {
      let fields = [];

      const orig = this.reportOrig,
        fm = this.reportForm;
      if (orig && fm) {
        if (orig.examDesc != fm.examDesc) {
          fields.push("检查所见");
        }
        if (orig.examDiagnosis != fm.examDiagnosis) {
          fields.push("检查诊断");
        }
      }

      return fields.length > 0 ? fields : null;
    },
    //报告图像是否采集的图像
    isSameImage(rimg, cimg) {
      if (this.typeofDcm(cimg)) {
        return (
          cimg.fileUrl === rimg.fileUrl || cimg.fileUrl.endsWith(rimg.path)
        );
      } else {
        return rimg.sopInstanceUid == cimg.sopInstanceUid;
      }
    },

    loadReport(modelPath, exam, id, base64) {
      return Promise.resolve(1);
    },

    loadAndSaveReportOrTemplate(modelPath, exam, id, base64) {
    },

    getReportPng() {
      let call = "_ctx.services.preview.getReportPng";
      console.log("getReportPng:", call);
      return sendMessageAsync({
        type: "report-system-call-fn",
        args: [],
        call: call,
        id: "getReportPng",
      });
    },

    postPicture(base64) {
      let call =
        "_ctx.services.preview.reportContentRef.value.setWidgetContentValue";
      console.log("postPicture:", call);
      return sendMessageAsync({
        type: "report-system-call-fn",
        args: ["signature", {src: base64}],
        call: call,
        id: "postPicture",
      });
    },

    saveReport(id, modelPath, imgBa64, exam) {
      if (!modelPath) return Promise.resolve("未知结构化报告路径");
      let modelPathAr = modelPath.split(";");
      if (this.showG && modelPathAr.length > 1) {
        modelPath = modelPathAr[1];
      } else {
        modelPath = modelPathAr[0];
      }

      let vm = this;
      let call = "_ctx.services.preview.writeReport";
      let args = [];
      // modelPath = vm.reportModelPath[vm.reportForm.examItem.dictValue]
      args.push(modelPath);
      args.push(id + "");
      args.push(true);
      const contentValue = {};

      if (
        undefined != exam &&
        StatusDict.audit == exam.resultStatus.dictValue &&
        undefined != exam.auditTime
      ) {
        contentValue["auditTime"] = {text: exam.auditTime.substr(0, 10)};
      } else contentValue["auditTime"] = {text: ""};

      if (undefined != imgBa64) {
        console.log('signImgBase64', imgBa64);
        contentValue["auditDoctorSign"] = {src: imgBa64};
      }
      args.push(contentValue);
      console.log("saveReport:", args);
      return sendMessageAsync({
        type: "report-system-call-fn",
        args: args,
        call: call,
        id: {id: id},
      });
    },

    base64ToFile(base64Data, filename) {
      const parts = base64Data.split(";base64,");
      const contentType = parts[0].split(":")[1];
      const raw = window.atob(parts[1]);

      // 将原始数据转换为Uint8Array
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }

      // 使用Blob对象创建File对象
      const blob = new Blob([uInt8Array], {type: contentType});
      blob.lastModifiedDate = new Date();
      blob.name = filename;

      return new File([blob], filename, {type: contentType});
    },

    addListener() {
    },
  },
  //加载完成执行
  mounted() {
    this.onPageLoad();
    let vm = this;
    eiapi.getReportInfo().then(response => {

      if (!response || !response.msg) {
        return;
      }
      console.log(response);
      let reportDesignTemplateConfig = response.data && response.data.reportDesignTemplateConfig;
      let currentIp = response.data.ip;
      let urlConfig = JSON.parse(reportDesignTemplateConfig);
      console.log(urlConfig);
      // let reportDSrc = "";
      // 配置了额外的映射则用而外映射
      if (currentIp && urlConfig.externalnetwork && urlConfig.externalnetwork.ips) {
        let ips = urlConfig.externalnetwork.ips;
        if (ips) {
          _.forEach(ips, (addr, ip) => {
            console.log(ip + addr)
            if (addr && _.includes(currentIp, ip)) {
              vm.reportDSrc = decodeURIComponent(addr);
            }
          })
        }
      }
      // 没有配置就用默认的
      if (!vm.reportDSrc) {
        vm.reportDSrc = decodeURIComponent(urlConfig["baseUrl"]);
      }
      // vm.reportDSrc =decodeURIComponent('http://localhost:5173/#/report/preview?path=/%E7%A9%BA%E7%99%BD.rtpl&op=write&showOpButton=true');
      console.log("reportDSrc:", vm.reportDSrc)
      // 审核查看模板
      vm.auditReportViewTemplate = decodeURIComponent(urlConfig["auditReportViewTemplate"]);
      console.log('报告模板地址：' + vm.auditReportViewTemplate);
      // console.log(response)
    })

    // this.getConfigKey("reportDesignTemplateConfig").then((response) => {
    //   if (!response || !response.msg) {
    //     return;
    //   }
    //   let urlConfig = JSON.parse(response.msg);
    //   // for (let key in urlConfig) {
    //   //   vm.reportModelPath[key] = urlConfig[key].structTemplate;
    //   // }
    //   vm.reportDSrc = decodeURIComponent(urlConfig["baseUrl"]);
    //   // vm.reportDSrc =decodeURIComponent('http://localhost:5173/#/report/preview?path=/%E7%A9%BA%E7%99%BD.rtpl&op=write&showOpButton=true');
    //   console.log("reportDSrc:",vm.reportDSrc)
    //   vm.auditReportViewTemplate =  decodeURIComponent(urlConfig["auditReportViewTemplate"]);
    //   console.log('报告模板地址：'+vm.auditReportViewTemplate);
    // });

    this.getConfigKey("examIemConfigKey").then((response) => {
      if (!response || !response.msg) {
        return;
      }
      let reportDesignUrlKey = "reportDesignTemplateConfig";

      vm.examIemConfigKey = JSON.parse(response.msg);
      for (let key in vm.examIemConfigKey) {
        if (!!vm.examIemConfigKey[key] && !!vm.examIemConfigKey[key][reportDesignUrlKey])
          vm.reportModelPath[key] =
        vm.examIemConfigKey[key][reportDesignUrlKey].structTemplate;
      }
      var aa = 0;
    });
  },

  computed: {
    //当前机房，当前编辑的检查信息
    ...mapGetters([
      "currentEquipRoom",
      "examInfoEdited",
      "reportToAuditForPrint",
      "reportToAudit",
      "reportForm_his",
    ]),

    formattedExamAge() {
      if (!this.reportForm || !this.reportForm.examAge) return '';
      let age = fmt_exam_age(this.reportForm);
      return age;
    },

    //采集的影像
    imagesSet() {
      const fm = this.reportForm,
        dicomStudies = fm.dicomStudies;
      if (!dicomStudies || dicomStudies.length <= 0) {
        return;
      }

      let imagesSet = [];

      dicomStudies.forEach((dicomStudy) => {
        const seriesSet =
          !!dicomStudy && !!dicomStudy ? dicomStudy.seriesSet : null;
        if (!seriesSet) {
          return;
        }

        seriesSet.forEach((ser) => {
          let images = ser.imagesSet;
          if (!images) {
            return true;
          }
          //刷新页面时,选中序号更新
          //var count = 1
          images.forEach((img) => {
            if (img.status !== 2 && !!img.uri) {
              imagesSet.push(img);
            }
            //if (img.selected) {
            //    img.selectNum = count++
            //}
          });
        });
      });
      //console.log(imagesSet);
      return imagesSet.reverse();
    },
    //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
    rollbackable() {
      const fm = this.reportForm;

      return (
        fm.id && //this.editable &&
        !fm.status &&
        matchAnyStatus(this.reportForm, StatusDict.exam, StatusDict.report)
      ); //(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
    },

    //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
    reportable() {
      const fm = this.reportForm;

      let reportable = (
        fm.id && //this.editable &&
        !fm.status &&
        matchAnyStatus(
          this.reportForm,
          StatusDict.regist,
          StatusDict.exam,
          StatusDict.report
        )
      ); //(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
      if (reportable) {
        console.log("当前检查状态可以提交");
      }
      return reportable;
    },

    writable() {
      return this.hasPermi(PERMI_WRITE);
    },

    //是否可二审
    secondAuditable() {
      let secondAuditable =
        matchAnyStatus(this.reportForm, StatusDict.audit)
        && this.hasPermi(PERMI_SECOND_AUDIT)
        && !isFinalAuditStatus(this.reportForm); //!!this.reportForm.resultStatus && '2' === this.reportForm.resultStatus.dictValue;
      if (secondAuditable) {
        console.log("当前检查可以二审");
      }
      return secondAuditable;
    },
    //是否可三审
    thirdAuditable() {
      let thirdAuditable =
        matchAnyStatus(this.reportForm, StatusDict.second_audit)
        && this.hasPermi(PERMI_THIRD_AUDIT)
        && !isFinalAuditStatus(this.reportForm); //!!this.reportForm.resultStatus && '2' === this.reportForm.resultStatus.dictValue;
      if (thirdAuditable) {
        console.log("当前检查可以三审")
      }
      return thirdAuditable
    },
    //是否可审核
    auditable() {
      return this.hasPermi(PERMI_AUDIT) && matchAnyStatus(
        this.reportForm,
        StatusDict.regist,
        StatusDict.exam,
        StatusDict.report
      ); //!!this.reportForm.resultStatus && '2' === this.reportForm.resultStatus.dictValue;
    },
    //是否可复审
    reauditable() {
      return matchAnyStatus(this.reportForm, StatusDict.audit); //!!this.reportForm.resultStatus && '3' === this.reportForm.resultStatus.dictValue;
    },
    //
    withdrawable() {
      let withdrawable = false; //!!this.reportForm.resultStatus && /^[34]$/.test(this.reportForm.resultStatus.dictValue);
      if ((this.hasPermi(PERMI_AUDIT) && matchAnyStatus(this.reportForm, StatusDict.audit))
        ||
        (this.hasPermi(PERMI_SECOND_AUDIT) && matchAnyStatus(this.reportForm, StatusDict.audit, StatusDict.second_audit))
        ||
        (this.hasPermi(PERMI_THIRD_AUDIT) && matchAnyStatus(this.reportForm, StatusDict.second_audit, StatusDict.third_audit))
      ) {
        withdrawable = true
      }
      /*if (withdrawable) {
        console.log("当前检查可以从初审医生回退");
      }*/
      return withdrawable;
    },
    secondWithdrawable() {
      let withdrawable = matchAnyStatus(
        this.reportForm,
        StatusDict.audit,
        StatusDict.second_audit
      ) && this.hasPermi(PERMI_SECOND_AUDIT); //!!this.reportForm.resultStatus && /^[34]$/.test(this.reportForm.resultStatus.dictValue);
      if (withdrawable) {
        console.log("当前检查可以从二审医生回退")
      }
      return withdrawable;
    },
    thirdWithdrawable() {
      let withdrawable = matchAnyStatus(
        this.reportForm,
        StatusDict.second_audit,
        StatusDict.third_audit
      ) && this.hasPermi(PERMI_THIRD_AUDIT); //!!this.reportForm.resultStatus && /^[34]$/.test(this.reportForm.resultStatus.dictValue);
      if (withdrawable) {
        console.log("当前检查可以从三审医生回退")
      }
      return withdrawable;
    },
    auditRollbackable() {
      return matchAnyStatus(
        this.reportForm,
        StatusDict.second_audit,
        StatusDict.third_audit
      )
    },
    //是否可打印
    printable() {
      //已报告、已审核、已复核、已打印、已归档
      return matchAnyStatus(
        this.reportForm,
        StatusDict.report,
        StatusDict.audit,
        StatusDict.second_audit,
        StatusDict.third_audit,
        StatusDict.reaudit,
        StatusDict.archive
      );
    },
    //提交按钮说明
    editButtonLabel() {
      const fm = this.reportForm;
      return matchAnyStatus(fm, StatusDict.regist, StatusDict.exam)
        ? "提交"
        : "保存";
    },
    //
    numImagesSelected() {
      const imagesSet = this.imagesSet;
      if (!imagesSet) {
        return 0;
      }

      return imagesSet.filter((i) => i.selected).length;
    },
  },

  watch: {
    //当前报告检查信息是否有修改
    examInfoEdited: {
      deep: true,
      handler(nval, oval) {
        //console.log(nval, oval);
        let fm = this.reportForm;
        if (!!nval && !!nval.id && !!fm && nval.id === fm.id) {
          const props = ["inpNo", "bedNo"];
          props.forEach((p) => {
            fm[p] = nval[p];
          });
        }
      },
    },

    //打印时要审核的报告
    reportToAuditForPrint: {
      deep: true,
      handler(nval, oval) {
        if (!nval) {
          return;
        }
        this.handleAudit({firmed: true});
      },
    },

    //打印时要审核
    // reportToAudit: {
    //   deep: true,
    //   handler(nval, oval) {
    //     if (!nval) {
    //       return;
    //     }
    //     this.handleAudit({ firmed: true });
    //   },
    // },
  },

  activated() {
    //this.reportDSrc+="?aa = " +new Date().getTime()
    this.ikey = new Date().getTime();
    console.log("this.reportDSrc", this.reportDSrc);
    this.onPageLoad();
  },

  deactivated() {
    console.log("this.reportDSrc", this.reportDSrc);
  },

  created() {
  },
};

export default model;
