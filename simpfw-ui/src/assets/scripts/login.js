import { mapGetters } from 'vuex'
import {cloneDeep} from "lodash";

import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt';

//医信签
import Linksign from "@/views/pacs/linksign";
import QRSign from "@/assets/scripts/pacs/linksign/QRSign"
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
import {setRoom} from "@/utils/equip-room"
import {modal as LinkSignCom} from "@/assets/scripts/pacs/linksign/mixins";
//
import chpasswd from "@/assets/scripts/chpasswd";

import config from "@/assets/scripts/global/config";

import DesUtil from "@/utils/DesUtil";



//登录方式
const LoginMode = Object.freeze({
  regular: "regular",
  linksign: "linksign",
  linksignOTP: "linksignOTP",
  linksignSMS: "linksignSMS"
});
//各种登录方式最后输入状态
const LoginFormLatest = {};
//
const LoginAction = Object.freeze({
  CHPASSWD: "CHPASSWD",
  LOGIN: "LOGIN"
});

export default {
  name: "Login",

  dicts: ["uis_exam_item"],

  components: {Linksign},

  mixins: [chpasswd, LinkSignCom],

  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: null,
        password: null,
        rememberMe: false,
        code: "",
        uuid: "",
        equipRoom: {roomCode: null},
        examItemCode:null,
        authTime: null
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号/工号/手机号码" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码/验证码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },

      loading: false,
      // 验证码开关
      captchaOnOff: false,
      // 注册开关
      register: false,
      redirect: undefined,
      //登录方式
      loginMode: LoginMode,
      activeLoginMode: LoginMode.regular,
      //
      loginAction: LoginAction,
      currLoginAction: LoginAction.LOGIN,
      //下拉列表
      combo: {
        //机房列表
        equipRoom: [{roomCode: null, roomName: "请选择房间"}],

        authTime: [{value: 3, label: "3分钟"}
          , {value: 30, label: "30分钟"}
          , {value: 60, label: "1小时"}
          , {value: 120, label: "2小时"}
          , {value: 180, label: "3小时"}
          , {value: 240, label: "4小时"}
          , {value: 300, label: "5小时"}]
      },

      qr: null,
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
    this.findEquipRoom();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      let equipRoomCode = Cookies.get('equipRoomCode')
      equipRoomCode = !equipRoomCode || "undefined" === equipRoomCode || "null" === equipRoomCode? null : equipRoomCode
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : DesUtil.decode(username, config.securityKey),
        password: password === undefined ? this.loginForm.password : DesUtil.decode(password, config.securityKey),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        equipRoom: {roomCode: equipRoomCode},//this.loginForm.equipRoom
        authTime: (this.loginForm.authTime || 300)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          ///let fm = this.loginForm;
          ///if(fm.equipRoom && fm.equipRoom.roomCode) {
          ///  fm.equipRoom = this.combo.equipRoom.find(er => er.roomCode === fm.equipRoom.roomCode) || fm.equipRoom;
          ///}

          this.loading = true;
          let loginForm =  cloneDeep(this.loginForm);
          loginForm.username = DesUtil.encode(this.loginForm.username, config.securityKey);
          loginForm.password = DesUtil.encode(this.loginForm.password, config.securityKey);
          if (loginForm.rememberMe) {
            var username = loginForm.username;
            var password = loginForm.password;
            Cookies.set("username", username, { expires: 30 });
            Cookies.set("password", password, { expires: 30 });
            Cookies.set('rememberMe',loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }

          ///Cookies.set('equipRoomCode', (fm.equipRoom? fm.equipRoom.roomCode : ""), { expires: 30 });
          this.$store.dispatch("Login", loginForm).then(() => {
            // this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
            this.successForward();
          }).catch(() => {
            this.loading = false;
            if (this.captchaOnOff) {
              this.getCode();
            }
          });
        }
      });
    },
    //登录成功跳转
    successForward() {
      //房间信息
      let fm = this.loginForm, equipRoom = fm.equipRoom;
      //取完整房间信息
      if(equipRoom && equipRoom.roomCode) {
        equipRoom = this.combo.equipRoom.find(er => er.roomCode === equipRoom.roomCode) || equipRoom;
      }
      //Cookie记住登录房间
      Cookies.set('equipRoomCode', (equipRoom? equipRoom.roomCode : ""), { expires: 30 });
      //书写/检查房间
      setRoom(equipRoom);
      //
      this.$store.commit("SET_EQUIPROOM", equipRoom);
      //跳转
      this.$router.push({ path: this.redirect || "/" ,query:this.$route.query}).catch(err => console.error(err));
    },

    //切换登录方式
    setLoginMode(mode) {
      //
      const prevMode = this.activeLoginMode;
      this.activeLoginMode = mode;
      //
      if(LoginMode.linksign === mode || LoginMode.linksignOTP === mode || LoginMode.linksignSMS === mode) {
        if(!this.qr){
          this.qr = new QRSign(this.successForward);
          this.qr.activated = true;
          this.refreshQr();
        }
        //
        this.qr.activated = LoginMode.linksign === mode;
      } else if(this.qr) {
        this.qr.activated = false;
      }
      //
      if(LoginMode.regular === mode || LoginMode.linksignOTP === mode || LoginMode.linksignSMS === mode) {
        LoginFormLatest[prevMode] = cloneDeep(this.loginForm);
        //恢复上次输入状态
        const fml = LoginFormLatest[mode] || {};
        let {username, password} = fml;

        this.loginForm.username = username;
        this.loginForm.password = password;
      }
    },
    //
    switchLoginMode() {
      let mode = LoginMode.linksign === this.activeLoginMode? LoginMode.regular : LoginMode.linksign;
      this.setLoginMode(mode);
    },
    //刷新二维码
    refreshQr() {
      if(this.qr) { this.qr.refreshQrcode(); }
    },
    //获取房间列表
    findEquipRoom() {
      findRoom({}).then(res => {
        this.combo.equipRoom = this.combo.equipRoom.concat(res && res.rows? res.rows : []);
      })
    },

    //登录/修改密码
    setLoginAction(act) {
      this.currLoginAction = act;
    },
    //切换聚集模板
    changeFocus(val){
        let doms = document.getElementById (val)
        doms.focus()
        doms.select()

    }
  },
  computed: {
    ...mapGetters([
      'appTitle'
    ]),
    //账号输入框为空时
    usernamePlaceholder() {
      if(LoginMode.linksignSMS === this.activeLoginMode) {
        return "手机号码";
      }

      return "账号/工号";
    },
    //密码为空时
    passwordPlaceholder() {
      if(LoginMode.linksignSMS === this.activeLoginMode) {
        return "短信验证码";
      }
      if(LoginMode.linksignOTP === this.activeLoginMode) {
        return "动态密码/签名密码";
      }

      return "密码";
    },
    //密码输入框/明文输入框
    vericodeFieldType() {
      if(LoginMode.linksignSMS === this.activeLoginMode) {
        return "text";
      }
      return "password";
    }
  }
};
