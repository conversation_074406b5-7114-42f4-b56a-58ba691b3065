import deepmerge from "deepmerge";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/equiproom/queuenumberrule/api";
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

let model = {
  name: "QuereNumberRule",
  extends: BaseGridModel,
  dicts: [ "uis_queue_number_rule_type", "uis_exam_item", "uis_exam_modality", "uis_inp_type" ],
  components: { },
  data() {
    let dat = {
      queryForm: { }

      , editForm: {
        id: null
        , ruleType: {}
        , ruleTypeSpec: {}
        , rulePrefix: null
        , rulePattern: null
        , priority: null
        , reservedNo: null
      },

      editFormOptions: {
        visible: false,
        combo_queueNumberRuleTypeSpec: [],
        rules: {
          "ruleType.dictValue": {required: true, message: "请选择规则分类"},
          "ruleTypeSpec.dictValue": {required: true, message: "请选择检查类型"},
          reservedNo: {required: true, message: "请输入预留号"}
        },
        combo_equipRoom: []
      }
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.rulePrefix = fm.rulePattern = fm.priority = fm.reservedNo = null;
      fm.ruleType = {};
      fm.ruleTypeSpec = {};

      this.editFormOptions.combo_queueNumberRuleTypeSpec = [];
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editFormOptions.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let data = res.data, ruleType = data.ruleType;
        if(!ruleType) {
          data.ruleType = {};
        } else {
          this.onChangeRuleType(data.ruleType.dictValue);
        }

        if(!data.ruleTypeSpec) {
          data.ruleTypeSpec = {};
        }

        Object.assign(this.editForm, res.data);
        this.editFormOptions.visible = true;
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          delete fm.status;
          if (fm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              this.editFormOptions.visible = false;
              this.getList();
            });
          } else {
            api.save(fm).then(res => {
              this.$modal.msgSuccess("新增成功");
              this.editFormOptions.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除 */
    doDelete(row) {
      return api.del(row.id);
    },

    onChangeRuleType(item) {
      //console.log(arguments);
      let fm = this.editForm, spec;
      fm.ruleTypeSpec = {};
      
      spec = this.dict.type["uis_" + item];

      this.editFormOptions.combo_queueNumberRuleTypeSpec = spec || [];
    },
    //关闭编辑窗口
    cancelEdit() {
      this.editFormOptions.visible = false;
    },

    findEquipRoom() {
      findRoom({}).then(res => {
        this.editFormOptions.combo_equipRoom = res && res.rows || [];
      });
    }
  },

  created() {
    this.findEquipRoom();
  }
};

export default model;