import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

const EqiopRoomPicker = {

  methods: {
    //开始选择
    selectEquipRoom() {
      let pickableEquipRoom = this.pickableEquipRoom;
      //
      let prom;
      if(pickableEquipRoom && pickableEquipRoom.length) {
        prom = Promise.resolve(pickableEquipRoom);
      } else {
        prom = this.findPickableEquipRoom();
      }
      //
      return prom.then(this.canSelectEquipRoom);
    },

    canSelectEquipRoom() {
      const h = this.$createElement;
      //生成下拉选项
      let options = this.pickableEquipRoom.map(e => h('el-option', {props: {value: e.roomCode, label: e.roomName}}, null));
      //最后选择的机房
      let equipRoom = null;
      //选择框
      return this.$msgbox({
        title: '选择机房',
        message: h('el-select', {props: {value: null}, on: {change: (val) => {equipRoom = this.pickableEquipRoom.find(e => e.roomCode === val);}}}, options),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(action => {
        return equipRoom;
      });
    },

    //读取机房列表
    findPickableEquipRoom() {
      return findRoom({}).then(res => {
        const pickableEquipRoom = res && res.rows || [];
        this.pickableEquipRoom = pickableEquipRoom;
        return pickableEquipRoom;
      });
    }  
  }
}

export {EqiopRoomPicker};