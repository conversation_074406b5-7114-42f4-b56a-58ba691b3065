import {mergeWith} from "lodash";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import { treeselect as deptTreeselect } from "@/api/system/dept";
import * as api from "@/assets/scripts/pacs/equiproom/dicominfo/api";
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

import {mergeWithNotNull, undefinedOrNull} from "@/utils/common";
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

function emptyEntity() {
  return {
        id: null
        , scpAe: null
        , scpIp: null
        , scpPort: null
        , scuAe: null
        , scuIp: null
        , scuPort: null
        , modalityCode: null
        , modality: null
        , deviceCode: null
        , device: null
        , path: null
        , pathSecond: null
        , transmissionType: null
        , transmissionTypes: []
        , isSequence: 0
        , status: 0
        , equipRoom: {roomCode: null}
        , dept: {deptCode: null}
        , examItemCode: []
        , deviceNo: null
        , dataSource: {dictValue: null},
      };
}

let model = {
  name: "DicomInfo",
  extends: BaseGridModel,
  components: { Treeselect },
  mixins: [ExamDataScope],
  dicts: [ "uis_exam_item", "data_source"],
  data() {
    let dat = {

      editForm: emptyEntity(),

      editFormOptions: {
        visible: false,
        rules: {
          //"equipRoom.roomCode": {required: true, message: "请选择所属机房"},
          "modality": {required: true, message: "请输入设备名称"},
          "modalityCode": {required: true, message: "请输入设备型号"},
          "deviceNo": {required: true, message: "请输入设备编码"}
        },
        combo_equipRoom: []
      },

      deptTreeData: []
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.searchForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    //读取部门树信息
    buildDeptTree() {
      deptTreeselect().then(res => {
        this.deptTreeData = res.data;
      });
    },
    // 表单重置
    resetEditForm() {
      this.editForm = emptyEntity();
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editFormOptions.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();

      api.get(row.id).then(res => {
        let data = res.data;
        mergeWith(this.editForm, data, null, mergeWithNotNull);
        if(!!data.examItems) data.examItems.forEach(element => {
          this.editForm.examItemCode.push(element.dictValue);
        }); 
        this.editFormOptions.visible = true;
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          let cb = () => {
            this.$modal.msgSuccess("保存成功");
            this.editFormOptions.visible = false;
            this.getList();
          };

          const fm = this.editForm;
          fm.transmissionType = fm.transmissionTypes && fm.transmissionTypes.length? fm.transmissionTypes.join(",") : null;
          fm.examItems = [];
          if(!!this.editForm.examItemCode){
            this.editForm.examItemCode.forEach(c => {
              let dict = this.ctrlData.dict.uis_exam_item.find(d => c === d.value);
              fm.examItems.push(dict? dict.raw : {dictValue: c});
            })
          }

          let dataSourceValue = this.editForm.dataSource.dictValue;
          if(!!dataSourceValue){
              let dict = this.ctrlData.dict.data_source.find(d => dataSourceValue === d.value);
              fm.dataSource = dict.raw;
          }else{
            fm.dataSource = {dictCode: null};
          }

          if (fm.id) {
            api.update(fm).then(cb);
          } else {
            api.save(fm).then(cb);
          }
        }
      });
    },
    /** 删除 */
    doDelete(row) {
      return api.del(row.id);
    },

    //关闭编辑窗口
    cancelEdit() {
      this.editFormOptions.visible = false;
    },

    findEquipRoom() {
      findRoom({}).then(res => {
        this.editFormOptions.combo_equipRoom = res && res.rows || [];
      });
    }
  },

  created() {
    this.buildDeptTree();
    this.findEquipRoom();
  },

  watch: {
    "editForm.transmissionType": {
      deep: true,
      handler (newv, oldv) {
        this.editForm.transmissionTypes = !undefinedOrNull(newv)? newv.split(",") : [];
      }
    },
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    "dict.type.data_source": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    // "ctrlData.dict.uis_exam_item":{
    //   deep: true,
    //   handler(nv, ov) {
    //     console.log("检查项目字典变更", nv, ov);
    //   }
    // },
  },

  computed: {
    scpActivated() {
      let types = this.editForm.transmissionTypes;
      return types && types.length? (-1 === types.indexOf("0")) : false;
    },
    scuActivated() {
      let types = this.editForm.transmissionTypes;
      return types && types.length? (-1 === types.indexOf("1")) : false;
    }
  }
};

export default model;