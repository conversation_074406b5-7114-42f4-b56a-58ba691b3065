import deepmerge from "deepmerge";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import {listData as queryDictData} from "@/api/system/dict/data";

import * as api from "@/assets/scripts/pacs/equiproom/equiproomstatus/api";

let model = {
  name: "EquipRoomStatus",
  extends: BaseGridModel,

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      queryDictData({pageSize: 9999,dictType: 'uis_equip_room'}).then(res => {
        this.loading = false;
        this.grid.data = res.rows;
      });
    },
    
    /**
     * 字段取值
     */
    regProp(row, prop) {
      const props = prop.split("."), propData = props.length == 1? row : row[props[0]], propName = props[props.length - 1];
      return {data: propData, name: propName, value: (propData? propData[propName] : null)};
    },

    /**
     * 是否启用
     */
    isValid(val) {
      return "undefined" == (typeof(val)) || null === val || 0 === val || "0" === val;
    },

    /**
     * 更新状态
     */
    updateStatus(row, prop) {
      const cp = deepmerge({}, row);
      const props = this.regProp(cp, prop), propData = props.data, propName = props.name;;
      const val = props.value;
      //extend: 上午，下午，全天
      if(!propData) {
        cp["extend"] = propData = {};
      }
      //
      propData[propName] = this.isValid(val)? 1 : 0;
      //
      api.update(cp).then(res => {
        this.$modal.msgSuccess("设置成功");
        this.getList();
      });
    },
    
    /**
     * 状态说明
     */
    colBtn(row, prop) {
      const props = this.regProp(row, prop), val = props.value;
      return this.isValid(val) || "0" === val? "启用" : "停用";
    }
  }
};

export default model;
