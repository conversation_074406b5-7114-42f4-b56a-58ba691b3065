import {cloneDeep, mergeWith} from "lodash";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import {undefinedOrNull, mergeWithNotNull} from "@/utils/common";

import * as api from "./api";
import {find as findDevices} from "@/assets/scripts/pacs/equiproom/dicominfo/api";

//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";


function emptyForm() {
  return {
    roomName: null,
    roomCode: null,
    workAm: 0,
    workPm: 0,
    workDay: 0,
    workPerm: 0,
    examItemCode: [],
    device: {id: null},

    workAmFlag: true,
    workPmFlag: true,
    workDayFlag: true,
    workPermFlag: true,

    imageService: null,

    examItems: []
  };
}

let model = {
  name: "EquipRoomStatus",
  mixins: [ExamDataScope],
  extends: BaseGridModel,
  dicts: [ "uis_exam_item" ],

  props: {
    editable: {type: Boolean, default: true}
  },

  data() {
    return {
      editFormOptions: {
        visible: false,
        rules: {          
          "roomName": {required: true, message: "请输入房间名称"},
          "roomCode": {required: true, message: "请输入房间号码"}
        }
      },

      combo: {
        devices: []
      },

      editForm: emptyForm()
    }
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find({pageSize: 9999}).then(res => {
        this.loading = false;
        this.grid.data = res.rows;
      });
    },
    /**
     * 是否启用
     */
    isWork(val) {
      return undefinedOrNull(val) || 0 === val || "0" === val;
    },
    //重置表单
    resetEditForm() {
      this.editForm = emptyForm();
    },
    //添加
    handleAdd() {
      this.resetEditForm();

      this.editFormOptions.visible = true;
    },
    //编辑
    handleEdit(item) {
      this.resetEditForm();

      api.get(item.id).then(res => {
        mergeWith(this.editForm, res.data, null, mergeWithNotNull);
        //
        this.setExamItemCode();

        this.editFormOptions.visible = true;
      });
    },
    /**
     * 更新状态
     */
    updateStatus(row, prop) {
      //
      const cp = cloneDeep(row);
      //当前状态
      const val = cp[prop];
      //
      cp[prop] = this.isWork(val)? 1 : 0;
      //
      api.update(cp).then(res => {
        this.$modal.msgSuccess("设置成功");
        this.getList();
      });
    },
    
    /**
     * 状态说明
     */
    workLabel(row, prop) {
      const val = row[prop];
      return this.isWork(val) || "0" === val? "启用" : "停用";
    },

    /**
     * 提交
     */
    submitEditForm() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          //
          fm.workAm = fm.workAmFlag? 0 : 1;
          fm.workPm = fm.workPmFlag? 0 : 1;
          fm.workDay = fm.workDayFlag? 0 : 1;
          fm.workPerm = fm.workPermFlag? 0 : 1;
          //
          let examItems = [];
          if(fm.examItemCode) {
            fm.examItemCode.forEach(c => examItems.push({dictValue: c}));
          }
          fm.examItems = examItems;

          let cb = res => {
            this.$modal.msgSuccess(res.message || "保存成功。");
            this.editFormOptions.visible = false;
            this.getList();
          };
          
          if (fm.id) {
            api.update(this.editForm).then(cb);
          } else {
            api.save(fm).then(cb);
          }
        }
      });
    },
    /**
     * 取消编辑
     */
    cancelEdit() {
      this.editFormOptions.visible = false;
    },
    //调用接口执行删除
    doDelete(item) {
      api.del(item.id);``
    },
    //选中的检查项目编码下拉列表值
    setExamItemCode() {
      let fm = this.editForm, examItemCode;
      examItemCode = fm.examItems? fm.examItems.map(e => e.dictValue) : [];
      fm.examItemCode = examItemCode;
    },
    //获取设备列表
    findDevices() {
      findDevices({pageSize: 9999}).then(res => {
        this.combo.devices = res.rows;
      });
    }
  },

  created() {
    this.findDevices();
  },

  watch: {
    //观察商务检查状态改变是否变更
    "editForm.workAm": {
      handler(newv, oldv) {
        this.editForm.workAmFlag = this.isWork(newv);
      }
    },
    "editForm.workPm": {
      handler(newv, oldv) {
        this.editForm.workPmFlag = this.isWork(newv);
      }
    },
    "editForm.workDay": {
      handler(newv, oldv) {
        this.editForm.workDayFlag = this.isWork(newv);
      }
    },
    "editForm.workPerm": {
      handler(newv, oldv) {
        this.editForm.workPermFlag = this.isWork(newv);
      }
    },

    "editForm.examItems": {
      deep: true,
      handler(newv, oldv) {
        this.setExamItemCode();
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }
  }
};

export default model;
