import deepmerge from "deepmerge";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";

import * as api from "@/assets/scripts/pacs/equiproom/equiproomsettings/api";
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

let model = {
  name: "EquipRoomSettings",
  extends: BaseGridModel,
  dicts: [ "uis_exam_item", "uis_exam_parts_type" ],
  components: { ExamPartsTree },
  data() {
    let dat = {
      queryForm: {
        regionName: null
      }

      , editForm: {
        visible: false
        , title: "编辑"
        , id: null
        , equipRoom: {}
        , examItem: {}
        , examParts: []
        , amNum: null
        , pmNum: null
        , waitNum: null

        , examItem_dictCode: []
      },

      editFormOpts: {
        combo_equipRoom: []
      }
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.amNum = fm.pmNum = fm.waitNum = null;
      fm.equipRoom = {};
      fm.examItem = {};
      fm.examParts = [];
      //取消选中的部位
      if(fm.examParts_ids) {
        this.$refs["examPartsTree"].setCheckedKeys(fm.examParts_ids, false);
      }
      fm.examParts_ids = [];
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editForm.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let data = res.data;
        data.equipRoom = data.equipRoom || {};
        data.examItem = data.examItem || {};
        data.examParts = data.examParts || [];
        //
        let examParts_ids = null;
        if(data.examParts.length) {
          examParts_ids = data.examParts.map(ep => ep.id);
        }
        data.examParts_ids = examParts_ids || [];

        Object.assign(this.editForm, res.data);
        this.editForm.visible = true;
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          //
          delete fm.status;
          //检查部位
          let examParts = [];
          let checkedParts = this.$refs["examPartsTree"].getCheckedNodes();
          for(let i = 0, parts; i < checkedParts.length; i ++) {
            parts = checkedParts[i];
            examParts.push({id: parts.id});
          }
          fm.examParts = examParts;
          //更新或新增
          if (fm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(fm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除 */
    doDelete(row) {
      return api.del(row.id);
    },

    colFmt_examParts(row, col, val, idx) {
      if(!val) {
        return null;
      }
      //console.log(arguments);
      return val.map(ep => ep.partsName).join(",");
    },

    findEquipRoom() {
      findRoom({}).then(res => {
        this.editFormOpts.combo_equipRoom = res && res.rows || [];
      });
    }
  },

  created() {
    this.findEquipRoom();
  }
};

export default model;