import request from '@/utils/request'

const ctx = '/equiproom';

// 查询列表
export function find(query) {
  return request({
    url: ctx + '/list',
    method: 'post',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 查询详细
export function get(id) {
  return request({
    url: ctx + '/get?id=' + id,
    method: 'get'
  })
}
// 查询详细
export function load(query) {
  if(!query || (!query.id && !query.roomCode)) { return null; }
  
  return request.get(ctx + '/get', {params: query})
}

// 新增
export function save(data) {
  return request({
    url: ctx + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: ctx + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request({
    url: ctx + '/del/' + id,
    method: 'delete'
  })
}

//读取相关参数
export function params(id) {
  return request.get(ctx + "/params", {params: {id: id}});
}


