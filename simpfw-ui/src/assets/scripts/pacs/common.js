// //初始翻页
// const BlankPager = {
//         total: 0,
//         currentPage: 1,
//         pageSize: 20,
//         pageLast: 0,
//         reset: true,
//         nextEnabled: false
//       };
// export {BlankPager}
// //数据权限字典
// const DataScopeDefined = [
//   {value: 1, label: "全院"}
//   , {value: 2, label: "科室"}];
// export {DataScopeDefined}
// //检查类型
// const Modalities = {
//   ECG: 21
// };
// export {Modalities};
// //检查状态
// const ResultStatus = {
//   REF: 11
// };
// export {ResultStatus};
// //数据源
// const DataSource = {
//   ENDOSCOPE: 4
// };
// export {DataSource};
//
// export const DigitYON = [{value: 0, label: "否"}, {value: 1, label: "是"}];

/**
 * canvas转换成图片base64
 * @param {*} canvas 
 * @returns 
 */
export function imageFromCavas(canvas) {
  return canvas.toDataURL("image/jpeg", 1.0);
}
/**
 * 
 * @param {*} ele 
 * @returns 
 */
function classNameList(ele) {
  return ele.className.split(/\s+/);
}
/**
 * 
 * @param {*} ele 
 * @param {*} className 
 * @returns 
 */
export function addClassName(ele, className) {
  if(!className) { return; }

  let eleClz = classNameList(ele);
  //没有则加
  const pos = eleClz.findIndex(clz => clz === className);
  if(-1 === pos) { 
    //更新
    eleClz.push(className);
    ele.className = eleClz.join(" ");
  }
}
/**
 * 
 * @param {*} ele 
 * @param {*} className 
 * @returns 
 */
export function removeClassName(ele, className) {
  if(!className) { return; }

  let eleClz = classNameList(ele);
  const pos = eleClz.findIndex(clz => clz === className);
  if(-1 !== pos) { 
    eleClz.splice(pos, 1); 
    ele.className = eleClz.join(" ");
  }
}

//获取屏幕DPI
export function dpi() {
  let vals;
  if ("undefined" !== (typeof(window.screen.deviceXDPI))) {
    vals = [window.screen.deviceXDPI, window.screen.deviceYDPI];
  } else {
    let dimeEle = document.createElement("DIV");
    dimeEle.style.cssText = "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:9999;visibility:hidden";
    document.body.appendChild(dimeEle);
    vals = [parseInt(dimeEle.offsetWidth), parseInt(dimeEle.offsetHeight)];
    dimeEle.parentNode.removeChild(dimeEle);
  }
  return vals;
}

//像素转毫米
export function px2mm(px, dpi = dpi()) {
  const px1 = 25.4 / dpi;
  return px * px1;
}

//请求上下文
let baseContext = process.env.VUE_APP_BASE_API;
if(0 != baseContext.indexOf("http://") && 0 != baseContext.indexOf("https://")) {
  baseContext = location.protocol + "//" + location.host + baseContext;
}
export { baseContext };