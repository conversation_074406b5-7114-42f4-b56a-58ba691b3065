//vuex
import { mapGetters } from 'vuex';
// web socket
import WebSocketModel from "@/assets/scripts/pacs/BaseWebSocketModel";
//
import settings from "@/views/pacs/image/ImageCollectionSettings";
//当前房间
import CurrentEquipRoom from "@/views/pacs/equiproom/currentequiproom";

import {params as getEquipRoomParams} from "@/assets/scripts/pacs/equiproom/api";

const model = {
  name: "PAGE_UIS_IMAGECOLLECTION",

  mixins: [ WebSocketModel ],

  components: { settings, CurrentEquipRoom },
  
  data() {
    return {
      socketEnabled: false,
      socketUrl: null
    };
  },

  methods: {
    prepare() {
      let currRoom = this.currentEquipRoom;
      if(!currRoom || !currRoom.id) { return; }
      //影像服务已连接
      if(this.isConnectedSocked) {
        console.log("已连接影像采集服务。");
        return;
      }
        //读取机房配置：影像服务地址，文件存储位置
        getEquipRoomParams(currRoom.id).then(r => {
          let room = r? r.equipRoom : null;
          //使用影像服务采集或浏览器调用摄像头
          if(!!room && !!room.imageService) {
            //影像服务
            this.socketUrl = `ws://${room.imageService}/uis/image`;
            this.openSocket();
          }
        });
    },

    handleSocketOpen() {
      this.socketEnabled = true;
    },

    openSettings() {
      this.$refs["settingDialog"].open();
    },

    attachImage() {
      this.handleSocketSend({action: "attachimage"});
    },

    attachVideo() {
      this.handleSocketSend({action: "attachvideo"});
    },

    detachImage() {
      this.handleSocketSend({action: "detachimage"});
    },

    detachVideo() {
      this.handleSocketSend({action: "detachvideo"});
    }
  },
  computed: {
    ...mapGetters(['currentEquipRoom']),
  },

  watch: {
    currentEquipRoom(nv, ov) {
      if(nv && nv.roomCode) {
        this.prepare();
      }
    }
  },

  mounted() {
    this.prepare();
  }
};

export default model;