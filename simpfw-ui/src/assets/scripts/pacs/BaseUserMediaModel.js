//影像宽高比
import {ASPECT_RATIO} from "@/assets/scripts/pacs/image/util";

const model = {

  data() {
    return {
      isConnectedUserMedia: false
    }
  },

  methods: {
    //打开摄像头
    async connectDevice() {
      const videoViewport = this.getVideoViewport();
      if(!videoViewport || !!videoViewport.srcObject) {
        return;
      }
      //
      const viewportWidth = videoViewport.parentNode.clientWidth;
      const viewportHeight = Math.floor(viewportWidth / ASPECT_RATIO);
      const opts = {
        video: true, audio: false,
        video: {
          width: viewportWidth, height: viewportHeight
        }
      };
      try {
        const stream = await navigator.mediaDevices.getUserMedia(opts)
        this.handleMediaStream(stream);

        this.isConnectedUserMedia = true;
      } catch (err) {
        //TypeError: Cannot read properties of undefined (reading 'getUserMedia')
        //NotFoundError: The object can not be found here.
        console.log(err);
        this.$modal.msgError(err);
      }
    },
    //处理影响流
    handleMediaStream(stream) {
      //把stream赋给显示摄像头画面的video
      this.getVideoViewport().srcObject = stream;

      this.syncCaptureImage();
    },
    //断开摄像头
    disconnectDevice() {
      this.isConnectedUserMedia = false;

      let videoViewport = this.getVideoViewport(), stream;
      if(!videoViewport || !(stream = videoViewport.srcObject)) {
        return;
      }

      const tracks = stream.getTracks();
      tracks.forEach(track => track.stop());
      
      videoViewport.srcObject = null;
    },

    // canvas 实时刷新显示
    syncCaptureImage() {
      const videoViewport = this.getVideoViewport()
        , imageViewport = this.getImageViewport();
      if(!videoViewport || !videoViewport) { 
        return; 
      }

      try {
        const ctx = imageViewport.getContext("2d");
        ctx.drawImage(videoViewport, 0, 0, imageViewport.width, imageViewport.height);

        requestAnimationFrame(this.syncCaptureImage);
      } catch (err) {
        this.isConnectedUserMedia = false;
        console.error(err);
      }
    },
    //采集
    captureImage() {
      const imageViewport = this.getImageViewport();
      if(!imageViewport) { return; }

      let imageData = imageFromCavas(imageViewport);
      this.handleCapturedImage(imageData);
    },

    getVideoViewport() { return null; },

    getImageViewport() { return null; },

    handleCapturedImage(imageData) {}
},

  // computed: {
  //   isConnectedUserMedia() {
  //     const videoViewport = this.getVideoViewport();
  //     return !!videoViewport && !!videoViewport.srcObject;
  //   }
  // },

  beforeDestroy() {
    this.disconnectDevice();
  }
};
export default model;
