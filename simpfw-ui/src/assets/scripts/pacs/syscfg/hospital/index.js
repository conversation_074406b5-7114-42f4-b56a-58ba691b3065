import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import AreaTree from "@/views/pacs/syscfg/area/comp/AreaTree";

import * as api from "@/assets/scripts/pacs/syscfg/hospital/api";
import * as areaApi from "@/assets/scripts/pacs/syscfg/area/api";

const blankRegion = {
  regionCode: null
  , resionName: null
};
const blankHospital = {
  hospitalCode: null
  , hospitalName: null
};

let model = {
  name: "Hospital",
  extends: BaseGridModel,
  dicts: ['hospital_grade'],
  components: { Treeselect, AreaTree },
  data() {
    let dat = {
      queryForm: {
        hospitalName: null
      }

      , editForm: {
        visible: false
        , title: "编辑"
        , id: null
        , hospitalCode: null
        , hospitalName: null
        , superiorHospital: blankHospital
        , region: blankRegion
        , hospitalGradeCode: null
        , hospitalPostCode: null
        , hospitalTel: null
        , hospitalAddress: null
      }

      , areaTreeData: []
    };
    
    return dat;
  },
  created() {
    this.loadArea();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.hospitalCode = fm.hospitalName = fm.hospitalTel = fm.hospitalPostCode
        = fm.hospitalAddress = fm.hospitalGradeCode = null;
      fm.superiorHospital = blankHospital;
      fm.region = blankRegion;
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      const areaTree = this.$refs["areaTree"];
      let sup;
      if (mix && mix.hospitalCode) {
        sup = mix;
      } else if(areaTree.current()) {
        sup = areaTree.current().data;
      }
      if(sup) {
        this.editForm.superiorHospital = sup;
      }
      this.editForm.visible = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let data = res.data;
        let sup = data.superiorHospital;
        if(!sup) {
          data.superiorHospital = blankHospital;
        }
        if(!data.region) {
          data.region = blankRegion;
        }
        Object.assign(this.editForm, res.data);
        this.editForm.visible = true;
        this.editForm.title = "修改";
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          if (this.editForm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              this.editForm.visible = false;
              this.getList();
              this.buildTree();
            });
          } else {
            api.save(this.editForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              this.editForm.visible = false;
              this.getList();
              this.buildTree();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        this.buildTree();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleNodeClick(node) {
      this.queryForm.region = node.data;
      this.getList();
    },

    loadArea() {
      areaApi.treeselect().then(res => {
        this.areaTreeData = res.data;
      });
    },
    areaTreeSelectNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.data.regionCode,
        label: node.label,
        children: node.children
      };
    }
  }
};

export default model;