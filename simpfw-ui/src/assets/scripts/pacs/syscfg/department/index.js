import * as api from "@/assets/scripts/pacs/syscfg/department/api";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import DeptTree from "@/views/pacs/syscfg/department/comp/DeptTree";

const blankHospital = {
  hospitalCode: null
  , hospitalName: null
};
const blankDept = {
  deptCode: null
  , deptName: null
};

let model = {
  name: "Department",
  extends: BaseGridModel,
  components: { Treeselect, DeptTree },
  data() {
    let dat = {
      queryForm: {
        deptName: null
        , superiorDept: null
        , deptHospital: null
      }

      , editForm: {
        id: null
        , deptCode: null
        , deptName: null
        , outpOrInp: null
        , deptTel: null
        , superiorDept: blankDept
        , deptHospital: blankHospital
      }
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.deptCode = fm.deptName = null;
      fm.superiorDept = blankDept;
      fm.deptHospital = blankHospital;
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      const deptTree = this.$refs["deptTree"];
      let sup;
      if (mix && mix.deptCode) {
        sup = mix;
      } else if(deptTree.current()) {
        sup = deptTree.current().data;
      }
      if(!sup || !sup.deptCode && !sup.hospitalCode) {
        this.$modal.msg("请选择院区或科室.");
        return ;
      }
      //
      let fm = this.editForm, host;
      //选择院区节点/部门节点
      if(sup.hospitalCode) {
        host = sup;
      } else {
        host = sup.deptHospital;

        fm.superiorDept = sup;
      }
      fm.deptHospital = host;

      fm.visible = true;
      this.title = "添加部门";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let sup = res.data.superiorDept;
        if(!sup) {
          res.data.superiorDept = blankDept;
        }
        res.data.outpOrInp = 1 === res.data.outpOrInp? true : false;
        const fm = this.editForm;
        Object.assign(fm, res.data);
        fm.visible = true;
        fm.title = "修改部门";
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;

          let outpOrInp = fm.outpOrInp;
          fm.outpOrInp = true === outpOrInp? 1 : (false === outpOrInp? 9 : outpOrInp);

          if (fm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(this.editForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleNodeClick(node) {
      const nodeData = node.data;
      let host = null, dept = null;
      if(nodeData.hospitalCode) {
        host = nodeData;
      } else {
        dept = nodeData;
      }
      this.queryForm.deptHospital = host;
      this.queryForm.superiorDept = dept;
      this.getList();
    }
  }
};

export default model;