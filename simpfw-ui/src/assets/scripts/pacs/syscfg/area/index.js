import * as api from "@/assets/scripts/pacs/syscfg/area/api";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import AreaTree from "@/views/pacs/syscfg/area/comp/AreaTree";

let model = {
  name: "Area",
  extends: BaseGridModel,
  components: { Treeselect, AreaTree },
  data() {
    let dat = {
      queryForm: {
        regionName: null
      }

      , editForm: {
        visible: false
        , title: "编辑"
        , id: null
        , regionCode: null
        , regionName: null
        , superiorRegion: {
          regionCode: null
          , regionName: null
        }
      }
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.regionCode = fm.regionName = fm.superiorRegion.regionCode = fm.superiorRegion.regionName = null;

    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      const areaTree = this.$refs["areaTree"];
      let sup;
      if (mix && mix.regionCode) {
        sup = mix;
      } else if(areaTree.current()) {
        sup = areaTree.current().data;
      }
      if(sup) {
        this.editForm.superiorRegion.regionCode = sup.regionCode;
        this.editForm.superiorRegion.regionName = sup.regionName;
        this.editForm.superiorRegion.regionCodePath = sup.regionCodePath;
      }
      this.editForm.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let sup = res.data.superiorRegion;
        if(!sup) {
          res.data.superiorRegion = {regionCode: null, regionName: null};
        }
        Object.assign(this.editForm, res.data);
        this.editForm.visible = true;
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          if (this.editForm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              this.editForm.visible = false;
              this.getList();
            });
          } else {
            api.save(this.editForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              this.editForm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleNodeClick(node) {
      this.queryForm.superiorRegion = node.data;
      this.getList();
    }
  }
};

export default model;