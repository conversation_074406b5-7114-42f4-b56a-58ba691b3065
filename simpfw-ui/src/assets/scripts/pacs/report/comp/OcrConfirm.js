//检查信息
import * as api from "@/assets/scripts/uis/exammanagement/examinfo/api";
import {mapGetters} from 'vuex';
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorkerSrc from 'pdfjs-dist/build/pdf.worker.entry';
//工具

//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";

import ReportConfirmAl from "@/views/pacs/report/comp/ReportConfirmAl";

import ReportFileUploader from "@/views/pacs/report/comp/ReportFileUploader";

import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import {DS_MODALITIES, OTOL_MODALITIES, RAD_MODALITIES, TCD_MODALITIES} from "@/assets/scripts/pacs/modalities";

import {PatiemtOptions} from "@/assets/scripts/pacs/exammanagement/patient/mixins";

import {md5} from 'js-md5';
import {getDicts} from '@/api/system/dict/data'
import {INPUTTYPES} from "@/assets/scripts/pacs/Const";

import {fmt_exam_age,currDate} from "@/utils/common";

import {REPORTCHECKSHEETTYPE} from "@/assets/scripts/pacs/Const";

import {actions} from "@/assets/starter/globalactons";

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorkerSrc;

//定时刷新列表
let timer_getList = null;

class FileType {
  static get UPLOAD() {
    return 'UPLOAD';
  }

  static get HISTORY() {
    return 'HISTORY';
  }

  static get HISTORYNOMATCH() {
    return 'HISTORYNOMATCH';
  }

  static get values() {
    return [this.UPLOAD, this.HISTORY, this.HISTORYNOMATCH];
  }
}

export default {
  name: "OcrConfirm",
  extends: BaseGridModel,

  dicts: ["uis_exam_item"],

  components: {ReportConfirmAl, LinksignPopup, ReportFileUploader},

  mixins: [ExamDataScope, PatiemtOptions],

  props: {
    //定时刷新秒数
    refresh: {type: Number, default: 5},
    // sheetType: {type: String, default: REPORTCHECKSHEETTYPE.MU},
  },

  data() {
    return {
      FileType: FileType,
      // {文件url:patientInfo}
      fileMatchData: [],

      // 上传文件对应的文件数据 {{fileUrl, uploadTime}: file }
      uploadFileMap: new Map(),
      // 已导入文件对应的md5集合 {fileUrl,: md5 }
      uploadFileMd5Map: new Map(),
      // examUid对应的上传文件行数据 {examUid: [row]}
      examInfoUpFileRowMap: new Map(),
      // 历史文件对应的行数据 {{fileUrl, uploadTime, examUid}: row }
      historyFileRowMap: new Map(),
      // 历史文件对应的文件详细信息 {{fileUrl, uploadTime, examUid}: fileInfoDto }
      historyFileInfoMap: new Map(),

      // 历史文件对应的文件详细信息 {{fileUrl, uploadTime, examUid}: fileInfoDto }
      unmatchFileInfoMap: new Map(),

      loading: true,

      pdfDoc: null,
      numPdfPages: 0,

      extOperExportAsDoc: false,
      //
      qr: null,
      multipleSelection: [],
      examItemCodes: [],
      itemCode: [],
      // ocrExamConclusion:"",
      // ocrExamDiagnosis:"",
      cacheKey: "PatiemtOptions",


      total: 0,//总条数
      pagesize: 10,//指定展示多少条
      pageNum: 1,//当前页码
      useExamInfo: false,  // 是否使用传递的检查信息
      defaultExamInfo: null,      // 存储传递的检查信息
      overwriteFiles: true, //默认覆盖文件
      matchRowCount: 0,   //匹配的记录数
      currentTotalUpFiles: 0,
      currentSuccessUpFiles: 0,
      currentSkippedUpFiles: 0,

      hoveredRow: null, // 添加这一行用于跟踪当前悬停的行
      processingMessage: '',// 添加处理进度消息

      examInfoFromRoute: null,
      lastChangeByRoute: true,

      createTime_props: [{label: "今天",value:0},
        {label: "昨天",value:1},
        {label: "三天",value:3},
        {label: "七天",value:7},
        {label: "自定义时间",value:-1}],
      createTimeValue:0,
      startDate: '',
      patientMainForm: null,
      sheetType: REPORTCHECKSHEETTYPE.MU,
      createTimeGe: currDate(),
      createTimeLt: currDate()
    };
  },

  computed: {
    ...mapGetters(['examModality', 'loginUsrExamItem']),
    inputTypes() {
      return INPUTTYPES;
    },
  },

  mounted() {
    let vm = this;
    if(!!this.$route.query.sheetType){
      this.sheetType = this.$route.query.sheetType;
    }
    this.delayProcess();

    this.cacheKey = this.sheetType;
    this.mainForm = this.read();
    if(!!this.mainForm){
      this.examItemCodes = this.mainForm.ocrExamItemCodes;
      this.createTimeValue = this.mainForm.createTimeValue;
    }
    let codes = vm.examItemCodes || [];

    //校验界面没有选择项目
    if (0 === codes.length) {
      this.loginUsrExamItem.filter(item => {
        return !(item.extend && item.extend.extendI1 === 1);
      }).forEach(e => vm.itemCode.push(e.dictValue));
      codes = vm.itemCode;
    }

    if (0 === codes.length) {
      getDicts("uis_exam_item").then(res => {
        ExamDataScope.methods.applyDataCtrl(DataCtrlDict.ItemType.dict, res.data, true).then(res => {
          //console.log("getDicts-item:",res);
          res.forEach(e => vm.itemCode.push(e.dictValue));
          codes = vm.itemCode;
          vm.getPdfMsg(codes);
          if(this.isIFMSheetType()){
            this.processUnmatchFileInfos();
          }
          this.searchFormChange();
        });
      })
    } else {
      vm.getPdfMsg(codes);
      if(this.isIFMSheetType()){
        this.processUnmatchFileInfos();
      }
      this.searchFormChange();
    }

    if (this.examInfoFromRoute) {
      this.processFileInfos(this.examInfoFromRoute);
      this.lastChangeByRoute = true;
    }
    console.log("注册方法")
    actions.registerAction('ocr.select.time_range',this.setCreateTimeValue)
  },

  activated() {
    //this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  },

  created() {
    this.examInfoFromRoute = this.$route.params.examInfo;
    this.test = this.$route.params.test;
    console.log("examRowData from patient sheet.", this.examInfoFromRoute);
  },

  methods: {

    setCreateTimeValue(value){
      console.log('设置当前时间之',value)
      this.createTimeValue = value
      this.refreshTime(value)
    },
    getFileMatchData(){
      if(this.isIFMSheetType()){
        return this.fileMatchData;
      }else{
        return this.fileMatchData.slice((this.pageNum-1)*this.pagesize,this.pageNum*this.pagesize)
      }
    },
    // readMainForm(){
    //   this.mainForm = this.read();
    //   this.patientMainForm = this.mainForm;
    //   if (this.patientMainForm) {
    //     if (this.patientMainForm.examItemCodes) {
    //       this.searchFormSimp.examItemCodes = this.patientMainForm.examItemCodes;
    //     }

    //     if(this.patientMainForm.resultStatusValues){
    //       this.searchFormSimp.resultStatusValues = this.patientMainForm.resultStatusValues;
    //     }

    //     if (this.patientMainForm.createTimeValue !== undefined) {
    //       this.createTimeValue = this.patientMainForm.createTimeValue;
    //       this.searchFormChange(this.createTimeValue);
    //     }
    //   }
    // }
    //轮询
    isIFMSheetType(){
      return REPORTCHECKSHEETTYPE.IFM===this.sheetType;
    },
    delayProcess() {
      clearTimeout(timer_getList); // 确保清除之前的定时器
      if (!!this.refresh&&this.isIFMSheetType()) {
        timer_getList = setTimeout(() => {
          this.processUnmatchFileInfos("delay");
          this.delayProcess(); // 在完成一次查询后重新设置定时器
        }, this.refresh * 1000);
      }
    },

    refreshTime(value){
      const currentDate = new Date();
      const pastDate = new Date();
      pastDate.setDate(currentDate.getDate() - value);
      const startOfDay = new Date(pastDate.toLocaleDateString()).getTime();

      this.createTimeGe = new Date(startOfDay);
      this.createTimeLt = (value === 1) ? new Date(startOfDay) : currentDate;
    },

    searchFormChange(){
      this.mainForm.createTimeValue = this.createTimeValue;
      this.mainForm.ocrExamItemCodes = this.examItemCodes;
      this.save();

      this.showCustomDatePickers = (this.createTimeValue === -1);

      if (!this.showCustomDatePickers) {
          this.refreshTime(this.createTimeValue);
          if(this.isIFMSheetType()) this.processUnmatchFileInfos();
      }else{
        this.createTimeGe = null;
        this.createTimeLt = null;
      }
    },
    changeDatePicker(value){
      this.createTimeGe = value[0];
      this.createTimeLt = value[1];
      if(this.isIFMSheetType()) this.processUnmatchFileInfos();
    },
    // 处理每一行的选中状态变化
    /*handleSelectionChange(row) {
      if (row.selected) {
        // 如果选中，加入到选中的行数据中
        this.multipleSelection.push(row);
      } else {
        // 如果取消选中，从 `selectedRows` 中移除该行
        const index = this.multipleSelection.indexOf(row);
        if (index > -1) {
          this.multipleSelection.splice(index, 1);
        }
      }

      // 更新全选状态
      this.updateSelectAllState();
    },*/

    // 切换全选/取消全选
    /*toggleAllSelection() {
      if (this.isAllSelected) {
        // 全选：将所有行的 `selected` 设置为 `true`
        this.fileMatchData.forEach(row => row.selected = true);
        this.multipleSelection = [...this.fileMatchData]; // 更新选中的行
      } else {
        // 取消全选：将所有行的 `selected` 设置为 `false`
        this.fileMatchData.forEach(row => row.selected = false);
        this.multipleSelection = []; // 清空选中的行
      }

      // 强制更新全选复选框的状态
      this.$nextTick(() => {
        this.updateSelectAllState(); // 更新全选复选框的选中状态
      });
    },

    // 更新全选复选框的状态
    updateSelectAllState() {
      const selectedCount = this.multipleSelection.length;
      const totalCount = this.fileMatchData.length;

      // 如果选中的行数等于总行数，则全选复选框选中
      this.isAllSelected = selectedCount === totalCount;

      // 如果选中的行数大于 0 小于总行数，则为部分选中状态
      this.isIndeterminate = selectedCount > 0 && selectedCount < totalCount;
    },*/

    current_change(pageNum) {
      this.pageNum = pageNum;
      if(this.isIFMSheetType()){
        this.processUnmatchFileInfos();
      }
    },

    // examItemCodesChange(val) {
    //   this.mainForm.ocrExamItemCodes = val;
    //   this.save();
    // },

    loadReports(inputType) {
      this.$refs.ReportFileUploader.prepare(inputType);
    },

    getUploadFileKeyFromRow(row) {
      return this.getUploadFileKey(row.fileUrl, row.uploadTime)
    },

    getUploadFileKey(fileUrl, uploadTime) {
      return JSON.stringify({
        fileUrl: fileUrl,
        uploadTime: uploadTime,
      })
    },

    getHistoryFileKeyFromRow(rowData) {
      return this.getHistoryFileKey(rowData.fileUrl,
        rowData.uploadTime,
        rowData.examInfo ? rowData.examInfo.examUid : '');
    },

    getHistoryFileKey(fileUrl, uploadTime, examUid) {
      return JSON.stringify({
        fileUrl: fileUrl,
        uploadTime: uploadTime,
        examUid: examUid  //在同一个examUid的范围内覆盖
      });
    },

    addValueToMap(map, key, value) {
      if (map.has(key)) {
        // 如果键已存在，向对应数组中添加值
        map.get(key).push(value);
        return map.get(key).length;
      } else {
        // 如果键不存在，初始化为数组并添加值
        map.set(key, [value]);
        return 1;
      }
    },

    handleOverwriteChange(overwriteFile) {
      this.overwriteFiles = overwriteFile;
      for (let row in this.multipleSelection) {
        const historyFileKey = this.getHistoryFileKeyFromRow(row);
        if (this.historyFileRowMap.has(historyFileKey)) {
          this.historyFileRowMap.get(historyFileKey).status = overwriteFile ? 2 : 0;
        }
      }
    },

    checkSelectable(row) {
      return row.examInfo||row.fileType === FileType.HISTORYNOMATCH;
    },

    getTooltipContent(row) {
      let content = '';

      // 添加匹配信息
      if (row.ocrContents) {
        content += row.ocrContents;
      }

      return content;
    },

    getPreferredFileName(file) {
      return file.webkitRelativePath || file.name;
    },

    getReportFiles(data) {
      let vm = this;
      vm.loading = true;
      console.log("data", data);
      //初始化匹配结果表格状态
      //this.clearOcrConfirmTableStatus();
      this.currentTotalUpFiles = data.files.length;
      this.currentSuccessUpFiles = 0;
      this.currentSkippedUpFiles = 0;
      let currentUpFiles = [];

      let examItems = vm.examItemCodes;
      if (examItems.length === 0) {
        examItems = vm.itemCode;
      }
      // 创建一个队列来串行处理文件
      const processFiles = async () => {
        for (const file of data.files) {
          let preferredFileName = this.getPreferredFileName(file)
          if (!preferredFileName.toLowerCase().endsWith(".pdf")) {
            this.currentTotalUpFiles--;
            continue;
          }

          try {
            const arrayBuffer = await new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = e => resolve(e.target.result);
              reader.onerror = reject;
              reader.readAsArrayBuffer(file);
            });

            const md5Val = md5(arrayBuffer);
            const uploadFileKey = this.getUploadFileKey(preferredFileName, data.uploadTime);

            // 检查是否已存在相同的md5值 - 现在这个检查是线程安全的
            if (this.uploadFileMd5Map.has(file.name)
              && this.uploadFileMd5Map.get(file.name) === md5Val) {
              this.currentSkippedUpFiles++;
              continue;
            }

            let form = new FormData();
            form.append("fileInfo", JSON.stringify({
              fileName: file.name,
              fileMd5: md5Val,
            }));
            form.append("examItems", examItems);

            const r = await api.searchFileExist(form);
            if (r.data > 0) {
              this.currentSkippedUpFiles++;
              continue;
            }

            // 更新Map - 现在这个更新是线程安全的
            currentUpFiles.push(file);
            this.uploadFileMap.set(uploadFileKey, file);
            this.uploadFileMd5Map.set(file.name, md5Val);
          } catch (error) {
            console.error('Error processing file:', error);
            this.currentSkippedUpFiles++;
          }
        }

        // 所有文件处理完成后，执行OCR匹配
        if (currentUpFiles.length === 0) {
          vm.loading = false;
          return;
        }

        let currentProcessingIndex = 0; // 添加当前处理的文件索引
        for (const file of currentUpFiles) {
          currentProcessingIndex++;
          this.processingMessage = `正在处理第 ${currentProcessingIndex} 个文件，总共 ${currentUpFiles.length} 个文件`;
          let form = new FormData();
          form.append("fileNames", file.name)
          form.append("fileMd5s", this.uploadFileMd5Map.get(file.name))
          form.append("files", file);
          form.append("examItems", examItems);

          try {
            const res = await api.ocrMatch(form);
            let fileOCRResultMap = res.data;
            for (let [key, value] of Object.entries(fileOCRResultMap)) {
              let row;
              let filename = key;
              let ocrMatchResult = value;
              let matchList = ocrMatchResult.examInfos;
              if (matchList.length !== 1) {
                row = this.createErrorMatchNewRow(filename,
                  data.uploadTime,
                  matchList.length);
              } else {
                vm.processExamInfo(matchList);
                let currentExamInfo = matchList[0];
                row = this.createNewRowAndSelect(filename,
                  filename,
                  data.uploadTime,
                  currentExamInfo,
                  FileType.UPLOAD);
                this.addValueToMap(this.examInfoUpFileRowMap, currentExamInfo.examUid, row);
              }
              row.foundValMessage = ocrMatchResult.foundValueMsg;
              row.ocrContents = ocrMatchResult.ocrContents;
              row.fileLabel = ocrMatchResult.fileLabel;
              row.ocrMatchResult = ocrMatchResult;
              for (let examItem of this.ctrlData.dict.uis_exam_item) {
                if (examItem.value === ocrMatchResult.examItemCode) {
                  row.matchExamItem = examItem.label;
                  row.matchExamItemCode = examItem.value;
                  break;
                }
              }
            }
            this.postProcessForGetReportFiles()
            this.currentSuccessUpFiles++;
          } catch (e) {
            console.error('Error in ocr match:', e)
          }
        }
        vm.loading = false;
      };

      // 开始处理文件
      processFiles().catch(error => {
        console.error('Error in processFiles:', error);
        vm.loading = false;
      });
    },

    createNewRowAndSelect(fileUrl,
                          replacedInitFileName,
                          uploadTime,
                          examInfo,
                          fileType) {
      return this.createNewRow(fileUrl,
        replacedInitFileName,
        uploadTime,
        examInfo,
        null,
        1,
        true,
        fileType
      );
    },

    createErrorMatchNewRow(fileUrl,
                           uploadTime,
                           matchCount) {
      return this.createNewRow(fileUrl,
        fileUrl,
        uploadTime,
        null,
        null,
        matchCount,
        false,
        FileType.UPLOAD
      )
    },

    createNewRow(fileUrl,
                 replacedInitFileName,
                 uploadTime,
                 examInfo,
                 repeatMd5File,
                 matchCount,
                 selected,
                 fileType,
                 selectable = true) {
      let row = {
        id: this.matchRowCount++,
        fileUrl: fileUrl,
        replacedInitFileName: replacedInitFileName,
        uploadTime: uploadTime,
        examInfo: examInfo,
        //overwriteFile: false,
        repeatMd5File: repeatMd5File,
        matchCount: matchCount,
        status: selected ? 0 : 1, //0 正常，1 移除，2 覆盖
        fileType: fileType,
        selectable: selectable,
      };
      this.fileMatchData.push(row);
      //排序
      this.sortFileMatchData();
      if (selected) {
        this.$refs.mapTable.toggleRowSelection(row, true);
      }
      return row;
    },

    rowClassName(row) {
      let rowData = row.row
      let historyFileKey = this.getHistoryFileKeyFromRow(rowData);
      if (this.historyFileRowMap.has(historyFileKey)) {
        return 'history-row';
      }
      return '';
    },

    postProcessForGetReportFiles() {
      this.total = this.fileMatchData.length;
      this.pageNum = 1;
    },

    deleteHistoryMapByExamUid(examUid, historyMap) {
      for (const key of historyMap.keys()) {
        let fileKey = JSON.parse(key)
        if (fileKey.examUid === examUid) {
          historyMap.delete(key);
        }
      }
    },

    selectExam(fileMatchData) {
      const row = this.fileMatchData.find(e => e.fileUrl === fileMatchData.fileUrl && e.uploadTime === fileMatchData.uploadTime);
      if (row) {
        row.ocrMatchResult.manualMatch = true;
        let lastExamInfo = row.examInfo;
        row.examInfo = fileMatchData.patientInfo[0];
        row.matchCount = 1;
        row.status = 0;
        this.addValueToMap(this.examInfoUpFileRowMap, row.examInfo.examUid, row);
        this.$nextTick(() => {
          this.$refs.mapTable.toggleRowSelection(row, true);
        });
        //如果examUid下只有一个uploadFile则先清除之前的历史文件数据
        if (lastExamInfo) {
          let activeUpFileCount = this.examInfoUpFileRowMap.get(lastExamInfo.examUid).length;
          this.examInfoUpFileRowMap.set(
            lastExamInfo.examUid,
            this.examInfoUpFileRowMap.get(lastExamInfo.examUid)
              .filter(item => item.fileUrl !== row.fileUrl && item.uploadTime !== row.uploadTime)
          )
          if (activeUpFileCount === 1) {
            for (let i = 0; i < this.fileMatchData.length; i++) {
              let currentRow = this.fileMatchData[i]
              if (lastExamInfo.examUid === currentRow.examInfo.examUid) {
                this.fileMatchData.splice(i, 1);
                i--; // 删除元素后，数组长度减1，需要更新索引
              }
            }
            this.deleteHistoryMapByExamUid(lastExamInfo.examUid, this.historyFileRowMap);
            this.deleteHistoryMapByExamUid(lastExamInfo.examUid, this.historyFileInfoMap);
          }
        }
        this.processFileInfos(row.examInfo)
      }
    },

    changeMatch(row) {
      console.log("row:", row);
      this.$refs.reportConfirmAl.view(row.fileUrl, row.uploadTime);
    }
    ,
    cancelMatch(row) {
      this.fileMatchData.forEach(e => {
        if (e.fileUrl == row.fileUrl) {
          e.patientInfo = [];
        }
      });
      this.$refs.mapTable.toggleRowSelection(row, false);
    },
    getList: function (params) {
      let vm = this;

      // api.find(params).then(res => {

      // }).catch();
    }
    ,

    buildPdf(base64) {
      const dat = atob(base64);

      let promFun = () => {
        return pdfjsLib.getDocument({
          data: dat,
          cMapPacked: true,
          cMapUrl: "static/cmaps/",
        }).promise;//, cMapPacked: true
      }

      promFun().then(pdf => {
        this.extOperExportAsDoc = false;
        this.pdfDoc = pdf;
        this.numPdfPages = pdf.numPages;
        //生成第1页
        this.$nextTick(() => {
          this.renderPdfPage(1)
        })
      })
    }
    ,

    //生成指定PDF页
    renderPdfPage(num) {
      let vm = this;
      //
      this.pdfDoc.getPage(num).then(page => {
        //
        let canvas = vm.$refs.pdfView.querySelector(`div[data-page='report-previewer-page-${num}'] canvas`)
        let ctx = canvas.getContext("2d");
        ctx.mozImageSmoothingEnabled = false;
        ctx.webkitImageSmoothingEnabled = false;
        ctx.msImageSmoothingEnabled = false;
        ctx.imageSmoothingEnabled = false;
        //scale避免糊
        let viewport = page.getViewport({scale: 1.6});
        //let scale = Math.min(canvas.clientWidth / viewport.width, canvas.clientHeight / viewport.height);
        //viewport.scale = scale;
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        //viewport.width = canvas.clientWidth
        //viewport.height = canvas.clientHeight;
        let renderContext = {
          canvasContext: ctx,
          viewport: viewport,
          //transform: [scale, 0, 0, scale, 0, 0]
        };
        page.render(renderContext).promise.then(() => {
          //page.getTextContent()
          //继续直到最后1页
          if (this.numPdfPages > num) {
            this.renderPdfPage(num + 1)
          } else {
            if (this.extOperPrint) {
              this.plint();
            }
          }
        });
      });
    },


    /*base64ToBlob(base64String, contentType) {
      contentType = contentType || '';
      var sliceSize = 1024;
      var byteCharacters = atob(base64String);
      var byteArrays = [];

      for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        var slice = byteCharacters.slice(offset, offset + sliceSize);

        var byteNumbers = new Array(slice.length);
        for (var i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }

        var byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      return new Blob(byteArrays, {type: contentType});
    },

    // 使用Blob对象创建File对象
    blobToFile(blob, fileName) {
      var file = new File([blob], fileName, {type: blob.type});
      return file;
    },*/

    getPdfMsg(examItem) {
      let vm = this;
      if (undefined === this.$route.query.date) {
        vm.loading = false;
        return;
      }
      const moment = require('moment-timezone');
      const currentTime = moment.tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

      let path = this.$route.query.ip + '/' + this.$route.query.date + '/' + this.$route.query.filename;
      let filename = this.$route.query.filename;
      let request = new FormData();
      request.append("subPath", path);
      api.getVirtualPrinterFile(request).then(response => {
        console.log('通过后端获取虚拟打印机文件成功');
        let base64 = response.data;
        //base64 转file类型
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        // 创建Blob对象
        const blob = new Blob([byteArray], {type: 'text/plain'});

        let file = new File([blob], filename, {type: 'text/plain'});

        console.log('开始进行ocr匹配')
        vm.getReportFiles({
          files: [file],
          uploadTime: currentTime
        })
      })

      /*let form = new FormData();
      form.append("path", path);
      form.append("examItem", examItem);
      console.log("form", form);

      this.currentTotalUpFiles = 1;
      this.currentSuccessUpFiles = 0;
      this.currentSkippedUpFiles = 0;

      api.getPdfMsg(form).then(res => {
        console.log("getPdfMsg", res);
        for (let key in res.data) {

          vm.fileDate.set(key, res.data[key]);
          vm.buildPdf(res.data[key].fileBase64);
          // var blob = vm.base64ToBlob(res.data[key].fileBase64, 'application/pdf'); // 指定文件类型
          // var file = vm.blobToFile(blob, key); // 指定文件名
          // vm.fileDate.set(key,file);
          // vm.pdfShow(key);

          //base64 转file类型
          const byteCharacters = atob(res.data[key].fileBase64);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);

          // 创建Blob对象
          const blob = new Blob([byteArray], {type: 'text/plain'});

          let file = new File([blob], key, {type: 'text/plain'});
          vm.fileDate.set(key, file);
          vm.uploadFileMap.set(this.getUploadFileKey(key, currentTime), file)
          let reader = new FileReader();
          reader.onloadend = (e) => {
            let md5Val = md5(e.target.result);
            vm.uploadFileMd5Map.set(this.getUploadFileKey(key, currentTime), md5Val);
          }
          reader.readAsArrayBuffer(file)
        }

        for (let filename in res.msg) {
          let row;
          if (res.msg[filename].length !== 1) {
            row = this.createErrorMatchNewRow(filename,
              currentTime,
              res.msg[filename].length);
          } else {
            let currentExamInfo = res.msg[filename][0];
            row = this.createNewRowAndSelect(filename,
              currentTime,
              currentExamInfo);
          }
          row.foundValMessage = res.ocr_data[filename];
        }

        this.currentSuccessUpFiles = 1;
        vm.loading = false;

      }).catch();*/
    },

    pdfShow(row) {
      let vm = this;
      // if(vm.multipleUpload){
      let file;
      if (row.fileType === FileType.UPLOAD) {
        let fileKey = vm.getUploadFileKeyFromRow(row)
        file = vm.uploadFileMap.get(fileKey)
        const reader = new FileReader();
        reader.onload = function (event) {
          const base64String = event.target.result.split(',')[1];
          vm.buildPdf(base64String);
        };

        // 将文件以DataURL形式读入页面
        reader.readAsDataURL(file);
      } else if (row.fileType === FileType.HISTORY||row.fileType === FileType.HISTORYNOMATCH) {
        let request = new FormData();
        request.append("filePath", row.storagePath);
        api.getFile(request).then(response => {
          let base64 = response.data;
          vm.buildPdf(base64);
        })
      } else {
        throw Error("未知的文件类型，请联系管理员解决")
      }
      //const file = vm.fileDate.get(fileUrl)


      // }else{
      //   vm.buildPdf(vm.fileDate.get(fileUrl));
      // }
    },

    //年龄列
    colFmt_age(row) {
      if (undefined === row) return '';
      const pat = row;
      //const pat = row.patientInfo;
      return pat && pat.age ? (pat.age + (pat.ageUnit ? pat.ageUnit.dictLabel : '')) : (pat ? pat.age : '');
    },

    handleRowDblClick(row) {
      console.log("handleRowDblClick", row);
      this.pdfShow(row);
    },

    // 获取显示文本
    getOverwriteStatusText(row) {
      return row.status === 0 ? '' : '确认后覆盖';
    },

    isOverwriteFile(row) {
      const historyFileKey = this.getHistoryFileKeyFromRow(row);
      if (row.status !== 1
        && this.overwriteFiles
        && this.historyFileRowMap.has(historyFileKey)
        && this.examInfoUpFileRowMap.has(row.examInfo.examUid)) {
        let upRows = this.examInfoUpFileRowMap.get(row.examInfo.examUid);
        let htRow = this.historyFileRowMap.get(historyFileKey);
        for (let upRow of upRows) {
          if (upRow.status === 0 && upRow.fileUrl === htRow.fileUrl) {
            return true;
          }
        }
      }
      return false;
    },

    handleSelectionChange(selection) {
      // 比较当前选择和之前的选择
      const added = selection.filter(item => !this.multipleSelection.includes(item));
      const removed = this.multipleSelection.filter(item => !selection.includes(item));

      if (added) {
        console.log('Selected:', added); // 记录用户选择的行
        added.forEach(row => {
          row.status = 0;
          if (this.isOverwriteFile(row)) {
            row.status = 2;
          }
        })
      }
      if (removed) {
        console.log('Deselected:', removed); // 记录用户取消选择的行
        removed.forEach(row => {
          row.status = 1; //测试看是否会触发全表更新
        })
      }

      this.multipleSelection = [...selection];
      this.sortFileMatchData();
      this.lastChangeByRoute = false;
      console.log("handleSelectionChange:", this.multipleSelection, this.fileMatchData);
    },

    test() {
      this.fileMatchData.forEach(e => {
        //this.multipleSelection.push(e);
        //this.$refs.mapTable.toggleRowSelection(e, true);
      });
    },

    turnToWritePage(item) {
      let toRoute = "MTReportWriting";
      if (!!item.examModality && RAD_MODALITIES.includes(item.examModality.dictValue)) {
        toRoute = "RadReportWriting";
      } else if (!!item.examModality && DS_MODALITIES.includes(item.examModality.dictValue)) {
        toRoute = "dsReportWriting";
      } else if (!!item.examModality && OTOL_MODALITIES.includes(item.examModality.dictValue)) {
        toRoute = "MTReportWriting";
      } else if (!!item.examModality && TCD_MODALITIES.includes(item.examModality.dictValue)) {
        toRoute = "ReportWriting";
      }
      this.$router.push({name: toRoute, params: {activeTab: "PatientAuditSheet", examInfo: item, sendPicture: true}});
    },

    clearOcrConfirmTableStatus() {
      this.$refs.mapTable.clearSelection();
      this.fileMatchData = [];
      this.postProcessForGetReportFiles();
      this.multipleSelection = [];
      this.uploadFileMap.clear();
      this.uploadFileMd5Map.clear();
      this.historyFileRowMap.clear();
      this.unmatchFileInfoMap.clear();
      this.matchRowCount = 0;
      this.currentTotalUpFiles = 0;
      this.currentSuccessUpFiles = 0;
      this.currentSkippedUpFiles = 0;
    },

    clear(){
      let vm = this;
      let newForm = new FormData();
      const fileInfoArray = vm.fileMatchData
        .filter(item => {
          // 跳过被移除的待上传文件
          return item.status === 0
        })
        .map((item, index) => {
          let uploadFileKey = vm.getUploadFileKey(item.fileUrl, item.uploadTime);
          let fileInfo = vm.unmatchFileInfoMap.get(uploadFileKey);
          // 添加文件
          newForm.append(`ids`, fileInfo.id);
        });
        api.deleteFilesByIds(newForm).then(res=>{
          vm.clearOcrConfirmTableStatus();
          vm.processUnmatchFileInfos();
        });
    },

    uploadFolder(data) {
      let vm = this;
      let fun;
      fun = api.uploadOCRReports;

      fun(data).then(res => {
        /*if ('qrnoauth' === res.errC) {
          if (null == vm.qr) {
            vm.qr = new QRSignRel(() => {
              vm.qr.activated = false;
              ///opt.firmed = true;
              cup(data);
            });
          }
          vm.qr.activated = true;
          vm.qr.refreshQrcode();
          vm.loading = false;
          return;
        }*/

        vm.$modal.msgSuccess(res.msg);

        vm.processing = false;
        vm.triggerBind("success");
        vm.clearOcrConfirmTableStatus();
        vm.loading = false;
        vm.$modal.msgSuccess("操作完成。");
        let exam = data.get("routeExamInfo");
        exam = JSON.parse(exam);
        /*let exams = data.getAll('examInfos');
        if (exams.length === 1) {
          exam = JSON.parse(exams[0]);
        }*/
        vm.turnToWritePage(exam);
        vm.close();
      }).catch(err => vm.processing = false);
    },

    getOriginFileName(url) {
      return url.replace(/^.*[\\/]/, "");
    },

    confirm() {
      let vm = this;
      vm.loading = true;

      let newForm = new FormData();
      newForm.append('mergeStrategy', 'APPEND')
      let isFirstUpload = false;
      const fileInfoArray = vm.fileMatchData
        .filter(item => {
          // 跳过被移除的待上传文件
          return !((item.fileType === FileType.UPLOAD && item.status === 1) ||
          ((item.fileType === FileType.HISTORYNOMATCH && item.status === 1)|| !item.examInfo));
        })
        .map((item, index) => {
          //newForm.append('examInfos', JSON.stringify(item.examInfo));
          const file = vm.uploadFileMap.get(this.getUploadFileKeyFromRow(item));
          // 添加文件
          newForm.append(`files`, file);

          // 返回文件信息对象
          let data;

          if (item.fileType === FileType.HISTORYNOMATCH) {
            if (!isFirstUpload) {
              isFirstUpload = true;
              newForm.append("routeExamInfo", JSON.stringify(item.examInfo));
            }
            let uploadFileKey = vm.getUploadFileKey(item.fileUrl, item.uploadTime);
            data = vm.unmatchFileInfoMap.get(uploadFileKey);
            data.status = item.status;
            data.fileType = FileType.HISTORYNOMATCH;
            data.examUid = item.examInfo.examUid;
            data.examItemCode = item.examInfo.examItem.dictValue;
          } else if (item.fileType === FileType.UPLOAD) {
            if (!isFirstUpload) {
              isFirstUpload = true;
              newForm.append("routeExamInfo", JSON.stringify(item.examInfo));
            }
            data = {
              examUid: item.examInfo.examUid,
              examItemCode: item.examInfo.examItem.dictValue,
              fileName: item.fileUrl,
              fileMd5: vm.uploadFileMd5Map.get(file.name),
              fileSize: file.size,
              uploadTime: item.uploadTime,
              fileType: item.fileType,
              fileLabel: item.fileLabel,
              ocrMatchResult: item.ocrMatchResult,
              status: item.status
            }
          } else if (item.fileType === FileType.HISTORY) {
            if (!isFirstUpload) {
              newForm.append("routeExamInfo", JSON.stringify(item.examInfo));
            }
            let historyFileKey = this.getHistoryFileKeyFromRow(item);
            data = this.historyFileInfoMap.get(historyFileKey);
            data.status = item.status;
            data.fileType = FileType.HISTORY
          } else {
            throw new Error("未知的文件类型，请联系管理员处理")
          }
          return data;
        });

      if (fileInfoArray && fileInfoArray.length > 0) {
        // 添加文件信息数组
        newForm.append('fileInfos', JSON.stringify(fileInfoArray));
        vm.uploadFolder(newForm);
      } else {
        vm.processing = false;
        vm.triggerBind("success");
        vm.clearOcrConfirmTableStatus();
        vm.loading = false;
        vm.$modal.msgSuccess("操作完成。");
      }
    },

    // 添加排序方法
    sortFileMatchData() {
      this.fileMatchData.sort((a, b) => {
        //按匹配结果排序
        const aMatchCount = a.matchCount;
        const bMatchCount = b.matchCount;
        const weight = (x) => (x === 1 ? 0 : x > 1 ? 1 : 2);
        if (aMatchCount !== bMatchCount) {
          return weight(aMatchCount) - weight(bMatchCount);
        }

        // 按登记号排序
        const registNoA = a.examInfo ? a.examInfo.patientInfo.registNo || '' : '';
        const registNoB = b.examInfo ? b.examInfo.patientInfo.registNo || '' : '';
        if (registNoA !== registNoB) {
          return registNoA.localeCompare(registNoB);
        }

        // 按检查流水号排序
        const serialNoA = a.examInfo ? a.examInfo.examSerialNo || '' : '';
        const serialNoB = b.examInfo ? b.examInfo.examSerialNo || '' : '';

        if (serialNoA !== serialNoB) {
          return serialNoA.localeCompare(serialNoB);
        }

        // 按文件名排序
        const fileNameA = a.replacedInitFileName;
        const fileNameB = b.replacedInitFileName;
        if (fileNameA !== fileNameB) {
          return fileNameA.localeCompare(fileNameB);
        }
      });
    },

    // 处理患者检查文件信息并展开
    processExamInfo(examInfoArray) {
      if (!examInfoArray || !Array.isArray(examInfoArray)) {
        return [];
      }

      let expandedData = [];
      examInfoArray.forEach(examInfo => {
        if (examInfo.fileInfos) {
          try {
            if (examInfo.fileInfos instanceof Array) {
              // 遍历解析后的文件信息
              this.processFileInfos(examInfo);
            }
          } catch (e) {
            console.error('解析历史fileInfo失败:', e);
          }
        }
      });
      return expandedData;
    },

    processFileInfos(examInfo) {
      if (examInfo.fileInfos && examInfo.fileInfos.length > 0) {
        examInfo.fileInfos.forEach((fileInfo) => {
          const historyFileKey = this.getHistoryFileKey(fileInfo.fileName, fileInfo.uploadTime, fileInfo.examUid);
          let historyRow = this.historyFileRowMap.get(historyFileKey);
          if (!historyRow) {
            this.historyFileInfoMap.set(historyFileKey, fileInfo);
            historyRow = this.createNewRowAndSelect(fileInfo.fileName, fileInfo.replacedInitFileName,
              fileInfo.uploadTime, examInfo, FileType.HISTORY);
            historyRow.storagePath = fileInfo.filePath;
            this.historyFileRowMap.set(historyFileKey, historyRow);
          }
          if (this.isOverwriteFile(historyRow)) {
            historyRow.status = 2;
          }
        })
      }
    },

    /*getUploadFileKeyFromRow(row) {
      return this.getUploadFileKey(row.fileUrl, row.uploadTime)
    },

    getUploadFileKey(fileUrl, uploadTime) {
      return JSON.stringify({
        fileUrl: fileUrl,
        uploadTime: uploadTime,
      })
    },*/

    formatDate(date) {
      if(!date) return null;
      const year = date.getFullYear(); // 获取年份
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，注意月份是从 0 开始的
      const day = String(date.getDate()).padStart(2, '0'); // 获取日期
      return `${year}-${month}-${day}`;
  },

    processUnmatchFileInfos(opts) {
      let vm = this;
      let form = new FormData();
      let examItems = vm.examItemCodes;
      if (examItems.length === 0) {
        examItems = vm.itemCode;
      }
      if(0==examItems.length||!examItems){
        return;
      }
      form.append("examItemCodes", examItems);
      form.append("createTimeGe", this.formatDate(this.createTimeGe));
      let vDate = new Date(this.createTimeLt);
      let veDate = new Date();
      veDate.setDate(vDate.getDate() + 1);
      const ltDay = new Date(veDate.toLocaleDateString()).getTime();
      form.append("createTimeLt", veDate);
      form.append("pageSize", this.pagesize);
      form.append("pageNum", this.pageNum);

      //点击按钮触发
      if (opts && (opts instanceof MouseEvent)) {
        vm.loading = true;
      }

      api.getUnmatchFileInfos(form).then(res => {
        for(let i = vm.fileMatchData.length - 1; i >= 0; i--) {
          const item = vm.fileMatchData[i];
          const itemKey = vm.getUploadFileKey(item.fileUrl, item.uploadTime);
          if(item.fileType === FileType.HISTORYNOMATCH&&null==item.examInfo) {
            vm.fileMatchData.splice(i, 1);
            vm.unmatchFileInfoMap.delete(itemKey);
          }
        }
        const resKeys = [];
        res.rows.forEach((fileInfo) => {
            const unmatchFileKey = vm.getUploadFileKey(fileInfo.fileName, fileInfo.uploadTime);
            resKeys.push(unmatchFileKey);
            let unmatchFileRow = vm.unmatchFileInfoMap.get(unmatchFileKey);
            if(!unmatchFileRow){
              unmatchFileRow = vm.createNewRow(fileInfo.fileName,fileInfo.replacedInitFileName, fileInfo.uploadTime, null,fileInfo.fileMd5,fileInfo.ocrMatchResult?fileInfo.ocrMatchResult.examInfos.length:0,false, FileType.HISTORYNOMATCH);
              unmatchFileRow.storagePath = fileInfo.filePath;
              if(!!fileInfo.ocrMatchResult){
                unmatchFileRow.foundValMessage = fileInfo.ocrMatchResult.foundValueMsg;
                unmatchFileRow.ocrContents = fileInfo.ocrMatchResult.ocrContents;
                unmatchFileRow.fileLabel = fileInfo.ocrMatchResult.fileLabel;
                for (let examItem of vm.dict.type.uis_exam_item) {
                  if (examItem.value === fileInfo.ocrMatchResult.examItemCode) {
                    unmatchFileRow.matchExamItem = examItem.label;
                    unmatchFileRow.matchExamItemCode = examItem.value;
                    break;
                  }
                }
              }
              unmatchFileRow.ocrMatchResult = fileInfo.ocrMatchResult;
              vm.unmatchFileInfoMap.set(unmatchFileKey, fileInfo);


              // if(flag === "auto"){
              //   this.$notify({
              //     title: '警告',
              //     message: '检测到有未匹配的历史文件: ' + fileInfo.fileName,
              //     type: 'warning'
              //   });
              // }
            }
        })

        // for(let i = vm.fileMatchData.length - 1; i >= 0; i--) {
        //   const item = vm.fileMatchData[i];
        //   const itemKey = vm.getUploadFileKey(item.fileUrl, item.uploadTime);
        //   if(!resKeys.includes(itemKey)&&item.fileType === FileType.HISTORYNOMATCH) {
        //     vm.fileMatchData.splice(i, 1);
        //     vm.unmatchFileInfoMap.delete(itemKey);
        //   }
        // }

        vm.loading = false;
        vm.$store.commit("SET_UNMATCHFILENUM", vm.fileMatchData.length);
        vm.total = res.total;
      }).catch(err => {vm.loading = false;});
    },
    //年龄问题，以检查日期和出生日期计算
    colFmt_exam_age(row, column, cellValue, index) {
      return fmt_exam_age(row.examInfo, column, cellValue, index);
    },
  },

  watch: {
    // 监听整个路由对象
    $route: {
      handler(to, from) {
        // 判断是否是跳转到当前页面
        if (to.name === 'OcrConfirm') {
          // 获取传递的参数
          if (to.params && to.params.examInfo) {
            if (this.fileMatchData.length > 0 && !this.lastChangeByRoute) {
              this.$confirm(`发现有未确认的报告匹配操作，是否清空页面<br/>并查看 <span style="color: red; font-weight: bold;">${to.params.examInfo.patientInfo.name}</span> 的报告`,
              '提示', {
                confirmButtonText: '清空',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }).then(() => {
                this.examInfoFromRoute = to.params.examInfo;
                this.clearOcrConfirmTableStatus();
                this.processFileInfos(this.examInfoFromRoute);
                this.lastChangeByRoute = true;
              }).catch(() => {
                // 用户点击取消
                //this.$router.push(from)
              });
            } else {
              this.examInfoFromRoute = to.params.examInfo;
              this.clearOcrConfirmTableStatus();
              this.processFileInfos(this.examInfoFromRoute);
              this.lastChangeByRoute = true;
            }
          }
        }
      }
    },
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },
  }
};
