import auth from '@/plugins/auth'
//
import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import {transfer as transferImage, backport as backportImage} from "@/assets/scripts/pacs/image/api.imageTranster"
import * as mh from "@/assets/scripts/mine-helper";
import ReportImageUploader from "@/views/uis/report/comp/ReportImageUploader";
//
const TransferActions = {
  all: "all",
  multi: "batch"
};

export const ImageTransfer = {
  components: {ReportImageUploader},

  data() {
    return {
      imageContextmenuItems: [{cmd: 'exam-image::transfer', name: '转移'},{cmd: 'exam-image::export', name: '导出'}],
      reportForm: {},
      //
      transferAction: null,

      transferImages: []
    }
  },

  methods: {

    showImageContextmenu(evt) {
      evt.preventDefault();
      //权限判断
      if(!auth.hasPermi('exam-report:write')) {
        return;
      }

      const ele = evt.currentTarget || evt.target;
      const idx = ele.dataset['index'], img = this.imagesSet[parseInt(idx)];
      if(!img) {
        console.warn("index=%d, image=%o", idx, img);
        return;
      }
      this.$refs.imageContextmenu.show(evt, img);
    },

    handleImageContextMenu(item, data) {
      if(!data) {
        return;
      }
      //
      if(!matchAnyStatus(this.reportForm, StatusDict.exam, StatusDict.report)) {
        this.$modal.alert("当前检查信息检查进度应为\"已检查\"或\"已报告\"。");
        return;
      }
      //是否选做报告图像
      let prom = data.selected? this.$modal.confirm(`该图像已选中为报告的图像，是否${item.name}？`) : Promise.resolve(1);
      //
      prom.then(r => {
        const images = [data];
        switch(item.cmd) {
          case 'exam-image::transfer':
            this.toPickExam(images);
            break;
          case 'exam-image::export':
            this.handleBackportImage(images);
            break;
          }
      });
    },
    //转移
    handleTransferImage(images, examInfoTo) {
      if(!images || !images.length || !examInfoTo || !examInfoTo.id) {
        this.$modal.alert("请选择图像和检查。");
        return;
      }

      if(this.reportForm.id === examInfoTo.id) {
        this.$modal.alert("选择的检查和当前检查为同一个检查，请重新选择。").then(() => {
            this.toPickExam(images);
        });
        return;
      }
      const rep = this.reportForm;
      const imagesId = images.map(ima => ima.id);
      transferImage(imagesId, rep.id, examInfoTo.id).then(res => {
        const numTransfered = res.numAffected;
        const msg = !isNaN(numTransfered)? `转换到 ${examInfoTo.patientInfo.name} ${numTransfered}张图像` : "转移完成。";
        this.$modal.msgSuccess(msg);
        this.afterTransferImage();
      });
    },
    //导出
    handleBackportImage(images) {
      if(!images || !images.length) {
        this.$modal.alert("请选择图像。");
        return;
      }

      const rep = this.reportForm;
      const imagesId = images.map(ima => ima.id);
      backportImage(imagesId, rep.id).then(r => {
        mh.download(r);
      });
    },

    //toPickExam(image) {},

    //afterTransferImage() {},
    //
    handleResbackImage() {
      const rep = this.reportForm;
      if(!rep || !rep.id) {
        this.$modal.alert("请选择报告。");
        return;
      }
      this.$refs.reportImageUploader.prepare(rep);
    },
    //转移动作：多选/全部
    setTransferAction(act) {
      this.transferAction = act;
      //选择的动作
      switch(act) {
      case TransferActions.all:
        const imagesSet = this.imagesSet, transferImages = this.transferImages;
        if(!imagesSet || imagesSet.length === 0) {
          this.$modal.alert("该检查/报告没有图像。");
          return;
        }
        imagesSet.forEach(img => {
          if(-1 === transferImages.findIndex(timg => timg.id === img.id)) {
            //未选做报告的图像
            if(!img.selected) {
              img.transferSelected = true;
              transferImages.push(img);
            }
          }
        });
        this.batchTransfer();
        break;
      default:
        this.transferImages.forEach(img => img.transferSelected = false);
        this.transferImages.length = 0;
      }
    },
    //批量/全部转移
    batchTransfer() {
      const images = this.transferImages;
      if(!images || images.length === 0) {
        this.$modal.alert("请选择转移的图像。");
        return;
      }
      let pro = images.filter(img => img.selected).length > 0? this.$modal.confirm("所选图像含已选中为报告的图像，是否转移？") : Promise.resolve(1);
      pro.then(r => {
        this.toPickExam(images);
      });
    },

    //选择转移图像
    selectTransferImage(state, item) {
      let imgId = item.sopInstanceUid, selectedImages = this.transferImages;
      const idx = selectedImages.findIndex(si => si.sopInstanceUid === imgId);
      if(!state && -1 !== idx) {
        selectedImages.splice(idx, 1);
      } else if(state && -1 === idx) {
        selectedImages.push(item);
      }
    },
    //选择影像转入的检查
    toPickExam(image) {
      this.$refs.examPicker.show(image);
    },
    //影像转移成功后调用
    afterTransferImage() {
      this.loadReportImages();
    },
    //导入后执行
    afterResbackImage() {
      //登记->已检查
      const rep = this.reportForm;
      if(rep.resultStatus && matchAnyStatus(rep, StatusDict.regist)) {
        rep.resultStatus.dictValue = StatusDict.exam;
      }
      //
      this.loadReportImages();
    }
  },

  computed: {
    transferActive() {
      return TransferActions.all === this.transferAction || TransferActions.multi === this.transferAction;
    }
  }
};