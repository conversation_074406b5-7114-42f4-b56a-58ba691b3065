import Cookies from 'js-cookie'
import {cloneDeep} from "lodash";

import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//默认/初始配置
const SearchOptionsDef = {
  resultStatusValues: [StatusDict.exam, StatusDict.report, StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive],
  //examDoctorUserNames: [],
  datesCreated: 0,
  datesExamed: 1,
  reportDoctorUserNames: []
};
//export {SearchOptionsDef};
//
export const SearchOptions = {
  data() {
    return {
      mainForm: cloneDeep(SearchOptionsDef)
    }    
  },

  methods: {
    //取
    read(cacheKey) {
      cacheKey = cacheKey || this.cacheKey;
      //console.log(cacheKey);
      if(!cacheKey) {
        return null;
      }
      let cache = Cookies.get(cacheKey);
      //console.log(cache);
      if(cache) {
        let val = JSON.parse(cache);
        if(!!val.reportDoctor) {
          delete val.reportDoctor;
        }
        if(!!val.examDoctorUserNames) {
          delete val.examDoctorUserNames;
        }
        return val;
      }
      return cloneDeep(SearchOptionsDef);
    },
    //存
    save() {
      const cacheKey = this.cacheKey;
      //console.log(cacheKey);
      if(!cacheKey) {
        return null;
      }
      Cookies.set(cacheKey, JSON.stringify(this.mainForm), { expires: 7 });
    }
  }
}