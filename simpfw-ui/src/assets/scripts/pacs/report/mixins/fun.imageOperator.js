export const ImageOperator = {
  data() {
    return {
      millisLastWheel: 0
    };
  },

  methods: {
    scrollImageByWheel(evt) {
      const millis = new Date().getTime();
      if(millis - this.millisLastWheel < 160) { return; }
      this.millisLastWheel = millis;
      
      const backward = evt.deltaY > 0;
      //const e = imageScrollView.target, scrollStep = 147, scrollLeft = ;

      this.scrollImage(backward? 1 : -1);
    }
  }
};