//报告状态
import {StatusDict, matchAny as matchAnyResultStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//
const OPER_EXPORTASDOC = "exportAsDoc", OPER_PRINT = "print";

export const PageFitFun = {
  data() {
    return {
      options: null,

      pageFitted: false,

      dialogRefVal: "reportViewerDialog"
    };
  },

  methods: {
    //页面大小
    fitPage() {
      const de = document.body, maxWidth = de.clientWidth - (16 * 2), maxHeight = de.clientHeight - (56 + 8);
      const pageViews = this.dialogRef.querySelectorAll(".report-previewer-wrap"), pageView = pageViews[0];
      const pageViewWidth = pageView.offsetWidth;
      //填满水平、垂直缩放比例
      const verzoom = 1.0 * maxWidth / pageViewWidth, horZoom = 1.0 * maxHeight / pageView.offsetHeight;
      //防止撑破，取较小
      const zoom = Math.min(verzoom, horZoom);
      //console.log("预览缩放 %f", zoom);
      pageViews.forEach((p, i) => {
        //p.style.zoom = zoom;
        p.style.transform = `scale(${zoom})`;
        //
        let originX = "left", originY = "top";
        if(zoom > 1) {
          originX = (pageViewWidth / 2) + "px";// * zoom
        }
        //第二页
        //if(1 === i) {
        //  originY = (pageViewHeight / 2) + "px";
        //}
        p.style.transformOrigin = `${originX} ${originY}`;
      });
      //
      this.pageFitted = true;
    },
    //实际大小
    actualSize() {
      const pageViews = this.dialogRef.querySelectorAll(".report-previewer-wrap");
      pageViews.forEach(p => {
        //p.style.zoom = 1;
        p.style.transform = "scale(1)";//
        p.style.transformOrigin = "left top";//
      });
      //
      this.pageFitted = false;
    },
    //从css获取scale
    scaleValue(transcale) {
      return parseFloat(transcale.replace(/scale\s*\(\s*([\d.]+)\s*\)/, "$1"));
    }
  },

  computed: {
    //导出为模板
    /*extOperExportAsDoc() {
      const opt = this.options;
      return !!opt && opt.includes(OPER_EXPORTASDOC);
    },*/
    //打开即打印
    extOperPrint() {
      const opt = this.options;
      return !!opt && opt.includes(OPER_PRINT);
    },
    //是否可审核
    auditable() {
      const report = this.report;
      return !!report && matchAnyResultStatus(report, StatusDict.report);
    },
    //
    dialogRef() {
      return this.$refs[this.dialogRefVal].$el;
    }
  }
}