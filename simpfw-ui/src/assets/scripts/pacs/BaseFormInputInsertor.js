import "@/assets/styles/pacs/BaseFormInputInsertor.css";

//import {triggerBind} from "@/utils/common";

const eventNameListened = "focus";

let model = {
  
  props: {
    targets: {type: Array}
  },

  data() {

    return {
      dataset: [],
      //
      lastFocusElement: null,
      //
      targetsListerned: false
    };
    
  },
  created() {
    this.loadData();
  },
  methods: {
    //子组件实现
    loadData() { },
    //点击条目后执行
    onSelect(words, evt) {
      //console.log(evt);
      //const sel = window.getSelection(), nod = sel && sel.anchorNode;
      //console.log(nod);
      //const fel = nod && nod.querySelector && nod.querySelector(".el-textarea__inner") || null;
      const fel = this.lastFocusElement;
      if(!fel) {
        this.$modal.msg("请将鼠标点击在需插入关键短语位置。");
        return;
      }

      this.modifyContent(fel, words);
    },
    //修改指定元素内容
    modifyContent(fel, words, notifier) {
      let felnam = fel.name, felval = fel.value;
      if(!felnam) {
        this.$modal.msg("不支持使用该功能。");
        return ;
      }
      if(!words) {
        return ;
      }
      //console.log(felval);
      const wordsLen = words.length;
      let poss = fel.selectionStart, pose;
      if(poss || 0 === poss) {
        pose = fel.selectionEnd;
        felval = felval.substring(0, poss) + words + felval.substring(pose);
        //poss += wordsLen;
        //pose += wordsLen;
        //光标处插入/替换
        if(pose === poss) {
          pose = poss = poss + wordsLen;
        } else {
          pose = poss + wordsLen;
        }
      } else {
        felval = words;
        poss = pose = wordsLen;
      }
      //
      if("function" === (typeof notifier)) {
        notifier(felnam, felval);
      } else {
        this.triggerBind("change", felnam, felval);
      }
      //光标定位到短语插入位置后 this.$nextTick
      const restoreCursor = () => {
        fel.focus();
        fel.selectionStart = poss;
        fel.selectionEnd = pose;
      };
       
      this.$nextTick(restoreCursor);
      //setTimeout(restoreCursor, 1000);
    },

    //获取焦点/待插入条目的元素
    setLastFocusElement(evt) {
      //console.log(evt);
      evt.stopPropagation();
      this.lastFocusElement = evt.targetElement || evt.srcElement;
    },
    //支持的元素，获取焦点事件
    setup() {
      const eles = this.targetsElement;
      //console.log(eles);
      if(!eles || !eles.length) {
        return;
      }
      //
      const evtn = eventNameListened, fx = this.setLastFocusElement;
      eles.forEach(e => {
        e.removeEventListener(evtn, fx);
        e.addEventListener(evtn, fx, false);
      });
      //防止
      this.targetsListerned = true;
    },

    clean() {
      this.setLastFocusElement = null;
      const eles = this.targetsElement;
      if(eles) {
        eles.forEach(e => {
          e.removeEventListener(eventNameListened, this.setLastFocusElement);
        });
      }
    }
  },
  //
  mounted() {
    //console.log("mounted");
    this.setup();
  },
  deactivated() {
    //console.log("deactivated");
    this.clean();
  },
  //销毁
  beforeDestroy() {
    //console.log("beforeDestroy");
    this.clean();
  },
  //
  computed: {
    //支持的元素
    targetsElement() {
      const targets = this.targets;
      if(!targets || !targets.length) {
        return null;
      }
      const sel = targets.map(t => ("textarea[name=" + t + "]")).join(",");
      return document.querySelectorAll(sel);
    }
  },
  //
  deactivated() {
    //console.log("deactivated");
    this.lastFocusElement = null;
  }

  /*watch: {
    targets(nval, oval) {
      if(nval && nval.length && !nval.equalsStrict(oval) && !this.targetsListerned) {//
        this.targetsListerned = false;
        this.setup();
      }
    }
  }*/
};

export default model;
