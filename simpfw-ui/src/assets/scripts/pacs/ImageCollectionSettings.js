const model = {

  data() {
    return {
      opened: false,

      form: {
        combo_deviceTypes: []
        
        , deviceType: null
      }
    };
  },

  methods: {
    submitForm() {
      const vm = this;

      vm.close();
    },

    open() {
      this.opened = true;
    },

    close() {
      this.opened = false;
    }
  },

  /*watch: {
    settingsOpen(newVal, oldVal) {
      this.opened = newVal;
    }
  }*/
};

export default model;