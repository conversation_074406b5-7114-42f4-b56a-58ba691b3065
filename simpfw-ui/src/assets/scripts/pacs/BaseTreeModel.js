import {DBLCLICK_TIMEOUT, DBLCLICK_INTERVAL, currDatetime} from "@/utils/common";//, triggerBind

const props = {
  children: "children",
  label: "label"
};

let model = {
  data() {
    return {
      tree: {
        // 树选项
        data: undefined,
        current: undefined,
        props: props
      },
      //
      nodeClickEvent: {
        conflict: false //点击事件冲突：绑定单击双击
        , timer: null
        , data: null
        , millis: null
        , lastData: null
        , lastMillis: null
      }
    };
  },
  created() {
    this.buildTree();
    //
    this.nodeClickEvent.conflict = "function" == (typeof this.$listeners["nodeDblclick"]);
  },
  methods: {

    buildTree() {
      console.warn("无节点数据源。");
    },

    handleNodeClick(node) {
      try {
        let clickEvent = this.nodeClickEvent;
        //是否绑定节点双击事件
        if(!clickEvent.conflict) {
          clickEvent.data = node;
          this.judgeClickEvent();
          return;
        }

        clearTimeout(clickEvent.timer);
        //上一次点击信息
        clickEvent.lastMillis = clickEvent.millis;
        clickEvent.lastData = clickEvent.data;
        //当次点击信息
        clickEvent.data = node;
        clickEvent.millis = currDatetime().getTime();
        //
        //this.tree.current = node;
        //this.triggerBind("onChangeArea", node);
        //
        clickEvent.timer = setTimeout(this.judgeClickEvent, DBLCLICK_TIMEOUT);
      } catch (err) { console.error(err); }
    },
    //识别单双击
    judgeClickEvent() {
      const {lastData, lastMillis, data, millis} = this.nodeClickEvent;
      //
      //同一个节点两次点击间隔小于设定时间
      const isDbl = (lastData && lastData.id === data.id) 
        && lastMillis && (DBLCLICK_INTERVAL >= (millis - lastMillis));
      //
      this.tree.current = data;
      if(isDbl && 1 === this.triggerBind("nodeDblclick", data)) {
        //console.log("dblclick");
        return ;
      }
      //
      if(1 === this.triggerBind("nodeClick", data)) {
        return;
      }
      //console.log("click");
      this.triggerBind("onChangeArea", data);
    },

    current() {
      return this.tree.current;
    }
  }
};

export default model;

export {props};
