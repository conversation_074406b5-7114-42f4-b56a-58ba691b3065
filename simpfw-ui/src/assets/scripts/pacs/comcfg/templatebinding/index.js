import deepmerge from "deepmerge";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import { find as findTemplate } from "@/assets/scripts/pacs/tplcfg/template/api";

import * as api from "@/assets/scripts/pacs/comcfg/templatebinding/api";

let model = {
  name: "TemplateBinding",
  extends: BaseGridModel,
  dicts: ['uis_exam_item', 'uis_template_type'],
  components: { },
  data() {
    let dat = {
      queryForm: { }

      , editForm: {
        id: null
        , examItem: {}
        , templateType: {}
        , imageQuantity: null
        , template: {}
        , preferred: null
        , status: null

        , combo_template: []
      }
    };
    
    return dat;
  },

  created() {
    this.findTemplate();
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = null;
      fm.examItem = {}
      fm.templateType = {}
      fm.imageQuantity = null;
      fm.template = {};
      fm.preferred = null;
      fm.status = true;
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editForm.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let fm = this.editForm, data = res.data;
        data.status = 0 === data.status? true : false;
        //
        data.examItem = data.examItem || {};

        data.templateType = data.templateType || {};

        data.template = data.template || {};

        //data.preferred = 1 === data.preferred? true : false;

        Object.assign(fm, res.data);
        fm.examItem = fm.examItem || {};
        fm.visible = true;
        fm.title = "修改";
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          fm.status = fm.status? 0 : 1;

          //fm.preferred = fm.preferred? 1 : 0;
          //
          if (fm.id) {
            api.update(fm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(fm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    colFmt_rpcts(row, col, val, idx) {
      let contentItem = row["contentItem"];
      if(contentItem) {
        return contentItem.map(ci => ci.dictLabel).join(",");
      }
      return null;
    },

    findTemplate() {
      findTemplate().then(res => {
        this.editForm.combo_template = res.rows;
      });
    }
  }
};

export default model;
