import request, {postForm} from '@/utils/request'

const ctx = '/comcfg/examDataCtrl';

// 查询列表
export function find(query) {
  return postForm({
    url: ctx + '/list',
    data: query
  })
}

// 查询详细
export function get(id) {
  return request.get(ctx + '/get?id=' + id)
}

// 新增
export function save(data) {
  return request.post(ctx + '/save', data)
}

// 修改
export function update(data) {
  return request.put(ctx + '/save', data)
}

// 删除
export function del(id) {
  return request.delete(ctx + '/del/' + id)
}


// 删除
export function struct() {
  return request.get(ctx + '/struct')
}
