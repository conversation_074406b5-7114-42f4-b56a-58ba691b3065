import {isEmptyObject} from "@/utils/common";
import * as api from "@/assets/scripts/pacs/comcfg/examdatactrl/api";
//对象类别，项目类别
import {DataCtrlDict} from "./index";

export default {
  data() {
    let d = {
      //{'dict': [{itemId: 'uis_exam_item:01', itemId: 'uis_exam_item:02', itemId: 'uis_exam_type:01'}], 'room': [{itemId: 'rno3'}]}
      ctrlDataStruct: null,
      ctrlData: {
        [DataCtrlDict.ItemType.dict]: {},
        [DataCtrlDict.ItemType.room]: []
      }
    };

    return d;
  },

  methods: {
    /**
     * @param itemType dict/room
     * @data
     */
    async applyDataCtrl(itemType, data,ctrlDataStructNotSave) {
      if(!itemType || !data || data.length === 0) { return this.putDataCtrl(itemType, data); }
      //设置的权限
      let eds = this.ctrlDataStruct;
      if(!eds || isEmptyObject(eds)) {
        let st = await api.struct();
        eds = st.data;
        if(!!eds && !isEmptyObject(eds)&&!ctrlDataStructNotSave) {
          this.ctrlDataStruct = eds;
        }
      }
      if(!eds || isEmptyObject(eds)) { return this.putDataCtrl(itemType, data); }
      //未设置该项目类权限
      const items = eds[itemType];
      if(!items || items.length === 0) { return this.putDataCtrl(itemType, data); }
      //数据字典，是否有设置该类字典权限
      if(DataCtrlDict.ItemType.dict === itemType) {
        //数据字典比对
        let dct = data[0], dictType;
        dct = !!dct.raw? dct.raw : dct;
        dictType = dct.dictType;
        if(-1 === items.findIndex(i => i.itemId.startsWith(dictType + ":"))) {
          return this.putDataCtrl(itemType, data);
        }
      }
      //ds-数据权限配置项，vad-要验证的数据
      let cp = (ds, vad) => {
        //
        if(DataCtrlDict.ItemType.dict === itemType) {
          //数据字典比对
          let v = !!vad.raw? vad.raw : vad;
          return ds.itemId === `${v.dictType}:${v.dictValue}`;
        } else if(DataCtrlDict.ItemType.room === itemType) {
          //房间比对
          return ds.itemId === vad.roomCode;
        }
        return false;
      };
      const ret = data.filter(d => items.findIndex(i => cp(i, d)) !== -1);
      //
      return this.putDataCtrl(itemType, ret);
    },

    putDataCtrl(itemType, data) {
      if(!data || data.length === 0) { return data; }

      if(DataCtrlDict.ItemType.dict === itemType) {
        let dict = data[0], dictType = dict.raw? dict.raw.dictType : dict.dictType;
        //this.ctrlData[itemType][dictType] = data;
        if(undefined!=this.ctrlData)this.$set(this.ctrlData[itemType], dictType, data);
      } else if(DataCtrlDict.ItemType.room === itemType) {
        this.ctrlData[itemType] = data;
      }

      return data;
    }
  }
}
