import * as api from "@/assets/scripts/pacs/comcfg/examdatactrl/api";
//选择科室
import DeptPicker from "@/views/system/dept/DeptPicker";
//选中用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//获取机房列表
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
//字典
import {listType as listDictType} from "@/api/system/dict/type";
import {listData as listDictData} from "@/api/system/dict/data";

export const DataCtrlDict = {
  ObjType: {
    dept: "dept", role: "role", user: "user"
  },

  ItemType: {
    dict: "dict", room: "room"
  }
};

export default {
  name: "ExamDataCtrl",

  components: {UserPicker, DeptPicker},

  data() {
    return {
      // 遮罩层
      loading: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 9999
      },
      // 表单参数
      form: {},
      formRules: {
        objType: [
          {required: true, message: "请选择权限对象类型"}
        ],
        objId: [
          {required: true, message: "请选择权限对象"}
        ]
      },

      itemForm: {},

      defaultProps: {
        children: "children",
        label: "label"
      },
      //
      grid: {
        data: []
      },
      //
      open: false,

      combo: {
        objTypes: [
          {value: DataCtrlDict.ObjType.dept, label: "科室"},
          //{value: DataCtrlDict.ObjType.role, label: "系统角色"},
          {value: DataCtrlDict.ObjType.user, label: "系统用户"},
        ],

        itemTypes: [
          {value: DataCtrlDict.ItemType.dict, label: "数据字典"},
          {value: DataCtrlDict.ItemType.room, label: "检查机房"},
        ],

        itemData: []
      }
    };
  },
  created() {
    this.resetEditForm();

    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      api.find().then(r => {
          this.loading = false;
          this.grid.data = r.rows;
        }
      );
    },
    // 取消按钮
    cancelEdit() {
      this.open = false;
      this.resetEditForm();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.resetEditForm();
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      const id = row.id;
      api.get(id).then(r => {
        this.form = r.data;
        if (this.form.detail) {
          for (let detailElement of this.form.detail) {
            this.resetItemForm(detailElement)
          }
        }
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let fm = this.form, prom;
          if (!!fm.id) {
            prom = api.update(fm);
          } else {
            prom = api.save(fm);
          }
          prom.then(r => {
            this.$modal.msgSuccess("保存完成");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    resetEditForm() {
      this.form = {objType: null, objId: null, objName: null, detail: []};

      this.resetItemForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(() => {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },

    handleDeleteDetail(drow) {
      let fm = this.form, detail = fm.detail;
      if (!detail) {
        return;
      }

      let pos = detail.findIndex(d => d.id === drow.id);
      if (-1 !== pos) {
        detail.splice(pos, 1);
      }
    },
    //
    colFmt_objType(row, colMod, val) {
      if (!val) {
        return val;
      }
      const typ = this.combo.objTypes.find(t => t.value === val);
      return !typ ? null : typ.label;
    },
    //
    colFmt_itemType(row, colMod, val) {
      if (!val) {
        return val;
      }
      const typ = this.combo.itemTypes.find(t => t.value === val);
      return !typ ? null : typ.label;
    },

    handleChangeObjType() {
      let fm = this.form;
      fm.objId = null;
      fm.objName = null;
    },

    toPickObj() {
      let fm = this.form;
      switch (fm.objType) {
        case 'dept':
          this.$refs.deptPicker.findData();
          break;
        case 'role':
          break;
        case 'user':
          this.$refs["userPicker"].showPicker({target: null, posts: null});//[{postCode:'YS'}]
          break;
        default:
          return;
      }
    },

    pickObj() {
      let fm = this.form, obj, objId, objName;
      switch (fm.objType) {
        case 'dept':
          obj = arguments[0];
          objId = obj.deptCode;
          objName = obj.deptName;
          break;
        case 'role':
          obj = {};
          objId = obj.roleKey;
          objName = obj.roleName;
          break;
        case 'user':
          obj = arguments[1];
          objId = obj.userName;
          objName = obj.nickName;
          break;
        default:
          return;
      }
      fm.objId = objId;
      fm.objName = objName;
      //this.$set(fm, objName, objName);
    },

    handleChangeSubItemType(val, row) {
      this.doHandleChangeItemType(row.combo, row.itemForm);
    },
    handleChangeItemType() {
      let ifm = this.itemForm;
      let combo = this.combo;
      this.doHandleChangeItemType(combo, ifm)
    },

    doHandleChangeItemType(combo, itemForm) {
      itemForm.itemId = null;
      itemForm.itemName = null;
      itemForm.multiple = true;
      combo.itemData = [];
      switch (itemForm.itemType) {
        case DataCtrlDict.ItemType.room:
          if (!!combo.rooms) {
            combo.itemData = combo.rooms;
            return;
          }
          findRoom({}).then(r => {
            const rows = r.rows;
            if (rows.length === 0) {
              return;
            }

            let itemData = rows.map(i => {
              return {value: i.roomCode, label: i.roomName, raw: i}
            });
            combo.rooms = combo.itemData = itemData;
          });
          break;
        case DataCtrlDict.ItemType.dict:
          if (!!combo.dictTypes) {
            combo.itemData = combo.dictTypes;
            return;
          }
          listDictType({status: 0}).then(r => {
            const rows = r.rows;
            if (rows.length === 0) {
              return;
            }

            let itemData = rows.map(i => {
              return {value: `type:${i.dictType}`, label: `${i.dictName}(${i.dictType}) >`, raw: i}
            });
            combo.dictTypes = combo.itemData = itemData;
          });
          break;
      }
    },

    handleChangeSubItem(val, row) {
      this.doHandleChangeItem(val, row.itemForm, row.combo, row);
    },
    handleChangeItem(itemId) {
      this.doHandleChangeItem(itemId, this.itemForm, this.combo);
    },

    doHandleChangeItem(itemId, itemForm, combo, row) {
      if (DataCtrlDict.ItemType.dict === itemForm.itemType) {
        const typeItemId = "type:", backItem = {value: "back:", label: "< 返回"}
        //返回
        if (-1 !== itemId.findIndex(i => i === backItem.value)) {
          if (row) {
            this.handleChangeSubItemType(null, row);
          } else {
            this.handleChangeItemType();
          }
          return;
        }
        let itemId0 = itemId.length > 0 ? itemId[0] : null;
        //选择非字典类型
        if (!itemId0.startsWith(typeItemId)) {
          //ifm.multiple = true;
          return;
        }
        //ifm.multiple = false;
        //选择字典类型，获取类型值
        const dictType = itemId0.substring(typeItemId.length);
        //展开
        let exp = () => {
          this.$nextTick(this.$refs.itemsSelector.toggleMenu);
        };

        let dicts = combo.dicts;
        if (!dicts) {
          combo.dicts = dicts = {};
        } else if (!!(combo.itemData = dicts[dictType])) {
          //exp();
          return;
        }
        combo.itemData = [backItem];
        listDictData({dictType, status: 0}).then(r => {
          const rows = r.rows;
          if (rows.length === 0) {
            return;
          }

          let itemData = rows.map(i => {
            return {value: `${dictType}:${i.dictValue}`, label: i.dictLabel, raw: i}
          });
          itemData.unshift(backItem);
          combo.dicts[dictType] = combo.itemData = itemData;

          //exp();
        });

      }
    },

    resetItemForm(row) {
      if (row) {
        // 使用 Vue.set 或 this.$set 来确保响应式
        this.$set(row, 'itemForm', {
          itemType: null,
          itemId: null,
          itemName: null,
          multiple: true
        });
        this.$set(row, 'combo', {
          ...this.combo,
          itemTypes: [...this.combo.itemTypes],
          itemData: []
        });
      } else {
        this.itemForm = {
          itemType: null,
          itemId: null,
          itemName: null,
          multiple: true
        };
        this.combo.itemData = [];
      }
    },

    handleRemoveSubDetail(row, index) {
      // 创建一个全新的数组
      row.subDetail.splice(index, 1);
      this.resetItemForm(row);
    },

    addSubItem(row) {
      let itemForm = row.itemForm;
      let combo = row.combo;
      let subDetail = row.subDetail;

      itemForm.itemId.forEach(i0 => {
        let item = combo.itemData.find(i => i.value === i0);
        if (!item) {
          return true;
        }

        if (!subDetail) {
          row.subDetail = subDetail = [];
        }
        subDetail.push({
          itemType: itemForm.itemType,
          itemId: i0,
          itemName: item.label,
        });
      });
      this.resetItemForm(row);
    },

    addItem() {
      let fm = this.form, detail = fm.detail, ifm = this.itemForm;
      if (!ifm || !ifm.itemType || !ifm.itemId) {
        return;
      }
      ifm.itemId.forEach(i0 => {
        let item = this.combo.itemData.find(i => i.value === i0);
        if (!item) {
          return true;
        }

        if (!detail) {
          fm.detail = detail = [];
        }
        let row = {
          itemType: ifm.itemType,
          itemId: i0,
          itemName: item.label,
          subDetail: []
        };
        this.resetItemForm(row);
        detail.push(row);

      });

      this.resetItemForm();
    }
  }
};
