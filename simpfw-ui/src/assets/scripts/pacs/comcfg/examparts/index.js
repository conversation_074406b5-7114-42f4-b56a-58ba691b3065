import deepclone from "clone";
import deepmerge from "deepmerge";

import * as api from "@/assets/scripts/pacs/comcfg/examparts/api";
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import {default as ExamPartsTree} from "@/views/pacs/comcfg/examparts/comp/ExamPartsTreeSingle";

function emptyForm() {
  return {
    id: null
    , partsCode: null
    , partsName: null
    , orderNum: null
    , partsPinyin: null
    , partsEnglish: null
    , partsCodeHis: null
    , partsExamFactor: null
    , partsReportFactor: null
    , exposureValue: null
    , examCosts: null
    , examItem: {}
    , partsType: {extend: {}}
    , parent: {}
    , equipRoom: {}
    , modalityCode: null
    , modalityName: null
    , inpTypesCode: null
    //下拉
    , inpTypesCodes: null
  };
}

let model = {
  name: "ExamParts",
  extends: BaseGridModel,
  dicts: ['uis_exam_modality', 'uis_exam_item', 'uis_exam_parts_type', 'uis_inp_type'],
  components: { ExamPartsTree },
  data() {
    let dat = {
      queryForm: {
        partsName: null
      }, 

      editForm: emptyForm(),
      editFormRules: {
        "partsCode": { required: true, message: '请输入部位代码' },
        "partsName": { required: true, message: '请输入部位名称' },
        "modalityCode": { required: true, message: '请选择检查类型' }
      },

      editFormOpts: {
        combo_equipRoom: []
      }
    };
    return dat;
    
  },
  methods: {
    /** 查询列表 */
    getList() {
      let qfm = this.queryForm;
      qfm.pageNum = this.grid.pager.pageNum;
      qfm.pageSize = this.grid.pager.pageSize;

      this.loading = true;
      api.find(qfm).then(res => {
        this.loading = false;
      
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      this.editForm = emptyForm();
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      this.editForm.title = "新增";
      const fm = this.editForm, examPartsTree = this.$refs["examPartsTree"];
      let sup;
      if (mix && mix.id) {
        sup = mix;
      } else if(examPartsTree.current()) {
        sup = examPartsTree.current().data;
      }
      if(sup) {
        fm.parent = sup;
      }
      fm.visible = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let dat = res.data;
        if(!dat.parent) {
          dat.parent = {};
        }
        if(!dat.examItem) {
          dat.examItem = {};
        }
        if(!dat.partsType || !dat.partsType.extend) {
          dat.partsType = {extend: {}};
        }
        if(!res.data.equipRoom) {
          dat.equipRoom = {};
        }
        //
        if(dat.inpTypesCode) {
          const inpTypesCodes = dat.inpTypesCode.split(",");
          dat.inpTypesCodes = inpTypesCodes;//.map(c => parseInt(c));
        }
        //
        Object.assign(this.editForm, res.data);
        this.editForm.visible = true;
        this.editForm.title = "修改";
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        const fm = this.editForm;
        fm.status = 0;
        if (valid) {
          //检查类型
          const dictItem = this.getDictItem(this.effect_dict_uis_exam_modality, fm.modalityCode, "dictValue");
          if(!!dictItem) {
            fm.modalityName = dictItem.raw.dictLabel;//dictItem.label;
          }
          //
          fm.inpTypesCode = fm.inpTypesCodes? fm.inpTypesCodes.join(",") : null

          if (fm.id) {
            api.update(fm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(fm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.resetEditForm();
      this.editForm.visible = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleNodeClick(node) {
      //this.queryForm.parent = node.data;
      let params = {};
      let uid = node.uid, nid = node.id;
      if("C" === uid.substring(0, 1)) {
        params.partsType = {dictValue: nid};
      } else {
        params.id = nid;
      }
      this.queryForm = params;
      //
      this.getList();
    },

    onSelectPartsType(val) {
      const dictData = this.dict.type["uis_exam_parts_type"];
      let dict = dictData.find(d => val === d.raw.dictCode), ext;
      const fm = this.editForm;
      if(dict && (dict = dict.raw)) {
        ext = deepclone(dict.extend);
      }
      fm.partsType.extend = ext || {};
    },

    findEquipRoom() {
      findRoom({}).then(res => {
        this.editFormOpts.combo_equipRoom = res && res.rows || [];
      });
    }
  },

  created() {
    this.findEquipRoom();
  },

  computed: {

    /**
     * 检查类型字典
     */
    effect_dict_uis_exam_modality() {
      const orig = this.dict.type.uis_exam_modality;

      let items = [];
      if(orig && orig.length) {
        const conv = d => {
          return {value: d.dictValue, label: ("--" + d.dictLabel), raw: d};
        };
      //层级
        orig.forEach(e => {
          if(e.raw.parent && e.raw.parent.dictCode) {
            return true;
          }
          items.push(e);
          //
          let d = e.raw;
          if(d.children && d.children.length) {
            d.children.forEach(c => {
              items.push(conv(c));
            });
          }
          //
          orig.forEach(c => {
            if(c.raw.parent && c.raw.parent.dictCode === d.dictCode) {
              items.push(conv(c.raw));
            }
          });
      });
      }
      //
      return items;
    },

  }
};

export default model;