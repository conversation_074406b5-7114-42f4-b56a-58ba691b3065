import request, {postForm} from '@/utils/request'

const url = '/comcfg/examparts';

// 查询列表
export function find(query) {
  return postForm({
    url: url + '/list',
    data: query
  })
}

// 查询详细
export function get(id) {
  return request({
    url: url + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: url + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: url + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request({
    url: url + '/del/' + id,
    method: 'delete'
  })
}

// 查询下拉树结构
export function treeselect(params) {
  return request.post(url + '/tree'
    , params
  )
}

