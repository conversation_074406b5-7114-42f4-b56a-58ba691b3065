import deepmerge from "deepmerge";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/comcfg/reportcontentsettings/api";

let model = {
  name: "ReportContentSettings",
  extends: BaseGridModel,
  dicts: ['uis_exam_item', 'uis_report_content_item'],
  components: { },
  data() {
    let dat = {
      queryForm: { }

      , editForm: {
        id: null
        , examItem: {}
        , contentItem: []
        , contentItems_dictValue: []
        , status: null
      }
    };
    
    return dat;
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.status = null;
      fm.examItem = {};
      fm.contentItem = [];
      fm.contentItems_dictValue = [];
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editForm.visible = true;
      this.editForm.status = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let fm = this.editForm, data = res.data;
        data.status = 0 === data.status? true : false;
        let contentItems_dictValue = [];
        if(data.contentItem) {
          data.contentItem.forEach(d => contentItems_dictValue.push(d.dictValue));
        }

        Object.assign(fm, res.data);
        fm.contentItems_dictValue = contentItems_dictValue;
        fm.examItem = fm.examItem || {};
        fm.visible = true;
        fm.title = "修改";
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          fm.status = fm.status? 0 : 1;
          //
          if(fm.examItem && fm.examItem.dictValue) {
            const dictData = this.dict.type.uis_exam_item;
            const dict = dictData.find(d => d.value === fm.examItem.dictValue);
            if(dict) {
              fm.examItem = deepmerge(dict.raw, {});
            }
          }
          //
          let contentItem = [];
          let contentItems_dictValue = fm.contentItems_dictValue;
          if(contentItems_dictValue && contentItems_dictValue.length) {
            const dictData = this.dict.type.uis_report_content_item;
            contentItems_dictValue.forEach(dv => {
              const dict = dictData.find(d => d.value === dv);
              if(dict) {
                contentItem.push(deepmerge(dict.raw, {}));
              }
            });
          }
          fm.contentItem = contentItem;
          if (fm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(this.editForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    colFmt_rpcts(row, col, val, idx) {
      let contentItem = row["contentItem"];
      if(contentItem) {
        return contentItem.map(ci => ci.dictLabel).join(",");
      }
      return null;
    }
  }
};

export default model;
