import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";


export default {
  dicts: ['sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      grid: {
        total: 0,
        //
        pager: {
          total: 0,
          pageNum: 1,
          pageSize: 10
        },
        // 表格数据
        data: []
      },

      searchForm: { 
        pageNum: 1,
        pageSize: 10,

        status: undefined 
      },

      editForm: {
        visible: false, 
        title: "编辑", 
        status: null, 
        statusFlag: true
      }
    };
  },
  //created() {
  mounted() {
    this.getList();
  },
  methods: {
    
    getList() { console.warn("须实现getList") },
    
    handleQuery() {
      this.grid.pager.pageNum = 1;
      this.getList();
    },
    
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 取消编辑
    cancelEdit() {
      this.editForm.visible = false;
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const vm = this;
      vm.$modal.confirm('是否确认删除？').then(() => {
        return vm.doDelete(row);
      }).then(() => {
        vm.getList();
        vm.$modal.msgSuccess("删除成功");
      }).catch((err) => {if("cancel" !== err) {console.error(err);vm.$modal.msgError(err);}});
    },
    /**删除操作*/
    doDelete(row) {
      throw "删除操作未实现.";
    },

    /**表格“状态”列格式化*/
    colFmt_Status(row, col, val, ind) {
      return 0 === val || "0" === val? "启用" : "禁用";
    },

    /**表格字典类型属性列格式化*/
    colFmt_dictData(row, col, val, idx) {

      return this.colFmt_object(row, col, val, idx);
    },

    /***/
    colFmt_object(row, col, val, idx) {
      if(0 === val || val && !(val instanceof Array) && !(val instanceof Object)) {
        return val;
      }
      //通过表格“prop/property”取值
      let prop = col.property, props = prop.split("."), propsLen = props.length;
      //
      let propData = row[props[0]];
      if(propData && propsLen > 2) {
        for(let i = 1; i < propsLen - 1; i ++) {
          propData = propData[props[i]];
        }
      }
      if(!propData) {
        return null;
      }
      //
      let propName = propsLen > 1? props[propsLen - 1] : prop;
      //值为字典列表
      if(propData instanceof Array) {
        return propData.map(e => e[propName]).join(",");
      }
      //
      return propData[propName] || null;
    },

    revDict(type, vald) {
      if(vald && vald.dictValue) {
        const dictData = this.dict.type[type];
        let dict = dictData.find(d => vald.dictValue === d.value);
        return dict && dict.raw;
      }
      return null;
    },

    revDicts(type, valds) {
      let dicts = [];

      if(valds && valds.length) {
        //检查项目字典
        const dictData = this.dict.type[type];
        valds.forEach(dv => {
          let dict = dictData.find(d => dv === d.value);
          if(dict && (dict = dict.raw)) {
            dicts.push(dict);
          }
        });

        return dicts;
      }

      return null;
    },

  }
};