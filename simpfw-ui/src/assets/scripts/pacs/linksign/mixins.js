import {cloneDeep} from "lodash";

import * as api from "./api";
//默认授权时长 300 / 60 = 5小时
const authTimeDef = 300;
//手机号码验证
function validatePhone(val) {
  return /^((13[0-9])|(14(0|[5-7]|9))|(15([0-3]|[5-9]))|(16(2|[5-7]))|(17[0-8])|(18[0-9])|(19([0-3]|[5-9])))\d{8}$/.test(val);
}
//
function valdateVericode(val) {
  return /^[0-9]{6}$/.test(val);
}

export const modal = {
  methods: {
    aboutlinksignOtp() {
      this.$modal.alert("请打开手机微信或企业微信上的【医信签】小程序或企业微信上的【动态令牌】应用，查看您的动态令牌数据。");
    },

    cfmotp() {
      this.$refs.loginForm.validate(v => {
        const fm = cloneDeep(this.loginForm);
        if(!v) {
          return false;
        }

        if(!valdateVericode(fm.password)) {
          this.$modal.msgError("口令为6位数字，请确认。");
          return;
        }

        if(!fm.authTime) {
          fm.authTime = authTimeDef;
        }

        fm.uuid = this.qr.sessionId;

        api.cfmotp(fm).then(r => this.qr.snipQrAuthStatus(r));
      });
    },
  
    aqrsms() {
      let fmr = this.$refs.loginForm;
      fmr.clearValidate();

      fmr.validateField(['username'], errM => {
        if(errM) {
          //alert(errM);
          return;
        }
        const fm = this.loginForm;
        if(!validatePhone(fm.username)) {
          this.$modal.msgError("请输入正确手机号码。")
          return;
        }
        api.aqrsms({uuid: this.qr.sessionId, username: fm.username}).then(r => this.$modal.msgSuccess("短信已发送。"));
      });
    },

    cfmsms() {
      this.$refs.loginForm.validate(v => {
        const fm = cloneDeep(this.loginForm);
        if(!v) {
          return false;
        }

        if(!validatePhone(fm.username)) {
          this.$modal.msgError("请输入正确手机号码。")
          return;
        }

        if(!valdateVericode(fm.password)) {
          this.$modal.msgError("短信验证码为6位数字，请确认。");
          return;
        }

        if(!fm.authTime) {
          fm.authTime = authTimeDef;
        }

        fm.uuid = this.qr.sessionId;

        api.cfmsms(fm).then(r => this.qr.snipQrAuthStatus(r));
      });
    }
  }
};