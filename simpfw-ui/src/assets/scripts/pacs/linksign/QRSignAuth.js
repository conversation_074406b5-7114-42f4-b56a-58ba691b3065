import uuid from "uuid";

import request, {postForm} from '@/utils/request';
import {noop} from "@/utils/common";

const SessionIdPrefix = "QR::";

export {SessionIdPrefix};

//扫码登录相关
var QRSignAuth = (function(){

  var _model = {
    //扫码登录的token以"QR"开头
    fromQR() {
      let token = null;//LoginHelper.validateToken();
      //console.log(token);
      return !!token && 0 == token.indexOf(SessionIdPrefix);
    },
    //扫码登录token
    makeSessionId() {
      return (SessionIdPrefix + uuid().replace(/\-/g,''));
    },
    //生成登录码的地址
    makeCode(sessionId,url) {
      let codeHref = url + "/code?sessionId=" + sessionId + "&_=" + new Date().getTime();
      return request.get(codeHref);
    },

    /**
     * 扫码登录状态
     * @param opts {params, callback, caller}
     */
    checkStatus: function(opts,url) {
      let {params, callback, caller} = opts;
      let handler = noop;
      if("function" == (typeof callback)) {
        if(caller) {
          handler = callback.bind(caller);
        } else {
          handler = callback;
        }
      }

      const cfg = {url: url + '/authcheck', data: params};
      postForm(cfg).then(handler).catch(err => handler(err));
    },
    //扫码登录配置相关
    checkCfg(successHandler, errorHandler,url) {
      let vm = this;
      request.get(url + '/cfg')
      .then(res => {
        console.log("res", res);
        if(res && 200 === res.code) {
          successHandler(res);
        } else {
          errorHandler(res);
        }
      })
      .catch(err => errorHandler(err));
    }

  }
  return _model;

}) ();

export default QRSignAuth;

