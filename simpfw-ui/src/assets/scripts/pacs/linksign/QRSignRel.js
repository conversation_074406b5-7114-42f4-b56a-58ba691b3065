import request from '@/utils/request';

import QRSignAuth from './QRSignAuth'
import QRSign from './QRSign'

//为操作做的认证
class QRSignRel extends QRSign {
  constructor(successHandler) {
    super(successHandler);
  }

  snipQrAuthStatus(s) {
    //console.log(this, "snipQrAuthStatus", s);
    if(s && s.token) {
      request.get('/qrauth/rel?qrToken=' + s.token).then(res => {
        if('function' === (typeof this.successHandler)) {
          this.successHandler();
        }
      });
      return;
    }

    this.checkQrAuthStatusDelay();
  }

  checkQrAuthStatusParams() {
    let params = super.checkQrAuthStatusParams();
    params.forRel = true;
    return params;
  }
}

export default QRSignRel;