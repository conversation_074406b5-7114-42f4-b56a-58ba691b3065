import {BlankImage} from '@/utils/common'

import {Message} from 'element-ui'
//
import store from "@/store";
import {setToken} from '@/utils/auth'

import {getConfigKey} from "@/api/system/config";
import request from "@/utils/request";

//刷新二维码间隔：秒
const refreshQrcode_between = 6;

class QRSignBase {
  constructor(cacheKey, authHelper, successHandler) {
    this.naviText = "扫码登录";
    this.refreshButtonText = "刷新二维码";
    this.cancelButtonText = "退出登录";

    this.activated = false;
    this.openDig = false;
    //二维码地址
    this.data = BlankImage;
    this.loading = true;
    this.lastLoad = 0;
    this.oauthWindowURL = null;
    //签名标识
    this.sessionId = null;
    //定时检测签名状态时间间隔, 毫秒
    this.interval_checkQrAuthStatus = 4000;
    this.timer_checkQrAuthStatus = null;
    //检测总时长, 防止一直检测对签名服务器压力
    this.times_checkQrAuthStatus = 0;
    //处于自动检测签名状态
    this.autoCheckQrAuthStatus = true;
    //长时间未扫码或自动检测失效, 手动检测是否签名成功
    this.timer_checkQrAuthStatusManual = null;
    this.times_checkQrAuthStatusManual = 0;
    //扫码签名成功后执行
    this.successHandler = successHandler;
    //认证信息本地会话
    this.cacheKey = cacheKey;
    //相关接口，参考QRAuthHelper实现
    this.authHelper = authHelper;
    this.url = "/qrauth";
    this.useGXCA = false;
    this.enableTest = false;

    getConfigKey("uis.auth.testca.service").then(
      (response) => {
        //测试环境有配置参数，优先使用
        if (undefined !== response.msg && response.msg.length > 0) {
          this.url = "/testAuth";
          this.enableTest = true
        } else {
          getConfigKey("uis.auth.gxca.service").then(
            (response) => {
              //易手签有配置参数，优先使用
              if (undefined !== response.msg && response.msg.length > 0) {
                this.url = "/gxcaqrauth";
                this.useGXCA = true
              } else {
                getConfigKey("uis.wztsign.service").then(
                  (response) => {
                    //网证通有配置参数，优先使用
                    if (undefined !== response.msg && response.msg.length > 0) {
                      this.url = "/cqrauth";
                    }
                  }
                )
              }
            }
          )
        }
      }
    )
  }

  /**
   * 获取签名标识
   */
  checkQrSession() {
    if (!this.cacheKey) {
      console.error("错误：须明确会话cacheKey。");
      return;
    }
    let vm = this, qrSessionid = null;//store.state.token;//StoreUtil.fetch(this.cacheKey);
    vm.sessionId = qrSessionid || this.authHelper.makeSessionId();
  }

  clearQrSession() {
    if (!this.cacheKey) {
      console.error("错误：须明确会话cacheKey。");
      return;
    }
    //console.warn("注意：未实现清除会话");
    //StoreUtil.clear(this.cacheKey);
  }

  /**
   * 读取二维码
   */
  async refreshQrcode() {
    if (!this.activated) {
      this.data = BlankImage;
      return;
    }
    //间隔6秒, 防止频繁读取
    let millis = new Date().getTime();
    if (this.lastLoad > 0 && (millis - this.lastLoad) <= refreshQrcode_between * 1000) {
      Message("刷新略显频繁, 请稍后再试.");
      return;
    }
    //取sessionId，没有就生成
    this.checkQrSession();
    //
    clearTimeout(this.timer_checkQrAuthStatus);
    //
    this.loading = true;

    let response = await getConfigKey("uis.auth.gxca.service")
    //易手签有配置参数，优先使用
    if (undefined !== response.msg && response.msg.length > 0) {
      this.url = "/gxcaqrauth";
    } else {
      response = await getConfigKey("uis.wztsign.service")
      //网证通有配置参数，优先使用
      if (undefined !== response.msg && response.msg.length > 0) {
        this.url = "/cqrauth";
      }
    }
    //获取二维码
    const res = await this.authHelper.makeCode(this.sessionId, this.url);
    this.data = "data:image/png;base64," + res.data;
    this.oauthWindowURL = res.msg;

    this.refreshQrcodeSuccess();

  }

  createSign(file) {
    let requestUrl = this.url + "/createSign"
    let form = new FormData()
    form.append("signFile", file)
    return request({
      url: requestUrl,
      method: "post",
      data: form
    })
  }

  getSignInfo(signNum) {
    let requestUrl = this.url + "/getSignInfo"
    let form = new FormData()
    form.append("signNum", signNum)
    return request({
      url: requestUrl,
      method: "post",
      data: form
    })
  }

  saveSign(exam, filePath) {
    let requestUrl = this.url + "/saveSign"
    let form = new FormData()
    form.append("examUid", exam.examUid)
    form.append("signFilePath", filePath)
    return request({
      url: requestUrl,
      method: "post",
      data: form
    })
  }

  /**
   * 读取二维码成功执行
   */
  refreshQrcodeSuccess() {
    let qr = this;
    qr.loading = false;
    qr.lastLoad = new Date().getTime();
    qr.times_checkQrAuthStatus = 0;
    this.checkQrAuthStatusDelay();
  }

  /**
   * 读取二维码失败执行
   */
  refreshQrcodeFail() {
    this.data = BlankImage;
    clearTimeout(this.timer_checkQrAuthStatus);
  }

  /**
   * 检测扫码签名状态
   */
  checkQrAuthStatus() {
    let vm = this;
    if (!vm.activated) {
      vm.checkQrAuthStatusDelay();
      return;
    }
    const checkStatusOpts = {params: this.checkQrAuthStatusParams(), callback: vm.snipQrAuthStatus, caller: vm};
    this.authHelper.checkStatus(checkStatusOpts, this.url);
  }

  //
  checkQrAuthStatusParams() {
    return {sessionId: this.sessionId};
  }

  /**
   * 定时检测扫码签名状态
   */
  checkQrAuthStatusDelay() {
    let qr = this;
    clearTimeout(qr.timer_checkQrAuthStatus);
    //空轮询, 不超过3分钟
    qr.autoCheckQrAuthStatus = qr.times_checkQrAuthStatus <= (3 * 60 * 1000)
    if (!qr.autoCheckQrAuthStatus) {
      qr.times_checkQrAuthStatusManual = 0;
      return;
    }
    //
    qr.timer_checkQrAuthStatus = setTimeout(() => {
      qr.checkQrAuthStatus();
    }, qr.interval_checkQrAuthStatus);
    qr.times_checkQrAuthStatus += qr.interval_checkQrAuthStatus;
  }

  /**
   * 解析签名状态结果
   */
  snipQrAuthStatus(s) {
    //console.log(this, "snipQrAuthStatus", s);
    //console.log(s);
    let vm = this;
    //签名成功
    if (s && 200 == s.code && s.token) {
      const token = s.token;
      setToken(token);
      store.commit("SET_TOKEN", token);//vm.sessionId
      //vm.enterWelcome();
      if ("function" == (typeof vm.successHandler)) {
        vm.successHandler();
      }
      return;
    }

    if (s && "已失效" == s.msg) {
      vm.clearQrSession();
      //if(vm.times_checkQrAuthStatus) {
      //  vm.refreshQrcode();
      //} else {
      Message({
        duration: 0,
        showClose: true,
        message: "二维码已失效或授权已过期, 请刷新二维码.",
        type: 'warning'
      });
      //}
      return;
    }
    //
    if (!s && vm.times_checkQrAuthStatusManual) {
      setTimeout(() => {
        vm.times_checkQrAuthStatusManual = 0;
      }, 1000);
    }

    vm.checkQrAuthStatusDelay();
  }

  /**
   * 手动检测签名状态
   */
  checkQrAuthStatusManual(evt) {
    if (evt) {
      evt.stopPropagation();
    }
    let qr = this;
    if (qr.activated && !qr.autoCheckQrAuthStatus && !qr.times_checkQrAuthStatusManual) {
      qr.times_checkQrAuthStatusManual++;
      qr.checkQrAuthStatus();
      qr.times_checkQrAuthStatus = 0;
    }
  }

}

export default QRSignBase;
