import '@/assets/styles/pacs/linksign/QRDialog.css';

import Linksign from "@/views/pacs/linksign/index";
import QRSign from "@/assets/scripts/pacs/linksign/QRSign"

import * as caapi from "@/assets/scripts/pacs/linksign/caapi"
import DesUtil from '@/utils/DesUtil'

const loginMode = {
    default: "up"
    , pin: "pin"//口令
    , qr: "qr"//扫码
    , ca: "ca"//ca扫码
  }

const model = {

  props: {
      qr: {type: Object, default: null},
  },

  components: {Linksign},

  data() {
    return {
        logining: false,
        loginForm: {
            mode: "ca",

            username: '',
            password: '',
            verifyCode: null,
            cacheKey: "certAuthForm",

            // qr: new CAQRSign(this.enterWelcome.bind(this)),

            userTypes: [{code: "0", name: "证书用户"}, {code: "1", name: "被授权用户"}]
            , userType: "0"

            , enabledGuest: false
            //在CA账号认证失败后尝试常规账号登录，只在选择“被授权用户”时失败后执行
            , regularUserTest: false

            , verifyCodeType: false
            , verifyCodeExpired: 5
            , verifyCodeTimeout: 0
          },

          loginRule: {
            username: [{required: true, message: '请输入用户ID或工号', trigger: 'blur'}],
            password: [{required: true, message: '请输入口令', trigger: 'blur'}],
            // verifyCode: [{required: true, message: '请输入验证码', trigger: 'blur'}],
        },
    };
  },

  methods: {

    handleSubmit(event){
        var self = this;

        //if(self.loginFailTimes >= maxLoginFailTimes) {
        //  self.$alert("登录失败次数已达" + maxLoginFailTimes + "次, 请尝试重置密码或联系管理员取回账号.");
        //  return;
        //}

        self.$refs["certAuthForm"].validate((valid) => {
          if(!valid){
            return false;
          }

          var fm = self.loginForm;
          var secKey = "$,$$,$$$";
          //认证信息
          var username = DesUtil.encode(fm.username, secKey);
          var password = DesUtil.encode(fm.password, secKey);
          //认证类型
          var userType = fm.userType;

          self.logining = true;

          let form = new FormData();
          form.append("username", username);
          form.append("password", password);
          form.append("userType", userType);

        //   LoginHelper.auth(prin, self.loginSuccess, self.loginFailure.bind(self));
          caapi.pinLogin(form).then(res => {
            self.logining = false;
            this.qr.successHandler();
            console.log("qr",this.qr);
            this.cancel();

          }).catch(err=>{self.logining = false;});


          return true;

        });
      },

    //采用的登录方式
    setLoginMode(mode) {
        let vm = this;

        vm.loginForm.mode = mode;
        //支持扫码认证
        if(loginMode.ca == mode&&this.qr) {
            this.qr.naviText = "扫描登陆";
            this.qr.activated = true;
            this.refreshQrcode();
        }else if(loginMode.pin == mode){
          this.qr.naviText = "口令登陆";
            this.qr.activated = false;
            this.handleSubmit();
        }
    },

    refreshQrcode() {
      if(this.qr) { this.qr.refreshQrcode(); }
    },
    /**
     * 退出系统
     */
    cancel() {
      if(this.qr) {
        console.log("cancel");
        this.qr.activated = false;
        this.qr.openDig = false;
        this.loginForm.mode = loginMode.ca;
      }
    },

    abandQR() {
      this.$emit("abandQR");
      this.cancel();
    }
  },

  computed: {
    opened() {
      this.setLoginMode(this.loginForm.mode);
      return !!this.qr && this.qr.openDig;
    },

    title() {
      return this.qr? this.qr.naviText : '--';
    },

    refreshButtonText() {
      return this.qr? this.qr.refreshButtonText : '--';
    }
  }
};

export default model;
