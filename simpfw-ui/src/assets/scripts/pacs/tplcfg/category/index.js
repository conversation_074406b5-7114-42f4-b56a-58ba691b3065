
import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/tplcfg/category/api";

import EditForm from "@/views/pacs/tplcfg/category/EditForm";

let model = {
  name: "TemplateCategory",
  extends: BaseGridModel,
  dicts: ["uis_report_template_private_flag"],
  components: { EditForm },
  data() {
    let dat = {
      queryForm: {
        treeEnabled: true,
        pageSize: 10,
        pageNum: 1
      }
      , personalFlag:'0'
      , editForm: {
        id: null
        , cateName: null
        , examParts: null
        , examPartsIds: null
        , parent: { }
      }
    };
    
    return dat;
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },

    colFmt_examParts(rows, col, val, idx) {
      if(!val) {
        return "";
      }
      return val.map(p => p.partsName).join(",");
    },
    colFmt_personalFlag(rows, col, val, idx) {
      return 0 === val || "0" === val? "公共模板" : "个人模板";
    },
    handleAdd(mix) {
      this.$refs.editFormDialog.handleAdd(mix);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.editFormDialog.handleUpdate(row);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    
  },
  computed: {
    
    cateTreeData(){
      let cateTreeData=[]
      if(this.grid.data.length<0){
        return cateTreeData
      }
      //console.log(this.grid.data)
      this.grid.data.forEach(e=>{
        if(e.personalFlag==this.personalFlag){
          cateTreeData.push(e)
        }
      })    
      return cateTreeData
    }
  },
};

export default model;
