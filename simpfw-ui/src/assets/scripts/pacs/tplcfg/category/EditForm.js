import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import * as api from "@/assets/scripts/pacs/tplcfg/category/api";

//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";
import TemplateTree from "@/views/pacs/tplcfg/template/comp/TemplateTree";

import { mapGetters } from 'vuex';
import auth from '@/plugins/auth'

export default {
  //extends: BaseDialogModel,
  mixins: [ExamDataScope],
  dicts: ['uis_report_template_private_flag',"uis_exam_modality","uis_exam_item"],
  components: {Treeselect, ExamPartsTree,TemplateTree},

  data() {
    return {
      cateTreeDataTemp: [],

      editForm: {
        title: null
        , isEdit :false
        , personalFlag:"0"
        , visible: false
        , id: null
        , cateName: null
        , examParts: null
        , examPartsIds: []
        , parent: { }
        ,examModalityCodes:[] 
        ,examItemCodes:[]
        ,examModality:[] 
        ,examItem:[]
        , status:true
      },

      editFormRules: {
        "parent.id": { required: true, message: '请选择父类模板' },
        "cateName": { required: true, message: '请填写分类名称' },
        "examModalityCodes": { required: true, message: '请选择检查类型' },
        "examItemCodes": { required: true, message: '请选择检查项目' },
      },

      selectNodeLevel:0
    }
  },
  computed:{
    ...mapGetters(['examModality']),
    cateTreeData(){
      let cateTreeData=[]
      if(this.cateTreeDataTemp.length<0){
        return cateTreeData
      }

      this.cateTreeDataTemp.forEach(e=>{
        if(e.data.personalFlag==this.editForm.personalFlag){
          cateTreeData.push(e)
        }
      })
      return cateTreeData
    }
  },
  methods: {

    setSelectNodeLevel(level){
      this.selectNodeLevel = level;
      
      if(undefined==this.selectNodeLevel||0==this.selectNodeLevel){
        this.editFormRules.examModalityCodes.required = true;
        this.editFormRules.examItemCodes.required = false;
      }else if(1==this.selectNodeLevel){
        this.editFormRules.examModalityCodes.required = false;
        this.editFormRules.examItemCodes.required = true;
      }else{
        this.editFormRules.examModalityCodes.required = false;
        this.editFormRules.examItemCodes.required = false;
        this.$refs.editForm.clearValidate();
      }
    },
    
    selectParent(node){
      this.setSelectNodeLevel(node.level+1);
    },

    change(node){
      if(undefined==node) this.setSelectNodeLevel(0);
    },

    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      //
      let examPartsTree;
      if(fm.examPartsIds && (examPartsTree = this.$refs["examPartsTree"])) {
        examPartsTree.setCheckedKeys(fm.examPartsIds, false);
      }
      //
      fm.id = fm.cateName = fm.examParts = null;
      fm.status = true;

      fm.parent = {}
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      let vm = this;
      this.resetEditForm();
      this.editForm.title = "增加";
      if(mix && mix.id) {
        this.editForm.parent = mix;
      }
      this.editForm.visible = true;
      

      this.setCateTreeNodeState(this.cateTreeData,0);
      this.setSelectNodeLevel(this.getTreeNodeLevel(this.cateTreeData,mix?mix.id:null));
      //vm.$refs.tplTree.init();
      // setTimeout(function() {
      //   vm.$refs.tplTree.init();
      //   // 要执行的代码
      // },  500); // 3000毫秒 = 3秒
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let vm = this;
      this.resetEditForm();
      api.get(row.id).then(res => {
        let fm = this.editForm, data = res.data;
        data.parent = data.parent || {};
        data.status = 0 === data.status? true : false;
        fm.examModalityCodes = null;
        fm.examItemCodes = null;
        if(res.data.examModality.length>0) fm.examModalityCodes = res.data.examModality[0].dictValue;
        if(res.data.examItem.length>0) fm.examItemCodes = res.data.examItem[0].dictValue;
        Object.assign(fm, res.data);
        fm.visible = true;
        fm.title = "修改";
        fm.personalFlag = row.personalFlag
        fm.isEdit = true
        // this.$refs.tplTree.init();
        this.setCateTreeNodeState(this.cateTreeData, 0,row);
        this.setSelectNodeLevel(this.getTreeNodeLevel(this.cateTreeData,row.parent?row.parent.id:null));
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      let vm = this;
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          //增加/更新成功回调
          const cb = r => {
            fm.visible = false;
            this.$modal.msgSuccess("保存成功");
            this.buildCateTree();
            
            setTimeout(function() {
              vm.$refs.tplTree.init();
            },  1000); 
            this.triggerBind("refresh", fm);
          };
          //提交
          fm.status = true === fm.status? 0 : 1;
          if (fm.id) {
            api.update(this.editForm).then(cb);
          } else {
            api.save(this.editForm).then(cb);
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },

    //点击树节点触发
    handleNodeClick(node,tree) {
      if(!auth.hasPermi('template-edit:level1')&&0==node.level) {
        this.$modal.msgSuccess("没有编辑权限");
        return;
      }

      if(!auth.hasPermi('template-edit:level1')&&!auth.hasPermi('template-edit:level2')&&1==node.level) {
        this.$modal.msgSuccess("没有编辑权限");
        return;
      }
      if(node.isTemplate) {
        this.$modal.msgSuccess("模板内容无法选择");
        return
      };
      this.handleUpdate(node.data);    
    },

    //过滤公共/个人模板
    filterTemplate(value, node) {
      return node.data.personalFlag == value;
      //return !node.isTemplate || node.data.personalFlag == value;
    },
    handleCheckedExamParts(node, state) {
      //
      let checkedParts = this.$refs["examPartsTree"].getCheckedNodes();
      let examParts = [];
      for(let i = 0, parts; i < checkedParts.length; i ++) {
        parts = checkedParts[i];
        examParts.push({partsCode: parts.id});
      }
      this.editForm.examParts = examParts;
    },
    //
    buildCateTree() {
      let param={};
      api.treeselect(param).then(res => {
        let nodes = res.data;
        this.setCateTreeNodeState(nodes,0);
        this.cateTreeDataTemp = nodes;
      });
    },

    setCateTreeNodeState(nodes, treeLevel,disnode) {
      let vm = this;
      if(!nodes) {
        return;
      }

      for(var i=0;i<nodes.length;i++){
        let n = nodes[i]
        n["level"] = treeLevel;
        n.isDisabled = !!disnode && disnode.id == n.id;
        if(0==treeLevel) {
          if(!auth.hasPermi('template-edit:level1')&&!auth.hasPermi('template-edit:level2')) n.isDisabled = true;
          // let includes = vm.examModality.some(item => n.data.examModality.dictValue.includes(item.dictValue));
          //let includes = n.data.examModality[0].dictValue.includes("JC");
          // let intersection = vm.examModality.filter(v => b.includes(v))
          let includes = false;
          for (let a of vm.examModality) {
            for (let b of n.data.examModality) {
              if (a.dictValue==b.dictValue) {
                includes =  true;
                break;
              }
            }
          }

          if(!includes) {
            nodes.splice(i, 1);
            continue;
          }
        }
        this.setCateTreeNodeState(n.children, treeLevel+1,disnode);
      }
    },

    getTreeNodeLevel(nodes,parentNodeId){
      let level = null;
      if(!nodes) {
        return;
      }
      for(var n of nodes){
        if(n.id==parentNodeId) {
          level = n.level+1;
          break;
        }
        let res = this.getTreeNodeLevel(n.children, parentNodeId);
        if(res) level = res;
      }
      return level;
    },

    setTemplateVis(){
      this.editForm.parent={}
    }
  },

  created() {
    this.buildCateTree();
    if(auth.hasPermi('template-edit:level1')) {
      this.editFormRules["parent.id"].required = false;
    }
  },

  watch: {
    "editForm.examParts": {
      handler(newv, oldv) {
        let examParts = newv;
        this.editForm.examPartsIds = examParts? examParts.map(p => p.id) : null;
      },
      deep: true
    },

    "editForm.examModalityCodes": {
      handler(newv, oldv) {
        this.editForm.examModality = [];
        this.editForm.examModality.push({dictValue:newv})
      },
    },

    "editForm.examItemCodes": {
      handler(newv, oldv) {
        this.editForm.examItem = [];
        this.editForm.examItem.push({dictValue:newv})
        // newv.forEach(e=>{
        //   this.editForm.examItem.push({dictCode:e})
        // });
      },
    },

    /**
     * 检查项目字典取值后执行
     */
     "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    /**
     * 检查类型字典取值后执行
     */
     "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },
  }
};