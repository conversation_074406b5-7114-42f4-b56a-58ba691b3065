//import {triggerBind} from "@/utils/common";

import BaseTreeModel from "@/assets/scripts/pacs/BaseTreeModel";

import * as api from "@/assets/scripts/pacs/tplcfg/category/api";
import { getUnfinishedLine } from "source-list-map/lib/helpers";

//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";
import auth from '@/plugins/auth'

import { mapGetters } from 'vuex';

let model = {
  extends: BaseTreeModel,

  props: {
    fakeRoot: {type: Boolean, default: true},
    defaultExpandAll: {type: Boolean, default: true},
    htooltip: {type: Boolean, default: true},
    menu: {type: Boolean, default: false},
  },

  dicts: ["uis_exam_item"],

  mixins: [ExamDataScope],
  data() {
    return {
      //定义点击次数,默认0次
        treeClickCount: 0,
        radio:0,
        personalFlag:0,
        expandedKeys:["C43"],
        currentNodeKey:null,
    }
  },
  methods: {
    init(){
      this.buildTree();
    },

    // updateTreeCateNode(id){
    //   api.get(id).then(res => {
    //     const tree = this.$refs.tree;
    //     let node = tree.getNode("C"+id);
    //   });
    // },

    change(){
      this.radio = this.radio++%3;
    },
    
    handleNodeClick(data,tree,note){
            //console.log(data,tree,note)
            this.treeClickCount++;
            //计时器,计算300毫秒为单位,可自行修改
            this.timer = window.setTimeout(() => {
            if (this.treeClickCount == 1) {
            //把次数归零
            this.treeClickCount = 0;
            //单击事件处理
            this.triggerBind("onChangeArea", data,tree);

            } else if (this.treeClickCount > 1) {
            //把次数归零
            this.treeClickCount = 0;
            //双击事件
            this.triggerBind("nodeDblclick", data,tree);
            }
        }, 300);        
    },
    buildTree() {
      let param={status:0};
      api.treeselect(param).then(res => {
        let data = res.data;
        //
        this.genuid(data);
        this.initTreeNode(data,0,false);
        
      
        //
        if(this.fakeRoot) {
          data = [{label: "全部", children: data}];
        }
        this.tree.data = this.hideHeadNode(data);
        //
        
        this.triggerBind("built", this.tree.data);
      });
    },

    initTreeNode(nodes, treeLevel,parentShow) {
      let vm = this;
      if(!nodes) {
        return;
      }

      nodes.forEach(n=>{
        n["level"] = treeLevel;
        let localParentShow = parentShow;
        if(0==treeLevel){
          for (let a of vm.examModality) {
            for (let b of n.data.examModality) {
              if (a.dictValue==b.dictValue) {
                localParentShow =  true;
                break;
              }
            }
          }
        }
        n["show"] = localParentShow;
        vm.initTreeNode(n.children, treeLevel+1,localParentShow);
      })
    },

    hideHeadNode(nodes){
      if(auth.hasPermi('template-edit:level1')) return nodes;
      let newNodes = [];
      for(let node of nodes){
        if(node.show){
          for(let children of node.children){
            newNodes.push(children);
          }
        } 
      }
      return newNodes;
    },

    //增加节点
    appendNode(node, parent) {
      const tree = this.$refs.tree;
      if(!!tree.getNode(node.uid)) {
        tree.remove(node.uid);
        //return;
      }
      if(!parent) {
        this.tree.data.push(node);
        return;
      }
      tree.append(node, parent);
    },
    //树节点id，唯一
    genuid(data) {
      if(!data) { return; }
      data.forEach(e => {
        if(!!e && !!e.id) {
          e.uid = ('C' + e.id);
          this.genuid(e.children);
        }
      });
    },
    //删除节点
    removeNode(uid) {
      this.$refs.tree.remove(uid);
    },
    //节点右键, 参数：事件，节点数据，节点，组件
    handleNodeContextmenu(evt, node, treeNode, vn) {
      this.triggerBind("nodeContextmenu", ...arguments);
    },
    //选择公共/个人模板
    setTemplateVis(personalFlag) {
      this.personalFlag = personalFlag;
      this.$refs.tree.filter(personalFlag);     
      this.expandHandle()
    },

    ischildrenIncludeExam(node,exam){
      let vm = this;
      var includeExam = false;
      node.forEach(e=>{
        if(e.isTemplate){
          e.data.examItem.forEach(e=>{
            if(e.dictValue == exam){
              includeExam = true;
            }
          });
        }else{
          if(e.children>0){
            return vm.ischildrenIncludeExam(e.children,exam);
          }
        }
      });
      return includeExam;
    },

    //过滤公共/个人模板
    filterTemplate(value, node) {
      let vm = this;
      var includeExam = node.show;
      //只显示相应检查项目的模板
      // if(this.ctrlData.dict.uis_exam_item){
      //   this.ctrlData.dict.uis_exam_item.forEach(i=>{
      //     if(node.isTemplate){
      //       node.data.examItem.forEach(e=>{
      //         if(e.dictValue == i.value){
      //           includeExam = true;
      //           return;
      //         }
      //       });
      //     }else{
      //       if(node.children>0){
      //         includeExam = vm.ischildrenIncludeExam(node.children,);
      //         return;
      //       }
      //     }
          
      //   });
      // }

      //菜单全部显示
      // if(this.menu) includeExam = true;
      
      return node.data.personalFlag == value&&includeExam;
      //return !node.isTemplate || node.data.personalFlag == value;
    },

    //切换时折叠起来
    expandHandle() {
      //console.log(this.$refs.tree.store);
      //this.expandAll = !this.expandAll;
      this.expandNodes(this.$refs.tree.store.root);
    },
    // 遍历树形数据，设置每一项的expanded属性，实现展开收起
    expandNodes(node) {
      node.expanded = false;
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = this.false;
        if (node.childNodes[i].childNodes.length > 0) {
          this.expandNodes(node.childNodes[i]);
        }
      }
    },    
  },

  watch:{
    /**
     * 检查项目字典取值后执行
     */
     "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    "ctrlData.dict.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.setTemplateVis(this.personalFlag); 
      }
    },

    "currentNodeKey": {
      deep: true,
      handler(nv, ov) {
      }
    },
  },

  computed:{
    ...mapGetters(['examModality']),
  },

};

export default model;