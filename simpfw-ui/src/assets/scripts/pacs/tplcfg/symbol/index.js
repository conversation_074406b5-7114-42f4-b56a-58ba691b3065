import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/tplcfg/symbol/api";
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

let model = {
  name: "Character",
  extends: BaseGridModel,
  dicts: ['uis_exam_item'],
  components: { },
  mixins: [ExamDataScope],
  data() {
    let dat = {
      queryForm: {
      }

      , editForm: {
        visible: false
        , title: "编辑"
        , id: null
        , characterCode: null
        , characterName: null
        , status: null
        , examItem_dictValue: []
      }
    };
    
    return dat;
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.characterCode = fm.characterName = fm.status = null;
      fm.examItem_dictValue = []
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();

      this.editForm.visible = true;
      this.editForm.status = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let fm = this.editForm, data = res.data,examItem_dictValue = [];
        if(!data.examItem) {
          data.examItem = [];
        } else {
          data.examItem.forEach(d => examItem_dictValue.push(d.dictValue));
        }
        data.examItem_dictValue = examItem_dictValue;

        data.status = 0 === data.status? true : false;

        Object.assign(fm, res.data);
        fm.visible = true;
        fm.title = "修改";
      });
    },

    //检查项目
    revExamItem() {
      let fm = this.editForm, examItem = [], examItem_dictValue = fm.examItem_dictValue;;

      if(examItem_dictValue && examItem_dictValue.length) {
        //检查项目字典
        const dictData = this.dict.type["uis_exam_item"];
        examItem_dictValue.forEach(dv => {
          let dict = dictData.find(d => dv === d.value);
          if(dict && (dict = dict.raw)) {
            examItem.push(dict);
          }
        });
      }

      fm.examItem = examItem;
    },

    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        this.revExamItem();
        if (valid) {
          const fm = this.editForm;
          fm.status = fm.status? 0 : 2;
          if (fm.id) {
            api.update(this.editForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              fm.visible = false;
              this.getList();
            });
          } else {
            api.save(this.editForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              fm.visible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }

  }
};

export default model;
