import {cloneDeep} from "lodash";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

//import * as api_phrase from "@/assets/scripts/pacs/tplcfg/phrase/api";
//模板分类
// import {treeselect as cateTreeselect} from "@/assets/scripts/pacs/tplcfg/category/api";
import * as cate_api from "@/assets/scripts/pacs/tplcfg/category/api";
//模板
import * as api from "@/assets/scripts/pacs/tplcfg/template/api";

//常用符号
import PhraseSelect from "@/views/pacs/tplcfg/phrase/comp/PhraseSelect";

//import TemplateCategoryTree from "@/views/pacs/tplcfg/category/comp/TemplateCategoryTree";
import TemplateTree from "@/views/pacs/tplcfg/template/comp/TemplateTree";

import { mapGetters } from 'vuex';

import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";
import auth from '@/plugins/auth'

let model = {
  name: "Template",
  dicts: ['uis_exam_item', 'uis_exam_result_prop', 'uis_report_template_private_flag','pacs_template_type'],
  components: { TemplateTree, PhraseSelect, Treeselect,ExamPartsTree },
  data() {
    let dat = {
      loading: false,

      editForm: {
        id: null
        , category: {}
        , personalFlag: "0" 
        , templateName: null
        , prompts: null
        , examDesc: null
        , examDiagnosis: null
        , operationSuggestion: null
        , examItem: []
        , status: null
        , preferred: null
        , examResultProp: {}
        , templateType:'2'
        , examItem_dictValue: []
        , examParts: null
        , examPartsIds: []
      },

      editFormRules: {
        "category.id": { required: true, message: '请选择模板分类' },
        "templateName": { required: true, message: '请输入模板名称', trigger: 'blur' },
      },

      cateTreeDataTemp: [],
      //树结构以及子菜单
      //目前暂定4级菜单
      cataTreeGrade:{
        cateTree_0:[],
        cateTree_1:[],
        cateTree_2:[],
        cateTree_3:[],
        cateTree_4:[],
        cateTreeVale_0:'',
        cateTreeVale_1:'',
        cateTreeVale_2:'',
        cateTreeVale_3:'',
        cateTreeVale_4:'',
        cateNew_1:false
      }


    };
    
    return dat;
  },
  methods: {
    
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = null;
      fm.category = {};
      fm.personalFlag = "0";
      fm.templateName = null;
      fm.prompts = null;
      fm.examDesc = null;
      fm.examDiagnosis = null;
      fm.operationSuggestion = null;
      fm.examItem = [];
      fm.status = null;
      fm.preferred = false;
      fm.examResultProp = {}
      fm.templateType='1'
      fm.examItem_dictValue = []
      let examPartsTree;
      if(fm.examPartsIds && (examPartsTree = this.$refs["examPartsTree"])) {
        examPartsTree.setCheckedKeys(fm.examPartsIds, false);
      }
      fm.examParts = null;

    },
    resset_cataTreeGrade(){
        const fm=this.cataTreeGrade
        fm.cateTree_0=[]
        fm.cateTree_1=[]
        fm.cateTree_2=[]
        fm.cateTree_3=[]
        fm.cateTree_4=[]
        fm.cateTreeVale_0=''
        fm.cateTreeVale_1=''
        fm.cateTreeVale_2=''
        fm.cateTreeVale_3=''
        fm.cateTreeVale_4=''
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      const fm = this.editForm;
      if(!!mix && !(mix instanceof Event) && !!mix.templateName) {
        Object.assign(fm, mix);
      }
      //fm.category = this.queryForm.category || fm.category;
      if(!fm.category || !fm.category.id) {
        this.$modal.alert("请选择模板分类。");
        return;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      //this.resetEditForm();
      this.loading = true;
      api.get(row.id).then(res => {
        this.endload();

        let data = res.data, fm = this.editForm, examItem_dictValue = [];
        if(!data) {
          this.$modal.alert("模板不存在或者为他人个人模板。");
          return ;
        }
        //
        data.category = data.category || {};
        //
        data.examResultProp = data.examResultProp || {};
        if(!data.examItem) {
          data.examItem = [];
        } else {
          data.examItem.forEach(d => examItem_dictValue.push(d.dictValue));
        }
        data.preferred = 0 === data.preferred;
        data.examItem_dictValue = examItem_dictValue;
        data.personalFlag = 0 === data.personalFlag? "0" : ("" + data.personalFlag);
        data.templateType = 1 === data.templateType? "1" : ("" + data.templateType)
        Object.assign(fm, res.data);
      }).catch(this.endload);
    },
    /** 提交按钮 */
    submitEditForm() {
      this.$refs["editForm"].validate(valid => {
        const fm = this.editForm;
        fm.status = 0;
        const preferredFlag = fm.preferred;
        fm.preferred = preferredFlag? 0 : 1;
        
        this.revExamResultProp();

        this.revExamItem();

        if (valid) {
          this.loading = true;

          if (fm.id) {
            api.update(fm).then(res => {
              this.endload();

              this.$modal.msgSuccess("修改成功");
              this.refreshCategory(fm);

              this.bubbleRefresh(fm);

              fm.preferred = preferredFlag;
            }).catch(this.endload);
          } else {
            api.save(fm).then(res => {
              this.endload();

              this.$modal.msgSuccess("新增成功");
              this.refreshCategory(fm);

              this.bubbleRefresh(res.data);
              //
              this.resetEditForm();
            }).catch(this.endload);
          }
        }
      });
    },
    setTemplateVis(){
      this.editForm.parent={}
    },
    // 取消按钮
    cancelEdit() {
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.$refs.tplTree.removeTemplate("T" + row.id);
        //
        this.resetEditForm();
      }).catch(() => {});
    },
    //点击树节点触发
    handleNodeClick(node,tree) {
      if(!auth.hasPermi('template-edit:level1')&&!auth.hasPermi('template-edit:level2')&&0==node.level) {
        this.$modal.msgSuccess("无法选择");
        return;
      }

      this.resetEditForm();
      if(!node) {
        return;
      }
      this.resset_cataTreeGrade()
      this.nodeTreeGrade(tree)
      let ndat = node.data;
      if(node.isTemplate) {
        this.handleUpdate(ndat);
      } else {
        ndat = cloneDeep(ndat);
        //下拉选择框
        ndat.id = "" + ndat.id;
        this.editForm.category = ndat;
        
      }
      
    },

    handleCheckedExamParts(node, state) {
      //
      let checkedParts = this.$refs["examPartsTree"].getCheckedNodes();
      //console.log(checkedParts);
      let examParts = [];
      for(let i = 0, parts; i < checkedParts.length; i ++) {
        parts = checkedParts[i];
        examParts.push({partsCode: parts.id});
      }
      this.editForm.examParts = examParts;
    },

    //阴阳性字典
    revExamResultProp() {
      let fm = this.editForm, examResultProp = fm.examResultProp;
      if(examResultProp && examResultProp.dictValue) {
        const dictData = this.dict.type["uis_exam_result_prop"];
        let dict = dictData.find(d => examResultProp.dictValue === d.value);
        examResultProp = dict && dict.raw;
      }
      fm.examResultProp = examResultProp || {};
    },
    //检查项目
    revExamItem() {
      let fm = this.editForm, examItem = [], examItem_dictValue = fm.examItem_dictValue;;

      if(examItem_dictValue && examItem_dictValue.length) {
        //检查项目字典
        const dictData = this.dict.type["uis_exam_item"];
        examItem_dictValue.forEach(dv => {
          let dict = dictData.find(d => dv === d.value);
          if(dict && (dict = dict.raw)) {
            examItem.push(dict);
          }
        });
      }

      fm.examItem = examItem;
    },
    //插入关键短语
    updateFormField(nam, val) {
      this.$set(this.editForm, nam, val);
    },
    //刷新节点
    refreshCategory(tpl) {
      let cate = tpl.category;
      cate = {id: cate.id, uid: ('C' + cate.id)};
      api.find({category: cate}).then(res => {
        this.$refs.tplTree.addTplToCate(cate, res.rows);
      })
    },
    //结束加载中
    endload() {
      this.loading = false;
    },
    //读取部门树信息
    buildCateTree() {
        let param={};
        cate_api.treeselect(param).then(res => {
          this.initTreeNode(res.data,0,false);
        this.cateTreeDataTemp = res.data;
      });
    },

    initTreeNode(nodes, treeLevel,parentShow) {
      let vm = this;
      if(!nodes) {
        return;
      }

      nodes.forEach(n=>{
        n["level"] = treeLevel;
        let localParentShow = parentShow;
        if(0==treeLevel){
          if(!auth.hasPermi('template-edit:level1')&&!auth.hasPermi('template-edit:level2')) n.isDisabled = true;
          for (let a of vm.examModality) {
            for (let b of n.data.examModality) {
              if (a.dictValue==b.dictValue) {
                localParentShow =  true;
                break;
              }
            }
          }
        }
        n["show"] = localParentShow;
        vm.initTreeNode(n.children, treeLevel+1,localParentShow);
      })
    },

    //解析树node获取各个目录的下拉框
    nodeTreeGrade(node){
        //console.log(node)
        if(!node.parent){
            //this.cataTreeGrade[cateTreeTemp]=node.data
        }else if(node.isLeaf && !node.data.isTemplate){
            var value='cateTreeVale_'+node.level
            this.cataTreeGrade[value]=node.data.label
            this.nodeTreeGrade(node.parent)
        }
        else if(node.isLeaf && !!node.data.isTemplate){
            
            this.nodeTreeGrade(node.parent)
        }
        else{
            var temp= 'cateTree_'+node.level
            var value='cateTreeVale_'+node.level
            this.cataTreeGrade[temp]=node.parent.childNodes
            this.cataTreeGrade[value]=node.data.label
            //this.cataTreeGrade[temp].value=node.data.id
            this.nodeTreeGrade(node.parent)
        }
        
    },
    //确保切换时显示正常
    companyChange(val) {
        var temp='cTree_'+val
        //this.$refs[temp].createdLabel = null
      },
     //调整左右大小
     dragControllerDiv: function () {
        var resize = document.getElementsByClassName('resize');
        var left = document.getElementsByClassName('left');
        var mid = document.getElementsByClassName('mid');
        var box = document.getElementsByClassName('box');
        for (let i = 0; i < resize.length; i++) {
            // 鼠标按下事件
            resize[i].onmousedown = function (e) {
                //颜色改变提醒
                resize[i].style.background = '#818181';
                var startX = e.clientX;
                resize[i].left = resize[i].offsetLeft;
                // 鼠标拖动事件
                document.onmousemove = function (e) {
                    var endX = e.clientX;
                    var moveLen = resize[i].left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
                    var maxT = box[i].clientWidth - resize[i].offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

                    if (moveLen < 150) moveLen = 150; // 左边区域的最小宽度为150px
                    if (moveLen > maxT - 1300) moveLen = maxT - 1300; //右边区域最小宽度为1300px

                    resize[i].style.left = moveLen; // 设置左侧区域的宽度

                    for (let j = 0; j < left.length; j++) {
                        left[j].style.width = moveLen + 'px';
                        mid[j].style.width = (box[i].clientWidth - moveLen - 10) + 'px';
                    }
                };
                // 鼠标松开事件
                document.onmouseup = function (evt) {
                    //颜色恢复
                    resize[i].style.background = '#d6d6d6';
                    document.onmousemove = null;
                    document.onmouseup = null;
                    resize[i].releaseCapture && resize[i].releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
                };
                resize[i].setCapture && resize[i].setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
                return false;
            };
        }
    },

    bubbleRefresh(item) {
      this.triggerBind("refresh", cloneDeep(item));
    }
  },

  computed: {
    ...mapGetters(['examModality']),
    editable() {
      const fm = this.editForm;
      return !!fm.category && !!fm.category.id;
    },
    cateTreeData(){
      let cateTreeData=[]
      if(this.cateTreeDataTemp.length<0){
        return cateTreeData
      }

      this.cateTreeDataTemp.forEach(e=>{
        if(e.data.personalFlag==this.editForm.personalFlag&&e.show){
          cateTreeData.push(e)
        }
      })
      return cateTreeData
    }
  },

  created() {
  //activated() {
    this.buildCateTree();
  },

  mounted() {
      this.dragControllerDiv()
  },
  watch:{
    "editForm.examParts": {
      handler(newv, oldv) {
        let examParts = newv;
        this.editForm.examPartsIds = examParts? examParts.map(p => p.id) : null;
      },
      deep: true
    },

    'cataTreeGrade.cateTreeVale_3':{
        handler(newVal, oldVal) {
            if(undefined!=this.$refs.cTree_3&&!!this.$refs.cTree_3.createdLabel){
                this.cataTreeGrade.cateNew_1=true
            }else{
                this.cataTreeGrade.cateNew_1=false
            }
            //console.log(newVal, oldVal)
        },
        deep: true
    }
    
  }
};

export default model;