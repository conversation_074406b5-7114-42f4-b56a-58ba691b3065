import Contextmenu from '@/components/Contextmenu';
import BaseFormInputInsertor from "@/assets/scripts/pacs/BaseFormInputInsertor";
import * as api from "@/assets/scripts/pacs/tplcfg/template/api";

import TemplateCategoryTree from "@/views/pacs/tplcfg/category/comp/TemplateCategoryTree";

//
import {getConfigKey} from "@/api/system/config";

const model = {
  dicts: ['uis_report_template_private_flag'],
  extends: BaseFormInputInsertor,
  components: {TemplateCategoryTree, Contextmenu},

  props: {
    wrapClass: {type: Object},
    params: {type: Object},
    htooltip: {type: Boolean, default: true},
    priflag: {type: Boolean, default: false},
    targets: {type: Array},
    menu: {type: Boolean, default: false},
  },

  data() {
    return {
      privateFlag: "0",
      templateData: null,
      //单选标志
      radioFlag:"-",
      //多选标志
      checkboxFlag:"+",
    };
  },

  methods: {
    init(){
      this.$refs.cateTree.init();
    },
    //点击类别，加载模板
    loadTemplates(treeData) {
      //console.log(treeData);
      //读取模板
      api.find(this.params).then(res => {
        let dat = res.rows;
        if(!dat || !dat.length) {
          return;
        }
        
        this.templateData = dat;

        treeData.forEach(cate => {
          this.addTplToCate(cate, dat);
        });
        this.setTemplateVis(this.privateFlag);
      });
    },
    //将模板添加到类别
    addTplToCate(cate, tplsDat) {
      const cateId = cate.id;
      if(!cateId) {
        return;
      }
      //
      let cateTree = this.$refs.cateTree;
      if(!cateTree) { return ; }
      tplsDat.forEach(tpl => {

        //处理结构化模板格式
        if(2==tpl.personalFlag){
          tpl["examDescAr"] = this.getExamDescAr(tpl.examDesc);
        }

        //类别
        if(tpl.category && tpl.category.id === cateId) {
          const nod = {id: tpl.id, label: tpl.templateName, data: tpl
            , isTemplate: true
            , uid: ("T" + tpl.id)
            , className: "node-template", icon: "el-icon-document",show:cate.show};
          cateTree.appendNode(nod, cate.uid);
        }
      });
      //
      let sub = cate.children;
      if(sub) {
        sub.forEach(s => {
          if(s.data && s.data.cateName) {
            this.addTplToCate(s, tplsDat);
          }
        });
      }
    },

    //获取结构化模板数组格式
    getExamDescAr(examDesc){
      var resAr = [];
      if(undefined==examDesc) return; 
      //分行
      var strRowAr = examDesc.split("\n");
      
      // strAr.forEach(e=>{
      //   resAr.push(this.handleLineToAr(e));
      // });

      strRowAr.forEach(e=>{
        //逐行处理单选标志
        var strRadioAr = this.handleLineToAr(e,this.radioFlag,1);
        // console.log("strRadioAr",strRadioAr);
        var strRadioCheckboxAr=[];
        strRadioAr.forEach(a=>{
          if(a.isStr==0){
            //逐行处理多选标志
            var strRowCheckboxAr = this.handleLineToAr(a.str,this.checkboxFlag,2);
            // console.log("strRowCheckboxAr",strRowCheckboxAr);
            strRadioCheckboxAr = strRadioCheckboxAr.concat(strRowCheckboxAr)
          }else{
            strRadioCheckboxAr.push(a);
          }
        });
        // console.log("strRadioCheckboxAr",strRadioCheckboxAr);
        resAr.push(strRadioCheckboxAr);
      });
      return resAr
          
    },

    //处理一行字符，返回一个数组
    handleLineToAr(str,flag,isFlag){
      //分组标识
      // var flag = '-'

      var resAr = [];
      //-起始位置
      var indexB = str.indexOf(flag); 
      //-结束位置
      var indexE = -1;
      
      //可选择字符
      var checkBoxAr = [];

      while(indexB !== -1) {
        //起始位置在中间，且不是紧跟上一个结束位置
        if(indexB>0&&indexB!=indexE+1){
          //保存可选择字符
          if(checkBoxAr.length>0){
            resAr.push({isStr:isFlag,select:1==isFlag?0:[],str:checkBoxAr});
            checkBoxAr = [];
          }
          //保存文本字符
          let text = {isStr:0,select:null,str:str.substring(indexE+1,indexB)};
          resAr.push(text);
        }

        // 从-开始位置查找-结束位置
        indexE = str.indexOf(flag,indexB + 1); 

        if(indexE<=0) break;

        //把- - 中间字符存入可选择字符数组
        if(indexE>0){
          checkBoxAr.push(str.substring(indexB+1,indexE));
        }
        
        indexB = str.indexOf(flag,indexE + 1);
      }

      //结尾处理
      if(checkBoxAr.length>0){
        resAr.push({isStr:isFlag,select:1==isFlag?0:[],str:checkBoxAr});
      }

      //结尾处理
      if(indexE<str.length-1){
        var lastText = str.substring(indexE+1,str.length);
        resAr.push({isStr:0,select:null,str:lastText});
      }
      return resAr;
    },

    //单击事件
    handleNodeClick(node,tree) {
      this.triggerBind("click", node,tree);
    },
    //双击事件
    handleNodeDblclick(node,tree) {
      //console.log(node)
      if(node.data.templateType==1&&!this.menu) this.onSelect(node.data.examDesc)
      else this.triggerBind("dblclick", node,tree);
      
    },
    //在树删除
    removeTemplate(uid) {
      this.$refs.cateTree.removeNode(uid);
    },
    //树节点右键菜单
    showNodeContextmenu(evt, node, treeNode, vn) {
      const contextmenu = this.$refs.tplTreeContextmenu;
      if(node.isTemplate) {
        contextmenu.show(evt, node);
      } else {
        contextmenu.show(evt, node);
        // contextmenu.hide();
      }
    },
    //点击树节点右键菜单
    clickNodeContextmenu(menu, node) {
      this.triggerBind("click", node);
    },
    //添加分类
    addCate(cateDat) {
      let cateTree = this.$refs.cateTree;
      let cate = {id: cateDat.id, label: cateDat.cateName, data: cateDat};
      const pcateDat = cateDat.parent;
      let pcate = !!pcateDat && !!pcateDat.id? {id: pcateDat.id, label: pcateDat.cateName, data: pcateDat} : null;
      cateTree.genuid([cate, pcate]);
      cateTree.appendNode(cate, pcate);
    },
    //
    setTemplateVis(value) {
      if(!this.priflag || !this.templateData) { return; }

      this.$refs.cateTree.setTemplateVis(value);
    }
  },

  created() {
    getConfigKey("eis.StructuredTemplateFlag").then(res => {
        if(!res || !res.msg) { return; }
        var flagAr = res.msg.split(",");

        this.radioFlag = flagAr[0];
        //多选标志
        this.checkboxFlag = flagAr[1];
        
      });
  },
};

export default model;