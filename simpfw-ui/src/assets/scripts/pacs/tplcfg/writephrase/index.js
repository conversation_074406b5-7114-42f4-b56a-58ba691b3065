import '@/assets/styles/pacs/common.css';

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/tplcfg/writephrase/api";

import WritePhraseTree from "@/views/pacs/tplcfg/writephrase/comp/WritePhraseTree";

let model = {
  name: "WritePhrase",
  extends: BaseGridModel,
  dicts: [ 'uis_report_content_item', 'uis_exam_item' ],
  components: { WritePhraseTree },
  data() {
    let dat = {
      queryForm: { }

      , editForm: {
        id: null
        , parent: {}
        , phraseContent: null
        , contentItem: {}
        , examItem: []

        , examItem_dictCode: []
      }
    };
    
    return dat;
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.queryForm).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
      });
    },
    // 表单重置
    resetEditForm() {
      const fm = this.editForm;
      fm.id = fm.phraseContent = null;
      fm.parent = {};
      fm.contentItem = {};
      fm.examItem = [];
      fm.status = true;

      fm.examItem_dictCode = [];
    },
    /** 新增按钮操作 */
    handleAdd(mix) {
      this.resetEditForm();
      //
      if(mix && mix.id) {
        this.editForm.parent = mix;
      } else if(this.queryForm.parent) {
        this.editForm.parent = this.queryForm.parent;
      }

      this.editForm.visible = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.resetEditForm();
      api.get(row.id).then(res => {
        let fm = this.editForm, data = res.data;
        data.parent = data.parent || {};
        //
        data.contentItem = data.contentItem || {};
        //
        if(data.examItem && data.examItem.length) {
          data.examItem_dictCode = [];
          data.examItem.forEach(e => data.examItem_dictCode.push(e.dictCode));
        }
        //
        data.status = 0 === data.status? true : false;

        Object.assign(fm, res.data);
        fm.visible = true;
      });
    },
    /** 提交按钮 */
    submitEditForm: function() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const fm = this.editForm;
          //
          let examItem = null;
          if(fm.examItem_dictCode) {
            examItem = [];
            fm.examItem_dictCode.forEach(dc => examItem.push({dictCode: dc}));
          }
          fm.examItem = examItem;
          //
          fm.status = fm.statusFlag? 0 : 1;
          //保存成功后调用
          let succ = () => {
            fm.visible = false;
            this.getList();

            this.$refs.writePhraseTree.buildTree();
          };
          //更新/添加
          if (fm.id) {
            api.update(fm).then(res => {
              this.$modal.msgSuccess("修改成功");

              succ();
            });
          } else {
            api.save(fm).then(res => {
              this.$modal.msgSuccess("新增成功");

              succ();
            });
          }
        }
      });
    },
    // 取消按钮
    cancelEdit() {
      this.editForm.visible = false;
      this.resetEditForm();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除？').then(function() {
        return api.del(row.id);
      }).then(() => {
        this.getList();
        
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleNodeClick(node) {
      this.queryForm.parent = node.data;
      this.getList();
    },

    colFmt_parent(row, col, val, idx) {
      if(!val) {
        return null;
      }
      const colProp = col.prop;
      return val[colProp];
    }
  }
};

export default model;
