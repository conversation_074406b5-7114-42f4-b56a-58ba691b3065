import { MessageBox } from 'element-ui';

import {undefinedOrNull} from "@/utils/common";//triggerBind,

//登记为0，已检查为1，已报告为2，已审核为3，复审状态为4，已打印为5，已完成为6，已取消为10
const StatusDict = {
  regist: "0",
  exam: "1",
  report: "2",
  audit: "3",
  second_audit: "11",
  third_audit: "12",
  reaudit: "4",
  print: "5",
  archive: "6",
  cancel: "10"
};
export {StatusDict};


/**
 * 检查是否为登记完成或已检查状态
 * 用于判断是否可执行“更改机房”、“检查呼叫”等操作
 */
export function examReachable(exam) {
  if(!exam || !exam.id) {
    MessageBox.alert("请选择检查。", "系统提示");
    return 0;
  }
  const resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
  //登记完成和已检查的可进行呼叫
  if(!undefinedOrNull(resultStatus) && !/^[01]$/.test(resultStatusCode)) {
    MessageBox.alert("该检查无法执行此操作，原因："  + resultStatus.dictLabel, "系统提示");
    return 0;
  }

  return 1;
}

/**
 *
 */
export function matchAny(exam, ...codes) {
  //console.log(exam, codes);
  const statusCode = !!exam && !!exam.resultStatus? exam.resultStatus.dictValue : null;

  if(undefinedOrNull(statusCode)) { return false; }

  return -1 != codes.findIndex(c => c === statusCode);
}

export function isFinalAuditStatus(exam) {
  const statusCode = !!exam && !!exam.resultStatus? exam.resultStatus.dictValue : null;
  const finalAuditStatusCode = exam.finalAuditStatus.dictValue;
  return statusCode === finalAuditStatusCode;
}
