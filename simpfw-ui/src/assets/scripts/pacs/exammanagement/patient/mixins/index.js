import Cookies from 'js-cookie'
import {cloneDeep} from "lodash";


//默认/初始配置
const PatiemtOptionsDef = {
  registWay:null,
  registFormxamModality:null,
  registFormInpType:null,
  registFormExamItem:null,
  createTimeValue:7,
  equipExamItemCode:null,
  examItemCodes:[],
  ocrExamItemCodes:[],
  inspectionOrg: {dictValue: null, extend: {
      extendI1: null
  }},
  resultStatusValues: [],
};
//export {SearchOptionsDef};
//
export const PatiemtOptions = {
  data() {
    return {
      mainForm: cloneDeep(PatiemtOptionsDef)
    }
  },

  methods: {
    //取
    read(cacheKey) {
      cacheKey = cacheKey || this.cacheKey;
      //console.log(cacheKey);
      if(!cacheKey) {
        return null;
      }
      let cache = Cookies.get(cacheKey);
      console.log(cache);
      if(cache) {
        let val = JSON.parse(cache);
        if(!!val.reportDoctor) {
          delete val.reportDoctor;
        }
        if(!!val.examDoctorUserNames) {
          delete val.examDoctorUserNames;
        }
        return val;
      }
      return cloneDeep(PatiemtOptionsDef);
    },
    //存
    save() {
      const cacheKey = this.cacheKey;
      console.log("idex",cacheKey);
      if(!cacheKey) {
        return null;
      }
      Cookies.set(cacheKey, JSON.stringify(this.mainForm), { expires: 7 });
    }
  }
}
