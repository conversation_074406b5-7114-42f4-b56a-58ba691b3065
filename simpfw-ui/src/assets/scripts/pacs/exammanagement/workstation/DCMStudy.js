import store from "@/store/index";

import {cloneDeep} from "lodash";

import Request from "@/utils/request.js";

import {undefinedOrNull, currDate} from "@/utils/common";
//科室信息
import { treeselect as deptTreeselect } from "@/api/system/dept";
//检查信息
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";
//设备信息
import {findDevices} from "@/assets/scripts/pacs/equiproom/dicominfo/api";
//机房信息
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';

import {EditModel, TransModel, UndoModel} from "@/assets/scripts/uis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//检查单
import ExamViewer from '@/views/uis/exammanagement/patient/comp/ExamViewer';
//import ExamEquipRoom from '@/views/uis/exammanagement/patient/comp/ExamEquipRoom';

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth"

//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";

import java from "highlight.js/lib/languages/java";



//表单标识
const formSimp = "simp";
//右键菜单
const TableContexmenuItems = [
    {cmd: 'img::toExam', name: '匹配', disabled: false},
    {cmd: 'img::cancelToExam', name: '取消匹配', disabled: false},
    {cmd: 'exam::dicomView', name: '打开图像', permissions: []}
    // {cmd: 'img::del', name: '删除影像', disabled: false}
];
//export {TableContexmenuItems};
function tableContextmenuItems() {
  const userPermissions = store.state.user.permissions, superPerm = "*:*:*";
  if(userPermissions.includes(superPerm)) {
    return TableContexmenuItems;
  }

  return TableContexmenuItems.filter(e => !e.permissions || e.permissions.length === 0 || userPermissions.includes(e.permissions[0]));
};
export {tableContextmenuItems};

//逻辑
export default {
  name: "DCMStudy",

  extends: BaseGridModel,

  mixins: [EditModel, TransModel, UndoModel],

  components: { Contextmenu, ExamViewer, OperationAuth },//, ExamEquipRoom

  dicts: ["uis_exam_item", "uis_inp_type", "uis_exam_modality", "uis_gender_type", "uis_exam_result_status"],

  props: {
    isNoExamNo: {type: Boolean, default: false},
  },

  data() {
    return {

      searchFormVisible: false,
      /**
       * 搜索框
       */
      searchForm:{
        id: null,
        examNo: null,
        examItemCodes: [],
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModality: {dictCode: null},
        examDoctor: {nockName: null},
        reportDoctor: {nockName: null},
        auditDoctor: {nockName: null},

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        },

        examInfo: {
          examNo: null,
        },

        inpTypeValues: [],
        noExamNo: false
      },

      /**
       * 当前
       */
      currentTableRow: null,
      currentTableRowStr: null,

      tableAction: tableContextmenuItems(),

      deptTreeData: [],

      searchFormSimp: {
        id:null,
        textFieldName: "patientInfo.name",
        textField: null,

        dateFieldName: "createTime",
        dateFieldGe: currDate(),
        dateFieldLt: currDate(),

        inpTypeValues: [],
        examDevicesCode: [],
        resultStatusValues: [],
        equipRoomsCode: []
      },
      //当前查询表单
      lastSearchForm: formSimp,
      //
      combo: {
        searchTextFields: [
          {name: "patientInfo.name", label: "姓名"},
          {name: "examInfo.examNo", label: "检查号"}
        ],
        searchDateFields: [
          {name: "examTime", label: "检查日期"},
          {name: "createTime", label: "接收日期"},
          // {name: "auditTime", label: "审核日期"},
          // {name: "applyTime", label: "预约日期"},
        ],
        //设备型号列表
        devices: [],
        //房间列表
        equipRooms: [],
        //当前选中检查项
        currentExam: null,
      }
    };
  },

  methods: {
    // setNoType(typ){
    //   console.log(typ)
    //   this.isNoExamNo = typ;
    // },
    /**
     * 搜索
     */
    getList(mix) {
      let params;
      //
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      if(formSimp ===  this.lastSearchForm) {
        //
        params = cloneDeep(this.searchFormSimp);
        //
        if(!!params.textField) {
          params[params.textFieldName] = params.textField;
        }
        delete params.textFieldName;
        delete params.textField;
        //
        if(!!params.dateFieldGe || !!params.dateFieldLt) {
          params[params.dateFieldName + 'Ge'] = params.dateFieldGe || null;
          params[params.dateFieldName + 'Lt'] = params.dateFieldLt || null;
        }
        delete params.dateFieldName;
        delete params.dateFieldGe;
        delete params.dateFieldLt;
      } else {
        params = cloneDeep(this.searchForm);
      }

      if(params.examItemCodes) {
        params.examItemCodes.forEach((c, i) => {
          params["examItemCodes[" + i + "]"] = c;
        });
        delete params["examItemCodes"];
      }
      //
      //if(!(mix instanceof Event) && (mix instanceof Object)) {
      //  if(mix.page) { params.pageNum = mix.page; }
      //  if(mix.limit) { params.pageSize = mix.limit; }
      //}
      //已删除状态
      let pos;
      if(!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
          params.resultStatusValues.splice(pos, 1);
          //params.resultStatusAsStatus = "2";
          params.status = 2;
      } else {
        params.status = 0;
      }

      params.noExamNo = this.isNoExamNo

      this.loading = true;
      imapi.findStudyVoList(params).then(res => {
        this.loading = false;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    },

    /**
     * 双击，显示图像
     * @param item 选择的检查记录
     */
    dblclickTableRow(item) {
      if (item.id != undefined){
        this.$emit("showImage", null,item.studyInstanceUid);
        // eiapi.get(item.id).then(res => {
        //   vm.currentTableRow = res.data;

        //   vm.currentTableRowStr = "姓名：" + (vm.currentTableRow.patientInfo.name || "-")
        //   + " 年龄：" + (vm.currentTableRow.patientInfo.age || "-")
        //   + " 影像号：" + (vm.currentTableRow.patientInfo.patientId || "-")
        //   + " 电话：" + (vm.currentTableRow.patientInfo.homePhone || "-")
        //   + " 地址：" + (vm.currentTableRow.patientInfo.address || "-")
        //   + "\r\n临床诊断：" + (vm.currentTableRow.clinicDiagnosis || "-")
        //    this.$emit("showImage", vm.currentTableRow)
        // })
      }
      
    },

    /**
     * 查看检查记录
     * @param item 选择的检查记录
     */
    selectTableRow(row, column) {
      var vm = this;
      console.log("selectTableRow",row);
      //
      vm.currentTableRow = row;
      this.$emit("changeCurrentStudy", vm.currentTableRow)
    },

    //查询
    search(opt) {
      this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号/检查房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }
      //登记房间
      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },
    formatPatientId(row) {
        if (row.examUid && row.patientInfo) {
            return row.patientInfo.registNo;
        } else {
            return row.patientIdOfStudy;
        }
    },
    formatPatientName(row) {
        if (row.examUid && row.patientInfo) {
            return row.patientInfo.name;
        } else {
            return row.patientName;
        }
    },

    //显示检查详情
    handleDetail(row) {
      //this.$modal.msg("未实现");
      this.$refs.examViewer.view(row);
    },

    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    // handleUndoDelete(row) {
    //   eiapi.undoDel(row.id).then(res => {
    //     if(res && 200 === res.code) {
    //       this.$modal.msgSuccess("执行成功。");
    //       this.getList();
    //     } else {
    //       this.$modal.msgError(res && res.msg || "执行失败。");
    //     }
    //   });
    // },
    //表格行右键菜单
    showTableAction(row, col, evt) {
      evt.preventDefault();
      var menuItems = tableContextmenuItems();
      for (var index in menuItems) {
        var item = menuItems[index];
        if (item.cmd == 'img::toExam') {
            item.disabled =  1==row.status ? false: true;
        }
        if (item.cmd == 'img::cancelToExam') {
            item.disabled = 1==row.status? true: false;
        }
      }
      this.$refs["tableContextmenu"].show(evt, row);
    },
    //右键菜单
    async handleTableAction(item, row) {
      let vm = this;
      switch(item.cmd) {
        case 'img::toExam':
          if(this.currentExam == undefined){
            this.$modal.msg("请选择一个检查");
            return;
          }

          if(this.currentTableRow == undefined){
            this.$modal.msg("请选择一个Study");
            return;
          }

          /*
          if(this.currentTableRow.modalities!=this.currentExam.examModality.dictCode){
            if(await this.$modal.confirm('设备类型不一致，是否继续匹配', '确认信息', {
              distinguishCancelAndClose: true,
              confirmButtonText: '是',
              cancelButtonText: '否'
            }).catch(() => {}) !== 'confirm') {
              this.$modal.msg("取消匹配");
              return
            }
          }

          if(this.currentTableRow.patientInfo==undefined||(this.currentTableRow.patientInfo.name!=this.currentExam.patientInfo.name)){
            if(await this.$modal.confirm('患者姓名不一致，是否继续匹配', '确认信息', {
              distinguishCancelAndClose: true,
              confirmButtonText: '是',
              cancelButtonText: '否'
            }).catch(() => {}) !== 'confirm') {
              this.$modal.msg("取消匹配");
              return
            }
          }

          let dicomStudieAr= [];
          dicomStudieAr.push({
            studyInstanceUid:this.currentTableRow.studyInstanceUid,
            examUid: this.currentExam.examUid,
            studyDate:this.currentTableRow.studyDate,
            id:this.currentTableRow.id});

          const nrow = {id: this.currentExam.id, dicomStudies:dicomStudieAr,resultStatus: {dictValue: ResultStatusDict.exam}};
          eiapi.examMatchingImg(nrow).then(res => {
            this.$modal.msg("匹配成功");
            this.$emit("refresh");
          })
          */

          if(vm.currentExam.patientInfo==undefined||(vm.currentTableRow.patientName!=vm.currentExam.patientInfo.name)
          ||(vm.currentTableRow.patientSex!=vm.currentExam.patientInfo.gender.dictValue)
          ||!this.currentExam.examModality.dictValue.includes(this.currentTableRow.modalities)
          ){
            if(await this.$modal.confirm('该检查与图像信息不匹配，是否确定匹配', '确认信息', {
              distinguishCancelAndClose: true,
              confirmButtonText: '是',
              cancelButtonText: '否'
            }).catch(() => {}) !== 'confirm') {
              // this.$modal.msg("取消匹配");
              return
            }
          }

          // 20230821
          var queryParams = {
            "examInfoId": vm.currentExam.id,
            "examUid": vm.currentExam.examUid,
            "accessionNumber": vm.currentExam.accessionNumber,
            "studyPkId": vm.currentTableRow.id,
            "studyInstanceUid": vm.currentTableRow.studyInstanceUid,
          };

          Request.post("/exammanagement/examInfo/matchExamAndStudy", queryParams)
            .then(res => {
                this.$modal.msg("匹配成功");
                this.$emit("refresh");
            })
            .catch(function (error) {
            });
          break;

        // 20230821
        case 'img::cancelToExam':
          if(this.currentTableRow == undefined){
            this.$modal.msg("请选择一个Study");
            return;
          }
          if(await this.$modal.confirm('是否确定取消该Study图像与检查的匹配关系', '确认信息', {
            distinguishCancelAndClose: true,
            confirmButtonText: '是',
            cancelButtonText: '否'
          }).catch(() => {}) !== 'confirm') {
            return
          }
          // 20230821
          var queryParams = {
            "examUid": vm.currentTableRow.examUid,
            "studyPkId": vm.currentTableRow.id,
            "studyInstanceUid": vm.currentTableRow.studyInstanceUid,
          };
          Request.post("/exammanagement/examInfo/cancelMatchedExamAndStudy", queryParams)
            .then(res => {
                console.log(res);
                this.$modal.msg("取消匹配成功");
                this.$emit("refresh");
            })
            .catch(function (error) {
                console.log(error);
            });
          break;
        case 'exam::dicomView':
          let toRoute2 = 'RadReportWriterImage'
          this.$router.push({name: toRoute2, query: {exam: null,studyInstanceUid:this.currentTableRow.studyInstanceUid}});
          //console.log("ReportWriting.js-exam::traceCase-item: ", row);
          //this.$router.push({ name: "TraceCaseAddEdit", params: {"examNo": row.examNo}});
          break;

        // case 'img::del':
        //   this.$modal.msg("删除影像");
        //   break;
        default:
          console.log("未实现");
      }
    },
    /**
     * 诊前准备或下午检查
     * @param props {'examPrerequire':0}-诊前准备状态码, {'examAtPm':1}-下午检查状态码
     */
    handlePrecond(row, props) {
      this.handleTrans(row, props).then(res => {
        if(res && 200 === res.code) {
          this.getList();
        }
      });
    },

    callUndoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if(res && 200 === res.code) {
          this.getList();
        }
      });
    },

    toggleSearchForm() {
      this.searchFormVisible = !this.searchFormVisible;
    },
    //读取部门树信息
    buildDeptTree() {
      deptTreeselect().then(res => {
        this.deptTreeData = res.data;
      });
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //读取设备型号列表
    findDevices() {
      findDevices().then(res => {
        this.combo.devices = res.data;
      });
    },
    //
    findEquipRooms() {
      findRoom({pageNo: 1, pageSize: 9999}).then(res => {
        this.combo.equipRooms = res.rows;
      });
    },

    changeCurrentExam(currentExam){
        this.currentExam = currentExam;
    },

    // 2023/08/01
    printDicomImages(images) {
        console.log("--------printDicomImages: ", images);
    },

  },

  created() {
    this.buildDeptTree();

    this.findDevices();

    this.findEquipRooms();
  }
};
