
import Patient from '@/views/pacs/exammanagement/workstation/Patient';
import Study from '@/views/pacs/exammanagement/workstation/DCMStudy';
import ExamView from '@/views/pacs/exammanagement/workstation/ExamViewer';
import ReportView from '@/views/pacs/exammanagement/workstation/ReportViewer';
import DcmView from '@/views/pacs/exammanagement/workstation/DCMViewer';
import DCMStudyViewer from '@/views/pacs/exammanagement/workstation/DCMStudyViewer';
import ReportWriterImage from '@/views/rad/report/ReportWriterImage';

export default {
  name: "WorkStation",

  components: {
    Patient,
    Study,
    "StudyNone": Study,
    ReportView: ReportView,
    ExamView,
    DcmView,
    DCMStudyViewer,
    ReportWriterImage,
  },

  data() {
    return {
      tabsName: {
        Study: "Study",
        StudyNone: "StudyNone",
        ReportView: "ReportView",
        ExamView: "ExamView",
      },
      currentTab: "Study",

      currentExam: null,
      currentStudy: null,
    };
  },

  methods: {
    handleAction(cmd, row) {

    },

    handleTabClick(tab){
      if(tab.name == "ExamView"){
        this.$refs.ExamView.view(this.currentExam)
      }
      if(tab.name == "ReportView"){
        this.$refs.ReportView.view(this.currentExam)
      }
      // if(tab.name == "StudyNone"){
      //   this.$refs.none.setNoType(true)
      // }
    },

    changeCurrentExam(row){
      this.currentExam = row;
      // 电子申请单
      this.$refs.ExamView.view(this.currentExam)
      // 检查报告
      this.$refs.ReportView.view(this.currentExam)
      // 未匹配图像
      this.$refs.none.changeCurrentExam(this.currentExam);
      // 全部图像
      this.$refs.all.changeCurrentExam(this.currentExam);
    },

    showImage(examInfoId, studyUId){
      this.$refs.ReportWriterImage.view(examInfoId,studyUId);
    },

    changeCurrentStudy(row){
      this.currentStudy = row;
      //this.$refs.DcmStudyView.write(this.currentStudy)
    },

    refresh(){
      this.$refs.all.search('simp');
      this.$refs.none.search('simp');
      this.$refs.patient.search('simp');
    },
  }
};
