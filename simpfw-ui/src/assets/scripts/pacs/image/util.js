export const Tags = {
  StudyInstanceUID: "0020000D"
  , StudyDate: "00080020"
  , StudyTime: "00080030"

  , SeriesInstanceUID: "0020000E"
  , SeriesDescription: "0008103E"
  , SeriesDate: "00080021"
  , SeriesTime: "00080031"
  , SeriesNumber: "00200011"
  
  , SOPInstanceUID: "00080018"
  , Rows: "00280010"
  , Columns: "00280011"
  , InstanceNumber: "00200013"
  , ModalityInStudy: "00080061"
  , OverlayType: "60000040"
  , TransferSyntaxUID: "00020010"
};

export const ImageIdsPrefix = {
  uri: "wadouri:",
  rs: "wadors:"
};

export const FakeUidPrefix = "***********.***********.99.99.99.";

export const FileTypes = {jpeg: {name: "jpg", code: "2"}, dicom: {name: "dcm", code: "1"}, video: {name: "vod", code: "3"}};
//获取dicom元素值
export function getTagValue(meta, tag) {
  let {[tag]: {Value: value} = {}} = meta;
  if(!!value && value.length) {
    return value[0];
  }

  return null;
}
//是否jpg影像
export function assertJpeg(uri) {
  return true;
  //return -1 !== uri.indexOf(FakeUidPrefix);
}
//影像数据请求协议
export function wadoForImage(uri) {
  if(assertJpeg(FakeUidPrefix)) {
    return uri;
  }
  return `${ImageIdsPrefix.uri}${uri}`;
}
//读取并显示影像
export function loadAndDisplay(element, imageId, fitWindow) {
  // 确保 imageId 唯一化，避免 HTTP 缓存
  const uniqueImageId = `${imageId}?_=${Date.now()}`;

  // 加载并显示图像
  return cornerstone.loadAndCacheImage(uniqueImageId).then(image => {
    //{columns: 0, rows: 0, height: 0, width: 0, sizeInBytes: 0...}
    //console.log(image);
    let viewport = cornerstone.getDefaultViewportForImage(element, image);
    cornerstone.displayImage(element, image, viewport);
    //
    if(fitWindow) {
      cornerstone.resize(element, true);
    }

    return image;
  });
}
//清除指定
export function removeImageCache(imageId) {
  let imageCache = cornerstone.imageCache;
  imageCache.removeImageLoadObject(imageId);
}
//下载影像
export function downloadBlob(element) {
  element.querySelector('canvas').toBlob(blob => {
    const URLObj = window.URL || window.webkitURL;
    const a = document.createElement('a');
    a.href = URLObj.createObjectURL(blob);
    a.download = file;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  });
}
//
//const ASPECT_WIDTH = 1920, ASPECT_HEIGHT = 1080;
const ASPECT_WIDTH = 1280, ASPECT_HEIGHT = 1024;
export {ASPECT_WIDTH, ASPECT_HEIGHT};
//保持影像宽高比
export const ASPECT_RATIO = ASPECT_WIDTH / ASPECT_HEIGHT;//(4 / 3);
//图像Base64格式
const PatImageBasse64 = /data:(image\/.+);base64,/;

export function base64ToBlob(data) {
  var mimeString = '';
  var raw, uInt8Array, i, rawLength;

  raw = data.replace(PatImageBasse64, function (header, imageType) {
    mimeString = imageType;

    return '';
  });

  raw = atob(raw);//Buffer.from(raw, 'base64')
  rawLength = raw.length;
  uInt8Array = new Uint8Array(rawLength);

  for (i = 0; i < rawLength; i += 1) {
    uInt8Array[i] = raw.charCodeAt(i);
  }

  return new Blob([uInt8Array], { type: mimeString });
}

/**
 * 是否压缩影像
 */
export function isCompressed(meta) {
  const txuid = getTagValue(meta, Tags.TransferSyntaxUID);
  
  return !!txuid && ("1.2.840.10008.1.2" != txuid
    && "1.2.840.10008.1.2.1" != txuid);

}