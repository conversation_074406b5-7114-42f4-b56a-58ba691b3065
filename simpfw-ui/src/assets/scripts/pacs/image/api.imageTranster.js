import request, {postForm} from '@/utils/request'
import { baseContext } from "@/assets/scripts/pacs/common";

const ctx = "/dicomImage/transfer";
//转移影像
export function transfer(imagesId, examInfoIdFrom, examInfoIdTo) {
  const url = ctx + "/transfer";
  return request.put(url, {imagesId, examInfoIdFrom, examInfoIdTo});
}
//导出影像
export function backport(imagesId, examInfoIdFrom) {
  const url = ctx + "/aqbackport";
  return request.post(url, {imagesId, examInfoIdFrom}).then(r => {
    let tk = r.tk, ts = r.ts;
    return `${baseContext}${ctx}/backport?tk=${tk}&_=${ts}`;
  });
  //return Promise.resolve(`${baseContext}${ctx}/backport?imageId=${imageId}&examInfoIdFrom=${examInfoIdFrom}`);
}
//导入影像
export function resback(form) {
  const url = ctx + "/resback";
  return request.post(url, form);
}

//批量导入影像
export function resbackBatch(form) {
  const url = ctx + "/resbackBatch";
  return request.post(url, form);
}

//导出影像
export function getPng(url) {
  return request.post(url,"11");
}

//获取结构化报告图片
export function saveDesignReport(form) {
  const url = ctx + "/saveDesignReport";
  return request.post(url, form);
}