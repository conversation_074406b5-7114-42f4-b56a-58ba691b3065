//影像相关工具
import {FileTypes} from "@/assets/scripts/pacs/image/util";

//dicom影像路径
const RE_Dcmurl = new RegExp("^[^/]+//[^/]+(/ext/dicom-web/rs/.+)$");

export const ReportImageFun = {

  methods: {

    //图像类型
    typeofVideo(image) {
      return FileTypes.video.name === image.fileType || FileTypes.video.code === image.fileType;
    },
    //图像类型
    typeofJpg(image) {
      return FileTypes.jpeg.name === image.fileType || FileTypes.jpeg.code === image.fileType;
    },
    //图像类型
    typeofDcm(image) {
      return FileTypes.dicom.name === image.fileType || FileTypes.dicom.code === image.fileType;
    },

    //采集的图像转成报告图像
    convImgToAtta(img) {
      if("report::image" === img.type) {
        return img;
      }
      let atta;
      //dicom
      if(this.typeofDcm(img)) {
        let fileUrl = img.fileUrl, grps = fileUrl? RE_Dcmurl.exec(fileUrl) : null;
        if(!grps) {
          return null;;
        }
        atta = {
          "path" : grps[1]
          , "fileType": FileTypes.dicom.name
        };
      } else {
        //动态/静态/其它
        atta = {
          "path": `${img.studyInstanceUid}/${img.seriesInstanceUid}/${img.sopInstanceUid}`
          , "fileType": FileTypes.jpeg.name
        };
      }
      return atta;
    }
  }
};