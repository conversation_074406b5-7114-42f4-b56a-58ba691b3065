import request,{postForm} from '@/utils/request'
import { baseContext } from "@/assets/scripts/pacs/common";

//
const studyCtx = "/dicomStudy";
//
const imageCtx = "/dicomImage";
//读取已采集的影像
export function getStudy(query) {
  return request.get(studyCtx + "/get", {params: query});
}
//读取已采集的影像
export function findStudies(query) {
  return request({
    method: "post",
    url: studyCtx + '/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}
//删除已采集的影像
export function delStudy(id) {
  return request.delete(studyCtx + "/del/" + id);
}
//保存采集
export function saveImage(roomCode, examInfoId, base64Data, fileUrl) {
  return postForm({url: studyCtx + "/saveImage"
    , data: {roomCode, examInfoId, seriesNumber: 1, base64Data, fileUrl}
    , timeout: (30 * 1000)
    , headers: {repeatSubmit: false}});
}
//删除报告的图像
export function delImage(id) {
  return request.delete(imageCtx + "/del/" + id);
}

//报告影像读取地址
export function imageLocate(img) {
  const {studyInstanceUid, seriesInstanceUid, sopInstanceUid} = img;
  return `${baseContext}${imageCtx}/locate/${studyInstanceUid}/${seriesInstanceUid}/${sopInstanceUid}`;
}

//获取后端采集的影像
export function gallary(roomCode = null, date = null) {
  return request.get(imageCtx + "/gallary", {params: {roomCode, date}});
}
//后端采集的影像读取地址
export function imageDetached(img) {
  const millis = new Date().getTime();
  return `${baseContext}${imageCtx}/detached?file=${img.fileUrl}&_=${millis}`;
}
//将后端采集图像挂到指定检查
export function saveImagesDetached(roomCode, examInfoId, images) {
  let study = {
    examInfoId: examInfoId,
    examInfo: {id: examInfoId, callInfo: {callRoom: {roomCode}}}
    , seriesSet: [
      {imagesSet: images}
    ]
  };
  return postForm({url: studyCtx + "/saveImagesDetached", data: study});
  //return request.post(studyCtx + "/saveImagesDetached", study);
}
//删除后端采集的影像
export function delImageDetached(images) {
  return request.post(imageCtx + "/gallary/delImages", images);
}
//影像浏览：dicom
export function dcmViewer(examInfoId, studyInstanceUid) {
  const url = imageCtx + "/dcmViewer?examInfoId=" + (examInfoId || "") + "&studyInstanceUid=" + (studyInstanceUid || "");
  return request.get(url, {timeout: (60 * 1000)});
}

export function indexImages(studyInstanceUid, seriesInstanceUid) {
  const url = baseContext + "/dicomStudy/v1/indexImages/studies/" + studyInstanceUid;
  return request.get(url, {timeout: (120 * 1000)});
}

//读取已采集的影像list
export function findStudyVoList(query) {
  return postForm({
    url: studyCtx + '/studyVo/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}
