import {undefinedOrNull} from "@/utils/common";
//
const SocketState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

const model = {
  data() {
    return {
      socketUrl: null,
      socket: null,
      isConnectedSocked: false,
      secondsReopenSocket: 8
    };
  },

  methods: {
    //打开socket
    openSocket() {
      let socket = this.socket;
      //
      if(socket && SocketState.CONNECTING === socket.readState) {
        socket.close();
      }
      //已连接
      if(this.isConnectedSocked) {
        return 2;
      }
      //
      if(!this.socketUrl) {
        this.$modal.msgError("未设置WebSocket地址。");
        return 1;
      }

      try {
        this.socket = socket = new WebSocket(this.socketUrl);
      } catch(err) {
        console.error(err);
        this.handleSocketError(err);
      }
      
      socket.addEventListener("open", this.handleSocketOpenMust);
      //socket.addEventListener("open", this.handleSocketOpen);
      socket.addEventListener("error", this.handleSocketError);
      socket.addEventListener("close", this.handleSocketClosedMust);
      //socket.addEventListener("close", this.handleSocketClosed);
      socket.addEventListener("message", this.handleSocketReceived);
      //
      window.onbeforeunload = this.closeSocket;

      return 1;
    },
    //socket断开触发
    handleSocketOpenMust(event) {
      //console.log("打开 WebSocket");
      this.isConnectedSocked = true;

      this.handleSocketOpen(event);
    },
    handleSocketOpen(event) {},
    //错误处理
    handleSocketError(event){
      console.error("错误: " + this.socketUrl, event);
      this.$modal.msgError("无法调用服务。");
    },
    //推送的数据
    handleSocketReceived(event) {
      //console.log(this, "接收", event);
    },
    //socket关闭触发
    handleSocketClosedMust() {
      this.isConnectedSocked = false;

      this.handleSocketClosed();
    },
    //socket关闭触发
    handleSocketClosed() { },
    //关闭socket
    closeSocket() {
      if(null != this.socket) { this.socket.close(); }
    },
    //发送消息
    handleSocketSend(mix) {
      if(undefinedOrNull(mix) || !this.socket) {
        return;
      }
      //
      if(SocketState.OPEN !== this.socket.readyState) {
        console.log("未连接。");
        return
      }
      let mess = mix;
      if("string" !== (typeof mess)) {
        mess = JSON.stringify(mess);
      }
      this.socket.send(mess);
    }
  },

  //computed: {
    //isConnectedSocked() {
    //  return !!this.socket && this.socket.readyState === WebSocket.OPEN;
    //}
  //},

  beforeDestroy() {
    this.closeSocket();
  }
};
export default model;