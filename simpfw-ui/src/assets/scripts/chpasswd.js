import { login, logout } from '@/api/login'
import { updateUserPwd } from "@/api/system/user";
import config from "@/assets/scripts/global/config";
import DesUtil from "@/utils/DesUtil";

export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/;
      if (!value) {
        callback(new Error("新密码不能为空"));
      } else if (!passwordPattern.test(value)) {
        callback(new Error("密码必须包含大小写字母和数字，且不少于8位"));
      } else {
        callback();
      }
    };

    return {
      passwdForm: {
        username: null,
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwdRules: {
        username: [
          { required: true, trigger: "blur", message: "账号不能为空" }
        ],
        oldPassword: [
          { required: true, trigger: "blur", message: "旧密码不能为空" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { required: true, validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" }
        ]
      }
    }
  },

  methods: {
    //登录->修改密码->退出
    chpasswd() {
      this.$refs.passwdForm.validate(valid => {
        if (!valid) {
          return;
        }

        let {username, oldPassword, newPassword, confirmPassword} = this.passwdForm;
        if(newPassword !== confirmPassword) {
          this.$modal.alert("两次输入的密码不一致")
          return;
        }

        username = DesUtil.encode(username, config.securityKey)
        oldPassword = DesUtil.encode(oldPassword, config.securityKey)
        newPassword = DesUtil.encode(newPassword, config.securityKey)
        confirmPassword = DesUtil.encode(confirmPassword, config.securityKey)

        this.$store.dispatch("Login", {username, password: oldPassword}).then(res => {
          return updateUserPwd(oldPassword, newPassword);
        }).then(res => {
          this.$modal.msgSuccess("修改成功");
          
          this.passwdForm = {
            username: null,
            oldPassword: null,
            newPassword: null,
            confirmPassword: null
          };

          this.logout();
        }).catch(e => {
          this.logout();
        });
      });
    },

    logout() {
      this.$store.dispatch("LogOut");
    }
  }
}