export default {
  data() {
    return {
      //信号源
      imageSources: [
        {value: "0", label: "信号1"},
        {value: "1", label: "信号2"},
        {value: "2", label: "信号3"},
        {value: "3", label: "信号4"},
      ],
      //
      imageSourcesSelected: "3",
      imageSourceChecked: ["3"]      
    }
  },

  computed: {
    //选择的采集来源
    effectViewports() {
      //非弹窗、单路
      if(!this.isPopup) {
        return [this.imageSourcesSelected];
      }
      //弹窗多路
      return this.imageSourceChecked;

    },
    //显示的采集窗宽
    viewportsFrame() {
      //选择的采集来源
      const imageSourceChecked = this.effectViewports;
      if(imageSourceChecked.length === 0 || imageSourceChecked.length == 1) { 
        return imageSourceChecked;
      }
      //
      let startNo = this.imageSources.length, endNo = -1;
      imageSourceChecked.forEach(n => {
        const nv = parseInt(n);
        startNo = Math.min(startNo, nv);
        endNo = Math.max(endNo, nv);
      });
      startNo = 2 * Math.floor(startNo / 2);
      //
      let viewports = [];
      for(let n = startNo; n <= endNo; n ++) {
        viewports.push('' + n);
      }
      return viewports;
    }
  },

  methods: {
    handleSelectImageSource(v) {
      this.imageSourcesSelected = v;
    },

    handleCheckImageSource(v) {
      console.log(v);
    }    
  }
};