import { mapGetters } from 'vuex';
import {cloneDeep} from "lodash";

//多路信号
import ImageSources from './mixins/ImageSources'

//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//患者信息查询
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//房间信息
import {params as getEquipRoomParams} from "@/assets/scripts/pacs/equiproom/api";
//影像宽高比
import {ASPECT_RATIO} from "@/assets/scripts/pacs/image/util";
//
import {StatusDict, matchAny} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//影像显示窗宽
import ImageMonitorViewport from '@/views/gis/report/comp/ImageMonitorViewport';
//采集的图像尺寸
function imageDime(availWidth, availHeight) {
  //
  let width = availWidth, height = width / ASPECT_RATIO;
  if(height > availHeight) {
    height = availHeight;
    width = availHeight * ASPECT_RATIO;
  }
  return {width, height};  
}
export {imageDime};
//静态/动态采集
const CollectMode = {Static: "STATIC", Dynamic: "DYNAMIC"}

const model = {
  mixins: [ImageSources],

  components: {ImageMonitorViewport},

  data() {
    return {
      examInfo: null,
      
      examInfoId: null,
      examInfoList: [],
      
      //呼叫检查的机房
      equipRoomParams: null,
      //
      isPopup: false,

      collectMode: CollectMode.Static,
      collectModesSupported: [CollectMode.Static],
      //
      isDeactivated: false,
      viewportSize: null
    }
  },

  methods: {
    //
    show(exam) {
      this.$nextTick(this.calcViewportSize);
      //是否选择了检查
      if(!exam || !exam.id) {
        this.$modal.msgWarning("请选择检查。");
      //  return;
      }
      //
      this.setExamInfo(exam);
      //检查是否允许采集：登记完成、已检查、已报告的允许采集
      if(!!exam && exam.id && (!this.examInfo || !this.examInfo.id)) {
        this.$modal.msgWarning("该检查不支持采集，原因：" + (exam.resultStatus? exam.resultStatus.dictLabel : "未知") + "。");
        return;
      }
      //检查列表
      this.findExamInfo();
      //
      this.prepareImage();
    },
    //当前采集的检查
    setExamInfo(exam) {
      //检查状态
      const collectable = !!exam && (!exam.resultStatus || !exam.resultStatus.dictValue || matchAny(exam, StatusDict.regist, StatusDict.exam, StatusDict.report));

      this.examInfo = collectable? exam : null;
      this.examInfoId = collectable && !!exam? exam.id : null;
      if(collectable && !!exam && !!exam.id) {
        //当前检查机房
        exam.equipRoom = this.currentEquipRoom;
      }
    },
    //读取房间信息，确定采集影像类型
    prepareImage() {
      let currRoom = this.currentEquipRoom;
      if(!currRoom || !currRoom.id) {
        this.$modal.alert("请选择检查机房.");
        return;
      }

      const examInfo = this.examInfo;
      if(!examInfo) { // || !examInfo.callInfo || !examInfo.callInfo.id
        this.$modal.msgWarning("请选择检查。");
        return;
      }

      let prom = Promise.resolve(currRoom);
      //
      prom.then(res => {
        if(!res || !res.id) {
          this.$modal.msgWarning("未明确检查房间|||。");
          return;
        }
        //读取机房配置：影像服务地址，文件存储位置
        getEquipRoomParams(res.id).then(rm => {
          this.equipRoomParams = {storage: rm.storage, equipRoom: rm.equipRoom}
          
        });
      });
    },

    //读取患者检查
    findExamInfo() {
      let params = {pageSize: 999, pageNum: 1, status: 0, datesCreated: 1, "orderBy": "h.id asc"};
      //本机
      /*const room = this.currentEquipRoom;
      params.callInfo = {callRoom: {roomCode: room.roomCode}};
      */
      //工作状态, 已检查，已登记
      params.resultStatusValues = [StatusDict.regist, StatusDict.exam];

      eiapi.find(params).then(res => {
        let rows = res.rows, currExamInfo = this.examInfo;
        //当前书写的检查不再监控检查下拉列表
        if(!!currExamInfo && !!currExamInfo.id && -1 === rows.findIndex(e => e.id === currExamInfo.id)) {
          let exam = cloneDeep(currExamInfo);
          exam.isTemp = true;
          rows.unshift(exam);
          //this.$modal.msgWarning("该检查登记时间为： " + exam.createTime);
        }
        this.examInfoList = rows;
      });
    },
    //选择患者检查
    changeExamInfo(id) {
      const exam = this.examInfoList.find(e => e.id === id);
      //this.examInfo = exam;
      this.setExamInfo(exam);
      //
      this.syncExamToSocket();
    },
    //患者检查下拉框显示
    selectOptionLabel(exam) {
      //姓名-排队号
      let lbl = [exam.patientInfo.name];
      if(!!exam.callInfo) {
        lbl.push("-");
        lbl.push(exam.callInfo.callNo);
      }
      return lbl.join("");
    },
    /**
     * 将当前报告发送至影像服务
     * 或告知影响服务当前无检查
     */
    syncExamToSocket() {return;
      //if(!this.socketEnabled) {
      //  return
      //}

      let exam = this.examInfo;
      //
      if(!this.isConnectedSocked && !!exam) {
        this.prepareImage();
        return;
      }
      //if(!!exam && !!exam.id) {
        let fx = () => {
          this.handleSocketSend({action: "setexam", data: exam});
        };
      //}
      //确保发送dicom信息
      if(!!exam && (!exam.dicomStudy || !exam.dicomStudy.id)) {
        imapi.getStudy({"examInfo.id": exam.id}).then(res => {
          this.examInfo.dicomStudy = res.data;

          fx();
        });
      } else {
        fx();
      }
    },
    //清空采集服务的检查信息
    resetExamToSocket() {
      this.examInfo = null;
      this.syncExamToSocket();
    },
    //弹出/嵌入视窗
    popup() {
      this.isPopup = !this.isPopup;

      this.$nextTick(() => {
        const diaWrap = this.$refs.imageMonitorViewportDialog.$el, dia = diaWrap.querySelector(".el-dialog");
        //console.log(dia);
        if(this.isPopup) {
          const re = document.body;
          re.appendChild(diaWrap);
          //
          const dime = {width: re.clientWidth * 0.9, height: re.clientHeight - 120};//imageDime(re.clientWidth * 0.9, re.clientHeight - 120);
          dia.style.width = (dime.width + 4 * 2 + 1 * 2) + "px";
          dia.style.height = dime.height + "px";
        } else {
          const wrap = this.$refs.imageMonitorWrap;
          wrap.appendChild(diaWrap);

          dia.style.width = "100%";
          dia.style.height = "unset";
        }
        //
        if(this.isPopup) {
          dia.style.top = ((document.body.clientHeight - dia.clientHeight) / 2) + "px";
          dia.style.left = ((document.body.clientWidth - dia.clientWidth) / 2) + "px";
        }
      });
    },
    //当前静态/动态采集
    setCollectMode(mode) {
      //
      //this.collectMode = mode;
      //
      this.handleSocketSend({action: "setmode", data: mode});
    },
    //动态采集
    recordPicure() {
      let collectMode = CollectMode.Static;
      if(CollectMode.Dynamic !== this.collectMode) {
        this.socketFreezing = true;
        //
        collectMode = CollectMode.Dynamic;
      }
      //
      this.setCollectMode(collectMode);
    },
    //采集
    captureImage() {
      if(!this.examInfoId) {
        return;
      }//触发所有选择的视窗
      const effectViewports = this.effectViewports;
      effectViewports.forEach(s => {
        const vp = this.$refs['imageMonitorViewport' + s];
        if(!vp) { return true; }
        vp[0].captureImage();
      })
    },
    //判断采集视窗是否活动：1、当前处于采集tab；2、视窗已选择
    isActivatedViewport(num) {
      const effectViewports = this.effectViewports;
      return !this.isDeactivated && -1 != effectViewports.findIndex(e => num === e);
    },
    //采集视窗
    calcViewportSize() {
      const viewportPane = this.$refs.imageMonitorWrap.querySelector('.image-viewport-pane');
      let viewportWidth = viewportPane.clientWidth, viewportHeight = viewportPane.clientHeight;
      console.log('w=%o, h=%o', viewportWidth, viewportWidth);
      this.viewportSize = {width: viewportWidth, height: viewportHeight}
    }
  },

  computed: {
    //vuex state
    ...mapGetters(['currentEquipRoom']),
    //是否有检查列表
    hasExamInfo() {
      return !!this.examInfoList && this.examInfoList.length > 0;
    },
    //当前是否动态采集模式
    isCollectStatic() {
      return CollectMode.Static === this.collectMode;
    },
    //当前是否静态采集模式
    isCollectDynamic() {
      return CollectMode.Dynamic === this.collectMode;
    },
    //是否支持动态踩采集/录像
    dynamicCollectSupported() {
      return this.collectModesSupported.includes(CollectMode.Dynamic);
    }
  },

  watch: {
    'currentEquipRoom'(newv, oldv) {
      this.findExamInfo();
    },
    /*examInfo(nv, ov) {
      this.syncExamToSocket();
    }*/

    isPopup() {
      this.calcViewportSize();
    }
  },
  
  activated() {
    //console.log("activated");
    this.isDeactivated = false;
  },

  deactivated() {
    //console.log("deactivated")
    this.isDeactivated = true;
  },

  beforeDestroy() {
    this.resetExamToSocket();
  }
};
export default model;
