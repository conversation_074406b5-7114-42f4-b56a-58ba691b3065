//WebSocket支持
import BaseWebSocketModel from "@/assets/scripts/pacs/BaseWebSocketModel";
//获取canvas图像
import { imageFromCavas } from "@/assets/scripts/pacs/common";
//编辑图像：选框
import ImageTools from './mixins/ImageTools'

//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//影像宽高比
import {ASPECT_WIDTH, ASPECT_HEIGHT} from "@/assets/scripts/pacs/image/util";
//
const WINURL = window.URL || window.webkitURL;
//重新连接影像服务
let timer_reopenSocket;

const model = {
  mixins: [BaseWebSocketModel, ImageTools],

  props: {
    num: {type: String},
    activated: {type: Boolean, default: false},
    examInfo: {type: Object, default: () => null},
    equipRoomParams: {type: Object, default: () => null},
    viewportSize: {type: Object, default: () => null}
  },

  data() {
    return {
      currImageObject: null,//当前影像数据用于浏览和采集
      captureImageWidth: ASPECT_WIDTH,
      captureImageHeight: ASPECT_HEIGHT,
      capturing: false,
      //byte-
      //currImageURL: null,
      currImageRef: null,

      //执行动态采集，等待后端返回是否动态采集成功
      socketFreezing: false,
    }
  },

  methods: {

    //读取房间信息，确定采集影像类型
    prepareImage() {
      //影像服务已连接
      if(this.isConnectedSocked) {
        return;
      }
      //采集窗宽是否激活，检查房间配置
      if(!this.activated || !this.equipRoomParams) { 
        return; 
      }
      //检查房间配置
      let examEquipRoom = this.equipRoomParams.equipRoom;
      if(!examEquipRoom || !examEquipRoom.id) {
        //this.$modal.alert("请选择检查机房.");
        console.warn("无检查机房信息.");
        return;
      }
      //检查信息
      const examInfo = this.examInfo;
      if(!examInfo || !examInfo.id) { // || !examInfo.callInfo || !examInfo.callInfo.id
        //this.$modal.msgWarning("请选择检查。");
        console.warn("无检查信息.");
        return;
      }
      //影像服务
      //this.socketUrl = `ws://${examEquipRoom.imageService}/videoService/uis/image`;
      this.socketUrl = `ws://${examEquipRoom.imageService}/uis/image`;
      this.openSocket();
    },
    //视频显示
    getVideoViewport() { return this.$refs.videoViewport; },
    //图片画布
    getImageViewport() {
      let vp = this.$refs['imageViewport'];
      if(!vp) { return null; }
      return vp.length? vp[0] : vp; 
    },
    getImageGraph() {
      const vp = this.getImageViewport();
      return !!vp? vp.getContext("2d") : null; 
    },
    //采集图片画布
    getImageCapture() { return this.$refs.imageCapture; },
    getCaptureGraph() {
      const vp = this.getImageCapture();
      return !!vp? vp.getContext("2d") : null; 
    },

    //静态采集
    captureImage() {
      if(this.capturing) {
        return;
      }
      //重绘成希望的尺寸
      const imageCapture = this.getImageCapture();//imageViewport = this.getImageViewport(), 
      if(!imageCapture) {
        console.log('无发绘制。');
        return;
      }
      const imageObject = this.currImageObject;
      if(!imageCapture) {
        console.log('无绘制源影像数据。');
        return;
      }
      const imageWidth = imageObject.width, imageHeight = imageObject.height;
      imageCapture.width = imageWidth;
      imageCapture.height = imageHeight;
      this.getCaptureGraph().drawImage(imageObject, 0, 0, imageObject.width, imageObject.height);

      let imageData = imageFromCavas(imageCapture);
      this.uploadImage(imageData);
    },
    //上传采集的图像
    uploadImage(imageData, fileUrl) {
      this.capturing = true;

      let exam = this.examInfo;
      let roomCode = this.equipRoomParams.equipRoom.roomCode;
      imapi.saveImage(roomCode, exam.id, imageData, fileUrl).then(res => {
        this.capturing = false;

        //this.$modal.msgSuccess("已采集1张图片.");
        let img = res.data;
        //
        if(!exam.dicomStudy || !exam.dicomStudy.id) {
          imapi.getStudy({"examInfo.id": exam.id}).then(res => {
            this.examInfo.dicomStudy = res.data;
            if(!fileUrl) {
              this.syncExamToSocket();
            }
          });
        }
        //
        this.triggerBind("capture", exam, img);
      }).catch(err => this.capturing = false);
    },
    //加载采集的图像然后绘制
    prepareDrawImage(imageData) {
      if(!imageData) { return; }
      let img = this.currImageRef;
      if(!img) {
        this.currImageRef = img = new Image();
      }
      img.onload = () => {
        this.drawImage(img);
        //销毁，防止撑爆
        if(imageData.indexOf('blob:') === 0) {
          WINURL.revokeObjectURL(imageData);
        }
      }
      img.src = imageData;//"data:image/jpeg;base64," + data;
    },
    //画图/绘制
    drawImage(img) {
      const viewport = this.getImageViewport(), imageWidth = img.width, imageHeight = img.height;
      if(!viewport) { 
        return;
      }
      const grp = this.getImageGraph();
      if(!grp) {
        return;
      }

      this.currImageObject = img;
      //允许最大宽、高度
      let viewportSize = this.viewportSize, availWidth = 0, availHeight = 0;
      if(viewportSize){
        availWidth = viewportSize.width, availHeight = viewportSize.height;
      }
      /*const pane = this.$refs.imageMonitorViewport.parentNode.parentNode, toolbar = pane.querySelector(".buttons-pane");
      availWidth = pane.clientWidth;
      availHeight = pane.clientHeight - Math.max(toolbar.offsetHeight, toolbar.clientHeight);
      if(this.isPopup) {
        const diaHead = pane.parentNode.querySelector(".el-dialog__header");
        availHeight -= Math.max(diaHead.offsetHeight, diaHead.clientHeight);
      }*/
      //按比例计算绘制尺寸
      //默认填满宽度
      let drawWidth = availWidth, drawHeight = drawWidth * imageHeight / imageWidth;
      //console.log("imageWidth=%d, imageHeight=%d, availHeight=%d, drawWidth=%d, drawHeight=%d"
      //  , imageWidth, imageHeight, availHeight, drawWidth, drawHeight);
      //如高度超出，调整宽度
      if(drawHeight > availHeight) {
        drawHeight = availHeight;
        drawWidth = drawHeight * imageWidth / imageHeight;
        //console.log("adjestd imageWidth=%d, imageHeight=%d, availHeight=%d, drawWidth=%d, drawHeight=%d"
        //  , imageWidth, imageHeight, availHeight, drawWidth, drawHeight);
      }
      viewport.width = drawWidth;
      viewport.height = drawHeight;
      //viewport.style.width = drawWidth + "px";
      //viewport.style.height = drawHeight + "px";
      grp.drawImage(img, 0, 0, drawWidth, drawHeight);
    },
    //连接成功后发送机房参数、设备参数
    handleSocketOpen() {
      //
      this.handleSocketSend(this.num);
      //当前静态/动态采集
      //this.socketFreezing = true;
      //this.handleSocketSend({action: "getmode"});
      //
      //this.syncExamToSocket();

      //this.enableEditImage();
    },
    //获取图片
    handleSocketReceived(event) {
      //console.log(this, "接收");
      const IMAGEDATA = "imagedata", DETACHEDIMAGE = "detached", MODE = "mode";
      let data = event.data, imageData;
      if("string" === typeof(data)) {
        let examMatched, dataName;
        let pos, msgo;
        if(0 === data.indexOf("{")) {
          msgo = JSON.parse(data);
          const currExamInfo = this.examInfo;
          if(!!msgo && !!currExamInfo) {
            examMatched = currExamInfo.examNo === msgo.examNo;
            dataName = msgo.name;
            data = msgo.data;
          }
        } else if(-1 !== (pos = data.indexOf(":"))) {
          examMatched = true;
          dataName = data.substring(0, pos);
          data = data.substring(pos + 1);
        } else {
          examMatched = true;
          dataName = IMAGEDATA;
        }

        if(examMatched && dataName === IMAGEDATA) {//推送的影像
          imageData = "data:image/jpeg;base64," + data;
        } else if(examMatched && dataName === DETACHEDIMAGE) {//控制器抓取的影像
          let fileUrl = data;
          this.uploadImage(null, fileUrl);
          return;
        } else if(dataName === MODE) {//静态/动态
          this.collectMode = data.current;
          this.collectModesSupported = data.supported;
          //
          this.socketFreezing = false;
          return;
        }
      } else {
        //const wurl = (window.URL || window.webkitURL);
        imageData = WINURL.createObjectURL(data);
        //销毁，避免撑爆
        /*if(this.currImageURL) {
          wurl.revokeObjectURL(this.currImageURL);
          this.currImageURL = imageData;
        }*/
      }

      if(this.activated) {
        this.prepareDrawImage(imageData);
      }
    },
    //socke断开，清空画布
    handleSocketClosed() {
      //console.log("断开");
      const grp = this.getImageGraph();
      if(!!grp) {
        grp.clearRect(0, 0, 9999, 9999);
      }
      //
      this.reopenSocket();
    },

    handleSocketError() {
      //console.log(arguments)
      //this.setCollectMode(CollectMode.Static);
    },
    //意外断开
    reopenSocket() {
      clearTimeout(timer_reopenSocket);
      console.info("尝试重连影像服务....");
      //console.log(this.examInfo);
      if(!!this.examInfo) {
        timer_reopenSocket = setTimeout(this.openSocket, 8 * 1000);
      }
    }
  },

  mounted() {
    this.prepareImage();    
  },

  watch: {
    activated(nv, ov) {
      if(nv) {
        this.prepareImage();
      }
    },

    examInfo(nv, ov) {
      if(nv) {
        this.prepareImage();
      }
    },
    equipRoomParams(nv, ov) {
      if(nv) {
        this.prepareImage();
      }
    }  
  }

};
export default model;
