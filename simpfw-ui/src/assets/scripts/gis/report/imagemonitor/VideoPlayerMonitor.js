import { mapGetters } from 'vuex';
import {cloneDeep} from "lodash";


// web socket
import WebSocketModel from "@/assets/scripts/pacs/BaseWebSocketModel";

//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//患者信息查询
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//房间信息
import {params as getEquipRoomParams} from "@/assets/scripts/pacs/equiproom/api";
//影像宽高比

//
import {StatusDict, matchAny} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//患者/检查列表
import ImageMonitorPatientSheet from "@/views/gis/report/comp/ImageMonitorPatientSheet";

//采集的图像尺寸
function imageDime(availWidth, availHeight) {
  //
  let width = availWidth, height = width / ASPECT_RATIO;
  if(height > availHeight) {
    height = availHeight;
    width = availHeight * ASPECT_RATIO;
  }
  return {width, height};  
}
export {imageDime};
//静态/动态采集
const CollectMode = {Static: "STATIC", Dynamic: "DYNAMIC"}

const model = {
  mixins: [WebSocketModel],

  components: { ImageMonitorPatientSheet},


  data() {
    return {
      examInfo: null,
      
      examInfoId: null,
      examInfoList: [],
     
      //呼叫检查的机房
      equipRoomParams: null,
      //
      isPopup: false,

      collectMode: CollectMode.Static,
      collectModesSupported: [CollectMode.Static],
      //
      isDeactivated: false,
      viewportSize: null,
      //检查进度限制
      examRestrict: {resultStatusValues: [StatusDict.regist, StatusDict.exam, StatusDict.report]},
    }
  },

  methods: {
    
    //
    show(exam) {
      console.log("----------------------检查信息"+exam.id)
      //this.$nextTick(this.calcViewportSize);
      //是否选择了检查
      if(!exam || !exam.id) {
        this.$modal.msgWarning("请选择检查。");
      //  return;
      }
      //
      this.setExamInfo(exam);
      //检查是否允许采集：登记完成、已检查、已报告的允许采集
      if(!!exam && exam.id && (!this.examInfo || !this.examInfo.id)) {
        this.$modal.msgWarning("该检查不支持采集，原因：" + (exam.resultStatus? exam.resultStatus.dictLabel : "未知") + "。");
        return;
      }
      //检查列表
      this.findExamInfo();
      //
      this.prepareImage();
    },
    //当前采集的检查
    setExamInfo(exam) {
      //检查状态
      const collectable = !!exam && (!exam.resultStatus || !exam.resultStatus.dictValue || matchAny(exam, StatusDict.regist, StatusDict.exam, StatusDict.report));

      this.examInfo = collectable? exam : null;
      this.examInfoId = collectable && !!exam? exam.id : null;
      if(collectable && !!exam && !!exam.id) {
        //当前检查机房
        exam.equipRoom = this.currentEquipRoom;
      }
    },
    //读取房间信息，确定采集影像类型
    prepareImage() {
      let currRoom = this.currentEquipRoom;
      if(!currRoom || !currRoom.id) {
        this.$modal.alert("请选择检查机房.");
        return;
      }

      const examInfo = this.examInfo;
      if(!examInfo) { // || !examInfo.callInfo || !examInfo.callInfo.id
        this.$modal.msgWarning("请选择检查。");
        return;
      }

      let prom = Promise.resolve(currRoom);
      //
      prom.then(res => {
        if(!res || !res.id) {
          this.$modal.msgWarning("未明确检查房间|||。");
          return;
        }
        //读取机房配置：影像服务地址，文件存储位置
        getEquipRoomParams(res.id).then(rm => {
          this.equipRoomParams = {storage: rm.storage, equipRoom: rm.equipRoom}
          
        });
      });
    },

    //读取患者检查
    findExamInfo() {
      console.log("-------------查找患者---------检查信息")

      let params = {pageSize: 999, pageNum: 1, status: 0, datesCreated: 1, "orderBy": "h.id asc"};
      //本机
      /*const room = this.currentEquipRoom;
      params.callInfo = {callRoom: {roomCode: room.roomCode}};
      */
      //工作状态, 已检查，已登记
      params.resultStatusValues = [StatusDict.regist, StatusDict.exam];

      eiapi.find(params).then(res => {
        let rows = res.rows, currExamInfo = this.examInfo;
        //当前书写的检查不再监控检查下拉列表
        if(!!currExamInfo && !!currExamInfo.id && -1 === rows.findIndex(e => e.id === currExamInfo.id)) {
          let exam = cloneDeep(currExamInfo);
          exam.isTemp = true;
          rows.unshift(exam);
          //this.$modal.msgWarning("该检查登记时间为： " + exam.createTime);
        }
        this.examInfoList = rows;
      });
    },
    //选择患者检查
    changeExamInfo(row) {
      const exam = row;//this.examInfoList.find(e => e.id === id);
      //this.examInfo = exam;
      this.setExamInfo(exam);
      //
      this.syncExamToSocket();
    },
    //患者检查下拉框显示
    selectOptionLabel(exam) {
      //姓名-排队号
      let lbl = [exam.patientInfo.name];
      if(!!exam.callInfo) {
        lbl.push("-");
        lbl.push(exam.callInfo.callNo);
      }
      return lbl.join("");
    },
    prepare() {
      let currRoom = this.currentEquipRoom;
      if(!currRoom || !currRoom.id) { return; }
      //影像服务已连接
      if(this.isConnectedSocked) {
        console.log("已连接影像采集服务。");
        return;
      }
        //读取机房配置：影像服务地址，文件存储位置
        getEquipRoomParams(currRoom.id).then(r => {
          let room = r? r.equipRoom : null;
          //使用影像服务采集或浏览器调用摄像头
          if(!!room && !!room.imageService) {
            //影像服务
            this.socketUrl = `ws://${room.imageService}/uis/image`;
            this.openSocket();
          }
        });
        console.log("---------重连成功-------------")

    },

    /**
     * 将当前报告发送至影像服务
     * 或告知影响服务当前无检查
     */
    syncExamToSocket() {
      //if(!this.socketEnabled) {
      //  return
      //}
      let exam = this.examInfo;
      //
      if(!this.isConnectedSocked && !!exam) {
        this.prepareImage();
        return;
      }
      //if(!!exam && !!exam.id) {
        let fx = () => {
          this.handleSocketSend({action: "setexam", data: exam});
        };
      //}
      //确保发送dicom信息
      if(!!exam && (!exam.dicomStudy || !exam.dicomStudy.id)) {
        imapi.getStudy({"examInfo.id": exam.id}).then(res => {
          this.examInfo.dicomStudy = res.data;

          fx();
        });
      } else {
        fx();
      }
    },
    //清空采集服务的检查信息
    resetExamToSocket() {
      this.examInfo = null;
      this.syncExamToSocket();
    },
    
    //当前静态/动态采集
    setCollectMode(mode) {
      //
      //this.collectMode = mode;
      //
      this.handleSocketSend({action: "setmode", data: mode});
    },
   

    //判断采集视窗是否活动：1、当前处于采集tab；2、视窗已选择
    isActivatedViewport(num) {
      const effectViewports = this.effectViewports;
      return !this.isDeactivated && -1 != effectViewports.findIndex(e => num === e);
    },

    // 显示采集程序
    showVideoPlayerApp(){
      // 推送房间列表
      this.prepareImage();
      // 房间信息在这里
      this.equipRoomParams;
      let currRoom = this.currentEquipRoom;
      // 推送当前的患者
      let exam = this.examInfo;
      //
      if(!this.isConnectedSocked && !!exam) {
        this.prepare();
        return;
      }
      //if(!!exam && !!exam.id) {
        let fx = () => {
          this.handleSocketSend({action: "setexam", data: exam});
        };
      //}
      //确保发送dicom信息
      if(!!exam && (!exam.dicomStudy || !exam.dicomStudy.id)) {
        imapi.getStudy({"examInfo.id": exam.id}).then(res => {
          this.examInfo.dicomStudy = res.data;

          fx();
        });
      } else {
        fx();
      }
      // 推送当前的存储信息

      // 唤醒app
      
    }
  },

  computed: {
    //vuex state
    ...mapGetters(['currentEquipRoom']),
    //是否有检查列表
    hasExamInfo() {
      return !!this.examInfoList && this.examInfoList.length > 0;
    },
    //当前是否动态采集模式
    isCollectStatic() {
      return CollectMode.Static === this.collectMode;
    },
    //当前是否静态采集模式
    isCollectDynamic() {
      return CollectMode.Dynamic === this.collectMode;
    },
    //是否支持动态踩采集/录像
    dynamicCollectSupported() {
      return this.collectModesSupported.includes(CollectMode.Dynamic);
    }
  },

  watch: {
    'currentEquipRoom'(newv, oldv) {
      this.findExamInfo();
    },
    /*examInfo(nv, ov) {
      this.syncExamToSocket();
    }*/

    isPopup() {
      this.calcViewportSize();
    }
  },
  
  activated() {
    //console.log("activated");
    this.isDeactivated = false;
  },

  deactivated() {
    //console.log("deactivated")
    this.isDeactivated = true;
  },

  beforeDestroy() {
    console.log("重连websocket ");
    this.resetExamToSocket();
  }
};
export default model;
