import { mapGetters } from 'vuex';
//检查信息接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//房间信息接口
import {load as loadRoom} from "@/assets/scripts/pacs/equiproom/api";
//排队信息
import {past as passExam} from "@/assets/scripts/gis/exammanagement/queue/api";
//本机列表
import EquipRoomPatientSheet from "@/views/gis/report/comp/EquipRoomPatientSheet";
//患者/检查列表
import PatientSheet from "@/views/gis/exammanagement/patient/comp/PatientSheet";
//报告书写表单
import ReportWriter from "@/views/gis/report/comp/ReportWriter";
//关键短语
import PhraseSelect from "@/views/pacs/tplcfg/phrase/comp/PhraseSelect";
//常用符号
import SymbolSelect from "@/views/pacs/tplcfg/symbol/comp/SymbolSelect";
//书写词库
import WritePhraseSelect from "@/views/pacs/tplcfg/writephrase/comp/WritePhraseSelect";
//
import TemplateTree from "@/views/pacs/tplcfg/template/comp/TemplateTree";
//
import TemplateOverview from "@/views/pacs/report/comp/TemplateOverview";
//
import ReportTemplate from "@/views/gis/report/comp/ReportPreviewer";
import ReportViewer from "@/views/gis/report/comp/ReportViewer";
//
import ExamEquipRoom from "@/views/gis/exammanagement/patient/comp/ExamEquipRoom";
//叫号
import ExamCalling from "@/views/gis/exammanagement/queue/comp/ExamCalling";
//申请单
import ExamViewer from '@/views/gis/exammanagement/patient/comp/ExamViewer';
//危急上报
import CriticalValues from "@/views/gis/report/comp/CriticalValues";
//召回报告
import ReportAuditWithdraw from "@/views/gis/report/comp/ReportAuditWithdraw";
//影像监视，采集
import ImageMonitor from "@/views/gis/report/comp/ImageMonitor";
//影像监视，采集
import VideoPlayerMonitor from "@/views/gis/report/comp/VideoPlayerMonitor";

//历史检查
import ExamHistory from "@/views/gis/exammanagement/patient/comp/ExamHistory";
//检查所见，检查诊断，术后医嘱支持插入指定内容
import BaseFormInputInsertor from "@/assets/scripts/pacs/BaseFormInputInsertor";
//排队列表
import Queue from "@/views/gis/exammanagement/queue/comp/Queue";
// 诊室叫号排队列表-NEW add@20230321
import QueueNew from "@/views/gis/exammanagement/queue/queue_new";
//当前房间
import CurrentEquipRoom from "@/views/pacs/equiproom/currentequiproom";
//提取影像
import ImageGallary from "@/views/pacs/report/comp/ImageGallary";
//患者列表右键菜单
import {tableContextmenuItems} from "@/assets/scripts/gis/exammanagement/patient/Index";
//检查进度
import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//混入表单输入框操作
const mixin = {
  methods: {
    modifyContent: BaseFormInputInsertor.methods.modifyContent
  }
};

//
const model = {
  name: "EisReportWriting",

  mixins: [mixin],

  components: { EquipRoomPatientSheet, PatientSheet, ReportWriter, SymbolSelect, PhraseSelect, WritePhraseSelect
    , TemplateTree, TemplateOverview, ReportTemplate, ReportViewer, ExamEquipRoom, ExamCalling, ExamViewer
    , CriticalValues, ReportAuditWithdraw, ImageMonitor,VideoPlayerMonitor, ExamHistory, Queue, CurrentEquipRoom
    , ImageGallary, QueueNew },

  data() {
    return {
      portableReportProps: ['examDesc', 'examDiagnosis'], //, 'operationSuggestion'
      gis_portableReportProps: ['examDesc', 'examDiagnosis','operationSuggestion','pathologyTreatment','pathologyDiagnosis'], 
      action: {cmd: null, data: null},
      tabsName: {
        ImageMonitor: "ImageMonitor",
        VideoPlayerMonitor: "VideoPlayerMonitor",
        PatientList: "PatientList",
        Template: "Template",
        Queue: "Queue",
        ImageGallary: "ImageGallary",
        PatientSheet: "PatientSheet"
      },
      currentTab: null,

      currExamItem: null,
      currFormField: null,
      //
      queueFilter: {examInfo: {examAtPm: 0}, examItemCodes: null, resultStatusCodes: [StatusDict.regist], callStatus: 0, examModalitiesCodes: []},

      patientSheetActions: tableContextmenuItems()
    };
  },

  methods: {
    //右键菜单/其它
    handleAction(cmd, row) {
      if(!row || !row.id) {
        this.$modal.alert("请选择检查/报告。");
        return;
      }
      //console.log("%s %o", cmd, row);
      switch(cmd) {
        //采集影像
        case "image::collect":
          //this.$refs.reportWriter.collectImages();
          this.toCollectImage(row);
          break;
        //更改机房
        case "exam-info::equip-room-change":
          this.$refs.examEquipRoom.change(row);
          break;
        //呼叫/复呼检查
        case "exam::call":
          this.callExam(row);
          break;
        //查看检查单
        case "exam-info::view": 
          this.$refs.examViewer.view(row);
          break;
        //编辑检查
        case "exam::edit": 
          this.$refs.patientSheet.handleUpdate(row);
          break;
        //删除检查
        case "exam::del": 
          this.$refs.patientSheet.handleDelete(row);
          break;
        //删除检查
        case "exam::undel": 
          this.$refs.patientSheet.undoDelete(row);
          break;
        //延迟检查  
        case "exam::at-pm": 
          this.$refs.patientSheet.handlePostpone(row);
          break;
        case "exam-info::past":
          this.handleQueuePast(row);
          break;
        //预览报告
        case "report::preview":
          this.previewReport(row);
          break;
        //打印报告
        case "report::print": 
        //生成报告
        case "report::exportAsDoc": 
        //生成报告再打印
        case "report::exportAsDoc,print": 
          this.previewReport(row, cmd.substring(8));
          break;
        //书写报告
        case "report::write": 
          this.$refs.reportWriter.write(row);
          this.$refs.examHistory.getList(row);
          this.currExamItem = row.examItem;
          break;
        //召回审核的报告
        case "report::withdrawAudit": 
          this.withdrawAuditReport(row);
          break;
        //危急值上报
        case "report::critical":  
          this.$refs.criticalValues.fill(row);
          break;
        case 'exam::copy':
            document.execCommand('Copy','false',null);
           break;
        case "report::activeCollectImage":
          this.activeCollectImage(row);
          break;
        case 'exam::traceCase':
          //console.log("ReportWriting.js-exam::traceCase-item: ", row);
          this.$router.push({ name: "TraceCaseAddEdit", params: {"examNo": row.examNo}});
          break;
        default: 
          this.action = {cmd: cmd, data: row};
      }
    },
    //更新检查
    refreshExamInfo() {
      this.$refs.patientList.getList();
    },
    //插入“常用符号/关键短语/书写词库”
    updateReport(nam, val, replace = true) {
      //console.log("%s %s %o", nam, val, replace);
      this.$refs.reportWriter.updateProp(nam, val, replace);
    },

    //下一个
    nextExam() {
      this.$refs.queue.toCallNext();
    },

    //保存报告
    saveReport() {
      this.$refs.reportWriter.save();
    },
    /**
     * 预览报告
     * @param mix 可指定报告
     * @param opt 是否执行打印
     */
    previewReport(mix, opt) {
      let item = this.assertExam(mix);
      if(!item) {
        item = this.currentReport();
      }
      if(!item) {
        this.$modal.alert("请选择预览的检查。");
        return;
      }
      this.$refs.reportViewer.view(item, opt);
    },
    //书写报告
    writeReport(item) {
      this.handleAction("report::write", item);
    },
    //召回审核的报告
    withdrawAuditReport(item) {
      this.$refs.reportAuditWithdraw.fill(item);
    },
    afterWithdrawReport() {
      this.refreshExamInfo();
      this.writeReport(this.currentReport());
    },

    //复呼
    callExam(mix) {
      let item = this.assertExam(mix);
      if(!item) {
        //item = this.$refs.queue.get();
        item = this.$refs.patientList.selectedExamInfo();
      }

      if(!item) {
        this.$modal.msg("请选择呼叫的检查。");
        return;
      }
      //this.confirmToUpdateResultStatus(item, StatusDict.exam);
      this.$refs.examCalling.open(item);
    },
    //显示模板信息，未指定node参数则隐藏
    showTemplate(node) {
      //console.log(node);
      const tplOverview = this.$refs.tplOverview;
      if(!node || !node.isTemplate) {
        tplOverview.view(null);
        return;
      }
      //this.applyTemplate(node, null, 2);
      tplOverview.view(node);
    },
    //使用模板
    //@mode 0-替换, 1-末尾追加, 2-光标处插入或替换选择
    applyTemplate(node, tree , props, mode = 1) {
      //console.log(arguments);
      if(!node || !node.isTemplate) {
        return;
      }
      //模板数据
      const tpl = node.data;
      //应用的字段，不指定则限定的字段
      props = props || this.portableReportProps;
      //逐个字段
      for(let i = 0; i < props.length; i ++) {
        //字段名
        const prop = props[i];
        var propVal = tpl[prop];
        //处理结构化模板
        if(2==node.data.personalFlag&&"examDesc"==prop){
          propVal = "";
          tpl["examDescAr"].forEach(e=>{
            e.forEach(a=>{
              if(a.isStr){
                propVal+=a.str;
              }else{
                propVal+=a.str[a.select];
              }
            });
            propVal+="\n";
          });
        }
        //光标处操作
        if(2 === mode) {
          const ele = document.querySelector("textarea[name=" + prop + "]");
          //console.log(ele, prop, propVal);
          if(!!propVal && ele.value.length>0){
            propVal = '\n' + propVal
          }
          this.modifyContent(ele, propVal, (nam, val) => this.updateReport(nam, val));
        } else {
          //替换/末尾追加
          this.updateReport(prop, propVal, 0 === mode);
        }
      }
    },
    //更多操作
    handleExtendCommand(cmd) {
      const exam = this.currentReport();
      this.handleAction(cmd, exam);
    },
    //打印报告
    printReport(mix) {
      let item = this.assertExam(mix);
      if(!item) {
        item = this.currentReport();
      }
      this.previewReport(item, "print");
    },

    //测试是否为检查信息
    assertExam(mix) {
      if(!!mix && !(mix instanceof Event) && !!mix.id) {
        return mix;
      }
      return null;
    },
    //医生签字
    signReport(mix) {
      let item = this.assertExam(mix);
      if(!item) {
        item = this.currentReport();
      }
      if(!item) {
        this.$modal.alert("请选择要签字的报告。");
        return;
      }
      this.$store.dispatch("GetInfo")
      .then(res => this.$modal.confirm(`签字医生为"${res.user.nickName}"，是否确定签字？`))
      .then(() => eiapi.signReport(item))
      .then(res => {
        if(200 === res.code) {
          this.$modal.alert("签字完成。");
        }
      });
    },
    //打开历史检查
    getHistList() {
      this.$refs.examHistory.show();//getList(this.currentReport());
    },
    //切换Tab
    switchTab(tab) {
      this.currentTab = tab;
      setTimeout(this.handleTabClick, 1000);
    },
    //切换Tab触发
    handleTabClick(comp) {
      const compName = this.currentTab;//comp.name;
      if(!compName) {
        return;
      }
      switch(compName) {
        case this.tabsName.VideoPlayerMonitor:
          this.toVideoPlayerApp(this.currentReport());
          break;
        case this.tabsName.ImageMonitor:
          this.toCollectImage(this.currentReport());
          break;
        case this.tabsName.PatientList:
          this.$refs.patientList.getList();
          break;
        case this.tabsName.Queue:
          this.$refs.queue.getList();
          break;
        case this.tabsName.ImageGallary:
          const cm = this.$refs.imageGallary;
          cm.findAsInitd();
          cm.show(this.currentReport());
          break;
        }
        //
        if(compName !== this.tabsName.ImageGallary) {
          this.$refs.imageGallary.stopTimer();
        }
    },
    //打开采集应用
    toVideoPlayerApp(exam) {
          if(this.tabsName.VideoPlayerMonitor != this.currentTab) {
            this.currentTab = this.tabsName.VideoPlayerMonitor;
          }
          this.$refs.videoPlayerMonitor.show(exam);      
    },
    //打开监视/摄像头
    toCollectImage(exam) {
      if(this.tabsName.ImageMonitor != this.currentTab) {
        this.currentTab = this.tabsName.ImageMonitor;
      }
      this.$refs.imageMonitor.show(exam);      
    },
    //采集
    handleCaptureImage(exam, image) {
      const report = this.currentReport();
      if(report && exam.id === report.id) {
        this.$refs.reportWriter.addCollectImage(image);
      }
    },
    handleVideoPlayer(exam){
      if(this.tabsName.VideoPlayerMonitor != this.currentTab) {
        this.VideoPlayerMonitor = this.tabsName.VideoPlayerMonitor;
      }
      this.$refs.videoPlayerMonitor.show(exam);      
    },
    //
    activeCollectImage(exam) {
      this.$refs.imageMonitor.setExamInfo(exam);
      this.$refs.imageMonitor.prepareImage();
    },
    deactiveCollectImage() {
   //   this.$refs.imageMonitor.resetExamToSocket();
      this.$refs.videoPlayerMonitor.resetExamToSocket();
    },
    //当前在编辑的报告/检查
    currentReport() {
      return this.$refs.reportWriter.get();
    },
    //获取登录的机房可执行检查项目
    findExamItemsAtRoom() {
      const prom = loadRoom({roomCode: this.currentEquipRoom.roomCode});
      !!prom && prom.then(res => {
        const room = res? res.data : null;
        if(room && room.examItems && room.examItems.length) {
          this.queueFilter.examItemCodes = room.examItems.map(r => r.dictValue);
        }
      });
    },
    //标识过号
    handleQueuePast(mix) {
      const exam = !!mix && !!mix.id? mix : this.$refs.patientList.selectedExamInfo();
      if(!exam || !exam.callInfo) {
        this.$modal.alert("请选择检查。")
        return;
      }
      passExam(exam.callInfo.id).then(() => {
        this.$refs.patientList.getList();
        this.$refs.queue.getList();
      });
    },

    //报告编辑内容变更触发
    focusFormField(field) {
      this.currFormField = field;
    },

    //生成pdf模板
    makeTplDoc(rep) {
      this.$refs.reportTemplate.view(rep);
    },

    applyReport(row){
      //console.log(row)
      this.applyTemplate({data:row,isTemplate:true})
    },
  },

  computed: {
    ...mapGetters(['currentEquipRoom']),
  },

  watch: {
    'currentEquipRoom': {
        immediate: true,
        handler(newv, oldv) {
          this.findExamItemsAtRoom();
        }
    }
  },

  created() {
    //限定检查类型
    this.queueFilter.examModalitiesCodes = this.topModalities;
    //初始tab
    this.switchTab(this.tabsName.PatientList);
  },
  
  activated() {
    const params = this.$route.params;
    if(!!params && !!params.report) {
      this.writeReport(params.report);
    }
  },

  beforeDestroy() {
    console.log("beforeDestroy");
    //
    this.showTemplate();

    this.deactiveCollectImage();
  },

  deactivated() {
    //
    this.showTemplate();
  }

};

export default model;