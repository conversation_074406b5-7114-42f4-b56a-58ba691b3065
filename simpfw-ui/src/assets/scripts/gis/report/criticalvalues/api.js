import request,{postForm} from '@/utils/request'

const ctx = '/exammanagement/criticalValues';

// 查询列表
export function find(query) {
  return postForm({
    url: ctx + '/list',
    data: query
  })
}

// 查询详细
export function get(id) {
  return request({
    url: ctx + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: ctx + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: ctx + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request.put(ctx + '/del/' + id)
}
