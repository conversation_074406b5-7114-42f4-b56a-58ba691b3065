import { mapGetters } from 'vuex';
//工具
import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";
//cornerstone
import "@/assets/scripts/cornerstone/cornerstone-setup";

import {mergeWithNotNull, undefinedOrNull} from "@/utils/common";
//获取当前用户信息
import { getUserProfile } from "@/api/system/user";
//检查信息接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//影像相关工具
import {FakeUidPrefix, FileTypes, assertJpeg, loadAndDisplay, removeImageCache} from "@/assets/scripts/pacs/image/util";
//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";
import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel"
//
import ReportWriterImageComm from "@/assets/scripts/gis/report/comp/ReportWriterImageComm";
//import ReportPreviewer from "@/views/gis/report/comp/ReportPreviewer";
//放大影像
import ImageBubble from "@/views/pacs/report/comp/ImageBubble"
//
import VideoView from "@/views/pacs/report/comp/VideoView";
//检查状态变更
import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import {ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//表单
import ReportWriterForm from "./ReportWriterForm";
//
import ReportWriterDetail from "@/views/gis/report/comp/ReportWriterDetail";

import imageView from '@/views/pacs/image/imageView'
//
import ReportWriterImageIllus from "@/views/gis/report/comp/ReportWriterImageIllus";
//选择用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//操作权限
import { default as PermiCheck} from "@/directive/permission/mixins";
//影像转移
import {ImageTransfer} from "@/assets/scripts/pacs/report/mixins/fun.imageTransfer";
//影像
import {ImageOperator} from "@/assets/scripts/pacs/report/mixins/fun.imageOperator";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//
import ExamPicker from "@/views/gis/exammanagement/patient/comp/ExamPicker";
//报告采集影像和报告影像
import { ReportImageFun } from "@/assets/scripts/pacs/image/fun.image";
//
const ErrM_Unwrite = "请选择要书写的报告。", PERMI_AUDIT = "exam-report:audit";

/**
 * 空白表单
 */
function emptyForm() {
  return {
    
    patientInfo: {
      id: null,
      medicalRecordNo: null,
      name: null, 

      gender: {}, 
      age: null,
      ageUnit: {},
    },

    examModality: {},
    examItem: {},
    examParts: [],
    resultStatus: {dictValue: null},
    //examResultProp: {dictValue: "0"},
    examResultProp: {},
    operationGrade: {dictValue: null},

    examDoctor: {},
    reportDoctor: {},

    examDoctorsName: null,
    examDoctorsCode: null,
    consultantsName: null,
    consultantsCode: null,
    recordersName: null,
    recordersCode: null,
    operNurse: {userName: null, nickName: null},
    anesDoctor: {userName: null, nickName: null},

    examDesc: null,
    examDiagnosis: null,
    operationSuggestion: null,
    pathologyDiagnosis: null,
    pathologyTreatment: null,
    anesWay: {dictValue: null},
    mirrorMeasure: {dictValue: null},
    breatherWay: {dictValue: null},
    retainSpecimenFlag: "0",
    operationComplicationFlag: "0",
    operationComplicationsDictCode:[],
    operationComplicationsCode:[],
    rsTreatmentMeasure: null,
    ocTreatmentMeasure: null,
    //报告的影像
    images: [],
    //采集的影像
    dicomStudy: {seriesSet: null},
    dicomStudies: []
  }
}

export { emptyForm };

const model = {

  name: "ReportWriter",
  
  components: {LinksignPopup, ImageBubble, ReportWriterDetail, imageView, UserPicker, VideoView, Contextmenu, ExamPicker, ReportWriterImageIllus},

  dicts: ["uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "uis_exam_result_prop", "gis_operation_grade"
    ,"gis_anes_way","gis_mirror_measure","gis_operation_complication","gis_breather_way"],

  mixins: [ResultStatus, ReportWriterForm, PermiCheck, ReportWriterImageComm, ImageTransfer, ImageOperator, ReportImageFun],

  data() {
    return {
      //当前用户
      currentUser: null,
      //报告内容
      reportForm: emptyForm(),
      //表单验证规则
      reportFormRules: {
        "operNurse.nickName": { required: true, message: '请选择或填写护士', trigger: 'blur' }
      },
      //
      reportOrig: null,
      //
      qr: null,
      //
      editable: false,
      //双击图像显示大图
      dialogVisible:false,
      imageURL:'',
      currentItemIdx:null,

      isFistfocus: true,

      d_examResultProp:false,
      his_opt:{}
    };
  },

  methods: {
    //初始
    onPageLoad() {
      //当前用户信息
      if(!this.currentUser) {
        getUserProfile().then(res => {
          this.currentUser = res.data;
        });
      }
      //加载历史表单数据，获取记录人员，检查医生，会诊医生
      if(!!this.reportForm_his){
          this.reportForm.recordersName=this.reportForm_his.recordersName
          this.reportForm.examDoctorsName=this.reportForm_his.examDoctorsName
          this.reportForm.consultantsName=this.reportForm_his.consultantsName
      }

      //表单元素焦点
      const fme =this.$refs.reportForm.$el;
      //触发的源：检查所见，检查诊断，术后医嘱
      const eles = [fme.examDesc, fme.examDiagnosis, fme.operationSuggestion];
      eles.forEach(ele => {
        if(!ele) { return true; }
        ele.addEventListener('focus', evt => {
          const ths = (evt.target || evt.srcElement).name;
          this.triggerBind('focusFormField', ths);
        });
        /*ele.addEventListener('blur', evt => {
          this.triggerBind('focusFormField', null);
        });*/
      });
    },

    //表单
    fillReportForm(dat) {

      if(dat) {
        let fm = emptyForm();
        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //
        if(fm.operationComplicationsCode) {
          let operationComplicationsDictCode = [];
          fm.operationComplicationsCode.forEach(p => {
            operationComplicationsDictCode.push(p.dictValue);
          });
          fm.operationComplicationsDictCode = operationComplicationsDictCode;
        }
        //
        if(fm.examParts) {
          let examParts_names = [];
          fm.examParts.forEach(p => {
            examParts_names.push(p.partsName);
          });
          fm.examParts_names = examParts_names.join(",");
        }
        //
        /*if(!fm.examResultProp || !fm.examResultProp.dictCode) {
          fm.examResultProp = {dictValue: "0"};
        }*/
        //
        //console.log(this.currentUser);
        if((!fm.reportDoctor || !fm.reportDoctor.nickName) && this.currentUser) {
          fm.reportDoctor = this.currentUser;
        }
        //
        //if(fm.resultStatus && fm.resultStatus.dictValue === StatusDict.report
        //   && (!fm.auditDoctor || !fm.auditDoctor.userName) && this.currentUser) {
        //  fm.auditDoctor = this.currentUser;
        //}

        //当报告为未审核状态时，同时记录人员，检查医生，会诊医生为空，将历史保存填入
        //console.log(this.reportable,this.auditable,this.withdrawable,)
        if((!fm.recordersName) && !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && this.reportForm_his) {
            fm.recordersName = this.reportForm_his.recordersName;
        }
        if((!fm.examDoctorsName)&& !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && !this.reauditable && this.reportForm_his) {
            fm.examDoctorsName = this.reportForm_his.examDoctorsName;
        }
        if((!fm.consultantsName)&& !matchAnyStatus(fm, StatusDict.audit, StatusDict.reaudit) && !this.reauditable && this.reportForm_his) {
            fm.consultantsName = this.reportForm_his.consultantsName;
        }

        this.reportForm = fm;
        //Object.assign(this.reportForm, fm);
        //console.log(this.reportForm);
        this.reportOrig = cloneDeep(fm);
      }
    },
    /**
     * 写报告
     */
    write(exam, opt = {}) {
      let prom;
      //未保存检查
      const modi = this.checkModi();
      if(modi) {
        modi.unshift("当前报告有以下未保存修改，是否打开新报告？");
        prom = this.$modal.confirm(modi.join("<br/>- "), {dangerouslyUseHTMLString: true});
      } else {
        prom = Promise.resolve(1);
      }
      prom.then(() => {
        //
        this.isFistfocus = true;
        //
        this.clearAuditForPrint();
        //
        this.setTransferAction(null);
        //
        this.editable = false;
        this.reportForm = emptyForm();
        this.currentItemIdx = null;
        this.reportOrig = null;
        //
        if(!exam || !exam.id) {
          return;
        }
        eiapi.get(exam.id).then(res => {
          const dat = res && res.data;
          if(dat) {
            //
            let images = dat.images;
            if(images) {
              images.forEach(e => {
                const parts = e.path? e.path.split("/") : null;
                if(parts && parts.length == 3) {
                  const studyInstanceUid = parts[0], seriesInstanceUid = parts[1], sopInstanceUid = parts[2];
                  e["studyInstanceUid"] = studyInstanceUid;
                  e["seriesInstanceUid"] = seriesInstanceUid;
                  e["sopInstanceUid"] = sopInstanceUid;
                  /*let studyInstanceUid0 = studyInstanceUid;
                  if(FileTypes.jpeg === e.fileType && -1 === studyInstanceUid0.indexOf(FakeUidPrefix)) {
                    studyInstanceUid0 = FakeUidPrefix + studyInstanceUid0;
                  }
                  //e["uri"] = imapi.loadImage(studyInstanceUid0, seriesInstanceUid, sopInstanceUid);*/
                }
              });
            }
            this.fillReportForm(dat);
            //获取检查类型，放射检查时，是否使用多屏
            if(!this.splitScreen(dat)) {
              this.loadReportImages();
            }
            //审核打印，审核后生成报告文档：jpg、pdf
            const {nextTick} =  opt;
            if(!!nextTick && (nextTick.includes("print") || nextTick.includes("exportAsDoc"))) {
              //this.$nextTick(() => {
                this.handlePreview('report::' + nextTick.join(","));
              //});
            }
            //是否允许直接采集
            const fm = this.reportForm;
            if("/ReportWriting" === this.$route.path && matchAnyStatus(fm, StatusDict.regist, StatusDict.exam, StatusDict.report)) {
              this.triggerBind("dispatchAction", "report::activeCollectImage", fm);
            }
          }
        });
      });
    },
    /**
     * 保存/审核报告
     * @param opt {cmd: null, firmed: false, silence: false}, 审核时传入cmd='audit'
     */
    save(opt = {}) {
      if(this.transferActive) { 
        this.$modal.msgWarning("请取消转移采集影像。");
        return;
      }

      const fm = this.reportForm;
      if(!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      //须选择阴/阳
      /*let erpc = !!fm.examResultProp? fm.examResultProp.dictValue : null;
      if("0" !== erpc && "1" !== erpc && "2" !== erpc) {
        this.d_examResultProp=true
        this.his_opt=opt
        //this.$modal.alert("请明确该检查为阴性或阳性。");
        return;
      }*/
      //
      if(!this.checkSelectedImagesCount()) {
        return;
      }

      if(!fm.operNurse.nickName){
        this.$modal.msgWarning("请选择或填写护士");
        return;
      }

      //保存不执行未保存检查
      this.reportOrig = null;
      //审核
      const auditReport = "audit" === opt.cmd;
      if(auditReport&&!fm.operationGrade.dictValue){
        this.$modal.msgWarning("审核报告须选择手术级别");
        return;
      }

      //
      let confirmMsg = auditReport? "是否审核该报告？<br>" : "是否保存报告？";
      //是否弹出确认框
      let firmed = opt.firmed;
      //检查所见、检查诊断、检查图像是否都为空
      if(!fm.examDesc) {
        confirmMsg += "该检查<span style='color: red;'>\"检查所见\"</span>无内容。请确认？<br>";
        firmed = false;
      }
       if( !fm.examDiagnosis ) {
        confirmMsg += "该检查<span style='color: red;'>\"检查诊断\"</span>无内容。请确认？<br>";
        firmed = false;
      }
      if(undefined!=this.imagesSet && !this.imagesSet.length) {
        confirmMsg += "该检查<span style='color: red;'>\"检查图像\"</span>无内容。请确认? <br>";
        firmed = false;
      }
      else if(!this.numImagesSelected) {
        confirmMsg += "该检查<span style='color: red;'>\"未选择图像\"</span>无内容。请确认? <br>";
        firmed = false;
      }
      //
      let prom = firmed? Promise.resolve(true) : this.$modal.confirm(confirmMsg, {dangerouslyUseHTMLString: true});
      prom.then(this.$refs.reportForm.validate).then(() => {

        let postData = cloneDeep(fm);
        //dicom影像路径
        //const re = new RegExp("^[^/]+//[^/]+(/ext/dicom-web/rs/.+)$");
        //报告图片
        let images = postData.images.map(e => {
          /*if("report::image" === e.type) {
            return e;
          }
          let imgo;
          //dicom
          if(this.typeofDcm(e)) {
            let fileUrl = e.fileUrl, grps = fileUrl? re.exec(fileUrl) : null;
            if(!grps) {
              return null;;
            }
            imgo = {
              "path" : grps[1]
              , "fileType": FileTypes.dicom.name
            };
          } else {
            //动态/静态/其它
            imgo = {
              "path": `${e.studyInstanceUid}/${e.seriesInstanceUid}/${e.sopInstanceUid}`
              , "fileType": FileTypes.jpeg.name
            };
          }

          return imgo;*/
          return this.convImgToAtta(e);
        }).filter(e => !!e);
        postData.images = images;
        //images.forEach(e => {
        //  e['path'] = `${e.studyInstanceUid}/${e.seriesInstanceUid}/${e.sopInstanceUid}`;
        //  e["fileType"] = assertJpeg(e.uri)? FileTypes.jpeg.name : FileTypes.dicom.name;
        //});
        //

        //保存当前form表单
        //console.log(fm)
        this.$store.dispatch("saveReportForm", cloneDeep(fm));

        if(auditReport) {
          return eiapi.auditReport(postData);
        } else {
          //更新呼叫机房未当前机房
          postData.equipRoom = this.currentEquipRoom;
          
          return eiapi.saveReport(postData);
        }
      }).then(res => {
        //console.log(res);
        //if(res && 200 == res.code) {
          //已知错误：获取扫码认证失败
          if('qrnoauth' === res.errC) {
            if(null == this.qr) {
              this.qr = new QRSignRel(() => {
                this.qr.activated = false;
                opt.firmed = true;
                this.save(opt);
              });
            }
            this.qr.activated = true;
            this.qr.refreshQrcode();
            return;
          }
          if(!opt.silence) {
            this.$modal.msgSuccess("操作完成。");
          }
          //重新读取修改的报告
          let rewriteOpt;
          //是否打印请求的审核
          if(auditReport) {
            //
            eiapi.get(fm.id).then(res => eiapi.uploadReportDoc(res.data) );
            //
            let nextTick = [];//"exportAsDoc"
            if(!!this.reportToAuditForPrint) {
              nextTick.push("print");
            }
            rewriteOpt = {nextTick};
          }
          //清除打印审核请求
          this.clearAuditForPrint();
          //
          this.write(fm, rewriteOpt);
          this.triggerBind("refreshExamInfo");
        //}
      }).catch(err => {
        console.error("执行出错, %s", err);
        //清除打印审核请求
        this.clearAuditForPrint();
      });
    },
    //读取已采集的图片
    loadReportImages() {
      //this.collectImages();
      let fm = this.reportForm;
      imapi.findStudies({examInfo: {id: fm.id}, pageSize: 9999})
        .then(res => {
          const studies = res.rows;
          if(!studies || studies.length <= 0) { return; }

          studies.forEach(study => {
            if(!study) { return true; }
            let seriesSet = study.seriesSet;

            if(!seriesSet || seriesSet.length <= 0) { return true; }

            seriesSet.forEach(ser => {
              let imagesSet = ser.imagesSet;
              if(imagesSet && imagesSet.length) {
                imagesSet.forEach((img, i) => {
                  let {studyInstanceUid, fileType} = img;
                  img.studyInstanceUid0 = ((FileTypes.jpeg.name === fileType || FileTypes.jpeg.code === fileType)? FakeUidPrefix : "") + studyInstanceUid;
                  this.structExamImage(img, imagesSet, i);
                });
              }
            });

          });
          //console.log(study)
          fm.dicomStudies = studies;
          //console.log(fm);
          this.loadExamImages();
        });
    },

    //当前编辑的报告
    get() {
      return this.reportForm;
    },
    //更新指定属性“检查所见/检查诊断/术后医嘱”
    //@replace true-替换, false-末尾追加
    updateProp(nam, val, replace = true) {
      if(!this.reportable || !val) {
        return;
      }

      let fm = this.reportForm;
      if(!fm.id) {
        this.$modal.alert(ErrM_Unwrite);
        return;
      }
      //console.log("name=%s, value=%s", nam, val);
      if(!replace) {
        val = (this.reportForm[nam] ? this.reportForm[nam]+'\n' : '')  + val;
      }
      //
      this.$set(this.reportForm, nam, val);
      //this.reportForm[nam] = val;
      //Object.assign(this.reportForm, this.reportForm);
    },

    // 设置图片类型，来源，选中状态/是否作为报告影像
    structExamImage(img, imagesSet, index) {
      if(!img.id) { return; }

      const {studyInstanceUid, sopInstanceUid, fileType} = img;
      let sopUri; 
      //jpeg和动态
      if(this.typeofJpg(img) || this.typeofVideo(img)) {
        sopUri = imapi.imageLocate(img); 
        //
        let selectedImages = this.reportForm.images;//已选影像
        let selectedImage = !!selectedImages? selectedImages.find(i => i.sopInstanceUid === sopInstanceUid) : null;

        img.fileType = img.fileType || FileTypes.jpeg.code;//assertJpeg(sopUri)? FileTypes.jpeg.name : FileTypes.dicom.name;
        img.uri = sopUri;
        img.selected = !!selectedImage;
        img.transferSelected = false;
        //为倒叙
        //selectedImages.forEach((ser, i) => {
        //    ser.selectNum = 1 + i;
        //})

        if(selectedImage) { selectedImage.uri = img.uri; }
      } else if(FileTypes.dicom.code == fileType || FileTypes.dicom.name == fileType) {
        //dicom
        //获取浏览地址，获取影像DICOM参数
        imapi.dcmViewer(this.reportForm.id, studyInstanceUid).then(res => {
          const dicomStudy = res.dicomStudy || {};
          if(!dicomStudy) {
            this.$modal.msgWarning("该检查没有影像信息。");
            return true;
          }
          const suid = dicomStudy.dicomStudyInstanceUid || dicomStudy.studyInstanceUid;
          return imapi.indexImages(suid);
        }).then(res => {
          const items = res.data;
          if(!!items && !!items.length) {
            items.forEach(ig => {
              //if(!ig.fileUrl.endsWith("/2")) {return true;}
              if(-1 != this.imagesSet.findIndex(e => e.sopInstanceUid === ig.sopInstanceUid)) { return true; }
              //
              const fileUrl = ig.fileUrl;
              //提交时保留的路径
              let selectedImages = this.reportForm.images;//已选影像
              let selectedImage = !!selectedImages? selectedImages.find(i => fileUrl.endsWith(i.path)) : null;
              //
              ig.uri = `wadors:${fileUrl}`;
              ig.selected = !!selectedImage;
              imagesSet.push(ig);
              //元信息
              cornerstoneWADOImageLoader.wadors.metaDataManager.add(ig.uri, JSON.parse(ig.noteInfo));
            });
            this.loadExamImages();//this.$nextTick();
          }
        });
      }
    },
    //cornerstone加载图片
    loadExamImages(opts = {}) {
      //
      //加载图片前，根据选择重新排序显示
    //   console.log(this.reportForm.images,this.imagesSet)
      this.reportForm.images.forEach((ser, i) => {
          ser.selectNum = 1 + i;
          this.imagesSet.forEach(img=>{
              if(this.isSameImage(ser, img)){
                  img.selectNum=ser.selectNum
              }
          })
          
      });
      //读取图像数据
      this.$nextTick(() => {
        this.getImagesElement().forEach((c, i) => {
          //console.log(i, c);
          const image = this.imagesSet[i];
          if(this.typeofVideo(image)) {
            cornerstone.disable(c);
          } else {
            try {
              //是否已
              cornerstone.getEnabledElement(c);
            } catch (err) {
              cornerstone.enable(c);
            }
            let idx = parseInt(c.parentNode.dataset.index);
            if(isNaN(idx)) {
              idx = i;
            }
            this.loadExamImage(c, idx);
          }
        });
        //滚动条到最近
        if(opts.focusLast) {
          this.$nextTick(() => {document.querySelector(".cornerstone-elements-pane").scrollLeft = 0;});
        }
      });
    },

    //获取>显示影像
    loadExamImage(element, idx) {
      const imagesSet = this.imagesSet;
      //console.log(imagesSet);
      let image = imagesSet[idx];
      if(!this.validateExamImage(image)) { return; }
      //console.log(image);
      let imageId = image.uri;//wadoForImage(image.uri);
      //console.log(imageId);
      try {
        loadAndDisplay(element, imageId);
      } catch (err) { console.error(err); }
    },
    reloadExamImage(img) {
      const imagesSet = this.imagesSet;
      const idx = imagesSet.findIndex(e => e.uri === img.uri);
      if(-1 !== idx) {
        removeImageCache(img.uri);

        const ele = this.getImagesElement()[idx];
        this.loadExamImage(ele, idx);
      }
    },
    //选择影像
    selectExamImage(state, item) {
      //
      item.selectNum = null;
      delete item.selectNum;
      //对比报告图像和采集图像
      let fimg = this.isSameImage;

      let selectedImages = this.reportForm.images;
      let idx = selectedImages.findIndex(si => fimg(si, item));

      //取消勾选
      if(!state && -1 !== idx) {
        selectedImages.splice(idx, 1);
        selectedImages.forEach((ser, i) => {
            ser.selectNum = 1 + i;
            this.imagesSet.forEach(img => {
                if(fimg(ser, img)){
                    img.selectNum=ser.selectNum
                }
            })
        })
      } else if(state && -1 === idx) {
        const count = selectedImages.length
        item.selectNum = count + 1;

        selectedImages.push(item);
      }
    },

    //删除影像
    deleteExamImage(item) {
      this.$modal.confirm("是否确定?").then(() => {
        const fm = this.reportForm, dicomStudy = fm.dicomStudies[0];
        
        if(!dicomStudy.seriesSet) { return ; }

        const series = dicomStudy.seriesSet.find(ser => ser.studyInstanceUid === item.studyInstanceUid && ser.seriesInstanceUid === item.seriesInstanceUid);
        
        let imagesSet = series? series.imagesSet : null;
        if(!imagesSet) { return ; }

        const idx = imagesSet.findIndex(i => i.sopInstanceUid === item.sopInstanceUid);
        if(-1 === idx) { return ; }
        const img = imagesSet[idx], selectedImages = fm.images;
        const pos = selectedImages.findIndex(si => si.sopInstanceUid === img.sopInstanceUid);
        if(-1 != pos) {
          selectedImages.splice(pos, 1);
        }
        imagesSet.splice(idx, 1);
        item.status = 2;
        //
        selectedImages.forEach((ser, i) => {
            ser.selectNum = 1 + i;
        })
        //
        //this.$nextTick(() => {
          imapi.delImage(item.id).then(this.loadExamImages);
        //});
          //
        if(this.dialogVisible) {
          const imagesLength=this.imagesSet.length-1
          //const delFlag=this.deleteExamImage(this.imagesSet[this.currentItemIdx])
          if(this.currentItemIdx>imagesLength){
              this.nextView(-1)
          }else if(imagesLength<0){
              this.dialogVisible=false
          }else{
              this.nextView(1)
          }
        }
    });
    },
    //是否图片已删除
    isExamImageDeleted(img) {
      return 2 === img.status;
    },
    validateExamImage(img) {
      return !this.isExamImageDeleted(img) && !!img.uri;
    },
    //上报危急值
    reportCriticalValues() {
      this.triggerBind("dispatchAction", "report::critical", this.reportForm);
    },
    //取消检查
    handleCancel() {
      this.confirmToUpdateResultStatus(this.reportForm, StatusDict.cancel).then(res => {
        this.$modal.msgSuccess("操作成功");
        this.triggerBind("refreshExamInfo");
      });
    },
    //预览
    handlePreview(mix) {
      //选择的图像情况
      if(!this.checkSelectedImagesCount()) {
        return;
      }
      //工作状态为检查中和已报告的，保存报告
      /*const exam = this.reportForm, resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
      if(!undefinedOrNull(resultStatusCode) && StatusDict.exam === resultStatusCode || StatusDict.report === resultStatusCode) {
        this.save({firmed: true, silence: true});
      }*/
      //是否指定操作
      let cmd = ("string" === (typeof mix)) && mix.startsWith("report::")? mix : "report::preview";
      this.triggerBind("dispatchAction", cmd, this.reportForm);
    },

    //审核
    handleAudit(mix) {
      if(!this.hasPermi(PERMI_AUDIT)) {
        this.$modal.alert("您没有审核报告的权限。");
        return;
      }
      let opt;
      //点击按钮审核
      if(mix instanceof Event) {
        this.clearAuditForPrint();

        opt = {};
      } else {
        opt = mix;;
      }
      opt.cmd = "audit";
      opt.firmed = true
      opt.silence = true;
      this.save(opt);
    },
    //复核
    handleReaudit() {
      const fm = this.reportForm;
      eiapi.reauditReport(fm).then(res => {
        this.$modal.msgSuccess("报告复核完成。");
        this.write(fm);
        this.triggerBind("refreshExamInfo");
      });
    },
    //采集调用
    addCollectImage(image) {
      const rp = this.reportForm; 
      if(!this.reportable) {
        const rs = rp.resultStatus, statusLabel = !!rs? rs.dictLabel : "未知";
        this.$modal.alert("无法采集影像，原因：该报告状态为" + statusLabel);
        return ;
      }
      this.structExamImage(image);
      //
      let seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      let imagesSet = seriesSet && seriesSet.length? seriesSet[0].imagesSet : null;
      if(!imagesSet) {
        imagesSet = [];
        this.reportForm.dicomStudies[0].seriesSet = [{imagesSet}];
        seriesSet = this.reportForm.dicomStudies[0].seriesSet;
      }
      seriesSet[0].studyInstanceUid = image.studyInstanceUid;
      seriesSet[0].seriesInstanceUid = image.seriesInstanceUid;
      imagesSet.push(image);
      //console.log(this.reportForm, seriesSet);
      this.loadExamImages({focusLast: true});
      //
      rp.resultStatus = Object.assign({}, rp.resultStatus, {dictValue: StatusDict.exam});
    },
    //放大影像
    zoomImage(evt) {
      let ele = !!evt? (evt.currentTarget || evt.target) : null, idx = !!ele? parseInt(ele.parentNode.dataset.index) : -1;
      if(-1 >= idx) { 
        this.$refs.imageBubble.close();
        return ; 
      }
      const imagesSet = this.imagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      //视频不预览
      if(this.typeofVideo(img)) {
        return;
      }
      //
      this.$refs.imageBubble.view(img, evt);
    },
    //预览影像
    /*viewImage(idx) {
      const imagesSet = this.imagesSet;
      if(!imagesSet || idx >= imagesSet.length) { return ; }
      const img = imagesSet[idx];
      this.$refs.imageView.view(img);
    },*/
    //聚焦最新采集的图像
    scrollImage(dir) {
      let imageScrollView = this.$refs.imageScrollView, cele;
      if(imageScrollView.clientWidth === imageScrollView.scrollWidth || !(cele = imageScrollView.querySelector(".cornerstone-element-container"))) { return; }

      let scollLeft = imageScrollView.scrollLeft;
      const scrollStep = 12 + cele.clientWidth;
      scollLeft += dir * scrollStep;
      imageScrollView.scrollLeft = Math.max(0, scollLeft);
    },
    //检查勾选的图像数是否合理
    checkSelectedImagesCount() {
      //图象数只能是0, 1, 2, 3, 4, 6
      const images = this.reportForm.images, numSupported = [0, 1, 2, 3, 4, 6];
      const numImages = !!images? images.length : 0;
      if(!numSupported.includes(numImages)) {
        this.$modal.alert("影像张数应为: " + numSupported.join(", "));
        return false;
      }

      return true;
    },
    //召回报告
    handleWithdrawAuditReport() {
      if(!this.hasPermi(PERMI_AUDIT)) {
        this.$modal.alert("您没有召回报告的权限。");
        return;
      }
      //this.$modal.confirm("是否确定召回报告？").then(res => {
          this.triggerBind("dispatchAction", "report::withdrawAudit", this.reportForm);
      //});
    },
    viewImage(evt){
        this.dialogVisible=true
        
        let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
        /*//console.log(this.imagesSet)
        this.imageURL=this.imagesSet[idx]
        this.currentItemIdx=idx
        //console.log( this.currentItemIdx)*/
        this.$refs.imageView.view(this.imagesSet, idx);
    },
    nextView(val){
        
        const imagesLength=this.imagesSet.length-1
        if((-1===val&&this.currentItemIdx+val<0) || (1===val&&this.currentItemIdx+val>imagesLength)){
            return
        }
        this.currentItemIdx=this.currentItemIdx+val
        this.imageURL=this.imagesSet[this.currentItemIdx]
    },
    //播放动态采集
    viewVideo(evt) {
      let ele = evt.currentTarget || evt.target, idx = parseInt(ele.parentNode.dataset.index);
      const image = this.imagesSet[idx];
      if(!!image && !!image.uri && this.typeofVideo(image)) {
        this.$refs.videoView.view(image);
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param opts {}
     */
    toPickUser(opts) {
      const fm = this.reportForm;
      if(!fm.id) { return; }
      //触发元素/选择后填入的属性
      let tar = opts.target;
      let names, codes;
      if(tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }
      //初始选中
      let selectedUsers = [];
      if(!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({userName: e, nickName: names[i]});
        });
      }
      opts.selectedUsers = selectedUsers;
      //打开选中框
      this.$refs.userPicker.showPicker(opts);
    },
    pickUser(tar, users) {
      let fm = this.reportForm, names = [], codes = [];
      if(!!users.length) {
        users.forEach(e => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }
      names = names.join(",");
      codes = codes.join(",");

      if(tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }
    },
    //
    cleanExam(){ 
      this.reportForm.examDesc=""
      this.reportForm.examDiagnosis=""
    },
    //清除打印审核请求
    clearAuditForPrint() {
      this.$store.dispatch("auditForPrint", null);
    },
    switchTab(val){
        if(this.isFistfocus){
          this.triggerBind('switchTab',val)
          this.isFistfocus=false
        }
    },
    //是否有未保存的修改
    checkModi() {
      let fields = [];

      const orig = this.reportOrig, fm = this.reportForm;
      if(orig && fm) {
        if(orig.examDesc != fm.examDesc) { fields.push("检查所见"); }
        if(orig.examDiagnosis != fm.examDiagnosis) { fields.push("检查诊断"); }
      }

      return fields.length > 0? fields : null;
    },
    //报告图像是否采集的图像
    isSameImage(rimg, cimg) {
      if(this.typeofDcm(cimg)) {
        return cimg.fileUrl === rimg.fileUrl || cimg.fileUrl.endsWith(rimg.path);
      } else {
        return rimg.sopInstanceUid == cimg.sopInstanceUid;
      }
    },
    //缩略图
    getImagesElement() {
      return document.querySelectorAll(".report-writer-image div.cornerstone-element");
    },

    //获取图像部位、病变信息
    getImageLesionInfo(index,omit){
      if(!index) return;
      let noteInfo = this.reportForm.images[index-1].noteInfo;
      if(!noteInfo) return;
      let attaInfo = JSON.parse(noteInfo);
      let parts = attaInfo.parts?attaInfo.parts:"";
      let lesion = attaInfo.lesion?attaInfo.lesion:"";
      let partsLength = parts.length;
      let lesionLength = lesion.length;

      //长度超过9且可省略，截取返回
      if(partsLength+lesionLength>9&&omit){
        if(partsLength>3){
          parts = parts.slice(0,3);
          parts+="...";
        }
  
        if(lesionLength>3){
          lesion = lesion.slice(0,3);
          lesion+="...";
        }
      }
      return parts+lesion;
    }
  },
  //加载完成执行
  mounted() {
    this.onPageLoad();
  },
  //组件
  activated() {
    this.onPageLoad();
  },

  computed: {
    //当前机房，当前编辑的检查信息
    ...mapGetters(['currentEquipRoom', 'examInfoEdited', 'reportToAuditForPrint','reportToAudit','reportForm_his']),
    //采集的影像
    imagesSet() {
      const fm = this.reportForm, dicomStudies = fm.dicomStudies;
      if(!dicomStudies || dicomStudies.length <= 0) { return; }

      let imagesSet = [];

      dicomStudies.forEach(dicomStudy => {
        const seriesSet = !!dicomStudy && !!dicomStudy? dicomStudy.seriesSet : null;
        if(!seriesSet) {
          return;
        }
        
        seriesSet.forEach(ser => {
            let images = ser.imagesSet;
            if (!images) {
                return true;
            }
            //刷新页面时,选中序号更新
            //var count = 1
            images.forEach(img => {
                if (img.status !== 2 && !!img.uri) {
                    imagesSet.push(img);
                }
                //if (img.selected) {
                //    img.selectNum = count++
                //}
            });
        });
      });
      //console.log(imagesSet);
      return imagesSet.reverse();
    },
    //是否可编辑，报告状态为有效（0），且工作状态为已检查或已报告，一些按钮是否可用
    reportable() {
      const fm = this.reportForm;

      return fm.id //this.editable && 
      && (!fm.status) 
      && matchAnyStatus(this.reportForm, StatusDict.regist, StatusDict.exam, StatusDict.report);//(!fm.resultStatus || !fm.resultStatus.dictValue || /^[012]$/.test(fm.resultStatus.dictValue));
    },
    //是否可审核
    auditable() {
      return matchAnyStatus(this.reportForm, StatusDict.report);//!!this.reportForm.resultStatus && '2' === this.reportForm.resultStatus.dictValue;
    },
    //是否可复审
    reauditable() {
      return matchAnyStatus(this.reportForm, StatusDict.audit);//!!this.reportForm.resultStatus && '3' === this.reportForm.resultStatus.dictValue;
    },
    //
    withdrawable() {
      return matchAnyStatus(this.reportForm, StatusDict.audit, StatusDict.reaudit,StatusDict.print);//!!this.reportForm.resultStatus && /^[34]$/.test(this.reportForm.resultStatus.dictValue);
    },
    //是否可打印
    printable() {
      //已报告、已审核、已复核、已打印、已归档
      return matchAnyStatus(this.reportForm, StatusDict.report, StatusDict.audit, StatusDict.reaudit, StatusDict.print,StatusDict.archive);
    },
    //提交按钮说明
    editButtonLabel() {
      const fm = this.reportForm;
      return matchAnyStatus(fm, StatusDict.regist, StatusDict.exam)? "提交"  : "保存";
    },
    //已选择图像数
    numImagesSelected() {
      const imagesSet = this.imagesSet;
      if(!imagesSet) { return 0; }
      
      return imagesSet.filter(i => i.selected).length;
    },
    //图像预览/编辑窗口宽度
    viewDialogDemi() {
      //预览的图像是未勾选/已勾选状态
      const imagesSet = this.imagesSet, img = imagesSet? imagesSet[this.currentItemIdx] : null;
      return img && img.selected? {width: "940px"} : {width: "800px"};
    }
  },

  watch: {
    //当前报告检查信息是否有修改
    examInfoEdited: {
      deep: true,
      handler(nval, oval) {
        //console.log(nval, oval);
        let fm = this.reportForm;
        if(!!nval && !!nval.id && !!fm && nval.id === fm.id) {
          const props = ["inpNo", "bedNo"];
          props.forEach(p => {
            fm[p] = nval[p];
          })
        }
      }
    },

    //打印时要审核的报告
    reportToAuditForPrint: {
      deep: true,
      handler(nval, oval) {
        if(!nval) {
          return;
        }
        this.handleAudit({firmed: true});
      }
    },

    "reportForm.operationComplicationsDictCode":{
      handler(nval, oval) {
        let vm = this;
        vm.reportForm.operationComplicationsCode = [];
        if(nval.length>0){
          nval.forEach(e=>{vm.reportForm.operationComplicationsCode.push({dictValue:e})});
        }
      }
    },

    // operationComplicationsCode:{
    //   handler(nval, oval) {
    //     let vm = this;
    //     vm.reportForm.operationComplicationsCode = [];
    //     if(nval.length>0){
    //       nval.forEach(e=>{vm.reportForm.operationComplicationsCode.push({dictValue:e})});
    //     }
    //   }
    // },

    //打印时要审核
    reportToAudit: {
        deep: true,
        handler(nval, oval) {
          if(!nval) {
            return;
          }
          this.handleAudit({firmed: true});
        }
      }
  }

};

export default model;