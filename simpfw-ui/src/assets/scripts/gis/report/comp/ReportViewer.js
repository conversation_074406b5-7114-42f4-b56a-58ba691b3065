import {cloneDeep} from "lodash";
//
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorkerSrc from 'pdfjs-dist/build/pdf.worker.entry';
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorkerSrc;
//
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
//
import * as api from "@/assets/scripts/gis/exammanagement/examinfo/api";
//报告采集影像和报告影像
import { ReportImageFun } from "@/assets/scripts/pacs/image/fun.image";
//
import { PageFitFun } from "@/assets/scripts/pacs/report/mixins/fun.PageFit";
//报告状态
import {StatusDict, matchAny as matchAnyResultStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//检查编辑
import {TransModel, UndoModel, ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

//
const model = {
  extends: BaseDialogModel,

  mixins: [ReportImageFun, PageFitFun,ResultStatus],

  data() {
    return {
      report: null,

      pdfDoc: null,
      numPdfPages: 0,
      
      extOperExportAsDoc: false
    }
  },

  methods: {
    /**
     * 浏览
     * @param {*} report 
     * @param {*} opt 'print'-打印
     */
    view(report, opt) {
      this.options = !!opt? opt.split(",") : null;
      
      this.report = cloneDeep(report);

      this.open();

      this.loadPdfFile();
    },
    //制作模板
    asReportTemplate() {
      this.triggerBind("makeTplDoc", this.report);

      this.close();
    },
    //审核报告
    audit() {
      const report = this.report;
      //请求审核报告
      try {
        this.$store.dispatch("audit", report);
      } catch (err) { console.error(err); }
      this.close();    
    },
    //打印
    plint() {
      const report = this.report;
      if(!matchAnyResultStatus(report, StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive)) {
        //const resultStatus = report.resultStatus;
        this.$modal.confirm("打印报告须审核，是否进行审核？").then(() => {
          //请求审核报告
          try {
            this.$store.dispatch("auditForPrint", cloneDeep(report));
            
          } catch (err) { console.error(err); }
          this.close();
        });
        return;
      }
      //防止未审核右键或浏览器菜单打印
      const dialogWrap = this.dialogRef, printingdialog = "printingdialog";
      let clz = dialogWrap.className.split(" ");
      if(!clz.includes(printingdialog)) { dialogWrap.className += " " + printingdialog; }

      //打印并更新检查状态为已打印
      this.updateResultStatus(this.report, StatusDict.print).then(res => {
        if(res && 200 === res.code) {
          window.print();
        }

        //恢复打印前视图
        //if(pageFitted) {
        //  this.switchPageSize();
        //}
        if(!clz.includes(printingdialog)) { dialogWrap.className = clz.join(" "); }
        /*if(asTemplate) {
          this.report = report;
        }*/
      });

    },
    //却换页面视图
    switchPageSize() {
      if(this.pageFitted) {
        this.actualSize();
      } else {
        this.fitPage();
      }

      const wrap = this.dialogRef;
      const pageView = wrap.querySelector(".report-previewer-wrap");
      //
      const dia = wrap.querySelector(".el-dialog");
      //
      let zoom = this.scaleValue(pageView.style.transform);
      const dialogWidth = pageView.offsetWidth * zoom;
      //
      dia.style.width = (dialogWidth + (16 * 2)) + "px";
      //
      let dialogBodyHeight = "unset";
      if(this.pageFitted) {
        dialogBodyHeight = pageView.offsetHeight * zoom;
        dialogBodyHeight = (dialogBodyHeight + (4 * 2)) + "px";
      }
      dia.querySelector(".el-dialog__body").style.height = dialogBodyHeight;    
    },
    /**
     * 读取PDF文件
     */
    loadPdfFile () {
      this.pdfDoc = null;
      this.numPdfPages = 0;
      //报告影像地址
      let report = this.report, images = report.images;
      if(!!images) {
        images = images.map(img => this.convImgToAtta(img));
        report.images = images.filter(img => !!img);
      }
      //
      this.extOperExportAsDoc = true;
      //
      api.viewReportDoc(report).then(r => {
        const dat = atob(r.data);
        return pdfjsLib.getDocument({ data: dat}).promise;//, cMapPacked: true
      }).then(pdf => {
        this.extOperExportAsDoc = false;
        this.pdfDoc = pdf;
        this.numPdfPages = pdf.numPages;
        console.log(pdf.numPages);
        //生成第1页 
        this.$nextTick(() => {
          this.renderPdfPage(1)
        })
      }, err => {
        this.extOperExportAsDoc = false;
        if(err.name == "MissingPDFException"){
          this.$model.msgWarning("无效的PDF")
        }
      })
    },
    //生成指定PDF页
    renderPdfPage (num) {
      //
      this.pdfDoc.getPage(num).then(page => {
        //
        let canvas = this.dialogRef.querySelector(`div[data-page='report-previewer-page-${num}'] canvas`)
        let ctx = canvas.getContext("2d");
        ctx.mozImageSmoothingEnabled = false;
        ctx.webkitImageSmoothingEnabled = false;
        ctx.msImageSmoothingEnabled = false;
        ctx.imageSmoothingEnabled = false;
        //scale避免模糊
        let viewport = page.getViewport({scale: 2});
        //let scale = Math.min(canvas.clientWidth / viewport.width, canvas.clientHeight / viewport.height);
        //viewport.scale = scale;
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        //viewport.width = canvas.clientWidth
        //viewport.height = canvas.clientHeight;
        let renderContext = {
          canvasContext: ctx,
          viewport: viewport,
          //transform: [scale, 0, 0, scale, 0, 0]
        };
        page.render(renderContext).promise.then(() => {
          //page.getTextContent()
          //继续直到最后1页
          if (this.numPdfPages > num) {
            this.renderPdfPage(num + 1)
          } else {
            if(this.extOperPrint) {
              this.plint();
            }
          }
        });
      });
    }
  }
};
export default model;
