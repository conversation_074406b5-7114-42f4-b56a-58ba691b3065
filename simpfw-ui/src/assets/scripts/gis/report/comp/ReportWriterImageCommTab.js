import {cloneDeep} from "lodash";

import { getToken } from '@/utils/auth'
import {baseURL} from "@/utils/request";

//多屏展示
import {browserName} from "@/assets/scripts/screen-helper";

import * as mh from "@/assets/scripts/mine-helper";
//WebSocket支持
import BaseWebSocketModel from "@/assets/scripts/pacs/BaseWebSocketModel";
//放射检查modality
import {RAD_MODALITIES} from "@/assets/scripts/pacs/modalities";
//连接失败重连
let timer_callPacsHelper;
//
let imageWindows = {};
//
export default {
  extends: BaseWebSocketModel,

  data() {
    return {
      socketEnabled: true,
      //
      socketBaseUrl: "ws://127.0.0.1:54985",
      //socketUrl: null,
      splitScreenUsed: false,

      connectTimes: 0,

      examInfo: null,

      imageWindowQueue: new Set(),
      focusWindowQueue: new Set(),

      str_nam_reportsOnWriter: "pacs_rad_reportsOnWriter"
    }
  },

  methods: {
    //助手连接地址
    checkSocketUrl()
    {
      if(!this.socketUrl) {
        this.socketUrl = this.socketBaseUrl + "?token=" + getToken();
      }
    },
    //连接助手
    callPacsHelper() {
      if(!this.socketEnabled) { return; }
      //
      this.checkSocketUrl();
      //
      try {
        clearTimeout(timer_callPacsHelper);
        //
        ++ this.connectTimes;
        //
        this.openSocket();
      } catch (err) {
        console.error(err);
      }
    },
    //连接助手失败或发消息失败
    handleSocketError(evt) {
      //console.error(evt);
      //是否连接失败
      if(!this.isConnectedSocked) {
        if(1 == this.connectTimes) {
          this.howto();
        }
        //
        timer_callPacsHelper = setTimeout(this.callPacsHelper, 8 * 1000);
      }
    },
    //分屏
    splitScreen(exam) {
      //获取检查类型，放射检查时，是否使用多屏
      const examModality = !!exam.examModality? exam.examModality.dictValue : null;
      if(!this.splitScreenUsed || !examModality || !RAD_MODALITIES.includes(examModality)) {
        return false;
      }
      //this.examInfo = exam;
      this.addReportOnWriter(exam);
      //未连接助手，打开新窗口
      if(!this.isConnectedSocked) {
        this.showImageWindow(1);
      } else {
        this.handleSocketSend('{"cmd":"monitor","token":"' + getToken() + '"}');
      }

      //反馈已分屏
      return true;
    },

    showImageWindow(wno = 0) {
      if(0 === wno || isNaN(wno)) {
        const e = this.imageWindowQueue.values().next();
        wno = !!e? e.value : null;
        if(!wno) { return; }
        this.imageWindowQueue.delete(wno);
      }

      const examInfo = this.examInfo;
      //打开影像窗口，在调用助手将影像窗口移动到其它屏幕
      const imageViewer = location.protocol + "//" + location.host + "/RadReportWriterImage";//?exam= + examInfo.id;
      /*if(this.isConnectedSocked) {
        this.handleSocketSend('{"cmd":"browse","token":"' + getToken() + '","browser":"' + browserName() + '","url":"' + imageViewer +'"}');
      } else {
        if(!!imageWin) {
          imageWin.close();
        }
        imageWin = window.open(imageViewer, "_RhPacsImageViewer");
      }*/
      //同一个窗口打开，新窗口
      let imageWin = imageWindows[wno];
      //console.log(wno, imageWin);
      const newWin = !imageWin || imageWin.closed;
      if(newWin) {
        const winLeft = 8, winTop = 8, winWidth = screen.availWidth - (2 * winLeft), winHeight = screen.availHeight - (2 * winTop);
        imageWin = window.open(imageViewer, "_RhPacsImageViewer_" + wno, "left=" + winLeft + ",top=" + winTop + ",width=" + winWidth + ",height=" + winHeight + ",toolbar=0,resizable=0");
        //console.log(wno, imageWin);
        if(!imageWin) {
          this.$modal.alert("您浏览器阻止了弹出窗口，请允许弹出窗口。");
          return;
        }
        imageWindows[wno] = imageWin;
        imageWin.addEventListener("load", () => {
          this.requireFocusWindow(wno);
        }, false);
      }
      imageWin.focus();
      //
      if(!newWin) {
        this.requireFocusWindow(wno);
      }
      //等窗口打开
      /*setTimeout(() => {
        this.focusWindow(wno);
      }, 512);*/
    },
    //未安装助手
    howto() {
      const nt = this.$notify({
          title: '提示',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: '未<button type="button" class="el-button el-button--primary el-button--mini"><span>安装</span></button>或未启用软航PACS助手。',
          duration: 0
      });
      
      nt.$el.querySelector("button").addEventListener("click", this.whato, false);
    },
    //下载助手
    whato(evt) {
      mh.download(baseURL + "/common/res/file?fileName=PacsHelper.zip");
    },

    /**
     * 暂停重连
     */
    stepReconnect() {
      if(this.splitScreenUsed) {
        this.socketEnabled = false;
        clearTimeout(timer_callPacsHelper);
      }
    },

    /**
     * 重连
     */
    reconnect() {
      if(this.splitScreenUsed && !this.isConnectedSocked && this.connectTimes > 0) {
        this.socketEnabled = true;
        this.callPacsHelper();
      }
    },
    //
    requireFocusWindow(wno) {
      this.focusWindowQueue.add(wno);
      console.log("焦点窗口数=%d", this.focusWindowQueue.size);
      this.focusWindow();
    },
    /**
     * 置顶窗口
     */
    focusWindow() {
      const it = this.focusWindowQueue.values();
      const e = it.next();
      const wno = !!e? e.value : null;
      if(!wno) {
        return;
      }
      //console.log("focusWindow: %d", wno);
      const imageWin = imageWindows[wno]
      if(!imageWin || imageWin.closed) {
        return;
      }

      imageWin.focus();

      if(this.isConnectedSocked) {
        //console.log("focusWindow=%o, %d @ %d", imageWin, wno, new Date().getTime());
        this.handleSocketSend('{"cmd":"focusWindow","token":"' + getToken() + '","browser":"' + browserName() + '","index":' + wno + '}');
      }
    },
    //
    handleSocketReceived(evt) {
      //console.log(evt);
      const msgo = JSON.parse(evt.data);
      //
      if("monitor" === msgo.cmd && this.ofRoute.writing) {
        //获得显示器信息，
        const monitorsInfo = msgo.data;
        if(!!monitorsInfo && monitorsInfo.length >= 2) {
          for(let i = 1; i < monitorsInfo.length; i ++) {
            /*let delay = (i - 1) * 3200;
            setTimeout(() => {
              this.showImageWindow(i);
              imageWindowQueue
            }, delay);*/
            this.imageWindowQueue.add(i);
          }
          this.showImageWindow();
        } else {
          this.showImageWindow(1);
        }
      } else if("focusWindow" === msgo.cmd && this.ofRoute.writing) {
        //继续下一个浏览器窗口
        const wno = msgo.index;
        if(!isNaN(wno)) {
          
          this.showImageWindow();

          //console.log(this.focusWindowQueue);
          this.focusWindowQueue.delete(wno);
          //console.log("focusWindow>%d @ %o", wno, new Date());
          setTimeout(this.focusWindow, 32);
        }
      }
    },

    addReportOnWriter(examInfo) {
      //this.$store.dispatch("addReportOnWriter", cloneDeep(exam));
      let reportsOnWriter = this.getReportsOnWriter();
      if(!reportsOnWriter) {
        reportsOnWriter = [];
      }
      const pos = reportsOnWriter.findIndex(r => r.id === examInfo.id);
      if(-1 === pos) {
        reportsOnWriter.push(examInfo);
        localStorage.setItem(this.str_nam_reportsOnWriter, JSON.stringify(reportsOnWriter))
      }
    },

    removeReportOnWriter(examInfo) {
      let reportsOnWriter = this.getReportsOnWriter();
      if(!reportsOnWriter) {
        return;
      }
      const pos = reportsOnWriter.findIndex(r => r.id === examInfo.id);
      if(-1 !== pos) {
        reportsOnWriter.splice(pos, 1);
        localStorage.setItem(this.str_nam_reportsOnWriter, JSON.stringify(reportsOnWriter))
      }
    },

    getReportsOnWriter() {
      let reportsOnWriter = localStorage.getItem(this.str_nam_reportsOnWriter);
      if(!!reportsOnWriter) {
        return JSON.parse(reportsOnWriter);
      }
      return null;
    }
  },

  created() {
    //是否报告和图像分屏
    if(this.ofRoute.writing || this.ofRoute.image) {
      this.splitScreenUsed = true;
      
      this.callPacsHelper();
    }
  },

  beforeDestroy() {
    this.stepReconnect();
  },

  activated() {
    this.reconnect();
  },

  deactivated() {
    this.stepReconnect();
  },

  computed: {
    ofRoute() {
      const routeName = this.$route.path;
      return {
        writing: ("/RadReportWriting" === routeName)
        , image: ("/RadReportWriterImage" === routeName)
      };
    }
  }/*,

  watch: {
    focusWindowQueue: {
      deep: true,
      handler(nval, oval) {
        console.log(nval);
      }
    }
  }*/
}