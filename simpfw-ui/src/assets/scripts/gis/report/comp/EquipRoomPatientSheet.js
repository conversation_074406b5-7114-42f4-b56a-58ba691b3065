import { mapGetters } from 'vuex';

import {cloneDeep, mergeWith} from "lodash";
//检查信息
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//排队信息
import {past as passExam} from "@/assets/scripts/gis/exammanagement/queue/api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
//检查信息编辑
import {EditModel, ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//查询偏好配置
import PatientListSearchOptions from '@/views/pacs/report/comp/PatientListSearchOptions';
//检查进度相关
import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//拆分检查
import ExamSplit from "@/views/gis/exammanagement/patient/comp/ExamSplit"
//
import {GIS_MODALITIES} from "@/assets/scripts/pacs/modalities";

import {mergeWithNotNull, undefinedOrNull} from "@/utils/common";

import {currDate, parseTime, currDatetime} from "@/utils/common";

let timer_getList;

export default {
  name: "ReportPatientList",

  extends: BaseGridModel,
  mixins: [ResultStatus, EditModel],

  components: { Contextmenu, PatientListSearchOptions, ExamSplit },

  dicts: ["uis_exam_item"],

  props: {
    refresh: {type: Number},
  },

  data() {
    return {

      /**
       * 搜索框
       */
      searchForm: {
        combo_props: [{value: "patientInfo.name", label: "姓名"}  
          , {value: "examNo", label: "检查号"}
          , {value: 'patientInfo.registNo',label:'登记号'}
          , {value: 'encounter.encounterMRN',label:'住院号'}
          , {value: 'callInfo.callNo',label:'排队号'}]

        , propName: "patientInfo.registNo"
        , propValue: null
      },

      tableAction: [
        {cmd: 'exam::call', name: '呼叫'}
        , {cmd: 'exam-info::view', name: '查看申请单'}
        , {cmd: 'exam-info::past', name: '恢复排队'}
        , {cmd: 'image::collect', name: '采集影像'}
        , {cmd: 'exam-info::edit', name: '修改信息'}
        , {cmd: 'exam-info::copy', name: '复制检查'}
        , {cmd: 'exam-info::cancel', name: '取消检查', assert: (ctx, exam) => {if(!!exam && !!exam.resultStatus && StatusDict.cancel === exam.resultStatus.dictValue) {ctx.name = '恢复检查';} else {ctx.name = '取消检查';}}}
        , {cmd: 'report::unlock', name: '报告解锁', disabled: true}
        , {cmd: 'exam-info::equip-room-change', name: '更改机房'}
        //, {cmd: 'report::print', name: '打印'}
        //, {cmd: 'change-exam-at-pm', name: '病理申请单打印', disabled: true}
        //, {cmd: 'change-exam-at-pm', name: '活检标签打印', disabled: true}
        //, {cmd: 'change-exam-at-pm', name: '知情同意书打印', disabled: true}
        , {cmd: 'report::preview', name: '预览报告'}
        , {cmd: 'report::critical', name: '危急报告'}
        //, {cmd: 'change-exam-at-pm', name: '追踪标记', disabled: true}
        //, {cmd: 'change-exam-at-pm', name: '申请诊断协助', disabled: true}
        //, {cmd: 'exam-info::delete', name: '删除检查', assert: (ctx, exam) => {if(!!exam && 2 === exam.status) {ctx.cmd = 'exam::undel';ctx.name = '删除恢复';} else {ctx.cmd = 'exam::del';ctx.name = '删除检查';}}}
        , {cmd: 'exam-info::split', name: '拆分报告'}
      ],

      currentRow: null

    };
  },

  methods: {

    /**
     * 在本机房的患者信息
     */
    getList: function(opts) {
      clearTimeout(timer_getList);
      //
      this.checkValue()
      const room = this.currentEquipRoom;
      if(!room || !room.roomCode) {
        console.warn("未选择房间。");
        return;
      }
      //
      //this.currentRow = null;
      this.triggerBind("selectRow", null);
      //查询参数
      let sfm = this.searchForm, params = {pageSize: sfm.pageSize, pageNum: sfm.pageNum, appointWithCreated: true};
      //超声
      params.examModalitiesCodes = this.topModalities;
      //读取无需诊前检查或诊前检查已就绪的
      params.examPrerequireExclude = true;
      //读取未删除数据
      params.status = 0;
      //本机
      params.callInfo = {callRoom: {roomCode: room.roomCode}};
      //
      const propName = sfm.propName;
      //根据下拉
      if(propName) {
        let propsName = propName.split(/\./), prop = params;
        for(let i = 0, len = propsName.length; i < len; i ++) {
          let pnam = propsName[i];
          if((i + 1) < len) {
            prop[pnam] = {};
            prop = prop[pnam];
            continue;
          }
          prop[pnam] = sfm.propValue;
        }
      }
      //偏好查询设置 暂时不启用
      // const searchOpt = this.$refs.searchOpt;
      // let sopts = !!searchOpt? searchOpt.readOpts() : null;
      // mergeWith(params, sopts);
      //是否显示加载中图标，定时刷新时不显示
      if(opts && (opts instanceof Event)) {
        this.loading = true;
      }

      //appointExamDateGe
      //当天预约检查
      // params.appointExamDateGe = parseTime(currDate());
      // //
      // params.appointExamDateLt = params.appointExamDateGe;

      //
      //this.grid.pager.total = 0;
      //this.grid.data = [];
      eiapi.find(params).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
        //
        this.$refs.dataGrid.setCurrentRow(this.currentRow);
        //
        this.timerGetList();
      }).catch(err => {this.grid.data = [];this.timerGetList();});
    },

    timerGetList() {
      //是否定时刷新
      if(this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },

    //显示检查详情
    handleDetail(row, evt) {
      this.showTableAction(row, null, evt);
    },
    //读取编辑
    //handleUpdate(row) {
    //  this.$router.push({path: "/exammanagement/PatientRegist", query: {"examInfo.id": row.id}});
    //},

    //显示表格行右键菜单
    showTableAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    //处理表格右键菜单
    handleTableAction(item, row) {
      switch(item.cmd) {
        case 'exam::call':        //呼叫
          this.toCall(row);
          return;
        case 'exam::call':        //恢复排队
          this.handleQueuePast(row);
          return;
        case 'exam-info::edit':   //编辑登记信息
          this.handleUpdate(row);
          return ;
        case 'exam-info::copy':   //复制检查
          this.handleCopy(row);
          return ;
        case 'exam-info::split':  //拆分检查
          this.handleSplit(row);
          return ;
        case 'exam-info::delete': //删除检查
          if(row.status && 2 === row.status) {
            this.undoDelete(row);
            return;
          }
          this.handleDelete(row);
          break;
        case 'exam-info::cancel': //取消检查
          let cfmMsg, uptoStatus = StatusDict.cancel;
          if(row.resultStatus && StatusDict.cancel === row.resultStatus.dictValue) {
            uptoStatus = StatusDict.regist;
            cfmMsg = "是否恢复已取消的检查？";
          }
          this.confirmToUpdateResultStatus(row, uptoStatus, cfmMsg).then(res => {
            if(200 === res.code) {
              this.$modal.msgSuccess("操作成功");
              this.getList();
            }
          });
          break;
        default:
          //this.$modal.msg("未实现");
          this.triggerBind("dispatchAction", item.cmd, row);
      }
    },

    //登记为0，已检查为1，已报告为2，已审核为3，复审状态为4，已打印为5，已完成为6，已取消为10
    colFmt_reportStatus(row) {
      if(2 === row.status) {
        return "已删除";
      }
      const resultStatus = row.resultStatus;
      if(resultStatus) {
        switch(resultStatus.dictValue) {
          case "1"://状态未已检查，可进行报告书写
            return "书写开始";
          case "2":
            return "书写完成";
          case "3":
            return "已审核";
          case "4":
            return "复审";
          case "5":
            return "已打印";
          case "6":
            return "已完成";
          case "10":
            return "已取消";
        }
      }
      return "登记完成";
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },

    writeReport(row) {
      /*const resultStatus = row.resultStatus;
      //
      let resultStatusCode = resultStatus? resultStatus.dictValue : null;
      if(!resultStatusCode || "0" === resultStatusCode) {
        this.$modal.confirm("该检查未执行，无法书写报告，是否开始检查？").then(res => {
          this.toCall(row);
        });
        return;
      }
      //
      if(!resultStatus || !/^[12]$/.test(resultStatus.dictValue)) {
        let warnM = resultStatus? resultStatus.dictLabel : null;
        this.$modal.msgWarning("该检查" + (warnM || "未执行") + "，无法书写报告。");
        return;
      }*/
      this.triggerBind("dispatchAction", "report::write", row);
    },
    //单击
    selectTableRow(row, orow) {
      this.currentRow = row;
      this.triggerBind("selectRow", row);
    },
    //双击
    handleTableRowDblClick(row) {
      this.writeReport(row);
    },
    //呼叫
    toCall(row) {
      this.triggerBind("dispatchAction", "exam::call", row);
      //this.$refs.examCalling.open(row);
    },

    //执行删除操作
    doDelete(row) {
      //console.log(row);
      if(!row.id) {
        return;
      }
      return eiapi.del(row.id);
    },
    //恢复删除的检查
    undoDelete(row) {
      this.$modal.confirm("是否恢复已删除的检查？")
      .then(() => eiapi.undoDel(row.id))
      .then(res => {
        if(200 === res.code) {
          this.getList();
          this.$modal.msgSuccess("恢复成功。");
        }
      });
    },
    //打开查询设置
    prepareSearchOpt() {
      this.$refs.searchOpt.open();
    },
    //复制
    handleCopy(row) {
      const resultStatus = row.resultStatus;
      let resultStatusCode = resultStatus? resultStatus.dictValue : null;
      //只支持复制工作状态为已登记/已检查的
      if(!!resultStatusCode && StatusDict.regist !== resultStatusCode && StatusDict.exam !== resultStatusCode) {
        this.$modal.alert("只允许复制工作状态为登记完成和已检查的。");
        return;
      }
      this.$modal.confirm("是否确定复制该检查？").then(() => {
        return eiapi.copy(row);
      }).then(res => {
        this.$modal.confirm("复制成功，是否编辑复制的检查。").then(() => {
           this.handleUpdate(res.data);
        }).catch(err => this.getList());
        
      });
    },
    //拆分
    handleSplit(row) {
      this.$refs.examSplit.fill(row);
    },
    //获取当前选择的检查
    selectedExamInfo() {
      return this.currentRow;
    },
    //标识过号
    handleQueuePast(row) {
      const exam = !!row && !!row.id? row : this.currentRow;
      if(!exam || !exam.callInfo) {
        this.$modal.alert("请选择检查。")
        return;
      }
      passExam(exam.callInfo.id).then(() => {
        this.getList();
      });
    },
    //年龄列
    colFmt_age(row) {
        const pat = row.patientInfo;
        return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
     //全选输入框文本
     handleFocusRegistCode($evt) {
        const el = $evt.target || $evt.srcElement;
        el.select();
      },
    //登记号补0
    checkValue(){
        let fm = this.searchForm
        if(fm.propName=='patientInfo.registNo'){
            
            if(!!fm.propValue&&fm.propValue.length>0 && fm.propValue.length<10){
                fm.propValue = fm.propValue.padStart(10,'0');
            }
        }
    },

    //
    colStyle({row, column, rowIndex, columnIndex}) {
      if('resultStatus.dictLabel' !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.resultStatus, stav = sta? sta.dictValue : null;
      if(!undefinedOrNull(stav)) {
        clz.push("table-cell-result-status-" + stav);
      }

      return clz.join (" ");
    }

  },

  computed: {
    ...mapGetters(['currentEquipRoom']),
  },

  watch: {
    'currentEquipRoom'(newv, oldv) {
      //console.log(newv, oldv);
      this.getList();
    }
  },

  activated() {
    //console.log("PatientList activated");
    this.timerGetList();
  },

  deactivated() {
    //console.log("PatientList deactivated");
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    //console.log("PatientList destroy");
    clearTimeout(timer_getList);
  }
};
