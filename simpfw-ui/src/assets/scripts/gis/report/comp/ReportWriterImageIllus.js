import { mergeWith as mergeWithDeep} from "lodash";

import ImageEditor from "tui-image-editor";

import { mergeWithNotNull} from "@/utils/common";

import { base64ToBlob } from "@/assets/scripts/pacs/image/util";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import LesionTree from "@/views/system/dict/comp/DictTree";
import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTreeSingle";
//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//
import * as api from "@/assets/scripts/gis/image/comp/api.illustration";

//图像信息
function tplInfo() {
  return {
    //部位
    examParts: {partsCode: null, partsName: null},
    //病变, 数据字典
    lesion: {dictLabel: null},
    mark: {illus: null, w: 0, h: 0, x: 0, y: 0}
  };
}
//部位示意图位置标志大小
const partsImageMarkSize = 4, ShapeStrokeWidth = 3;
//
const availTreeTypes = {parts: "bodyParts", lesion: "lesion"};
//
const illusSign = "-illus";

export default {
  extends: BaseDialogModel,

  components: {LesionTree, ExamPartsTree},

  props: ["report", "reportable", "imagesSet"],

  data() {
    return {
      //是否初始化
      drawble: false,
      //图像信息
      info: tplInfo(),
      //可用部位示意图
      partsImages: [],
      //当前部位示意图
      partsImage: null,
      //字典树
      treeTypes: availTreeTypes,
      //
      treeType: availTreeTypes.parts,
      //当前图像序号
      currentIndex: -1,
      //颜色配置
      availColors: [
        {code: "#000000", name: "黑"},
        {code: "#FFFFFF", name: "白"},
        {code: "#FFA413", name: "黄"},
        {code: "#FF0000", name: "红"},
        {code: "#0000FF", name: "蓝"},
      ],
      //字体大小配置
      availFontSize: [24, 32, 40, 48, 56],
      //
      imageEditor: {
        inst: null,
        color: "#000000",
        fontSize: 40,
        activeObjectId: null
      },
      //加载图像
      loading: null
    };
  },

  methods: {
    view(imagesSet, currIndex) {
      //根据检查项目获取部位示意图列表
      //this.imagesSet = imagesSet;
      this.currentIndex = currIndex;

      //this.s

      this.open();
      //等窗口打开
      this.$nextTick(() => {
        this.loadPartsImages().then(() => {
          if(1 === this.prepareDrawing()) {
            this.clearPartsMark();

            this.disableImageTool();
          }
          //
          this.handleLoadImage();
        });
      });
    },
    //保存修改
    applyInfo() {
      if(!this.reportable) { return; }
      //this.selectImage();
      //图像数据
      const con = this.imageEditor.inst.toDataURL();
      const blo = base64ToBlob(con);
      //提交的数据
      const form = new FormData();
      const report = this.report, img = this.current, info = this.info;
      const path = `${img.studyInstanceUid}/${img.seriesInstanceUid}/${img.sopInstanceUid}`;
      
      form.append("examInfoId", report.id);
      form.append("noteInfo", JSON.stringify({parts: info.examParts.partsName, lesion: info.lesion.dictLabel, mark: info.mark}))
      form.append("path", path);
      form.append("data", blo);
      //是否已选为报告图像，防止重复
      let attaSet = report.images, atta = attaSet.find(e => e.path === path || `${e.studyInstanceUid}/${e.seriesInstanceUid}/${e.sopInstanceUid}` === path);//this.attach;
      //console.log(attaSet, atta);
      //确定为检查报告而非影像（勾选未提交/未保存）
      if(atta && atta.path) {
        form.append("id", atta.id);
      }
      
      api.save(form).then(r => {
        const dat = r.data, ratta = dat.reportImage;
        //ratta["sopInstanceUid"] = dat.dicomImage.sopInstanceUid;
        if(!atta) {
          mergeWithDeep(ratta, dat.dicomImage, null, mergeWithNotNull);
          this.selectImage(ratta);
        } else if(atta) {
          mergeWithDeep(atta, ratta, null, mergeWithNotNull);
        }
        //
        const rimg = dat.dicomImage, fileName = img.fileName, fileNameIllus = rimg? rimg.fileName : null;
        /*if(fileName !== fileNameIllus) {
          let pos;
          img.fileName = fileNameIllus;

          pos = img.uri.lastIndexOf('/');//img.fileUrl.lastIndexOf(fileName);
          //img.fileUrl = img.fileUrl.substring(0, pos) + fileNameIllus;
          //img.fileUrl = img.uri.substring(0, pos + 1) + fileNameIllus;
          
          pos = img.uri.lastIndexOf(fileName);
          img.uri = img.uri.substring(0, pos) + fileNameIllus;
        }*/
        img.fileName = fileNameIllus;
        img.uri = imapi.imageLocate(rimg);
        //img.selected = true;
        //console.log(img);
        this.triggerBind('reloadImage', img);

        this.close();
      });

      //this.close();
      //this.imageEditor.inst.clearUndoStack();
    },
    //取消选择
    selectImage(ratta) {
      const img = this.current;
      mergeWithDeep(img, ratta, null, mergeWithNotNull);
      img.selected = true;
      console.log("img",img);
      this.triggerBind("selectImage", img.selected, img);
    },    
    //删除图像
    deleteImage() {
      const img = this.current;
      this.triggerBind("deleteImage", img);
    },
    //上一个/下一个图像
    nextImage(dir) {
      //
      this.clearPartsMark();

      this.currentIndex += dir;

      this.handleLoadImage();
    },
    //载入部位示意图
    loadPartsImages() {
      /*const partsImageNames = [7];//[1, 2, 3, 4, 5];
      //this.partsImages = partsImageNames.map(p => {return {name: p, image: require(`../../../../../static/images/${p}.png`)}});
      this.partsImages = partsImageNames.map(p => {return {name: p, image: api.bodyparts(p)}});*/
      this.partsImages = [];
      const examItem = this.report.examItem
      return api.bodypartsList(examItem).then(r => {
        //p: 文件夹/文件名
        const partsImages = r.data.map(p => {
          let nam = p;
          const pos = nam.indexOf('/');
          if(-1 !== pos) { nam = nam.substring(pos + 1); }
          //
          return {path: p, name: nam, image: api.bodyparts(p)};
        });
        //
        this.partsImages = partsImages;
        return partsImages;
      });
    },
    //初始
    //返回1-未执行，0-执行完成
    prepareDrawing() {
      if(this.drawble) { return 1; }
      this.drawble = true;

      this.setupImageEditor();

      this.switchPartsImage(this.partsImages[0]);

      return 0;
    },
    //选择部位示意图
    switchPartsImage(pi) {
      //
      this.clearPartsMark();
      //
      this.partsImage = pi? pi.image : null;
      this.info.mark.illus = pi? pi.path : null;
    },
    //在部位示意图上标注
    markPartsImage(evt) {
      const diag = this.$refs.partsImageIllus;
      if(!diag) {
        console.log("部位示意图未就绪。");
        return;
      }
      const g = diag.getContext("2d");
      //
      const markIllus = this.info && this.info.mark? this.info.mark.illus : null;
      this.clearPartsMark(g);
      //
      let x = evt.offsetX, y = evt.offsetY;
      g.beginPath();
      g.arc(x, y, partsImageMarkSize, 1, 360, true);
      g.fill();
      //
      this.info.mark = {illus: markIllus, x, y, w: diag.clientWidth, h: diag.clientHeight};
    },
    //清除部位示意图上标注
    clearPartsMark(g) {
      const mark = this.info.mark;
      if(!mark || !mark.x || !mark.y) { return; }

      if(!g || !g.canvas) {
        const diag = this.$refs.partsImageIllus;
        if(!diag) { return; }
        g = diag.getContext("2d");
      }
      const dif = 8, x = mark.x - partsImageMarkSize - dif, y = mark.y - partsImageMarkSize - dif
        , w = (partsImageMarkSize * 2) + dif, h = (partsImageMarkSize * 2) + dif;
      g.clearRect(x, y, w, h);

      mark.illus = null;
      mark.x = 0;
      mark.y = 0;
      mark.w = 0;
      mark.h = 0;
    },
    //
    useTree(type) {
      this.treeType = type;
    },
    //选择部位
    handleSelectParts(nod) {
      const uid = nod.uid;
      //部位分类不处理
      if(uid.startsWith("C")) { return; }
      this.info.examParts.partsName = nod.label;
    },
    //选择病变
    handleSelectLesion(nod) {
      let les = this.info.lesion, vals = [];
      if(les.dictLabel) {
        vals.push(les.dictLabel);
      }
      vals.push(nod.label);
      les.dictLabel = vals.join(",")
    },
    //加载图像
    handleLoadImage(opts) {
      this.loading = this.currentSupported;
      //
      const ed = this.imageEditor.inst;
      let imageUri = this.current.uri;
      if(opts && opts.original) {
        //imageUri = imageUri.replace(illusSign, "");
        imageUri += '?source=original';
      }
      ed.loadImageFromURL(imageUri, "image").then(r => {
        //
        this.afterLoadImage();
        //防止执行撤销时把图像撤掉
        ed.clearUndoStack();

        this.resizeImageEditor(r);
      });
      //
      let atta = this.attach, attaInfo, info = this.info, mark = info.mark;
      attaInfo = atta? JSON.parse(atta.noteInfo) : null;
      if(attaInfo && attaInfo.mark && attaInfo.mark.illus) {
        const markIllus = '' + attaInfo.mark.illus;
        const illus = this.partsImages.find(i => markIllus.indexOf('/') == -1 && i.name === markIllus || i.path === markIllus);
        this.switchPartsImage(illus);

        if(attaInfo.mark.x > 0 && attaInfo.mark.y > 0) {
          this.$nextTick(() => {
            this.markPartsImage({offsetX: attaInfo.mark.x, offsetY: attaInfo.mark.y});
          });
        }
      }
      //
      info.examParts.partsName = attaInfo && attaInfo.parts || null;
      info.lesion.dictLabel = attaInfo && attaInfo.lesion || null;
      mark.illus = attaInfo && attaInfo.mark && attaInfo.mark.illus || this.partsImages[0].path;
      mark.w = attaInfo && attaInfo.mark && attaInfo.mark.w || 0;
      mark.h = attaInfo && attaInfo.mark && attaInfo.mark.h || 0;
      mark.x = attaInfo && attaInfo.mark && attaInfo.mark.x || 0;
      mark.y = attaInfo && attaInfo.mark && attaInfo.mark.y || 0;
    },
    //图像加载后触发
    afterLoadImage() {
      this.loading = false;
    },
    //图像编辑功能
    setupImageEditor() {
      if(this.imageEditor.inst) { return; }

      const can = this.$refs.imageCanvas;
      const cfg = { 
        usageStatistics: false,
      };
      const vm = this;
      const imageEditor = new ImageEditor(can, cfg);
      imageEditor.on({
        //添加文字初始文字
        addText(pos) {
          imageEditor.addText("输入文字", {styles: {fill: vm.imageEditor.color, fontSize: vm.imageEditor.fontSize}, position: pos.originPosition})
        },
        //当前
        objectActivated(obj) {
          vm.imageEditor.activeObjectId = obj.id;
        }
      });

      this.imageEditor.inst = imageEditor;
    },
    //添加画箭头线
    enableArrayTool() {
      const ed = this.imageEditor;
      this.enableImageTool("LINE_DRAWING", {color: ed.color, width: ShapeStrokeWidth, arrowType: {head: "triangle"}});
    },
    //添加画矩形/圆形
    enableShapeTool(type) {
      //暂不支持画圆角框
      type = "square" === type? "rect" : type;
      this.enableImageTool("SHAPE");
      const ed = this.imageEditor;
      ed.inst.setDrawingShape(type, {stroke: ed.color, strokeWidth: ShapeStrokeWidth, fill: "transparent"});
    },
    //开启裁剪
    enableCropTool() {
      this.enableImageTool("CROPPER");
    },
    //应用裁剪
    applyCrop() {
      const ed = this.imageEditor.inst;
      if("CROPPER" != ed.getDrawingMode()) { return; }
      ed.crop(ed.getCropzoneRect()).then(r => {
        this.disableImageTool();

        this.resizeImageEditor(r);
      });      
    },
    //添加文字
    enableTextTool() {
      this.enableImageTool("TEXT", {color: this.imageEditor.color});
    },
    //开启绘制
    enableImageTool(nam, opt) {
      const ed = this.imageEditor.inst;
      if (ed.getDrawingMode() !== nam) {
        ed.stopDrawingMode();
        ed.startDrawingMode(nam, opt);
      }
    },
    //改变绘制颜色
    setToolColor(clr) {
      const ed = this.imageEditor, einst = ed.inst;
      ed.color = clr;
      //
      const dmode = einst.getDrawingMode();
      if("SHAPE" === dmode) {
        const m = einst._graphics._componentMap[dmode];
        const type = m._type, {fill, strokeWidth} = m._options;
        einst.setDrawingShape(type, {stroke: clr, strokeWidth, fill});
      } else if("TEXT" == dmode && ed.activeObjectId) {
        einst.changeTextStyle(ed.activeObjectId, {fill: clr});
        return;
      } else {
        einst.setBrush({color: clr});
      }
    },
    //终止绘制
    disableImageTool() {
      this.imageEditor.inst.stopDrawingMode();
      
      this.clearImageActiveObjects();
    },
    //设置尺寸
    resizeImageEditor(opt) {
      const can = this.$refs.imageCanvas, imgWidth = opt.newWidth, imgHeight = opt.newHeight;
      can.style.width = "100%";
      //图像宽度小
      if(imgWidth < can.clientWidth) {
        can.style.width = imgWidth + "px";
      }
      const height = (imgHeight / imgWidth) * can.clientWidth;
      can.style.height = height + "px";
    },
    //撤销
    undoTool() {
      this.imageEditor.inst.undo();
    },
    //改变文字大小
    setImageTextSize(size) {
      const ed = this.imageEditor, einst = ed.inst;
      ed.fontSize = size;

      if(ed.activeObjectId) {
        einst.changeTextStyle(ed.activeObjectId, {fontSize: size})
      }
    },
    //调用改变字体大小
    changeImageTextSize(val) {
      this.setImageTextSize(val);
    },
    //当前绘制
    getImageDrawingMode() {
      return this.imageEditor.inst.getDrawingMode();
    },
    //
    clearImageActiveObjects(evt) {
      if(evt && this.matchesImageEditor(evt.target)) {
        evt.stopPropagation();
        return;
      }
      this.imageEditor.activeObjectId = null;
      this.imageEditor.inst.deactivateAll();
    },
    //当前点击事件是否为绘制图像源触发
    matchesImageEditor(e) {
      if(!e || e.tagName === "BODY") {
        return false;
      }

      if(e.className && e.className.split(" ").findIndex(e => e === "image-wrap") !== -1) {
        return true;
      }

      return this.matchesImageEditor(e.parentNode);
    },
    //加载原图
    loadImageOriginal() {
      this.handleLoadImage({original: true});
    },
    //不用部位示意图
    disablePartsIllus() {
      this.switchPartsImage();
      this.clearPartsMark();
    }
  },

  computed: {
    //当前图像
    current() {
      const imagesSet = this.imagesSet, currentIndex = this.currentIndex, numImages = imagesSet? imagesSet.length : 0;
      return numImages > 0 && currentIndex >= 0 && currentIndex < numImages? imagesSet[currentIndex] : null;
    },
    //当前图像是否支持编辑
    currentSupported() {
      const curr = this.current;
      return curr && curr.fileType && /^(2|jpg)$/.test(curr.fileType);
    },
    //当前图像是否报告图像
    attach() {
      const report = this.report, img = this.current;
      const path = `${img.studyInstanceUid}/${img.seriesInstanceUid}/${img.sopInstanceUid}`;
      //是否已选为报告图像
      return report.images? report.images.find(i => i.path === path) : null;
    }
  },

  /*created() {
    this.loadPartsImages();
  },*/

  mounted() {
    const dia = this.$refs.imageToolDialog.$el;
    dia.addEventListener("click", this.clearImageActiveObjects, false);
    //dia.querySelector(".image-wrap").addEventListener("click", this.clearImageActiveObjects, false);
  }
};