import {cloneDeep} from "lodash";
//
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import logo from "@/assets/images/hospital-logo-report.png";
//图像展示
import {wadoForImage, loadAndDisplay, ASPECT_RATIO, ImageIdsPrefix} from "@/assets/scripts/pacs/image/util";
//检查信息，报告
//import { uploadReportDoc } from "../../exammanagement/examinfo/api";
//报告附件：报告图像、签名
import * as attapi from "@/assets/scripts/gis/exammanagement/examinfo/attachment/api";
//检查设备
import * as devapi from "@/assets/scripts/pacs/equiproom/dicominfo/api";
//图像读取
import {imageLocate} from "@/assets/scripts/pacs/image/api";
//
import {indexServer} from "@/assets/scripts/pacs/imageIndex/api"
//
import {BYTESPERCHAR, stringslice, undefinedOrNull} from "@/utils/common";
//
//import {imageFromCavas, addClassName, removeClassName} from "@/assets/scripts/pacs/common";
//报告状态
import {StatusDict, matchAny as matchAnyResultStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//
import {RAD_MODALITIES, EIS_MODALITIES} from "@/assets/scripts/pacs/modalities";
//
//import {emptyForm as emptyReport} from "@/assets/scripts/gis/report/comp/ReportWriter";

//检查编辑
import {TransModel, UndoModel, ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";


//单行高度
const LINE_HEIGHT = 30, LINE_SEP = "\n\r", FONT_SIZE = 14;
//报告的术后建议-检查诊断-检查所见，保持顺序
const EXAM_TEXTFIELDS = ["operationSuggestion", "examDiagnosis", "examDesc"];
//
const OPER_EXPORTASDOC = "exportAsDoc", OPER_PRINT = "print";
//
const formProps_UIS = {subtitle: "体检彩色超声诊断报告单", descLabel: "超声所见", diagLabel: "超声诊断"}
const formProps_RAD = {subtitle: "放射诊断报告单", descLabel: "检查所见", diagLabel: "检查诊断"}
const formProps_EIS = {subtitle: "内镜检查诊疗报告单", descLabel: "检查所见", diagLabel: "检查结论", suggLabel: "建议"}
//
const model = {
  extends: BaseDialogModel,

  mixins: [ResultStatus],
  /*props: {
    report: {type: Object}
  },*/
  data() {
    return {
      host: {
        title: "广西医科大学第二附属医院"
      },
      options: null,
      //报告内容
      report: {
        patientInfo: {}
      },
      //签名图片
      signImage: null,
      //头部logo
      logoImage: logo,
      //显示的内容
      pages: [],
      //是否适应页面
      pageFitted: true,
      //
      formProps: formProps_EIS,

      asReportTemplate: false,
      templateReport: {
        patientInfo: {
          name: "{pi.nm}", 
          registNo: "{pi.regNo}",
          gender: {dictLabel: "{pi.gd}"}, 
          age: "{pi.ag}"
        },

        inpNo: "{inpNo}",
        examNo: "{examNo}",
        reqDept: {deptName: "{reqdp.nm}"},
        examTime: "{examDt} {examTm}",
        examParts_names: "{examPrt}",
        bedNo: "{bedNo}",
        reportDate: "{rpDt}",
        reportDoctor: {nickName: "{reqd.nm}"},
        auditSign: "{adtd.sign}",
        examDevice: { 
          modality: "{devMod}"
        }
      }
    }
  },

  methods: {
    /**
     * 浏览
     * @param {*} report 
     * @param {*} opt 'print'-打印
     */
    view(report, opt) {
      
      this.pageFitted = true;

      if(!report || !report.id) {
        this.$modal.alert("请选择预览的报告或检查。");
        return;
      }
      //图象数只能是0, 1, 2, 3, 4, 6
      const images = report.images, numSupported = [0, 1, 2, 3, 4, 6];
      const numImages = !!images? images.length : 0;
      if(!numSupported.includes(numImages)) {
        this.$modal.alert("影像张数应为: " + numSupported.join(", "));
        return;
      }
      this.report = report;
      //
      this.findImages();
      //获取检查类型
      const examModality = !!report.examModality? report.examModality.dictValue : null;
      if(!!examModality && RAD_MODALITIES.includes(examModality)) {
        this.formProps = formProps_RAD;
      } else if(!!examModality && EIS_MODALITIES.includes(examModality)) {
          this.formProps = formProps_EIS;
      } else {
        this.formProps = formProps_UIS;
      }
      //
      this.readSignImage();
      //this.signImage = report.signImage;
      this.options = !!opt? opt.split(",") : null;
      //
      this.pages = [cloneDeep(report)];
      //
      this.findDevice();
      //
      this.open();
      //
      //this.readSignImage();

      this.$nextTick(() => {
        this.keepBuz();
        //
        if(this.options) {
          const delay = 1000;
          if(this.extOperExportAsDoc) {
            setTimeout(this.exportAsDoc, delay);
          } else if(this.extOperPrint) {
            setTimeout(this.plint, delay);
          }
        }
      });
    },
    //读取影像图片
    async displayImages() {
      const images = this.report.images;
      //console.log(images);
      if(!images || !images.length) {
        this.layoutPage();
        return;
      }
      //
      const dialogRef = this.$refs.reportPreviewerDialog.$el;
      const maxWidth = dialogRef.querySelector(".report-preview-main").clientWidth, numImages = images.length;
      //单张图片最大400px，每行最多放4张
      let width = maxWidth / numImages, numMax = 3;
      //当图片为1，2，3,6时1最多3行，图片数为4时1行2张
      if(4 === numImages) { numMax = 2; }
      //
      const eleMaxWidth = 400, eleMinWidth = Math.ceil(maxWidth / numMax);
      if(width > eleMaxWidth) {
        width = eleMaxWidth;
      } else if(width < eleMinWidth) {
        width = eleMinWidth;
      }
      width -= 18;
      //固定200
      //width = 350;

      switch(numImages){
        case 1:
          width=350
          break;
        case 2:
          width=350
          break;
        case 3:
          width=230
          break;
        case 4:
          width=300
          break;
        case 6:
          width=230
          break;
        default:
          width=230
          break;
      }
      //保持宽高比
      let height = width / ASPECT_RATIO;
      //
      const eles = dialogRef.querySelectorAll(".report-preview-image .cornerstone-element");
      //
      let indexServerAddr = await indexServer();
      //
      eles.forEach((c, i) => {
        const p = c.parentNode;
        p.style.width = width + "px";
        p.style.height = height + "px";
        try {
          cornerstone.getEnabledElement(c);
        } catch (err) {
          cornerstone.enable(c);
        }
        let img = images[i], imageUri = img.uri, segm;
        //查看历史报告，如果报告图像不是当时勾选，要生成地址
        if(!imageUri && img.path) {
          if((segm = img.path.split('/')).length == 3) {
            //假定jpg
            const [studyInstanceUid, seriesInstanceUid, sopInstanceUid] = segm;
            imageUri = imageLocate({studyInstanceUid, seriesInstanceUid, sopInstanceUid});
          } else {
            //假定dicom
            imageUri = img.path;
            if(0 !== imageUri.indexOf(indexServerAddr)) {
              imageUri = indexServerAddr + imageUri;
            }
            imageUri = ImageIdsPrefix.rs + imageUri;
          }
        }
        
        loadAndDisplay(c, imageUri, true).then(img => {
          const imageWidth = img.width, imageHeight = img.height;
          height = width * imageHeight / imageWidth;
          p.style.height = height + "px";
          cornerstone.resize(c, true);
        });
      });
      //
      this.layoutPage();
    },
    //读取签字
    readSignImage() {
      const rp = this.report;
      if(!rp.signImage && matchAnyResultStatus(rp, StatusDict.audit, StatusDict.reaudit)) {
        this.findAttach("report::sign").then(res => {
          let dat = res.data;
          //console.log(dat);
          if(dat && dat.length) {
            rp.signImage = res.dataContent && res.dataContent.length? res.dataContent[0] : dat[0].data;
          }
        });
        //rp.signImage = res.dataContent && res.dataContent.length? res.dataContent[0] : dat[0].data;
      }
    },
    //读取报告图片
    findImages() {
      const rp = this.report;
      //
      if(undefinedOrNull(rp.images)) {
        this.findAttach("report::image").then(r => {
          rp.images = r.data;
          this.$nextTick(this.keepBuz);
        });
        return true;
      }

      return false;
    },
    //读取报告影像和签名
    findAttach(type) {
      const rp = this.report;
      return attapi.find({examInfoId: rp.id, type, status: 0});
    },
    //
    keepBuz() {
      //
      this.scaleMainContent();
      //
      this.displayImages();
      //
      this.switchPageSize();
    },
    //审核
    audit(){
        const report = this.report;
        //请求审核报告
        try {

            this.$store.dispatch("audit", cloneDeep(report));
            
        } catch (err) { console.error(err); }
        this.close();
    },
    //是否指明打印
    plint() {
      const report = this.report;
      const asTemplate = this.asReportTemplate;
      if(asTemplate) {
        //this.report = emptyReport();
      } else
        if(!matchAnyResultStatus(report, StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive)) {
          //const resultStatus = report.resultStatus;
          this.$modal.confirm("打印报告须审核，是否进行审核？").then(() => {
            //请求审核报告
            try {
              this.$store.dispatch("auditForPrint", cloneDeep(report));
              
            } catch (err) { console.error(err); }
            this.close();
          });
          return;
        }
      //防止未审核右键或浏览器菜单打印
      const dialogWrap = this.$refs.reportPreviewerDialog.$el, printingdialog = "printingdialog";
      let clz = dialogWrap.className.split(" ");
      if(!clz.includes(printingdialog)) { dialogWrap.className += " " + printingdialog; }
      //
      //用实际效果打印
      //let pageFitted = this.pageFitted;
      //if(pageFitted) {
      //  this.switchPageSize();
      //}
      //
      //打印并更新检查状态为已打印
      this.updateResultStatus(this.report, StatusDict.print).then(res => {
        if(res && 200 === res.code) {
          window.print();
        }

        //恢复打印前视图
        //if(pageFitted) {
        //  this.switchPageSize();
        //}
        if(!clz.includes(printingdialog)) { dialogWrap.className = clz.join(" "); }
        /*if(asTemplate) {
          this.report = report;
        }*/
      });
    },
    /**
     * 是否需要分页
     */
    layoutPage() {
      //当前只考虑第1页来判断
      const wrap = this.$refs.reportPreviewerDialog.$el.querySelector(".report-previewer-wrap");//.report-previewer-form
      //可视高度
      let maxHeight = wrap.clientHeight;//
      //console.log(maxHeight);
      //扣去不可见区域
      maxHeight -= (16 * 2);
      //console.log(maxHeight);
      //页头
      maxHeight -= wrap.querySelector(".report-preview-header").offsetHeight;
      //console.log(maxHeight);
      //页脚
      maxHeight -= wrap.querySelector(".report-preview-footer").offsetHeight;
      //console.log(maxHeight);
      //第一页内容
      const mn = wrap.querySelector(".report-preview-main");//.report-previewer-form
      
      this.assertLayoutPage(maxHeight, mn);
    },
    /**
     * 循环计算内容高度是否超出
     * @param maxHeight 允许高度 
     */
    assertLayoutPage(maxHeight, pane) {
      //实际内容高度
      let height = pane.scrollHeight;
      //
      const dialogRef = this.$refs.reportPreviewerDialog.$el;
      const txtpan = dialogRef.querySelector(".report-preview-main .text-block-pre");
      //
      let lineHeight = parseFloat(getComputedStyle(txtpan).lineHeight);
      lineHeight = isNaN(lineHeight)? LINE_HEIGHT : lineHeight;
      //
      //console.log("可见高度=%f，内容高度=%f，行高=%f", maxHeight, height, lineHeight);
      //实际内容超出像素
      const diff = height - maxHeight;
      if(diff < lineHeight) {
        return;
      }
      //算出超出多少行，正文内容加标签：“检查所见”、“检查诊断”、“术后医嘱”
      let overLines = Math.ceil(diff / lineHeight);
      //console.log("超出行数=%d", overLines);
      //超出2行，通过缩小字体处理
      if(overLines <= 2) {
        this.scaleMainContent({fontSize: "13px"});
        //this.assertLayoutPage(maxHeight, pane);
        return;
      }
      //是否增加页数
      let pagesAdded = this.pages.length < 2;
      if(pagesAdded) {
        this.pages.push({examDesc: null, examDiagnosis: null, operationSuggestion: null});
      }
      let page1 = this.pages[0], page2 = this.pages[1], origPage2 = cloneDeep(page2);
      //报告的术后建议-检查诊断-检查所见，逆序
      let rp = this.report, textFields = EXAM_TEXTFIELDS;
      //逐渐减
      for(let i = 0; i < textFields.length; i ++) {
        let textField = textFields[i], text = rp[textField], lines = this.splitLines(text);
        if(!lines) { continue; }
        //正文内容+标签行数
        const numLines = lines.length, sub = overLines - numLines;
        //
        let pageText1 = null, pageText2 = null;
        if(sub > 0) {
          pageText2 = lines;
        } else {
          sub = Math.abs(sub);
          if(sub > 1) {
            pageText1 = lines.splice(0, sub);// - 1
          }
          pageText2 = lines;
        }
        page1[textField] = pageText1? pageText1.join(LINE_SEP) : null;
        page2[textField] = pageText2? pageText2.join(LINE_SEP) : null;

        overLines -= numLines;
        if(overLines < 0) { break; }
      }
      //
      this.$nextTick(() => {
        //
        if(pagesAdded) {
          const pageViews = dialogRef.querySelectorAll(".report-previewer-wrap");
          if(pageViews.length == 2) {
            //
            const pageView = pageViews[0], pageView2 = pageViews[1];
            //pageViews[1].style.zoom = pageViews[0].style.zoom;
            pageView2.style.transform = pageView.style.transform;
            //
            const zoom = this.scaleValue(pageView.style.transform);
            //
            if(this.pageFitted) {
              //第二页
              if(zoom !== 1) {
                let transorigin = pageView.style.transformOrigin.split(" ");
                let originX = transorigin[0], originY = transorigin[1];
                originY = (-1 * pageView2.offsetHeight) + "px";
                pageView2.style.transformOrigin = `${originX} ${originY}`;
              }
            }
            //窗口高度
            const dia = dialogRef.querySelector(".el-dialog .el-dialog__body");
            const dialogBodyHeight = pageView.offsetHeight * zoom;
            dia.style.height = (dialogBodyHeight * 2 + 16 + (4 * 2)) + "px";
          }
        }
        //内容是否更改，继续调
        let assertable = false;
        for(let p in origPage2) {
          if(!(p in page2)) {
            assertable = true;
            break;
          }

          if(origPage2[p] !== page2[p]) {
            assertable = true;
            break;
          }
        }
        if(assertable) {
          this.assertLayoutPage(maxHeight, pane);
        }
      });
    },
    //设置主内容字体
    scaleMainContent(opts = {}) {
      const {fontSize} = opts, mn = this.$refs.reportPreviewerDialog.$el.querySelector(".report-preview-main");
      if(!mn) { return; }
      mn.style.fontSize = fontSize? fontSize : "inherit";
    },
    //计算行数
    splitLines(text) {
      if(!text) { return null; }
      //人为断行
      const lines = text.split(/[\r\n]/g), breaqLines = [];
      //每行允许字数
      const pan = this.$refs.reportPreviewerDialog.$el.querySelector(".report-preview-main .text-block-pre");
      //字体大小
      let fontSize = parseFloat(getComputedStyle(pan).fontSize);
      fontSize = isNaN(fontSize)? FONT_SIZE : fontSize;
      //内容可见宽度=容器宽度-缩进量（margin-left=1.2em）
      let viewportWidth = pan.clientWidth - 2 * (fontSize * 1.2);
      //每行字节
      const charCountPerLine = Math.floor(viewportWidth / fontSize) * BYTESPERCHAR;
      //console.log("可视宽度=%f, 字体大小=%f, 每行允许字数=%d", viewportWidth, fontSize, charCountPerLine);
      //自动断行
      for(let i = 0; i < lines.length; i ++) {
        let line = lines[i];
        //
        if(!line || line.length <= charCountPerLine) {
          breaqLines.push(line);
          continue;
        }
        //
        while(line.length > charCountPerLine) {
          let para = stringslice(line, charCountPerLine);//line.substring(0, charCountPerLine);
          //
          breaqLines.push(para);
          
          line = line.substring(para.length);
        }
        //
        if(!!line) {
          breaqLines.push(line);
        }
      }
      //
      return breaqLines;
    },
    //切换适应页面/实际效果
    switchPageSize() {
      if(!this.pageFitted) {
        this.fitPage();
      } else {
        this.actualSize();
      }
      //
      //this.pageFitted = !this.pageFitted;
      //
      const dialogRef = this.$refs.reportPreviewerDialog.$el;
      const pageView = dialogRef.querySelector(".report-previewer-wrap");
      //this.dialogWidth = pageView.offsetWidth + (16 * 2);
      const dia = dialogRef.querySelector(".el-dialog");
      //let zoom = parseFloat(pageView.style.zoom);
      let zoom = this.scaleValue(pageView.style.transform);
      const dialogWidth = pageView.offsetWidth * zoom;
      //console.log("窗口宽度 %f", dialogWidth);
      dia.style.width = (dialogWidth + (16 * 2)) + "px";
      //
      let dialogBodyHeight = "unset";
      if(this.pageFitted) {
        dialogBodyHeight = pageView.offsetHeight * zoom;
        dialogBodyHeight = (dialogBodyHeight + (4 * 2)) + "px";
      }
      dia.querySelector(".el-dialog__body").style.height = dialogBodyHeight;
      //重置页面内容
      let pages = this.pages;
      if(pages.length == 2) {
        let page1 = this.pages[0], page2 = this.pages[1];
        EXAM_TEXTFIELDS.forEach(p => {
          let pv1 = page1[p], pv2 = page2[p];
          if(!!pv2) {
            page1[p] = !!pv1? (pv1 + LINE_SEP + pv2) : pv2;
          }
        });
        this.pages.splice(1, 1);
      }
      //重新布局页面
      this.$nextTick(this.layoutPage);
    },
    //页面大小
    fitPage() {
      const de = document.body, maxWidth = de.clientWidth - (16 * 2), maxHeight = de.clientHeight - (56 + 8);
      const pageViews = this.$refs.reportPreviewerDialog.$el.querySelectorAll(".report-previewer-wrap"), pageView = pageViews[0];
      const pageViewWidth = pageView.offsetWidth;
      //填满水平、垂直缩放比例
      const verzoom = 1.0 * maxWidth / pageViewWidth, horZoom = 1.0 * maxHeight / pageView.offsetHeight;
      //防止撑破，取较小
      const zoom = Math.min(verzoom, horZoom);
      //console.log("预览缩放 %f", zoom);
      pageViews.forEach((p, i) => {
        //p.style.zoom = zoom;
        p.style.transform = `scale(${zoom})`;
        //
        let originX = "left", originY = "top";
        if(zoom > 1) {
          originX = (pageViewWidth / 2) + "px";// * zoom
        }
        //第二页
        //if(1 === i) {
        //  originY = (pageViewHeight / 2) + "px";
        //}
        p.style.transformOrigin = `${originX} ${originY}`;
      });
      //
      this.pageFitted = true;
    },
    //实际大小
    actualSize() {
      const pageViews = this.$refs.reportPreviewerDialog.$el.querySelectorAll(".report-previewer-wrap");
      pageViews.forEach(p => {
        //p.style.zoom = 1;
        p.style.transform = "scale(1)";//
        p.style.transformOrigin = "left top";//
      });
      //
      this.pageFitted = false;
    },
    //检查设备
    findDevice() {
      let report = this.pages[0], callInfo = report.callInfo;
      if(!!callInfo && !!callInfo.callRoom && !!callInfo.callRoom.roomCode && (!callInfo.callRoom.device || !callInfo.callRoom.device.device)) {
        devapi.find({equipRoom: {roomCode: callInfo.callRoom.roomCode}}).then(res => {
          if(!!res.rows && res.rows.length > 0) {
            callInfo.callRoom.device = res.rows[0];
          }
        });
      }
    },
    //从css获取scale
    scaleValue(transcale) {
      return parseFloat(transcale.replace(/scale\s*\(\s*([\d.]+)\s*\)/, "$1"));
    },
    //生成模板
    plintAsReportTemplate() {
      this.asReportTemplate = true;
      //const report = this.report;
      //this.report = emptyReport();
      this.$nextTick(() => {
        this.plint();
        this.asReportTemplate = false;
        //this.report = report;
      });
    },
    //
    modalityOf(dict) {
      const rep = this.report;
      return !!rep && rep.examModality && dict === rep.examModality.dictValue;
    },
    //
    inpTypeOf(dict) {
      const rep = this.report;
      return !!rep && rep.inpType && dict === rep.inpType.dictValue;
    }
  },

  computed: {
    //二级标题
    subject() {
      return this.report.examItem && this.report.examItem.dictLabel || "超声";      
    },
    //是否已签名
    signed() {
      const rp = this.report;
      //return !!rp.signImage && !!rp.resultStatus && !!rp.resultStatus.dictValue && /^[34]$/.test(rp.resultStatus.dictValue);
      return !!rp.signImage && matchAnyResultStatus(rp, StatusDict.audit, StatusDict.reaudit);
    },
    //
    imagesSet() {
      const images = this.report.images;
      if(!images) {
        return null;
      }
      //每行最大图片数
      const numImages = images.length, numPerRow = numImages === 4? 2 : 3;
      let imagesSet = [];
      for(let i = 0; i < numImages; i ++) {
        let ri = Math.floor(i / numPerRow), row;
        if(ri < imagesSet.length) {
          row = imagesSet[ri];
        } else {
          imagesSet.push(row = []);
        }
        row.push(images[i]);
      }
      return imagesSet;
    },
    //
    extOperExportAsDoc() {
      const opt = this.options;
      return !!opt && opt.includes(OPER_EXPORTASDOC);
    },
    //
    extOperPrint() {
      const opt = this.options;
      return !!opt && opt.includes(OPER_PRINT);
    },
    affectedReport() {
      return this.asReportTemplate? this.templateReport : this.report;
    },

    auditable() {
      const report = this.report;
      return !!report && matchAnyResultStatus(report, StatusDict.report);
    }
  }
};
export default model;
