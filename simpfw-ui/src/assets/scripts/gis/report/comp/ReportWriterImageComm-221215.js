import { getToken } from '@/utils/auth'
import {baseURL} from "@/utils/request";

//多屏展示
import {browserName} from "@/assets/scripts/screen-helper";

import * as mh from "@/assets/scripts/mine-helper";
//WebSocket支持
import BaseWebSocketModel from "@/assets/scripts/uis/BaseWebSocketModel";
//放射检查modality
import {RAD_MODALITIES} from "@/assets/scripts/uis/modalities";
//连接失败重连
let timer_callPacsHelper;
//
let imageWins = [], imageWin;

export default {
  extends: BaseWebSocketModel,

  data() {
    return {
      socketEnabled: true,
      //
      socketBaseUrl: "ws://127.0.0.1:54985",
      //socketUrl: null,
      splitScreenUsed: false,

      connectTimes: 0,

      examInfo: null
    }
  },

  methods: {
    //助手连接地址
    checkSocketUrl()
    {
      if(!this.socketUrl) {
        this.socketUrl = this.socketBaseUrl + "?token=" + getToken();
      }
    },
    //连接助手
    callPacsHelper() {
      if(!this.socketEnabled) { return; }
      //
      this.checkSocketUrl();
      //
      try {
        clearTimeout(timer_callPacsHelper);
        //
        ++ this.connectTimes;
        //
        this.openSocket();
      } catch (err) {
        console.error(err);
      }
    },
    //连接助手失败或发消息失败
    handleSocketError(evt) {
      //console.error(evt);
      //是否连接失败
      if(!this.isConnectedSocked) {
        if(1 == this.connectTimes) {
          this.howto();
        }
        //
        timer_callPacsHelper = setTimeout(this.callPacsHelper, 8 * 1000);
      }
    },
    //分屏
    splitScreen(exam) {
      //获取检查类型，放射检查时，是否使用多屏
      const examModality = !!exam.examModality? exam.examModality.dictValue : null;
      if(!this.splitScreenUsed || !examModality || !RAD_MODALITIES.includes(examModality)) {
        return false;
      }
      this.examInfo = exam;
      if(!this.isConnectedSocked) {
        this.showImageWin();
      }
      //打开影像窗口，在调用助手将影像窗口移动到其它屏幕
      const imageViewer = location.protocol + "//" + location.host + "/RadReportWriterImage?exam=" + exam.id;
      /*if(this.isConnectedSocked) {
        this.handleSocketSend('{"cmd":"browse","browser":"' + browserName() + '","token":"' + getToken() + '","url":"' + imageViewer +'"}');
      } else {
        if(!!imageWin) {
          imageWin.close();
        }
        imageWin = window.open(imageViewer, "_RhPacsImageViewer");
      }*/
      //同一个窗口打开，新窗口
      const winLeft = 8, winTop = 8, winWidth = screen.availWidth - (2 * winLeft), winHeight = screen.availHeight - (2 * winTop);
      if(!imageWin || imageWin.closed) {
        imageWin = window.open(imageViewer, "_RhPacsImageViewer", "left=" + winLeft + ",top=" + winTop + ",width=" + winWidth + ",height=" + winHeight + ",toolbar=0,resizable=0");
        if(!imageWin) {
          this.$modal.alert("您浏览器阻止了弹出窗口，请允许弹出窗口。");
          return;
        }
      }
      imageWin.focus();
      //等窗口打开
      //setTimeout(this.focusWindow, 512);
      //反馈已分屏
      return true;
    },

    showImageWin() {
      const examInfo = this.examInfo;
      //打开影像窗口，在调用助手将影像窗口移动到其它屏幕
      const imageViewer = location.protocol + "//" + location.host + "/RadReportWriterImage?exam=" + examInfo.id;
      /*if(this.isConnectedSocked) {
        this.handleSocketSend('{"cmd":"browse","browser":"' + browserName() + '","token":"' + getToken() + '","url":"' + imageViewer +'"}');
      } else {
        if(!!imageWin) {
          imageWin.close();
        }
        imageWin = window.open(imageViewer, "_RhPacsImageViewer");
      }*/
      //同一个窗口打开，新窗口
      const winLeft = 8, winTop = 8, winWidth = screen.availWidth - (2 * winLeft), winHeight = screen.availHeight - (2 * winTop);
      if(!imageWin || imageWin.closed) {
        imageWin = window.open(imageViewer, "_RhPacsImageViewer", "left=" + winLeft + ",top=" + winTop + ",width=" + winWidth + ",height=" + winHeight + ",toolbar=0,resizable=0");
        if(!imageWin) {
          this.$modal.alert("您浏览器阻止了弹出窗口，请允许弹出窗口。");
          return;
        }
      }
      imageWin.focus();
      //等窗口打开
      //setTimeout(this.focusWindow, 512);
    },
    //未安装助手
    howto() {
      const nt = this.$notify({
          title: '提示',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          message: '未<button type="button" class="el-button el-button--primary el-button--mini"><span>安装</span></button>或未启用软航PACS助手。',
          duration: 0
      });
      
      nt.$el.querySelector("button").addEventListener("click", this.whato, false);
    },
    //下载助手
    whato(evt) {
      mh.download(baseURL + "/common/res/file?fileName=PacsHelper.zip");
    },

    /**
     * 暂停重连
     */
    stepReconnect() {
      if(this.splitScreenUsed) {
        this.socketEnabled = false;
        clearTimeout(timer_callPacsHelper);
      }
    },

    /**
     * 重连
     */
    reconnect() {
      if(this.splitScreenUsed && !this.isConnectedSocked && this.connectTimes > 0) {
        this.socketEnabled = true;
        this.callPacsHelper();
      }
    },
    /**
     * 置顶窗口
     */
    focusWindow() {
      if(imageWin) {
        imageWin.focus();
      }

      if(this.isConnectedSocked) {
        this.handleSocketSend('{"cmd":"focusWindow","browser":"' + browserName() + '","token":"' + getToken() + '"}');
      }
    }
  },

  created() {
    //是否报告和图像分屏
    const routeName = this.$route.path;
    if("/RadReportWriting" === routeName || "/RadReportWriterImage" === routeName) {
      this.splitScreenUsed = true;
      
      this.callPacsHelper();
    }
  },

  beforeDestroy() {
    this.stepReconnect();
  },

  activated() {
    this.reconnect();
  },

  deactivated() {
    this.stepReconnect();
  }
}