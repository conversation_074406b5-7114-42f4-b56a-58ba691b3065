const modal = {

  methods: {
    autoFocusTemplateTab() {
      const fmr = this.$refs.reportForm.$el;
      const els = ['examDesc', 'examDiagnosis', 'operationSuggestion'];
      for(let el of els) {
        fmr[el].addEventListener("focus", this.focuseTemplateTab);
      }
    },

    focuseTemplateTab() {
    try {
        this.triggerBind("switchTab", "Template");
      } catch (err) { console.error(err); }
    }
  },

  mounted() {
    try {
      //this.autoFocusTemplateTab();
    } catch (err) { console.error(err); }
  }
};

export default modal;