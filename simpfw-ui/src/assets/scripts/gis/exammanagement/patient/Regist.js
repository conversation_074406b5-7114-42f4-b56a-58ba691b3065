import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";

//import Treeselect from "@riophae/vue-treeselect";
//import "@riophae/vue-treeselect/dist/vue-treeselect.css";
//患者信息
import * as api from "@/assets/scripts/gis/exammanagement/patient/api";
//检查信息
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//获取机房列表
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
//转拼音
import {toPinyin} from "@/api/common";
//选择科室
//import { treeselect as deptTreeselect } from "@/api/system/dept";
import DeptPicker from "@/views/system/dept/DeptPicker";

import {props as treeProps} from "@/assets/scripts/pacs/BaseTreeModel";

//选检查部位
//import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";
import ExamPartsPicker from "@/views/pacs/comcfg/examparts/comp/ExamPartsPicker";
//选中用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//患者列表右键菜单
import PatientSheet from "@/views/gis/exammanagement/patient/comp/PatientSheet";
import {tableContextmenuItems} from "@/assets/scripts/gis/exammanagement/patient/Index";
//检查状态
import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//检查编辑
import {TransModel, UndoModel, ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

//更改机房
//import ExamEquipRoom from '@/views/gis/exammanagement/patient/comp/ExamEquipRoom';
import EquipRoomStatus from "@/views/pacs/equiproom/Index";
//排队
import Queue from "@/views/gis/exammanagement/queue/comp/Queue";
//选择查询接口返回数据
import OrderPicker from "@/views/gis/exammanagement/patient/comp/OrderPicker";
//选择退费医嘱
import OrderRefund from "@/views/gis/exammanagement/patient/comp/OrderRefund";
//放射检查modality
import {RAD_MODALITIES, EIS_MODALITIES} from "@/assets/scripts/pacs/modalities";

import {currDatetime, getAge, dateAdd, parseTime, undefinedOrNull, mergeWithNotNull} from "@/utils/common";
import {add as addNum} from "@/utils/NumberOps";
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";
//排队小票
import {QueueTicketPrint} from "@/assets/scripts/gis/exammanagement/queue/comp/Ticket";

//用户
import { getUserProfile } from "@/api/system/user";

//
const IGNORECONFIRM = "ignoreConfirm";
//转换拼音
let timer_convToPinyin;
//
const RegistWay = {
  manual: 'W',
  registNo: 'I',
  inpNo: 'Y'
}, RegistWayDef = RegistWay.inpNo, ModalityCodeDef = EIS_MODALITIES[0];
//
function emptyRegist() {
  return {
    registWay: RegistWayDef,
    
    patientInfo: {
      id: null,
      registNo: null,
      medicalRecordNo: null,
      qrCodeText: null, 
      healthCardId: null, 
      name: null, 
      namePingyin: null, 
      age: null,
      ageDisable: true, // lzw modify ,添加控件是否禁止
      birthday: null,
      birthPlace: null, 
      cardNo: null, 
      regNo: null, 
      insuranceNo: null, 
      assignedUnit: null, 
      address: null, 
      postcode: null, 
      homePhone: null, 
      phone: null, 
      contactName: null, 
      contactRelationship: null, 
      contactAddress: null, 
      contactPhone: null,

      healthCardType: {}, 
      gender: {dictValue: null}, 
      adoType: {}, 
      marriedStatus: {}, 
      nationality: {},
      nation: {}, 
      cardType: {}, 
      education: {}, 
      occupation: {}, 
      chargeType: {}, 
      ageUnit: {dictValue: 'Y'},
      vipFlag: '0'
    },

    id: null,
    examNo: null,
    examModality: {dictValue: ModalityCodeDef},
    inpType: {dictValue: null},
    examItem: {dictValue: 'qzj'},
    //equipRoom: {},
    examParts: [],

    //examDoctor: {},
    reqDept: {deptName: null},
    reqDoctor: {},
    inpNo: null,
    inpTimes: null,
    inpWard: {},
    inpRoom: {},
    bedNo: null,
    examDoctorsName: null,
    examDoctorsCode: null,

    examCost: null,
    examCostType: {dictValue: null},
    condParting: {dictValue: null},
    greenChannelFlag: null,
    appointTime: null,
    clinicDiagnosis: null,
    allergyHistory: null,
    clinicDisease: null,
    noteInfo: null,
    examPrerequire: null,
    reservedNoUsed: null,
    examAtPm: null,

    admNo: null,
    admSeriesNum: null,
    operationInfo: null,
    applyPath: null,
    ordId: null,ordName: null,arcimCode: null,ordBillStatus: null,
    ordPriorityCode: null,ordPriority: null,examPurpose: null,

    examParts_ids: [],
    examParts_names: null,

    greenChannelFlagValue: false,
    examPrerequireValue: false,
    reservedNoUsedValue: false,
    examAtPmValue: false,

    status: null,
    resultStatus: {
      dictValue: null
    },

    //排队信息：检查房间
    callInfo: {
      callRoom: {
        roomCode: null
      }
    }
  }
}

export {emptyRegist};

let timer_load;

const model = {

  name: "EisPatientRegist",

  dicts: ["uis_regist_way", "sys_yes_no", "uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "comm_married_status", "uis_inp_type"
    , "comm_occupation", "uis_cond_parting", "uis_inp_ward", "comm_nation", "uis_patient_sens_grade","uis_exam_cost_type"],

  components: { ExamPartsPicker, UserPicker, PatientSheet//, Treeselect
    //, ExamEquipRoom
    , EquipRoomStatus, Queue, OrderPicker, DeptPicker, OrderRefund },

  mixins: [ResultStatus, TransModel, UndoModel, ExamDataScope, QueueTicketPrint],

  data() {
    return {
      currentUser: null,

      IsEditFlag: false,

      treeSettings: treeProps, 

      deptTreeData: [],

      searchForm: {
        registWay: RegistWayDef,
        registCode: null
      }, 

      registForm: emptyRegist(),
      //当前修改的登记信息副本，新建或取消时对比是否有更改未保存
      registFormCopy: null,

      registFormRules: {
        "patientInfo.name": { required: true, message: '请输入患者姓名', trigger: 'blur' },
        "patientInfo.gender.dictValue": { required: true, message: '请选择患者性别' },
        "patientInfo.age": { required: true, message: '请输入患者年龄', trigger: 'blur' },
        "patientInfo.registNo": { required: true, message: '请输入患者登记号', trigger: 'blur' },
        "examModality.dictValue": { required: true, message: '请选择设备类型' },
        "inpType.dictValue": { required: true, message: '请选择就诊类别' },
        "examItem.dictValue": { required: true, message: '请选择检查项目' },
        "examParts_names": { required: true, message: '请选择检查部位' },
        "examCost": {  message: '请请输入检查费用', type: 'float' }
      },

      registFormOpts: {
        combo_equipRoom: [],
        combo_pinyin: []
      },

      loadRemoteProcessing: false,
      submitFormProcessing: false,
      registFormLoading: false,

      patientListActions: tableContextmenuItems(),//[{cmd: 'exam-queue-ticket::print', name: '打印排队小票'}]

      tab: {
        names: {
          examParts: "examParts",
        },
        current: "examParts"
      }
    };
  },

  methods: {

    //保存
    submitForm(opt) {
      const vm = this, regFm = vm.registForm;

      vm.$refs.registForm.validate(valid => {
        if(!valid) {
          this.$modal.msgWarning("请输入/选择标星(*)的信息。");
          return;
        }
        //是否直接提交，跳过一些操作
        let firmed = false;
        if(opt && !(opt instanceof Event)) {
          firmed = opt.firmed;
        }
        //
        // if(RegistWay.registNo === regFm.registWay && !vm.searchForm.registCode) {
        //   regFm.registWay = RegistWay.manual;
        // }
        //年龄有效性
        let cfmM, pat = regFm.patientInfo, ageUnit = pat.ageUnit;
        if(!firmed && !!ageUnit) {
          switch(ageUnit.dictValue) {
            case "Y":
              if(pat.age > 150) {
                cfmM = "患者年龄超过150岁。";
              }
              break;
            case "M":
              if(pat.age >= 12) {
                cfmM = "患者年龄已达12个月，相当于" + Math.floor(pat.age / 12) + "岁。";
              }
              break;
            case "D":
              if(pat.age >= 365) {
                cfmM = "患者年龄已达365天，相当于" + Math.floor(pat.age / 365) + "岁。";
              } else if(pat.age >= 30) {
                cfmM = "患者年龄已达30天，相当于" + Math.floor(pat.age / 30) + "月。";
              } else if(pat.age >= 7) {
                cfmM = "患者年龄已达7天，相当于" + Math.floor(pat.age / 7) + "周。";
              }
              break;
            case "W":
              if(pat.age >= 5) {
                cfmM = "患者年龄已达5周，相当于" + Math.floor((7 * pat.age) / 30) + "月。";
              }
              break;
            case "H":
              if(pat.age >= 24) {
                cfmM = "患者年龄超过24小时，相当于" + Math.floor(pat.age / 24) + "天。";
              }
              break;
          }
        }

        let cfm = !!cfmM? vm.$modal.confirm(cfmM + "是否返回修改？", {
          confirmButtonText: '继续提交',
          cancelButtonText: '返回修改',
        }) : Promise.resolve(true);

        cfm.then(() => {
          //执行完成触发
          const cb = res => {
            this.submitFormProcessing = false;
            if(200 == res.code) {
              this.$modal.msgSuccess("操作成功");
              const dat = res.data;
              if(dat) {
                //
                try {
                  const lastVer = cloneDeep(dat);
                  //告知检查信息有更新
                  this.$store.dispatch("lastEdited", lastVer);
                  //打印排队小票
                  if(!regFm.id) {
                    lastVer.opType = "register"; // 小票操作类型
                    this.printQueueTicket(lastVer);
                  }
                } catch (err) { console.error(err); }
                //保存后进入新建
                //if(!regFm.id) {
                  this.handleNew(IGNORECONFIRM);
                  return;
                //}
                //regFm.id = dat.id;
                //regFm.patientInfo.id = dat.patientInfo?dat.patientInfo.id : null;
              } 
              //
              //this.refreshPatientSheet();
              this.refreshModified(dat);
            } else {
              this.$modal.msgSuccess(res.msg || "操作失败");
            }
          };
          //
          regFm.greenChannelFlag = regFm.greenChannelFlagValue? 1 : null;
          regFm.examPrerequire = regFm.examPrerequireValue? 0 : null;
          regFm.reservedNoUsed = regFm.reservedNoUsedValue? 1: null;
          regFm.examAtPm = regFm.examAtPmValue? 1: null;
          //regFm.appointExamDate = currDatetime();
          //提交
          if(regFm.id) {
            //执行更新提交
            const cup = () => {
              this.submitFormProcessing = true;
              eiapi.update(regFm).then(cb).catch(() => this.submitFormProcessing = false);
            };
            
            if(!firmed) {
              //医嘱登记的检查部位是否有更改
              const fmc = !!this.registFormCopy? JSON.parse(this.registFormCopy) : null;
              if(!!fmc && fmc.ordId) {
                const uni = fmc.examParts.filter(ep => -1 === regFm.examParts.findIndex(e => ep.id === e.id)), ordId = regFm.ordId;
                if(uni.length > 0 && ordId) {
                  //查询已执行医嘱
                  eiapi.loadRemote(RegistWay.registNo, regFm.patientInfo.registNo, "E", ordId).then(res => {
                    const ords = res.data;
                    if(!!ords && ords.length > 0) {
                      this.$refs.orderRefund.show(ords, uni);
                    } else {
                      cup();
                    }
                  });
                  return;
                }
              }
            }
            //
            cup();
          } else {
            this.submitFormProcessing = true;
            eiapi.save(regFm).then(cb).catch(() => this.submitFormProcessing = false);
          }
          //
        });
      });
    },
    //勾选部位
    handleCheckExamParts(items) {
      let ei = this.registForm, examParts = items;
      //选中部位绑定的机房
      //let equipRoom;
      //if(examParts.length == 1 && (equipRoom = parts.equipRoom) && equipRoom.dictCode) {
      //  ei.equipRoom = cloneDeep(equipRoom);
      //}
      //
      /*if(undefinedOrNull(parts.examCosts)) {
        this.$modal.msg(`"${parts.partsName}"未设置检查费用，可进入"系统管理>其它设置>部位管理"进行设置`);
      }*/
      ei.examParts = examParts;
      ei.examParts_ids = examParts.map(p => p.id);
    },
    //双击取消部位
    uncheckExamParts(parts, pasive) {
      if(false !== pasive) {
        this.$refs["examPartsTree"].setChecked([parts.id], false);
        return;
      }

      let examParts = this.registForm.examParts;
      const idx = examParts.findIndex(p => p.id == parts.id);
      if(-1 != idx) {
        examParts.splice(idx, 1);
      }
    },
    //存入部位
    setExamInfo_examParts() {
      console.warn("未实现 " + setExamInfo_examParts)
    },

    /**
     * 读取检查信息/患者信息
     * @param examInfo 检查
     */
    handleEdit(examInfo) {
      const examInfoId = examInfo? examInfo.id : null;
      if(!examInfoId) {
        return;
      }

      //
      //var fm = this.registForm;
      //if(examInfoId == fm.id) {
      //  return;
      //}
      //添加一个标志。watch的时候不弹窗控制
      this.IsEditFlag = false

      this.resetRegistForm();

      this.registFormLoading = true;
      eiapi.get(examInfoId).then(res => {
        this.registFormLoading = false;
        const data = res.data;
        if(!data) {
          return;
        }
        //
        let cb = () => {
          data.patientInfo.ageDisable = false; 
          this.fillRegistForm(data);
          //暂存副本
          this.registFormCopy = JSON.stringify(this.registForm);
        };
        //
        if(2 === data.status) {
          this.$modal.confirm('该检查已被删除，是否继续编辑？').then(cb);
        } else {
          cb();
        }
        
      }).catch(() => this.registFormLoading = false);
    },
    //
    resetRegistForm() {
      //Object.assign(this.registForm, emptyRegist());
      this.registForm = emptyRegist();
      //
      this.registFormCopy = null;
    },
    //
    fillRegistForm(dat) {
      if(dat) {
        let fm = this.registForm;
        //
        if(dat.examParts) {
          let examParts_ids = [], examParts_names = [];
          dat.examParts.forEach(p => {
            examParts_ids.push(p.id);
            examParts_names.push(p.partsName);
          });
          fm.examParts_ids = examParts_ids;
          fm.examParts_names = examParts_names.join(",");
        }
        dat.greenChannelFlagValue = !!dat.greenChannelFlag;
        dat.examPrerequireValue = 0 === dat.examPrerequire;
        dat.reservedNoUsedValue = !!dat.reservedNoUsed;
        dat.examAtPmValue = 1 === dat.examAtPm;

        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //Object.assign(this.registForm, fm);
        //console.log(this.registForm);
        this.convToPinyin();
      }
    },
    //新建
    handleNew(mix) {
      //
      const reregist = () => {
        this.resetRegistForm();
        this.$router.push({name: this.$route.name});
      };
      //let prom = 'ignoreConfirm' === mix? Promise.resolve(true) : this.$modal.confirm("是否确定重新登记？");
      //prom.then(() => {
        if(IGNORECONFIRM === mix) {
            reregist();
        }
        //else if(JSON.stringify(this.registForm) != JSON.stringify(emptyRegist())){
        else if(this.checkModified()) {
          this.$modal.confirm("当前信息未保存，是否新建？").then(reregist)
        } else {
          reregist();
        }
        //this.$router.push({path: "/exammanagement/PatientList"});
      //});
    },
    /**
     * 取消登记/清空登记信息
     */
    handleCancel() {
      if(this.checkModified()) {
        this.$modal.confirm("有信息更新，是否需要保留之前的数据？").then(this.submitForm).catch(() => { this.handleNew(IGNORECONFIRM); });
      } else {
        this.handleNew(IGNORECONFIRM);
      }

      return;
      let item = this.registForm;
      //
      this.$modal.confirm("是否确定取消？").then(() => {
        if(item.id) {
          this.updateResultStatus(item, StatusDict.cancel).then(res => {
            if(res && 200 === res.code) {
              //重载表单
              this.refreshModified(item);
            }
          });
        } else {
          this.handleNew();
        }
      });
    },
    //
    handleDelete(mix) {
      let fm = this.registForm, item = !!mix && (mix instanceof Event)? fm : mix;
      if(item.id) {
        /*this.$modal.confirm("是否确定删除？").then(() => {
          return eiapi.del(item.id)
        }).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(item);
          }
        });*/
        this.$refs.patientSheet.verifyForDelete(item);
      }
    },
    undoDelete() {
      const fm = this.registForm;
      if(fm.id) {
        this.handleUndoDelete(fm).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    undoCancel() {
      const fm = this.registForm;
      if(fm.id) {
        this.handleUndoCancel(fm).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param posts 过滤用户岗位
     */
    toPickUser(tar, posts) {
      //检查医生，根据登陆账号岗位过滤用户岗位
      let deptId = null;
      //暂时不限制
      //if(tar=="examDoctors")  deptId = this.currentUser.deptId
      this.$refs["userPicker"].showPicker({target: tar, deptId:deptId,posts});
    },
    /**
     * 选择的用户
     * @param tar 触发选择的属性/表单元素
     * @param usr 选择的用户
     */
    pickUser(tar, usr) {
      //console.log(usr);
      const fm = this.registForm;

      if(tar) {
        if('examDoctors' === tar) {
          fm[`${tar}Code`] = usr.userName;
          fm[`${tar}Name`] = usr.nickName;
        } else {
          fm[tar] = usr;
        }
      }
    },
    //读取部门树信息
    //buildDeptTree() {
    //  deptTreeselect().then(res => {
    //    this.deptTreeData = res.data;
    //  });
    //},

    /**
     * 调用接口读取登记信息
     */
    loadRemote() {
      const sfm = this.searchForm, registWay = sfm.registWay;
      let registCode = sfm.registCode;
      if(!registWay || !registCode) {
        this.$modal.alert("请按登记方式输入.");
        return;
      }
      //住院号外其它方式增加补0到10位
      if (registCode.length < 10&&registWay!=RegistWay.inpNo) {
        registCode = registCode.padStart(10, '0');
        this.searchForm.registCode = registCode
      }

      //住院号增加补0到8位
      if (registCode.length < 8&&registWay==RegistWay.inpNo) {
        registCode = registCode.padStart(8, '0');
        this.searchForm.registCode = registCode
      }



      this.resetRegistForm();
      this.IsEditFlag = true
      this.loadRemoteProcessing = true;

      eiapi.loadRemote(registWay, registCode).then(res => {
        this.loadRemoteProcessing = false;

        let items = res.data;
        if(!items || !items.length) {
          this.$modal.msgWarning("没有相关数据.");
          return;
        }
        //触发一些更新，如数据字典，科室信息，检查部位
        const modified = res.modified;
        if(modified) {
          modified.forEach(mod => {
            if("dept" === mod) {
              //this.buildDeptTree();
            } else if(0 === mod.indexOf("dict::")) {
              this.dict.reloadDict(mod.substring(6));
            } else if("examParts" === mod) {
              //this.$refs["examPartsTree"].buildTree();
            }
          });
        }
        //
        if(items.length === 1) {
          this.pickOrd(items[0]);
          return ;
        }
        //alert(items.length);
        this.$refs.ordPicker.show(items);
      }).catch(() => this.loadRemoteProcessing = false);
    },
    pickOrd(exam) {
      //不使用其病历号
      exam.medicalRecordNo = null;

      this.resetRegistForm();
      if(!exam.examModality) { exam.examModality = {dictValue: "EIS"}; }
      this.fillRegistForm(exam);
    },
    //取消医嘱
    refundOrd(rows) {
      if(!!rows && rows.length > 0) {
        let fm = this.registForm, ordId = fm.ordId;
        if(ordId) {
          let sym = "@", ordsId = ordId.split(sym);
          for(let i = ordsId.length - 1; i >= 0; i --) {
            if(-1 !== rows.findIndex(r => r.ordId === ordsId[i])) {
              ordsId.splice(i, 1);
            }
            ordId = ordsId.join(sym);
            fm.ordId = ordId;
          }
        }
      }

      this.submitForm({firmed: true});
    },
    //页面加载
    prepareLoad() {
      clearTimeout(timer_load);

      timer_load = setTimeout(this.onLoad, 400);
    },
    onLoad() {
      let params = this.$route.params;
      //console.log(params);
      if(params && params.id) {
        this.handleEdit(params);
      }
      //
      this.layoutPage();
    },

    //改变年龄单位
    handleChangeAgeUnit () {
        this.handleChangeAge(this.registForm.patientInfo.age)
    },
    //改变年龄
    handleChangeAge (val) {
        const fm = this.registForm;
        //只有手工登记能输入年龄，其他不能输入。输入年龄时，动态改变生日
        //if (!fm.patientInfo.birthday) {
        const date = currDatetime()
        let bday = date
        switch (fm.patientInfo.ageUnit.dictValue) {
            case "Y":
                bday = dateAdd(date, -1 * parseInt(val), "y");
                break
            case "M":
                bday = dateAdd(date, -1 * parseInt(val), "m");
                break
            case "W":
                bday = dateAdd(date, -1 * parseInt(val), "w");
                break
            case "D":
                bday = dateAdd(date, -1 * parseInt(val), "d");
                break
            case "H":
                bday = dateAdd(date, -1 * parseInt(val), "h");
                break
        }


        fm.patientInfo.birthday = parseTime(bday);
        // }
                     
    },
    //改变年龄
    //handleChangeAge(val) {
    //  const fm = this.registForm;
    //  if(!fm.patientInfo.birthday) {
    //    const date = currDatetime(), bday = dateAdd(date, -1 * parseInt(val), "y");
    //    fm.patientInfo.birthday = parseTime(bday);
    //  }
    //},

    //选择生日
    handleChangeBirthday(val) {
      const fm = this.registForm;
      if(undefinedOrNull(fm.patientInfo.age)) {
        const date = currDatetime(), age = date.getFullYear() - val.getFullYear();
        if(age > 0) {
          fm.patientInfo.age = age;
        }
      }
    },
    //全选输入框文本
    handleFocusRegistCode($evt) {
      const el = $evt.target || $evt.srcElement;
      el.select();
    },
    //点击患者列表菜单
    handlePatientListAction(cmd, item) {
      //console.log(cmd, item);
      switch(cmd) {
        case 'exam::edit':
          this.handleEdit(item);
          break;
        case 'exam::del':
          this.handleDelete(item);
          break;
        case 'exam::precond':
        case 'exam::at-pm':
          let props;
          if('exam::precond' == cmd) {
            props = {examPrerequire: 0};
          } else {
            props = {examAtPm: 1};
          }
          this.handleTrans(item, props).then(res => {
            if(res && 200 === res.code) {
              this.refreshModified(item);
            }
          });
          break;
        //case 'exam::equip-room-change':
        //  this.$refs.examEquipRoom.change(item);
        //  break;
        case 'report::write':
          let toRoute = "EisReportWriting";
          if(!!item.examModality && RAD_MODALITIES.includes(item.examModality.dictValue)) {
            toRoute = "RadReportWriting";
          }
          this.$router.push({name: toRoute, params: {report: item}});
          break;
        case 'exam::copy':
          document.execCommand('Copy','false',null);
          break;
        case 'exam::traceCase':
          this.$router.push({ name: "TraceCaseAddEdit", params: {"examNo": item.examNo}});
          break;
        case 'exam-queue-ticket::print':
          this.printQueueTicket(item);
          break;
        default: 
          console.warn("未实现：", cmd);
      }
    },
    //刷新患者列表
    refreshPatientSheet() {
      this.$refs.patientSheet.getList();
    },
    //更相关数据
    refreshModified(item) {
      const fm = this.registForm;
      if(item && fm.id == item.id) {
        this.handleEdit(item);
      }
      this.refreshPatientSheet();
    },
    //布局
    layoutPage() {
      try {
        const eptp = document.querySelector(".examPartsTreePane");
        if(eptp) {
          eptp.style.height = (document.querySelector(".patient-info-card .el-card__body").clientHeight - 4 * 2) + "px";
        }
      } catch (err) { console.error(err); }
    },
    //机房列表
    findEquipRoom() {
      findRoom({}).then(res => {
        const equipRooms = res && res.rows || [];
        this.registFormOpts.combo_equipRoom = equipRooms;
        this.applyDataCtrl(DataCtrlDict.ItemType.room, equipRooms);
      });
    },
    //选择检查部位
    pickExamParts() {
      const fm = this.registForm, examMod = fm.examModality, examItem = fm.examItem, inpType = fm.inpType;
      const params = {modalityCode: (examMod? examMod.dictValue : null)
        , examItem: examItem
        , inpTypesCode: (inpType? inpType.dictValue : null)};
      //
      if(params.inpTypesCode) {
        params.inpType = this.dict.type.uis_inp_type.find(t => t.raw.dictValue === params.inpTypesCode).raw;
      }

      this.$refs.examPartsPicker.findData(params);
    },
    //查询登记号是否已登记
    findPatient() {
      const fm = this.registForm;
      if(!fm.patientInfo || !fm.patientInfo.registNo) {
        return;
      }
      api.find({registNo: fm.patientInfo.registNo}).then(res => {
        //console.log(res);
        if(res.total === 1) {
          const row = res.rows[0];

          row.age = getAge(row.birthday).age
          row.ageUnit.dictValue = getAge(parseTime(row.birthday, "{y}-{m}-{d}")).ageUint

          this.dict.type.uis_age_unit.forEach(res => {
              if (res.value == row.ageUnit.dictValue) {
                row.ageUnit.dictLabel = res.label
              }
          })

          const m = "<div>根据该登记号找到患者信息如下，是否使用？</div>" 
            + "<div>" + row.name 
            + "，" + (row.gender? row.gender.dictLabel : "") 
            + "，" + (row.age) + (row.ageUnit? row.ageUnit.dictLabel : "") + "</div>"
          this.$confirm(m, "确认", {
            dangerouslyUseHTMLString: true
          }).then(() => {
            mergeWithDeep(fm.patientInfo, row, null, mergeWithNotNull);
            this.convToPinyin();
          });
        } else if(res.total > 1) {
          this.$modal.msgWarning(`找到${res.total}条该登记号的患者信息，请核查登记信息。`);
        }
      });
    },
    //选择科室
    pickDept() {
      this.$refs.deptPicker.findData();
    },
    //选择科室
    handleCheckDept(dept) {
      this.registForm.reqDept = cloneDeep(dept);
    },
    delayConvToPinyin() {
      clearTimeout(timer_convToPinyin);
      timer_convToPinyin = setTimeout(this.convToPinyin, 1000);
    },
    //转换拼音
    convToPinyin() {
      clearTimeout(timer_convToPinyin);

      const fm = this.registForm, pat = fm.patientInfo;
      const patName = !!pat? pat.name : null;
      this.registFormOpts.combo_pinyin = [];
      if(!!pat && pat.namePingyin) {
        this.registFormOpts.combo_pinyin.push(pat.namePingyin);
      }
      if(!!patName) {
        toPinyin(patName).then(res => {
          const items = res.data || [];
          this.registFormOpts.combo_pinyin = items;
          if(!!pat) { pat.namePingyin = items[0]; }
        })
      }
    },
    //登记信息是否有更改
    checkModified() {
      return !!this.registFormCopy && JSON.stringify(this.registForm) !== this.registFormCopy;
    }
  },

  created() {
    getUserProfile().then(response => {
      this.currentUser = response.data;
    });
    //this.buildDeptTree();
    this.findEquipRoom();
  },

  mounted() {
    this.prepareLoad();
    //
    window.addEventListener("resize", this.layoutPage);
  },

  activated() {
    this.prepareLoad();
  },

  watch: {
    //选择的部位更改
    "registForm.examParts": {
      deep: true,
      handler(newVal, oldVal) {
        let examParts_names = [], examCost = 0;
        if(newVal && newVal.length) {
          newVal.forEach(p => {
            //拼接部位名称
            examParts_names.push(p.partsName);
            //费用计算
            let examCost0 = parseFloat(p.examCosts);
            if(!isNaN(examCost0)) {
              examCost = addNum(examCost, examCost0);
            }
          });
        }

        const eifm = this.registForm;
        eifm.examParts_names = examParts_names.join(",");
        //费用，医嘱登记的检查不计算
        if(!eifm.ordId) {
          eifm.examCost = examCost;
        }

        if(eifm.examParts_names==''){
            eifm.examParts_names=null
        }
        if(eifm.examCost=='0'){
            eifm.examCost=null//用于新建时， this.registForm, emptyRegist() 对比
        }
      }
    },
    //登记号改变
    "registForm.patientInfo.registNo": {
        deep: true,
        handler (newVal, oldVal) {
            //console.log(newVal, oldVal, this.IsEditFlag)
            if (oldVal != newVal && this.IsEditFlag && newVal) {
                this.findPatient()
                this.IsEditFlag = false
            }
        }
    },

    /**
     * 勾选/取消勾选"绿色通道"
     */
    "registForm.greenChannelFlagValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.greenChannelFlag = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"诊前检查"
     */
    "registForm.examPrerequireValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examPrerequire = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"启用保留号"
     */
    "registForm.reservedNoUsedValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.reservedNoUsed = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"下午检查"
     */
    "registForm.examAtPmValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examAtPm = nv? 1 : null;
      }
    },

    /**
     * 切换登记方式
     */
    "searchForm.registWay": {
      deep: true,
      handler(nv, ov) {
        this.registForm.registWay = nv;
        //this.$modal.confirm("是否清空'基本信息'和'检查信息'所有数据？").then(this.resetRegistForm);
        this.resetRegistForm();
        if (nv === RegistWay.manual) {
            this.registForm.patientInfo.ageDisable = false
        }
      }
    },

    /**
     * 检查类型字典取值后执行
     */
    "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        let items = [];
        if(nv && nv.length) {
          const conv = d => {
            return {value: d.dictValue, label: ("--" + d.dictLabel), raw: d};
          };
          //层级
          nv.forEach(e => {
            if(e.raw.parent && e.raw.parent.dictCode) {
              return true;
            }
            items.push(e);
            //
            let d = e.raw;
            if(d.children && d.children.length) {
              d.children.forEach(c => {
                items.push(conv(c));
              });
            }
            //
            nv.forEach(c => {
              if(c.raw.parent && c.raw.parent.dictCode === d.dictCode) {
                items.push(conv(c.raw));
              }
            });
          });
        }
        //
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, items);
      }
    },

    /**
     * 就诊类型字典取值后执行
     */
    "dict.type.uis_inp_type": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        const topModalities = this.topModalities;
        //所属设备类型的检查项目
        const nv0 = nv.filter(e => !e.raw || !e.raw.extend || !e.raw.extend.extendS1 || -1 !== topModalities.findIndex(m => m === e.raw.extend.extendS1));
        //权限
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv0);
      }
    },

    /**
     * 登记方式字典
     */
    "dict.type.uis_regist_way": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }
  },

  computed: {
    /**
     * 勾选/取消勾选绿色通道
     */
    regNoReadonly() {
      const sfm = this.searchForm, rfm = this.registForm;
      return RegistWay.manual !== sfm.registWay || !!rfm.id;
    },
    //是否可删除
    deleteEnabled() {
      const fm =  this.registForm;
      return fm.id && 2 !== fm.status;
    },
    //是否可撤销删除
    undoDeleteEnabled() {
      const fm =  this.registForm;
      return fm.id && 2 === fm.status;
    },
    //是否可撤销取消
    cancelEnabled() {
      const fm =  this.registForm;
      return fm.id && (!fm.resultStatus || !fm.resultStatus.dictValue || StatusDict.cancel !== fm.resultStatus.dictValue);
    },
    //是否可撤销取消
    undoCancelEnabled() {
      const fm =  this.registForm;
      return fm.id && !!fm.resultStatus && StatusDict.cancel === fm.resultStatus.dictValue;
    },
    //是否可编辑
    editEnabled() {
      const fm = this.registForm, resultStatus = fm.resultStatus, resultStatusCode = !!resultStatus? resultStatus.dictValue : null;
      return !resultStatusCode || /^[012]$/.test(resultStatusCode);
    },
    //是否禁止编辑年龄
    ageDisabled() {
      const sfm = this.searchForm, rfm = this.registForm;
      //非人工登记、非编辑记录时禁止编辑
      return RegistWay.manual !== sfm.registWay && !rfm.id;
    }
  }

};

export default model;