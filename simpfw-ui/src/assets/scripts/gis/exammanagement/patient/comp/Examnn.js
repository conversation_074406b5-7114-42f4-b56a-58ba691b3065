
import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/gis/exammanagement/examinfo/api";

import {EditModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

let model = {

  extends: BaseGridModel,
  mixins: [EditModel],

  props: ["examAtPm", "examPrerequire", "buttonIconState", "buttonTipState"],

  data() {
    return {
      searchForm: {
        id: null, 
        ordId: null, 
        examPrerequire: null,
        examAtPm: null,
        patientInfo: {
          name: null
        }
      }
    };
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      let fm = this.searchForm;
      fm.examPrerequire = this.examPrerequire;
      fm.examAtPm = this.examAtPm;
      api.find(fm).then(res => {
        this.loading = false;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    },

    /** 准备就绪 */
    handleChange(row) {
      this.triggerBind("change", row);
    }
  }
};

export default model;