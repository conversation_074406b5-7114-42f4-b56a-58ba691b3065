import {cloneDeep, mergeWith} from "lodash";

import {mergeWithNotNull, undefinedOrNull} from "@/utils/common";
//接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api.js";
//
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
//编辑
import {EditModel, UndoModel, TransModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth"
//查询偏好配置
import PatientListSearchOptions from '@/views/pacs/report/comp/PatientListSearchOptions';
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import {currDate, parseTime, currDatetime} from "@/utils/common";

//定时刷新列表
let timer_getList;

import { SearchOptions} from "@/assets/scripts/pacs/report/mixins";
export default {

  extends: BaseGridModel,
  mixins: [EditModel, UndoModel, TransModel,SearchOptions, ExamDataScope],
  dicts: ["uis_exam_result_status",'uis_exam_item'],

  components: { Contextmenu, OperationAuth, PatientListSearchOptions },

  props: {
    //定时刷新秒数
    refresh: {type: Number},
    //表格行右键菜单
    actions: {type: Array, default: () => []},
    //固定查询条件
    filter: {type: Object},//e.g. {status:0,datesCreated:1}
    //限制
    restrict: {type: Object},

    //部门，限制显示
    dept: {type: Object}
  },

  data() {
    return {
      searchForm: {
        combo_props: [{value: "patientInfo.name", label: "姓名"}  
          , {value: "examNo", label: "检查号"}
          , {value: 'patientInfo.registNo',label:'登记号'}
          , {value: 'encounter.encounterMRN',label:'住院号'}
          , {value: 'callInfo.callNo',label:'排队号'}]

        , propName: "patientInfo.registNo"
        , propValue: null
        ,resultStatusValues:null
        ,examItemCodes:null
        ,pageSize:17
      }
    }
  },

  methods: {

    /**
     * 搜索
     */
    getList: function(opts) {
      clearTimeout(timer_getList);
      //
      this.checkValue()
      this.handleCurrentChange(null);
      //查询参数
      let sfm = this.searchForm, params = {pageSize: sfm.pageSize, pageNum: sfm.pageNum};
      //父组件指定参数
      if(this.filter) {
        mergeWith(params, this.filter, true, mergeWithNotNull);
      }
      //偏好查询设置, 覆盖父组件
      //let searchOpts = this.$refs.searchOpt.readOpts();
      //mergeWith(params, searchOpts);
      
      params.resultStatusValues = cloneDeep(sfm.resultStatusValues);
      //检查进度限制
      const rst = this.restrict;
      if((!params.resultStatusValues || params.resultStatusValues.length === 0)
        && rst && rst.resultStatusValues) {
          params.resultStatusValues = rst.resultStatusValues
      }

      if(undefined!=this.dept) {
        params["examDept"] = {};
        params["examDept"]["deptId"] = this.dept.deptId;
      }

      //已删除状态
      let pos;
      if(!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
          params.resultStatusValues.splice(pos, 1);
          //params.resultStatusAsStatus = "2";
          params.status = 2;
      } else {
        params.status = 0;
      }
      //检查项目
      params.examItemCodes=sfm.examItemCodes
      //根据下拉
      let propName = sfm.propName, propValue = sfm.propValue;
      if(!!propName && !!propValue) {
        let propsName = propName.split(/\./), prop = params;
        for(let i = 0, len = propsName.length; i < len; i ++) {
          let pnam = propsName[i];
          if((i + 1) < len) {
            prop[pnam] = {};
            prop = prop[pnam];
            continue;
          }
          prop[pnam] = propValue;
        }
        //查询不限定时间
        params.datesCreated = null;
        delete params.datesCreated;
      }

      //当天预约检查
      // params.appointExamDateGe = parseTime(currDate());
      // //
      // params.appointExamDateLt = params.appointExamDateGe;
      params.appointWithCreated = true;

      //点击按钮触发
      if(!timer_getList || opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }
      //
      //params.status=0;
      //params.status = null;
      //
      eiapi.find(params).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;

        this.delayGetList();
      }).catch(this.delayGetList);
    },
    //轮询
    delayGetList() {
      if(this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },

    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if(1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    //删除
    undoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if(res && 200 === res.code) { this.getList(); }
      });
    },
    //延迟检查
    handlePostpone(row) {
        let props = {examAtPm: 1};
        this.handleTrans(row, props).then(res => this.getList());
    },
    //
    focusPropValue() {
      try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if(!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom? row.equipRoom.roomName : null;
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //打开查询设置
    prepareSearchOpt() {
      this.$refs.searchOpt.open();
    },
    //登记号补0
    checkValue(){
        let fm = this.searchForm
        if(fm.propName=='patientInfo.registNo'){
            
            if(!!fm.propValue&&fm.propValue.length>0 && fm.propValue.length<10){
                fm.propValue = fm.propValue.padStart(10,'0');
            }
        }
    },
    //
    colStyle({row, column, rowIndex, columnIndex}) {
      if('resultStatus.dictLabel' !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.resultStatus, stav = sta? sta.dictValue : null;
      if(!undefinedOrNull(stav)) {
        clz.push("table-cell-result-status-" + stav);
      }

      return clz.join (" ");
    }
  },

  watch: {
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },
    //
    dept:{
      handler(nv, ov){
        if(undefined==ov&&undefined!=nv){
          this.getList();
        }
      }
    }

  },

  activated() {
    this.delayGetList();
    this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  },

  computed: {
    combo_resultStatus() {
      const dict = this.dict.type.uis_exam_result_status, rst = this.restrict;
      if(!rst || !rst.resultStatusValues || !rst.resultStatusValues.length) {
        return dict;
      }
      return dict.filter(d => -1 !== rst.resultStatusValues.findIndex(v => v === d.value));
    }
  }
};
