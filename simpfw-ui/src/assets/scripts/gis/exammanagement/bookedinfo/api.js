import request, { postForm } from '@/utils/request'

const url = '/exammanagement/booked';

// 查询列表
export function find(query) {
  return postForm({
    url: url + '/list',
    data: query
  })
}
// 查询列表
export function findByAppoint(query) {
  return postForm({
    url: url + '/appointList',
    data: query
  })
}

// 查询列表
export function weekInfoList(query) {
  return request({
    url: url + '/weekInfoList',
    method: 'get'
  })
}

// 查询详细
export function get(id) {
  return request({
    url: url + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: url + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: url + '/update',
    method: 'post',
    data: data
  })
}

// 更新预约时间
export function updateAppointTime(query) {
  return request.post(url + '/updateAppointTimes', query);
}

// 删除
export function del(id) {
  return request.put(url + '/del/' + id)
}

// 查询下拉树结构
export function treeselect() {
  return request({
    url: url + '/tree'
  })
}

// 修改激活状态
export function updateActiveStatus(data) {
  return request({
    url: url + '/updateActiveStatus',
    method: 'put',
    data: data
  })
}

