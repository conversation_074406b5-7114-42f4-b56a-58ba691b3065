import store from "@/store/index";

import { cloneDeep, mergeWith as mergeWithDeep } from "lodash";


import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as api from "@/assets/scripts/gis/exammanagement/patient/api";
//检查信息
import * as biapi from "@/assets/scripts/gis/exammanagement/bookedinfo/api";
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//获取机房列表
import { find as findRoom } from "@/assets/scripts/pacs/equiproom/api";
//转拼音
import { toPinyin } from "@/api/common";
//选择科室
//import { treeselect as deptTreeselect } from "@/api/system/dept";
import DeptPicker from "@/views/system/dept/DeptPicker";

import { props as treeProps } from "@/assets/scripts/pacs/BaseTreeModel";

//选检查部位


//患者列表右键菜单
import PatientSheet from "@/views/gis/exammanagement/patient/comp/PatientSheet";
import { tableContextmenuItems } from "@/assets/scripts/gis/exammanagement/patient/Index";
//检查状态
import { StatusDict } from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//检查编辑
import { TransModel, UndoModel, ResultStatusModel as ResultStatus } from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

//选择查询接口返回数据
import OrderPicker from "@/views/gis/exammanagement/patient/comp/OrderPicker";
//选择退费医嘱
import OrderRefund from "@/views/gis/exammanagement/patient/comp/OrderRefund";
//选检查部位
//import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";
import ExamPartsPicker from "@/views/pacs/comcfg/examparts/comp/ExamPartsPicker";
//选中用户
import UserPicker from "@/views/system/user/comp/UserPicker";

//放射检查modality
import { RAD_MODALITIES, ES_MODALITIES,EIS_MODALITIES } from "@/assets/scripts/pacs/modalities";

//更改机房
//import ExamEquipRoom from '@/views/gis/exammanagement/patient/comp/ExamEquipRoom';
import EquipRoomStatus from "@/views/pacs/equiproom/Index";
//
import { DataCtrlDict } from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";
import { currDatetime, getAge, dateAdd, parseTime, undefinedOrNull, mergeWithNotNull } from "@/utils/common";
import { add as addNum } from "@/utils/NumberOps";

import OrderSheet from "@/views/gis/exammanagement/bookedinfo/comp/OrderSheet";
import HisSheet from "@/views/gis/exammanagement/bookedinfo/comp/HisSheet";

//排队小票
import {QueueTicketPrint} from "@/assets/scripts/gis/exammanagement/queue/comp/Ticket";

//定时刷新列表
let timer_getList;
const IGNORECONFIRM = "ignoreConfirm";
//转换拼音
let timer_convToPinyin;
const RegistWay = {
  manual: 'W',
  registNo: 'I',
  inpNo: 'Y'
}, RegistWayDef = RegistWay.registNo, ModalityCodeDef = EIS_MODALITIES[0],examItemCodeDef='qzj';

function emptyExamInfo() {
  return {
    registWay: RegistWayDef,
    IsEditFlag : false,
    patientInfo: {
      id: null,
      registNo: null,
      medicalRecordNo: null,
      qrCodeText: null,
      healthCardId: null,
      name: null,
      namePingyin: null,
      age: null,
      ageDisable: true, // lzw modify ,添加控件是否禁止
      birthday: null,
      birthPlace: null,
      cardNo: null,
      regNo: null,
      insuranceNo: null,
      assignedUnit: null,
      address: null,
      postcode: null,
      homePhone: null,
      phone: null,
      contactName: null,
      contactRelationship: null,
      contactAddress: null,
      contactPhone: null,

      healthCardType: {},
      gender: { dictValue: null },
      adoType: {},
      marriedStatus: {},
      nationality: {},
      nation: {},
      cardType: {},
      education: {},
      occupation: {},
      chargeType: {},
      ageUnit: { dictValue: 'Y' },
      vipFlag: '0'
    },

    id: null,
    examNo: null,
    examModality: { dictValue: ModalityCodeDef },
    inpType: { dictValue: 'I' },
    examItem: { dictValue: examItemCodeDef },
    examItemCode: null,
    //equipRoom: {},
    examParts: [],

    //examDoctor: {},
    reqDept: { deptName: null },
    reqDoctor: {},
    inpNo: null,
    inpTimes: null,
    inpWard: {},
    inpRoom: {},
    bedNo: null,
    examDoctorsName: null,
    examDoctorsCode: null,

    examCost: null,
    examCostType: { dictValue: null },
    condParting: { dictValue: null },
    greenChannelFlag: null,
    appointExamDate: null,
    appointExamTime: null,
    clinicDiagnosis: null,
    allergyHistory: null,
    clinicDisease: null,
    noteInfo: null,
    examPrerequire: null,
    reservedNoUsed: null,


    admNo: null,
    admSeriesNum: null,
    operationInfo: null,
    applyPath: null,
    ordId: null, ordName: null, arcimCode: null, ordBillStatus: null,
    ordPriorityCode: null, ordPriority: null, examPurpose: null,

    examParts_ids: [],
    examParts_names: null,

    greenChannelFlagValue: false,
    examPrerequireValue: false,
    reservedNoUsedValue: false,
    examAtPmValue: false,

    status: null,
    resultStatus: {
      dictValue: null
    },

    //排队信息：检查房间
    callInfo: {
      callRoom: {
        roomCode: null
      }
    },

  }
}

function emptyRegist() {
  return {
    registWay: RegistWayDef,
    IsEditFlag : false,
    examInfo:emptyExamInfo(),
    patientInfo: {
      id: null,
      registNo: null,
      medicalRecordNo: null,
      qrCodeText: null,
      healthCardId: null,
      name: null,
      namePingyin: null,
      age: null,
      ageDisable: true, // lzw modify ,添加控件是否禁止
      birthday: null,
      birthPlace: null,
      cardNo: null,
      regNo: null,
      insuranceNo: null,
      assignedUnit: null,
      address: null,
      postcode: null,
      homePhone: null,
      phone: null,
      contactName: null,
      contactRelationship: null,
      contactAddress: null,
      contactPhone: null,

      healthCardType: {},
      gender: { dictValue: null },
      adoType: {},
      marriedStatus: {},
      nationality: {},
      nation: {},
      cardType: {},
      education: {},
      occupation: {},
      chargeType: {},
      ageUnit: { dictValue: 'Y' },
      vipFlag: '0'
    },

    id: null,
    examNo: null,
    examModality: { dictValue: ModalityCodeDef },
    inpType: { dictValue: 'I' },
    examItem: { dictValue: examItemCodeDef },
    examItemCode: null,
    //equipRoom: {},
    examParts: [],

    //examDoctor: {},
    reqDept: { deptName: null },
    reqDoctor: {},
    inpNo: null,
    inpTimes: null,
    inpWard: {},
    inpRoom: {},
    bedNo: null,
    examDoctorsName: null,
    examDoctorsCode: null,

    examCost: null,
    examCostType: { dictValue: null },
    condParting: { dictValue: null },
    greenChannelFlag: null,
    appointExamDate: null,
    appointExamTime: null,
    clinicDiagnosis: null,
    allergyHistory: null,
    clinicDisease: null,
    noteInfo: null,
    examPrerequire: null,
    reservedNoUsed: null,


    admNo: null,
    admSeriesNum: null,
    operationInfo: null,
    applyPath: null,
    ordId: null, ordName: null, arcimCode: null, ordBillStatus: null,
    ordPriorityCode: null, ordPriority: null, examPurpose: null,

    examParts_ids: [],
    examParts_names: null,

    greenChannelFlagValue: false,
    examPrerequireValue: false,
    reservedNoUsedValue: false,
    examAtPmValue: false,

    status: null,
    resultStatus: {
      dictValue: null
    },

    //排队信息：检查房间
    callInfo: {
      callRoom: {
        roomCode: null
      }
    },

  }
}

export { emptyRegist };
//逻辑
export default {
  name: "bookedinfo",

  extends: BaseGridModel,

  dicts: ["uis_regist_way", "sys_yes_no", "uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "comm_married_status", "uis_inp_type"
    , "comm_occupation", "uis_cond_parting", "uis_inp_ward", "comm_nation", "uis_patient_sens_grade", "uis_exam_cost_type", 'gis_booked_time'],

  components: {
    ExamPartsPicker, UserPicker, PatientSheet//, Treeselect
    //, ExamEquipRoom
    , EquipRoomStatus, OrderSheet, HisSheet,OrderPicker,OrderRefund,DeptPicker
  },

  mixins: [ResultStatus, ExamDataScope, TransModel, UndoModel, QueueTicketPrint],
  data() {
    return {

      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 ;
        },

       
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '明天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '后天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 2);
            picker.$emit('pick', date);
          }
        }],
      },
      combo: {
        //设备型号列表
        devices: [],
        //房间列表
        equipRooms: []
      },
      registForm: emptyRegist(),
      registFormOpts: {
        combo_equipRoom: [],
        combo_pinyin: []
      },
      registFormRules: {
        "examInfo.patientInfo.name": { required: true, message: '请输入患者姓名', trigger: 'blur' },
        "examInfo.patientInfo.gender.dictValue": { required: true, message: '请选择患者性别' },
        "examInfo.patientInfo.age": { required: true, message: '请输入患者年龄', trigger: 'blur' },
        "examInfo.patientInfo.registNo": { required: true, message: '请输入患者登记号', trigger: 'blur' },
        "examInfo.examModality.dictValue": { required: true, message: '请选择检查类型' },
        "examInfo.inpType.dictValue": { required: true, message: '请选择就诊类别' },
        "examInfo.examItem.dictValue": { required: true, message: '请选择检查项目' },
        "examInfo.examParts_names": { required: true, message: '请选择检查部位' },
        "examInfo.examCost": { message: '请请输入检查费用', type: 'float' },
        "examInfo.appointExamDate": { required: true, message: '请输入预约日期', trigger: 'blur' },
        "examInfo.appointExamTime": { required: true, message: '请输入预约时间', trigger: 'blur' }

      },
      dialogVisible: false,
      loadRemoteProcessing: false,
      submitFormProcessing: false,
      registFormLoading: false,
      refresh: 5,
      bookedStatus:null,
      IsEditFlag : false,
      // 20230427
      currBookedInfo: {
        appointExamDate: '',
        appointExamTime: '',
      }
    };
  },

  methods: {

    getList: function (opts) {
      clearTimeout(timer_getList);

      if (!timer_getList || opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }

      biapi.weekInfoList().then(
        res => {
          this.grid = res
          this.delayGetList();
        }).catch(this.delayGetList);
    },
    delayGetList() {
      if (this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },
    selectRow(exam) {
      this.fillRegistForm(exam);
    },
    editBooked(exam) {
      let vm = this;
      //不使用其病历号
      exam.examInfo.medicalRecordNo = null;

      this.resetRegistForm();

      //待watch里部位更新完examCost后，在更新registForm，否则读取后端的examCost数据会被覆盖为0
      setTimeout(function(){
        if (!exam.examInfo.examModality) { exam.examInfo.examModality = { dictValue: ModalityCodeDef }; }
        vm.fillRegistForm(exam);
        vm.updateAppointTime()
        
        vm.registForm.examInfo.appointExamTime = exam.examInfo.appointExamTime
        vm.IsEditFlag=true
        vm.dialogVisible = true
        
        // 20230427, 记录修改前的预约时间，如果有
        vm.currBookedInfo.appointExamDate = exam.examInfo.appointExamDate
        vm.currBookedInfo.appointExamTime = exam.examInfo.appointExamTime
      },50);
      // console.log("this.currBookedInfo：", this.currBookedInfo)
    },
    registBooked(){
      this.resetRegistForm()
     
      this.IsEditFlag=false
      this.dialogVisible = true
    },
    //
    findEquipRooms() {
      findRoom({ pageNo: 1, pageSize: 9999 }).then(res => {
        const equipRooms = res.rows;
        this.combo.equipRooms = equipRooms;
        this.applyDataCtrl(DataCtrlDict.ItemType.room, equipRooms);
      });
    },
    handleOpen() { },
    focusPropValue() {
      try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {

          done();
        })
        .catch(_ => { });
    },
    //选择科室
    pickDept() {
      this.$refs.deptPicker.findData();
    },
    //选择科室
    handleCheckDept(dept) {
      this.registForm.reqDept = cloneDeep(dept);
    },
    delayConvToPinyin() {
      clearTimeout(timer_convToPinyin);
      timer_convToPinyin = setTimeout(this.convToPinyin, 1000);
    },
    //转换拼音  
    convToPinyin() {
      clearTimeout(timer_convToPinyin);

      const fm = this.registForm.examInfo, pat = fm.patientInfo;
      const patName = !!pat ? pat.name : null;
      this.registFormOpts.combo_pinyin = [];
      if (!!pat && pat.namePingyin) {
        this.registFormOpts.combo_pinyin.push(pat.namePingyin);
      }
      if (!!patName) {
        toPinyin(patName).then(res => {
          const items = res.data || [];
          this.registFormOpts.combo_pinyin = items;
          if (!!pat) { pat.namePingyin = items[0]; }
        })
      }
    },
    //登记信息是否有更改
    checkModified() {
      return !!this.registFormCopy && JSON.stringify(this.registForm) !== this.registFormCopy;
    },

    //改变年龄单位
    handleChangeAgeUnit() {
      this.handleChangeAge(this.registForm.examInfo.patientInfo.age)
    },
    //改变年龄
    handleChangeAge(val) {
      const fm = this.registForm.examInfo;
      //只有手工登记能输入年龄，其他不能输入。输入年龄时，动态改变生日
      //if (!fm.patientInfo.birthday) {
      const date = currDatetime()
      let bday = date
      switch (fm.patientInfo.ageUnit.dictValue) {
        case "Y":
          bday = dateAdd(date, -1 * parseInt(val), "y");
          break
        case "M":
          bday = dateAdd(date, -1 * parseInt(val), "m");
          break
        case "W":
          bday = dateAdd(date, -1 * parseInt(val), "w");
          break
        case "D":
          bday = dateAdd(date, -1 * parseInt(val), "d");
          break
        case "H":
          bday = dateAdd(date, -1 * parseInt(val), "h");
          break
      }


      fm.patientInfo.birthday = parseTime(bday);
      // }

    },
    updateAppointTime(){
      this.registForm.examInfo.appointExamTime=null
      const regFm = this.registForm;
      biapi.findByAppoint(regFm).then(res=>{
        this.bookedStatus=res.data
      }).catch(()=>{ this.registForm.examInfo.appointExamTime=null})
     
    },
    //选择生日
    handleChangeBirthday(val) {
      const fm = this.registForm.examInfo;
      if (undefinedOrNull(fm.patientInfo.age)) {
        const date = currDatetime(), age = date.getFullYear() - val.getFullYear();
        if (age > 0) {
          fm.patientInfo.age = age;
        }
      }
    },
    // 机房列表
    findEquipRoom() {
      findRoom({}).then(res => {
        const equipRooms = res && res.rows || [];
        this.registFormOpts.combo_equipRoom = equipRooms;
        this.applyDataCtrl(DataCtrlDict.ItemType.room, equipRooms);
      });
    },
    //选择检查部位
    pickExamParts() {
      const fm = this.registForm.examInfo, examMod = fm.examModality, examItem = fm.examItem, inpType = fm.inpType;
      const params = {
        modalityCode: (examMod ? examMod.dictValue : null)
        , examItem: examItem
        , inpTypesCode: (inpType ? inpType.dictValue : null)
      };
      //
      if (params.inpTypesCode) {
        params.inpType = this.dict.type.uis_inp_type.find(t => t.raw.dictValue === params.inpTypesCode).raw;
      }

      this.$refs.examPartsPicker.findData(params);
    },
    pickOrd(exam) {
      //不使用其病历号
      exam.examInfo.medicalRecordNo = null;

      this.resetRegistForm();
      if(!exam.examInfo.examModality) { exam.examInfo.examModality = {dictValue: "UIS"}; }
      this.fillRegistForm(exam);
    },
    //取消医嘱
    refundOrd(rows) {
      if(!!rows && rows.length > 0) {
        let fm = this.registForm.examInfo, ordId = fm.ordId;
        if(ordId) {
          let sym = "@", ordsId = ordId.split(sym);
          for(let i = ordsId.length - 1; i >= 0; i --) {
            if(-1 !== rows.findIndex(r => r.ordId === ordsId[i])) {
              ordsId.splice(i, 1);
            }
            ordId = ordsId.join(sym);
            fm.ordId = ordId;
          }
        }
      }

      this.submitForm({firmed: true});
    },
    //保存
    submitForm(opt) {
      const vm = this, regFm = vm.registForm;

      vm.$refs.registForm.validate(valid => {
        if (!valid) {
          this.$modal.msgWarning("请输入/选择标星(*)的信息。");
          return;
        }
        //是否直接提交，跳过一些操作
        let firmed = false;
        if (opt && !(opt instanceof Event)) {
          firmed = opt.firmed;
        }

        const examInfo = regFm.examInfo;

        //this.dialogVisible = false
        //mergeWithDeep(regFm, regFm.examCostType, null, mergeWithNotNull);
        ///mergeWithDeep(regFm, regFm.patientInfo, null, mergeWithNotNull);
        //delete regFm.patientInfo
        //delete regFm.healthCardType
        examInfo.examItemCode = examInfo.examItem.dictValue
        //mergeWithDeep(regFm, regFm.patientInfo, null, mergeWithNotNull);
        //mergeWithDeep(regFm, regFm.patientInfo, null, mergeWithNotNull);


        if (RegistWay.registNo === examInfo.registWay && !vm.searchForm.registCode) {
          examInfo.registWay = RegistWay.manual;
        }
        //年龄有效性
        let cfmM, pat = examInfo.patientInfo, ageUnit = pat.ageUnit;
        if (!firmed && !!ageUnit) {
          switch (ageUnit.dictValue) {
            case "Y":
              if (pat.age > 150) {
                cfmM = "患者年龄超过150岁。";
              }
              break;
            case "M":
              if (pat.age >= 12) {
                cfmM = "患者年龄已达12个月，相当于" + Math.floor(pat.age / 12) + "岁。";
              }
              break;
            case "D":
              if (pat.age >= 365) {
                cfmM = "患者年龄已达365天，相当于" + Math.floor(pat.age / 365) + "岁。";
              } else if (pat.age >= 30) {
                cfmM = "患者年龄已达30天，相当于" + Math.floor(pat.age / 30) + "月。";
              } else if (pat.age >= 7) {
                cfmM = "患者年龄已达7天，相当于" + Math.floor(pat.age / 7) + "周。";
              }
              break;
            case "W":
              if (pat.age >= 5) {
                cfmM = "患者年龄已达5周，相当于" + Math.floor((7 * pat.age) / 30) + "月。";
              }
              break;
            case "H":
              if (pat.age >= 24) {
                cfmM = "患者年龄超过24小时，相当于" + Math.floor(pat.age / 24) + "天。";
              }
              break;
          }
        }

        let cfm = !!cfmM ? vm.$modal.confirm(cfmM + "是否返回修改？", {
          confirmButtonText: '继续提交',
          cancelButtonText: '返回修改',
        }) : Promise.resolve(true);

        cfm.then(() => {
          
          //
          examInfo.greenChannelFlag = examInfo.greenChannelFlagValue ? 1 : null;
          examInfo.examPrerequire = examInfo.examPrerequireValue ? 0 : null;
          examInfo.reservedNoUsed = examInfo.reservedNoUsedValue ? 1 : null;
          examInfo.examAtPm = examInfo.examAtPmValue ? 1 : null;
          examInfo.resultStatus.dictValue = "11"
          examInfo.status = 3
          //提交
          if (examInfo.id) {
            //执行更新提交
            const cup = () => {
              this.submitFormProcessing = true;
              let b_regFm = cloneDeep(regFm)
              // const b_examInfo = b_regFm.examInfo;
              // b_examInfo.id=b_regFm.exam_id
              // b_regFm["examInfo"] = examInfo;
              // b_regFm.appointTime = b_regFm.appointExamTime
              biapi.update(b_regFm).then(
                res => {
                  this.$modal.msgSuccess("操作成功");
                  this.submitFormProcessing = false
                  this.dialogVisible = false
                  this.getList()

                  // 20230427
                  if (this.currBookedInfo 
                      && (b_examInfo.appointExamTime != this.currBookedInfo.appointExamTime || b_examInfo.appointExamDate != this.currBookedInfo.appointExamDate)) {
                    console.log("修改呼吸内镜的预约时间，重新打印排队小票")
                    // 打印呼吸内镜排队小票
                    let opts = {
                      "examUid": b_examInfo.examUid,
                      "examModality": {
                        "dictLabel": b_examInfo.examModality.dictLabel,
                        "dictValue": b_examInfo.examModality.dictValue
                      },
                      "examItem": {
                        "dictLabel": b_examInfo.examItem.dictLabel,
                        "dictValue": b_examInfo.examItem.dictValue
                      },
                      "patientInfo": {
                        "name": b_examInfo.patientInfo.name,
                      },
                      "callInfo": {
                        "callNo": b_examInfo.callInfo.callNo,
                      },
                    }
                    this.printQueueTicket(opts);
                  }
                }
              ).catch(() => {

                this.submitFormProcessing = false
              });
              
              // let e_regFm = cloneDeep(regFm);
              // e_regFm.id=e_regFm.exam_id
              // // e_regFm.appointExamTime = e_regFm.appointTimeS;
              // // e_regFm.appointExamDate = new Date(e_regFm.appointExamDate);

              // eiapi.update(e_regFm).then().catch(() => this.submitFormProcessing = false);
            };

            if (!firmed) {
              //医嘱登记的检查部位是否有更改
              const fmc = !!this.registFormCopy ? JSON.parse(this.registFormCopy) : null;
              if (!!fmc && fmc.ordId) {
                const uni = fmc.examParts.filter(ep => -1 === examInfo.examParts.findIndex(e => ep.id === e.id)), ordId = examInfo.ordId;
                if (uni.length > 0 && ordId) {
                  //查询已执行医嘱
                  eiapi.loadRemote(RegistWay.registNo, examInfo.patientInfo.registNo, "E", ordId).then(res => {
                    const ords = res.data;
                    if (!!ords && ords.length > 0) {
                      this.$refs.orderRefund.show(ords, uni);
                    } else {
                      cup();
                    }
                  });
                  return;
                }
              }
            }
            //
            cup();
          }else {
            this.submitFormProcessing = true;
            let e_regFm = cloneDeep(regFm);
            // e_regFm.appointExamTime = e_regFm.appointTimeS;
            //  e_regFm.examInfo.appointExamDate = new Date(e_regFm.examInfo.appointExamDate);

            // let examInfo = cloneDeep(e_regFm);
            // e_regFm["examInfo"] = examInfo;

            //e_regFm.appointTime = new Date();
            biapi.save(e_regFm).then(
              res => {

                this.submitFormProcessing = false
                this.dialogVisible = false
                this.getList()

                // 预约后打印呼吸内镜排队小票
                dat.opType="booked";
                this.printQueueTicket(dat);
                this.$refs.orderSheet.getList();
              }
            ).catch(() => this.submitFormProcessing = false);
          }
          //
        });
      });
    },
    //勾选部位
    handleCheckExamParts(items) {
      let ei = this.registForm.examInfo, examParts = items;
      //选中部位绑定的机房
      //let equipRoom;
      //if(examParts.length == 1 && (equipRoom = parts.equipRoom) && equipRoom.dictCode) {
      //  ei.equipRoom = cloneDeep(equipRoom);
      //}
      //
      /*if(undefinedOrNull(parts.examCosts)) {
        this.$modal.msg(`"${parts.partsName}"未设置检查费用，可进入"系统管理>其它设置>部位管理"进行设置`);
      }*/
      ei.examParts = examParts;
      ei.examParts_ids = examParts.map(p => p.id);
    },
    //双击取消部位
    uncheckExamParts(parts, pasive) {
      if (false !== pasive) {
        this.$refs["examPartsTree"].setChecked([parts.id], false);
        return;
      }

      let examParts = this.registForm.examInfo.examParts;
      const idx = examParts.findIndex(p => p.id == parts.id);
      if (-1 != idx) {
        examParts.splice(idx, 1);
      }
    },
    //存入部位
    setExamInfo_examParts() {
      console.warn("未实现 " + setExamInfo_examParts)
    },

    /**
     * 读取检查信息/患者信息
     * @param examInfo 检查
     */
    handleEdit(examInfo) {
      const examInfoId = examInfo ? examInfo.id : null;
      if (!examInfoId) {
        return;
      }

      //
      //var fm = this.registForm;
      //if(examInfoId == fm.id) {
      //  return;
      //}
      //添加一个标志。watch的时候不弹窗控制
      this.IsEditFlag = false

      this.resetRegistForm();

      this.registFormLoading = true;
      eiapi.get(examInfoId).then(res => {
        this.registFormLoading = false;
        const data = res.data;
        if (!data) {
          return;
        }
        //
        let cb = () => {
          data.patientInfo.ageDisable = false;
          this.fillRegistForm(data);
          //暂存副本
          this.registFormCopy = JSON.stringify(this.registForm);
        };
        this.updateAppointTime()
        //
        if (2 === data.status) {
          this.$modal.confirm('该检查已被删除，是否继续编辑？').then(cb);
        } else {
          cb();
        }

      }).catch(() => this.registFormLoading = false);
    },
    //
    resetRegistForm() {
      //Object.assign(this.registForm, emptyRegist());
      this.registForm = emptyRegist();
      //
      this.registFormCopy = null;
    },
    //
    fillRegistForm(dat) {
      if (dat&&dat.examInfo) {
        let fm = this.registForm;
        //
        if (dat.examInfo.examParts) {
          let examParts_ids = [], examParts_names = [];
          dat.examInfo.examParts.forEach(p => {
            examParts_ids.push(p.id);
            examParts_names.push(p.partsName);
          });
          fm.examInfo.examParts_ids = examParts_ids;
          fm.examInfo.examParts_names = examParts_names.join(",");
        }
        dat.examInfo.greenChannelFlagValue = !!dat.examInfo.greenChannelFlag;
        dat.examInfo.examPrerequireValue = 0 === dat.examInfo.examPrerequire;
        dat.examInfo.reservedNoUsedValue = !!dat.examInfo.reservedNoUsed;
        dat.examInfo.examAtPmValue = 1 === dat.examInfo.examAtPm;

        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //Object.assign(this.registForm, fm);
        //console.log(this.registForm);
        this.convToPinyin();
      }
    },
    //新建
    handleNew(mix) {
      //
      const reregist = () => {
        this.resetRegistForm();
        this.$router.push({ name: this.$route.name });
      };
      //let prom = 'ignoreConfirm' === mix? Promise.resolve(true) : this.$modal.confirm("是否确定重新登记？");
      //prom.then(() => {
      if (IGNORECONFIRM === mix) {
        reregist();
      }
      //else if(JSON.stringify(this.registForm) != JSON.stringify(emptyRegist())){
      else if (this.checkModified()) {
        this.$modal.confirm("当前信息未保存，是否新建？").then(reregist)
      } else {
        reregist();
      }
      //this.$router.push({path: "/exammanagement/PatientList"});
      //});
    },
    /**
     * 取消登记/清空登记信息
     */
    handleCancel() {
      if (this.checkModified()) {
        this.$modal.confirm("有信息更新，是否需要保留之前的数据？").then(this.submitForm).catch(() => { this.handleNew(IGNORECONFIRM); });
      } else {
        this.dialogVisible = false
        //this.handleNew(IGNORECONFIRM);
      }

      return;
      let item = this.registForm;
      //
      this.$modal.confirm("是否确定取消？").then(() => {
        if (item.id) {
          this.updateResultStatus(item, StatusDict.cancel).then(res => {
            if (res && 200 === res.code) {
              //重载表单
              this.refreshModified(item);
            }
          });
        } else {
          this.handleNew();
        }
      });
    },
    //
    handleDelete(mix) {
      let fm = this.registForm, item = !!mix && (mix instanceof Event) ? fm : mix;
      if (item.id) {
        /*this.$modal.confirm("是否确定删除？").then(() => {
          return eiapi.del(item.id)
        }).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(item);
          }
        });*/
        this.$refs.patientSheet.verifyForDelete(item);
      }
    },
    undoDelete() {
      const fm = this.registForm;
      if (fm.id) {
        this.handleUndoDelete(fm).then(res => {
          if (res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    undoCancel() {
      const fm = this.registForm;
      if (fm.id) {
        this.handleUndoCancel(fm).then(res => {
          if (res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param posts 过滤用户岗位
     */
    toPickUser(tar, posts) {
      this.$refs["userPicker"].showPicker({ target: tar, posts });
    },
    /**
     * 选择的用户
     * @param tar 触发选择的属性/表单元素
     * @param usr 选择的用户
     */
    pickUser(tar, usr) {
      //console.log(usr);
      const fm = this.registForm;

      if (tar) {
        if ('examDoctors' === tar) {
          fm[`${tar}Code`] = usr.userName;
          fm[`${tar}Name`] = usr.nickName;
        } else {
          fm[tar] = usr;
        }
      }
    },
    selectOrderSheet(val) {
      this.$refs.orderSheet.searchForm.examInfo.appointExamDate = val.appointExamDate
      this.$refs.orderSheet.getList()
    },
    //读取部门树信息
    //buildDeptTree() {
    //  deptTreeselect().then(res => {
    //    this.deptTreeData = res.data;
    //  });
    //},

    //查询登记号是否已登记
    findPatient() {
      const fm = this.registForm;
      if(!fm.examInfo.patientInfo || !fm.examInfo.patientInfo.registNo) {
        return;
      }
      api.find({registNo: fm.examInfo.patientInfo.registNo}).then(res => {
        //console.log(res);
        if(res.total === 1) {
          const row = res.rows[0];

          row.age = getAge(row.birthday).age
          row.ageUnit.dictValue = getAge(parseTime(row.birthday, "{y}-{m}-{d}")).ageUint

          this.dict.type.uis_age_unit.forEach(res => {
              if (res.value == row.ageUnit.dictValue) {
                row.ageUnit.dictLabel = res.label
              }
          })

          const m = "<div>根据该登记号找到患者信息如下，是否使用？</div>" 
            + "<div>" + row.name 
            + "，" + (row.gender? row.gender.dictLabel : "") 
            + "，" + (row.age) + (row.ageUnit? row.ageUnit.dictLabel : "") + "</div>"
          this.$confirm(m, "确认", {
            dangerouslyUseHTMLString: true
          }).then(() => {
            mergeWithDeep(fm.examInfo.patientInfo, row, null, mergeWithNotNull);
            this.convToPinyin();
          });
        } else if(res.total > 1) {
          this.$modal.msgWarning(`找到${res.total}条该登记号的患者信息，请核查登记信息。`);
        }
      });
    },

    //预约时间段是否可以选
    appointTimeUsable(appointNumber,appointExamTime){
      //每个时间段最多可预约11人
      if(appointNumber>11) return true;

      let time = appointExamTime.split("--");
      if(time.length<2) return false;
      var timeArr = time[1].split(":");

      //当天预约时间段已过，不可选
      if(new Date(this.registForm.examInfo.appointExamDate)<=currDatetime()&&currDatetime()>new Date().setHours(timeArr[0],timeArr[1])) return true;
      return false;
    }
  },

  watch: {
    "dict.type.gis_booked_time": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },
    //选择的部位更改
    "registForm.examInfo.examParts": {
      deep: true,
      handler(newVal, oldVal) {
        let examParts_names = [], examCost = 0;
        if (newVal && newVal.length) {
          newVal.forEach(p => {
            //拼接部位名称
            examParts_names.push(p.partsName);
            //费用计算
            let examCost0 = parseFloat(p.examCosts);
            if (!isNaN(examCost0)) {
              examCost = addNum(examCost, examCost0);
            }
          });
        }

        const eifm = this.registForm.examInfo;
        eifm.examParts_names = examParts_names.join(",");
        //费用，医嘱登记的检查不计算
        if (!eifm.ordId) {
          eifm.examCost = examCost;
        }

        if (eifm.examParts_names == '') {
          eifm.examParts_names = null
        }
        if (eifm.examCost == '0') {
          eifm.examCost = null//用于新建时， this.registForm, emptyRegist() 对比
        }
      }
    },

    /**
     * 勾选/取消勾选"绿色通道"
     */
    "registForm.examInfo.greenChannelFlagValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examInfo.greenChannelFlag = nv ? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"诊前检查"
     */
    "registForm.examInfo.examPrerequireValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examInfo.examPrerequire = nv ? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"启用保留号"
     */
    "registForm.examInfo.reservedNoUsedValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examInfo.reservedNoUsed = nv ? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"下午检查"
     */
    "registForm.examInfo.examAtPmValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examInfo.examAtPm = nv ? 1 : null;
      }
    },

    /**
     * 切换登记方式
     */
    "searchForm.registWay": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examInfo.registWay = nv;
        //this.$modal.confirm("是否清空'基本信息'和'检查信息'所有数据？").then(this.resetRegistForm);
        this.resetRegistForm();
        if (nv === RegistWay.manual) {
          this.registForm.examInfo.patientInfo.ageDisable = false
        }
      }
    },

    /**
     * 检查类型字典取值后执行
     */
    "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        let items = [];
        if (nv && nv.length) {
          //层级
          nv.forEach(e => {
            items.push(e);
            let d = e.raw;
            if (d.children && d.children.length) {
              d.children.forEach(c => {
                items.push({ value: c.dictCode, label: ("--" + c.dictLabel), raw: c });
              });
            }
          });
        }
        //
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, items);
      }
    },

    /**
     * 就诊类型字典取值后执行
     */
    "dict.type.uis_inp_type": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }
  },

  created() {

    //this.delayGetList();
    this.findEquipRooms();

  },
  activated() {
    this.delayGetList();
    //this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  },

  computed: {
    /**
         * 勾选/取消勾选绿色通道
         */
    regNoReadonly() {
      const sfm = this.searchForm, rfm = this.registForm.examInfo;
      return RegistWay.manual !== sfm.registWay || !!rfm.id;
    },
    //是否可删除
    deleteEnabled() {
      const fm = this.registForm.examInfo;
      return fm.id && 2 !== fm.status;
    },
    //是否可撤销删除
    undoDeleteEnabled() {
      const fm = this.registForm.examInfo;
      return fm.id && 2 === fm.status;
    },
    //是否可撤销取消
    cancelEnabled() {
      const fm = this.registForm.examInfo;
      return fm.id && (!fm.resultStatus || !fm.resultStatus.dictValue || StatusDict.cancel !== fm.resultStatus.dictValue);
    },
    //是否可撤销取消
    undoCancelEnabled() {
      const fm = this.registForm.examInfo;
      return fm.id && !!fm.resultStatus && StatusDict.cancel === fm.resultStatus.dictValue;
    },
    //是否可编辑
    editEnabled() {
      const fm = this.registForm.examInfo, resultStatus = fm.resultStatus, resultStatusCode = !!resultStatus ? resultStatus.dictValue : null;
      return !resultStatusCode || /^[012]$/.test(resultStatusCode);
    },
    //是否禁止编辑年龄
    ageDisabled() {
      const sfm = this.searchForm, rfm = this.registForm.examInfo;
      //非人工登记、非编辑记录时禁止编辑
      return RegistWay.manual !== sfm.registWay && !rfm.id;
    }
  }
};
