import BaseDialogModel from "@/assets/scripts/gis/BaseDialogModel.js";
//接口
import * as biapi from "@/assets/scripts/gis/exammanagement/bookedinfo/api.js";

import SurgerySchedulePrint from "@/views/gis/exammanagement/bookedinfo/comp/SurgerySchedulePrint";

//检查状态
import { StatusDict } from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

export default {
    name: "SurgerySchedule",
    extends: BaseDialogModel,
    components: {SurgerySchedulePrint},
    data() {
      return {
        iteKey:1,
        updateArr:[],
        //房间名
        roomArr:[],
        //时间段
        timeRate:[],
        tableData:[],
        timeRateTblIndexmap:new Map(),
        searchForm: {
          appointExamDate: this.nowDate()
        },
        //拖拽的表格
        activeItem: null,
      };
    },

    created() {
      this.roomArr.push(" ");
    },

    methods: {
      nowDate() {
        let now = new Date();
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let formatMonth = month < 10 ? '0' + month : month;
        let formatDate = date < 10 ? '0' + date : date;
        let currentTime = year + '-' + formatMonth + '-' + formatDate;
        return currentTime
      },

      /**
     * 搜索
     */
      getList: function (opts) {
        let vm = this;
        //查询参数
        let sfm = this.searchForm;
        if(undefined==sfm.appointExamDate){
          this.$modal.msgWarning("请选择排班日期");
          return;
        }
        let params={examInfo:{}};
        params.examInfo.appointExamDate = sfm.appointExamDate;
        
        params.status = 0;

      
        this.loading = true;
       
        //
        biapi.find(params).then(res => {
          vm.loading = false;
          vm.emptyTableData();
          res.rows.forEach(e=>{
            var index = vm.timeRateTblIndexmap.get(e.examInfo.appointExamTime);
            if(undefined!=index){
                vm.tableData[index].array.push({code:e.examInfo.id,name:e.examInfo.patientInfo.name,resultStatus:e.examInfo.resultStatus.dictValue});
                if(vm.tableData[index].array.length>vm.roomArr.length-1){
                  vm.roomArr=[];
                  vm.tableData[index].array.forEach(e=>{
                      vm.roomArr.push(" ");
                  });
                  vm.roomArr.push(" ");
                }
            }
          });
          
        })
        .catch(this.loading = false);
      },

      emptyTableData(){
        let vm = this;
        vm.tableData=[];

        vm.timeRate.forEach((item, index)=>{
          vm.timeRateTblIndexmap.set(item,index);
          var ar = [];
          vm.tableData.push({date:item,array:ar});
        })
      },

      search(){
        this.getList();
      },
      //获取时间段
      getTime(row){
        let vm = this;
        return vm.timeRate[row];
      },
      //
      getTableData(row,col){
        let vm = this;
        return vm.tableData[row].array[col];
      },
      //
      setTableDate(row,col,value){
        this.tableData[row].array[col] = value;
      },

      getName(row,col){
        let vm = this;
        if(undefined==vm.tableData[row].array[col]) return "";
        return vm.tableData[row].array[col].name;
      },

      draggable(row,col){
        let vm = this;
        //已审核患者，不能修改排班
        if(undefined==vm.tableData[row].array[col]||vm.tableData[row].array[col].resultStatus>=StatusDict.audit) {
          return false;
        }
        return true;
      },
      //显示窗口
      show(appointExamDate,timeRate,roomNum) {
        let vm = this;
        //暂时不使用手术室表头
        // vm.roomArr = [];
        // roomNum.forEach(e=>{
        //   vm.roomArr.push(e.label);
        // });
        
        vm.roomArr = [];
        vm.updateArr = [];
        vm.timeRate = timeRate;
        vm.emptyTableData();
        vm.searchForm.appointExamDate = appointExamDate;
        vm.open();
        vm.getList();
      },

      submit(){
        let vm = this;
        let params=[];
        var idAr = [];
        var dateAr = [];

        vm.updateArr.forEach(e=>{
          let examInfo = {id:e.code,appointExamTime:e.date};
          params.push({examInfo:examInfo})
        });
        biapi.updateAppointTime(params).then(res => {
          this.$modal.msgSuccess("修改成功");
          vm.updateArr = [];
        })
        this.close();
      },
      //开始拖拽
      drag(e, row, col) {
        let vm = this;
        vm.activeItem = null;

        //是否可拖拽
        if(!vm.draggable(row,col)) {
          this.$modal.msgWarning("已审核患者不能修改");
          return;
        }
          

        //获取选中表格
        let data = vm.getTableData(row,col);
        if(undefined==data) {
          return ;
        }
        //拖拽表格
        vm.activeItem = { row: row, col: col, name: vm.getTableData(row,col).name,code:vm.getTableData(row,col).code };
      },
      allowDrop(e) {
        e.preventDefault();
      },
      //释放拖拽
      drop(e, row, col) {
        let vm = this;
        if(this.searchForm.appointExamDate<new Date(new Date().setHours(0, 0, 0, 0))){
          this.$modal.msgWarning("历史排班不能修改");
          return;
        }

        if(undefined==vm.activeItem) return;
      
        e.preventDefault();
        let data = vm.getTableData(row,col);
        if(undefined!=data)  return;

        //更新旧表格为空
        vm.setTableDate(vm.activeItem.row,vm.activeItem.col,null);
        //设定新表格
        vm.setTableDate(row,col,{name:vm.activeItem.name,code:vm.activeItem.code});
        //
        vm.iteKey = Math.random();
        //保存更新数据
        vm.updateArr.push({date:vm.getTime(row),name:vm.activeItem.name,code:vm.activeItem.code});
        
        //当拖拽到最后一列，且列数不大于11时，添加一个空列
        if(col>=vm.roomArr.length-1&&vm.roomArr.length<=11){
          vm.roomArr.push(" ");
        }
      },

      print(){
        let vm = this;
        if(vm.updateArr.length>0){
          this.$modal.msgSuccess("请先保存后再打印！");
          return;
        }

        var tmpTableData = [];
        vm.tableData.forEach(e=>{
          var value = {};
          value["date"] = e.date;
          for(var k=0;k<vm.roomArr.length;k++){
            if(k<e.array.length){
              value[""+k]=e.array[k].name;
            }else{
              value[""+k]="";
            }
          }
          tmpTableData.push(value);
        });
        var appointExamDate = new Date(vm.searchForm.appointExamDate);
        let year = appointExamDate.getFullYear();
        let month = appointExamDate.getMonth() + 1;
        let date = appointExamDate.getDate();
        let formatMonth = month < 10 ? '0' + month : month;
        let formatDate = date < 10 ? '0' + date : date;
        appointExamDate = year + '-' + formatMonth + '-' + formatDate;

        this.$refs.SurgerySchedulePrint.show(appointExamDate,vm.roomArr,tmpTableData);
      },
    },
  };