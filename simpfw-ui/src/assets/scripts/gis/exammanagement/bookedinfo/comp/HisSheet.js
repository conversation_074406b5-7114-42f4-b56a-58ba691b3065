
import { cloneDeep, mergeWith } from "lodash";

import { mergeWithNotNull, undefinedOrNull } from "@/utils/common";
//接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api.js";
//
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';

import { DataCtrlDict } from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

//右键菜单
import Contextmenu from '@/components/Contextmenu';


const RegistWay = {
  manual: 'W',
  registNo: 'I',
  inpNo: 'Y'
}, RegistWayDef = RegistWay.registNo

export default {

  extends: BaseGridModel,
  mixins: [ExamDataScope],
  dicts: ["uis_exam_result_status", 'uis_exam_item', 'uis_regist_way'],

  components: { Contextmenu },

  props: {
    //定时刷新秒数
    refresh: { type: Number },
    //表格行右键菜单
    actions: { type: Array, default: () => [] },
    //固定查询条件
    filter: { type: Object },//e.g. {status:0,datesCreated:1}
    //限制
    restrict: { type: Object }
  },

  data() {
    return {
      searchForm: {
        combo_props: [{ value: "patientInfo.name", label: "姓名" }
          , { value: "examNo", label: "检查号" }
          , { value: 'patientInfo.registNo', label: '登记号' }
          , { value: 'encounter.encounterMRN', label: '住院号' }
          , { value: 'callInfo.callNo', label: '排队号' }]

        , propName: "patientInfo.registNo"
        , propValue: null
        , resultStatusValues: null
        , examItemCodes: null
        , pageSize: 17
        , registWay: RegistWayDef
        , registCode: null
      },
      loadRemoteProcessing: false,
      currentRow:null,

    }
  },

  methods: {

    /**
         * 调用接口读取登记信息
         */
    loadRemote() {
      const sfm = this.searchForm, registWay = sfm.registWay;
      let registCode = sfm.registCode;
      if (!registWay || !registCode) {
        //this.$modal.alert("请按登记方式输入.");
        return;
      }
      //住院号外其它方式增加补0到10位
      if (registCode.length < 10&&registWay!=RegistWay.inpNo) {
        registCode = registCode.padStart(10, '0');
        this.searchForm.registCode = registCode
      }

      //住院号增加补0到8位
      if (registCode.length < 8&&registWay==RegistWay.inpNo) {
        registCode = registCode.padStart(8, '0');
        this.searchForm.registCode = registCode
      }
      //this.resetRegistForm();
      this.IsEditFlag = true
      // this.loadRemoteProcessing = true;
      
      eiapi.loadRemote(registWay, registCode).then(res => {
        //s this.loadRemoteProcessing = false;

        this.grid = res;
        if (!this.grid.data || !this.grid.data.length) {
          this.$modal.msgWarning("没有相关数据.");
          return;
        }
        this.handleCurrentChange(this.grid.data[0]);
        //触发一些更新，如数据字典，科室信息，检查部位
        const modified = res.modified;
        if (modified) {
          modified.forEach(mod => {
            if ("dept" === mod) {
              //this.buildDeptTree();
            } else if (0 === mod.indexOf("dict::")) {
              this.dict.reloadDict(mod.substring(6));
            } else if ("examParts" === mod) {
              //this.$refs["examPartsTree"].buildTree();
            }
          });
        }


      }).catch(() => this.loadRemoteProcessing = false);
    },


    //全选输入框文本
    handleFocusRegistCode($evt) {
      const el = $evt.target || $evt.srcElement;
      el.select();
    },



    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      this.currentRow=row
      //this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      //console.log(row)
      if(undefinedOrNull(row)){
        return
      }
      let exam = cloneDeep(row);
      exam["examInfo"] = cloneDeep(row);
      this.triggerBind("dblClickRow", exam);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if (1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },

    focusPropValue() {
      try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age ? (pat.age + (pat.ageUnit ? pat.ageUnit.dictLabel : '')) : (pat ? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if (!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom ? row.equipRoom.roomName : null;
    },


    //登记号补0
    checkValue() {
      let fm = this.searchForm
      if (fm.propName == 'patientInfo.registNo') {

        if (!!fm.propValue && fm.propValue.length > 0 && fm.propValue.length < 10) {
          fm.propValue = fm.propValue.padStart(10, '0');
        }
      }
    },
    //
    colStyle({ row, column, rowIndex, columnIndex }) {
      if ('resultStatus.dictLabel' !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.resultStatus, stav = sta ? sta.dictValue : null;
      if (!undefinedOrNull(stav)) {
        clz.push("table-cell-result-status-" + stav);
      }

      return clz.join(" ");
    }
  },

  watch: {
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }

  },

  activated() {

  },

  deactivated() {

  },

  beforeDestroy() {

  },

  computed: {
    combo_resultStatus() {
      const dict = this.dict.type.uis_exam_result_status, rst = this.restrict;
      if (!rst || !rst.resultStatusValues || !rst.resultStatusValues.length) {
        return dict;
      }
      return dict.filter(d => -1 !== rst.resultStatusValues.findIndex(v => v === d.value));
    }
  }
};
