import BaseDialogModel from "@/assets/scripts/gis/BaseDialogModel.js";


export default {
    name: "SurgerySchedulePrint",
    extends: BaseDialogModel,
    data() {
      return {
        appointExamDate:null,
        tableHead:[],
        tableData:[],
      };
    },

    methods: {

      //显示窗口
      show(appointExamDate,roomNum,tmpTableData) {
        let vm = this;
        vm.appointExamDate = appointExamDate;
        vm.tableHead = roomNum;
        vm.tableData = tmpTableData;
        vm.open();
        
        setTimeout(function(){window.print();},50);
        setTimeout(function(){vm.close();},50);
        
      },
    },
  };