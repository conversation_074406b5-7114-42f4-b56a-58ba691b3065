
import { cloneDeep, mergeWith } from "lodash";

import { mergeWithNotNull, undefinedOrNull } from "@/utils/common";
//接口
import * as biapi from "@/assets/scripts/gis/exammanagement/bookedinfo/api.js";
//

//检查信息
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
//编辑
import { EditModel, UndoModel, TransModel } from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';

//
import { DataCtrlDict } from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import SurgerySchedule from "@/views/gis/exammanagement/bookedinfo/comp/SurgerySchedule";

//排队小票
import {QueueTicketPrint} from "@/assets/scripts/gis/exammanagement/queue/comp/Ticket";

//报告状态
import {StatusDict, matchAny as matchAnyResultStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//定时刷新列表
let timer_getList;

import { SearchOptionsDef, SearchOptions } from "@/assets/scripts/pacs/report/mixins";

let activeStatusE = {
  active: 0,   //激活
  notActive: 3, //未激活
};

export default {

  extends: BaseGridModel,
  mixins: [EditModel, UndoModel, TransModel, SearchOptions, ExamDataScope, QueueTicketPrint],
  dicts: ["uis_exam_result_status", 'uis_exam_item','gis_booked_time','gis_operating_room'],

  components: { Contextmenu,SurgerySchedule  },

  props: {
    //定时刷新秒数
    refresh: { type: Number },
    //表格行右键菜单
    actions: { type: Array, default: () => [] },
    //固定查询条件
    filter: { type: Object },//e.g. {status:0,datesCreated:1}
    //限制
    restrict: { type: Object }
  },

  data() {
    return {
      searchForm: {
        combo_props: [{ value: "examInfo.patientInfo.name", label: "姓名" }
          , { value: "examInfo.examNo", label: "检查号" }
          , { value: 'examInfo.patientInfo.registNo', label: '登记号' }
          , { value: 'examInfo.inpNo', label: '住院号' }
          , { value: 'examInfo.callInfo.callNo', label: '排队号' }]

        , propName: "examInfo.patientInfo.registNo"
        , propValue: null
        , resultStatusValues: null
        , pageSize: 17
        ,examInfo:{
          examItem: {dictValue: 'qzj'}
          , appointExamDate: this.nowDate()
        }
        
      },
      currentRow: null,

    }
  },

  methods: {


    /**
     * 搜索
     */
    getList: function (opts) {
      clearTimeout(timer_getList);
      //
      this.checkValue()
      this.handleCurrentChange(null);
      //查询参数
      let sfm = this.searchForm, params = { pageSize: sfm.pageSize, pageNum: sfm.pageNum };
      //父组件指定参数
      if (this.filter) {
        mergeWith(params, this.filter, true, mergeWithNotNull);
      }
      //偏好查询设置, 覆盖父组件
      //let searchOpts = this.$refs.searchOpt.readOpts();
      //mergeWith(params, searchOpts);

      params.resultStatusValues = cloneDeep(sfm.resultStatusValues);
      //检查进度限制
      const rst = this.restrict;
      if ((!params.resultStatusValues || params.resultStatusValues.length === 0)
        && rst && rst.resultStatusValues) {
        params.resultStatusValues = rst.resultStatusValues
      }
      //已删除状态
      let pos;
      if (!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
        params.resultStatusValues.splice(pos, 1);
        //params.resultStatusAsStatus = "2";
        params.status = 2;
      } else {
        params.status = 0;
      }
      //检查项目
      params.examInfo = {};
      params.examInfo.examItem = {};
      params.examInfo.examItem.dictValue = sfm.examInfo.examItem.dictValue;
      params.examInfo.appointExamDate = sfm.examInfo.appointExamDate

      //根据下拉
      let propName = sfm.propName, propValue = sfm.propValue;
      if (!!propName && !!propValue) {
        let propsName = propName.split(/\./), prop = params;

        for (let i = 0, len = propsName.length; i < len; i++) {
          let pnam = propsName[i];
          if ((i + 1) < len) {
            prop[pnam] = {};
            prop = prop[pnam];
            continue;
          }
          prop[pnam] = propValue;
        }
        //查询不限定时间
        //params.datesCreated = null;
        // delete params.datesCreated;
      }


      //点击按钮触发
      if (!timer_getList || opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }
      //
      //params.status=0;
      //params.status = null;
      //
      biapi.find(params).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;

        this.delayGetList();
      }).catch(this.delayGetList);
    },
    //轮询
    delayGetList() {
      if (this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },
    nowDate() {
      let now = new Date();
      let year = now.getFullYear();
      let month = now.getMonth() + 1;
      let date = now.getDate();
      let formatMonth = month < 10 ? '0' + month : month;
      let formatDate = date < 10 ? '0' + date : date;
      let currentTime = year + '-' + formatMonth + '-' + formatDate;
      return currentTime
    },
    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      if(row){
        this.currentRow = row
        //this.triggerBind("selectRow", row);
      }
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if (1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return biapi.del(row.id);
    },
    //删除
    undoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if (res && 200 === res.code) { this.getList(); }
      });
    },
    //延迟检查
    handlePostpone(row) {
      let props = { examAtPm: 1 };
      this.handleTrans(row, props).then(res => this.getList());
    },
    //
    focusPropValue() {
      try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age ? (pat.age + (pat.ageUnit ? pat.ageUnit.dictLabel : '')) : (pat ? pat.age : '');
    },
    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if (!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom ? row.equipRoom.roomName : null;
    },
    colFmt_active(row, col, val, idx) {
      return 3 === val || "3" === val ? "未激活" : "已激活";
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //打开查询设置
    prepareSearchOpt() {
      this.$refs.searchOpt.open();
    },
    //登记号补0
    checkValue() {
      let fm = this.searchForm
      if (fm.propName == 'patientInfo.registNo') {

        if (!!fm.propValue && fm.propValue.length > 0 && fm.propValue.length < 10) {
          fm.propValue = fm.propValue.padStart(10, '0');
        }
      }
    },
    activeStatus() {
      if (undefinedOrNull(this.currentRow)) {
        this.$modal.msgWarning("请选择");
        return
      }

      const examInfo = this.currentRow.examInfo;

      let appointExamDate = new Date(examInfo.appointExamDate);
      if(appointExamDate<new Date(new Date().setHours(0, 0, 0, 0))
      ||appointExamDate>new Date(new Date().setHours(23, 59, 59, 59))){
        this.$modal.msgWarning("只能激活预约日期是今天的患者");
        return;
      }

      if ("3" != examInfo.status || 3 != examInfo.status) {
        this.$modal.msgWarning("已经激活过了");
        return
      }
      let params = {examInfo:{}};
      params.examInfo.id = examInfo.id;
      params.examInfo.status = activeStatusE.active;
      params.examInfo.callInfo = examInfo.callInfo;
      biapi.updateActiveStatus(params).then(() => {
        this.loading = true;
        this.grid.data = [];
        this.currentRow = null;
        this.getList();
        this.$modal.msgSuccess("激活成功");

        // 20230511 打印呼吸内镜排队小票
        // console.log("row: ", JSON.stringify(row));
        console.log("激活打印排队小票")
        let opts = {
          "opType": "active",
          "examUid": row.examUid,
          "examModality": {
            "dictLabel": row.examModality.dictLabel,
            "dictValue": row.examModality.dictValue
          },
          "examItem": {
            "dictLabel": row.examItem.dictLabel,
            "dictValue": row.examItem.dictValue
          },
          "patientInfo": {
            "name": row.patientInfo.name,
          },
          "callInfo": {
            "callNo": row.callInfo.callNo,
          },
        }
        this.printQueueTicket(opts);
      });
    },

    undoActiveStatus() {
      if (undefinedOrNull(this.currentRow)) {
        this.$modal.msgWarning("请选择");
        return
      }

      const examInfo = this.currentRow.examInfo;

      if ("0" != examInfo.status || 0 != examInfo.status) {
        this.$modal.msgWarning("该患者未激活");
        return
      }

      if(matchAnyResultStatus(examInfo,StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive)) {
        //const resultStatus = report.resultStatus;
        this.$modal.msgWarning("已审核患者无法取消激活!");
        return;
      }

      let params = {examInfo:{}};
      params.examInfo.id = examInfo.id;
      params.examInfo.status = activeStatusE.notActive;

      biapi.updateActiveStatus(params).then(() => {
        this.loading = true;
        this.grid.data = [];
        this.currentRow = null;
        this.getList();
        this.$modal.msgSuccess("取消激活成功");
      });
    },
    //
    colStyle({ row, column, rowIndex, columnIndex }) {
      if ('examInfo.status' !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.examInfo.status
      if (!undefinedOrNull(sta)) {
        clz.push("table-cell-result-status-" + sta);
      }

      return clz.join(" ");
    },
    oderOperate(opt, row) {
      switch (opt) {
        case "add":
          this.triggerBind("registBooked")
          break;
        case 'edit':
          if(matchAnyResultStatus(row.examInfo,StatusDict.audit, StatusDict.reaudit, StatusDict.print, StatusDict.archive)) {
            //const resultStatus = report.resultStatus;
            this.$modal.msgWarning("已审核患者无法取消激活!");
            break;
          }
          this.triggerBind("dblClickRow", row);
          break;
        case 'delete':
          this.$modal.confirm("是否确定删除？").then(() => { 
            biapi.del(row.id).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            })
          });
          break;
        default: break;
      }
    },

    //手术排班
    surgeryQueueSchedule(){
      //时间段
      var timeRate=[];
      this.dict.type.gis_booked_time.forEach(e=>timeRate.push(e.label));

      this.$refs.SurgerySchedule.show(this.searchForm.examInfo.appointExamDate,timeRate,this.dict.type.gis_operating_room);
    }
  },

  watch: {
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },
    
    // "dict.type.gis_booked_status": {
    //   deep: true,
    //   handler(nv, ov) {
    //     this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
    //   }
    // }
  },

  activated() {
    this.delayGetList();
    this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  },

  computed: {
    combo_resultStatus() {
      const dict = this.dict.type.uis_exam_result_status, rst = this.restrict;
      if (!rst || !rst.resultStatusValues || !rst.resultStatusValues.length) {
        return dict;
      }
      return dict.filter(d => -1 !== rst.resultStatusValues.findIndex(v => v === d.value));
    }

  }
};
