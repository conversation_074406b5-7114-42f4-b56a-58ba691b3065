import "@/assets/styles/uis/common.css";
//axios & ajax
import request from "@/utils/request";
//WebSocket
import BaseWebSocketModel from '@/assets/scripts/uis/BaseWebSocketModel';
//tools
import {currDatetime, parseTime, BlankImage} from "@/utils/common";
//
const ctx = "/exammanagement/callingBoard", examItemCtx = "/exammanagement/examItem/find";
//排队叫号接口信息
let apiInfo;
//
let timer_buildHeader, timer_readCalling;
//滚动排队信息
let timer_rollingPane;
//周每天名称
const weeknames = ["日", "一", "二", "三", "四", "五", "六"];

const model = {
  
  mixins: [BaseWebSocketModel],

  data() {
    return {
      deptFullTitle: "广西医科大学第二附属医院超声科",

      header: {
        logo: BlankImage,
        timestamp: 0,
        date: null,
        weekname: null,
        time: null,
      },

      queue: {
        calling: [],//呼叫中/检查中队列
        waiting: [],//等待队列
        past: [],//过号队列

        underway: []//呼叫中队列，用来放大/高亮
      },

      underwayDialogStyle: {
        left: "20px",
        top: "20px"
      },

      examItems: [],

      rollingPanels: [],
    }
  },

  methods: {
    //读取系统时间，初次读取排队信息
    async buildHeader() {
      clearTimeout(timer_buildHeader);
      //
      const interval = 1000;
      //取服务器时间
      let millis;
      if(!this.header.timestamp) {
        //取服务器时间
        try {
          await request.get(ctx + "/info").then(res => {
            //系统时间
            millis = res.timestamp;
            //排队叫号接口信息
            apiInfo = res.service;
            //读取排队叫号
            this.readCalling();
            //读取正在叫号信息
            this.readUnderway();
          });

        } catch (err) { 
          console.error(err);
          millis = currDatetime().getTime();
        }
        this.header.timestamp = millis;
      } else {
        this.header.timestamp += interval;
      }

      const now = new Date(this.header.timestamp);;
      this.header.date = parseTime(now, "{y}年{m}月{d}日");
      this.header.time = parseTime(now, "{h}:{i}:{s}");
      try {
        this.header.weekname = `星期${weeknames[now.getDay()]}`;
      } catch (err) { console.error(err); }

      timer_buildHeader = setTimeout(this.buildHeader, interval);
    },
    //读取检查项目
    findExamItem() {
      request.get(examItemCtx).then(res => {
        this.examItems = res.data;

        this.$nextTick(this.prepareRollingPane);
      });
    },
    //读取正在叫号，检查中，等待中，过号数据
    readCalling() {return;
      //Promise.resolve({"code":"0000","msg":"succss","data":{"missedList":[{"callNo":"A002","patientName":"测试体检DR","callRoomName":"体检男宾","inpTypeName":""},{"callNo":"A003","patientName":"测试体检DR","callRoomName":"体检男宾","inpTypeName":""}],"waitingList":[{"callNo":"B003","patientName":"测试GRDR","callRoomName":"","inpTypeName":""}],"queueingList":[{"examItemName":"心脏彩超","list":[{"callNo":"A004","patientName":"测试体检DR","callRoomName":"","inpTypeName":""}]},{"examItemName":"血管彩超","list":[{"callNo":"B004","patientName":"测试GRDR","callRoomName":"","inpTypeName":""}]}]}})
      let ver = apiInfo.ver && apiInfo.ver.rest? ('/'+apiInfo.ver.rest) : '';
      request.get(`http://${apiInfo.server}${ver}/queue/getAllQueues`, {timeout: (30 * 1000)})
      .then(res => {
        this.queue.calling = res.data.waitingList || [];
        this.queue.waiting = res.data.queueingList || [];
        this.queue.past = res.data.missedList || [];
        this.queue.underway = res.data.underway || [];

        this.timerReadCalling();

        this.$nextTick(this.udateUnderwayDialogStyle);
      }).catch(err => this.timerReadCalling);
    },
    //循环
    timerReadCalling() {
      clearTimeout(timer_readCalling);

      timer_readCalling = setTimeout(this.readCalling, 2 * 1000);
    },
    //正在叫号
    readUnderway() {
      let ver = apiInfo.ver && apiInfo.ver.ws? ('/'+apiInfo.ver.ws) : '';
      this.socketUrl = `ws://${apiInfo.server}${ver}/ws/queue/calling`;
      this.openSocket();
    },
    handleSocketReceived(evt) {

    },
    //是否正在叫号
    isUnderway(item) {
      return this.queue.underway && this.queue.underway.findIndex(r => r.id === item.examInfo.id) !== -1;
    },
    //突出显示正在叫号
    udateUnderwayDialogStyle() {
      let css = this.underwayDialogStyle, exam = this.underwayExam;
      if(exam) {
        const bd = document.body, mxWidth = bd.clientWidth, mxHeight = bd.clientHeight;
        const dia = this.$refs.underwayDialog;
        let left = (mxWidth - dia.clientWidth) / 2, top = (mxHeight - dia.clientHeight) / 2;
        css.left = left + "px";
        css.top = top + "px";
      }
    },
    //
    prepareRollingPane() {
      clearTimeout(timer_rollingPane);
      //
      let panels = [];
      document.querySelectorAll(".qc-queue-grid-parag-body").forEach(e => panels.push(e));
      document.querySelectorAll(".rolling-pane .el-card__body").forEach(e => panels.push(e));
      this.rollingPanels = panels;

      this.rollPane();
    },
    //滚动
    rollPane() {
      clearTimeout(timer_rollingPane);
      //
      this.rollingPanels.forEach((e, i) => {
        //if(i != 1) { return true; }
        //console.log("%d %d", e.clientHeight, e.scrollHeight)
        let scrollTop = e.scrollTop;
        e.scrollTop = scrollTop + 1;
        //滚动位置无变化，重新滚动
        if(e.scrollTop === scrollTop) {
          e.scrollTop = 0;
        }
      });
      timer_rollingPane = setTimeout(this.rollPane, 90);
    },
    //
    clearAll() {
      clearTimeout(timer_readCalling);
      clearTimeout(timer_buildHeader);
      this.closeSocket()
    }
  },

  created() {
    this.buildHeader();

    this.findExamItem();

    //this.readCalling();
  },

  /*mounted() {
    this.prepareRollingPane();
  },*/

  deactivated() {
    this.clearAll();
  },

  beforeDestroy() {
    this.clearAll();
  },

  computed: {
    underwayExam() {
      let items = this.queue.underway;
      return items && items.length? items[0] : null;
    }
  }

};
export default model;
