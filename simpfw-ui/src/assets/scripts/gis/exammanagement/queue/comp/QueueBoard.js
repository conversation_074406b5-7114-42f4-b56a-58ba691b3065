import "@/assets/styles/pacs/common.css";
//axios & ajax
import request from "@/utils/request";
//WebSocket
import BaseWebSocketModel from '@/assets/scripts/pacs/BaseWebSocketModel';
//tools
import {currDatetime, parseTime, BlankImage} from "@/utils/common";
//
const ctx = "/exammanagement/callingBoard", examItemCtx = "/exammanagement/examItem/find";
//排队叫号接口信息
let apiInfo;
//
let timer_buildHeader, timer_readCalling;
//滚动排队信息
let timer_rollingPane;
//周每天名称
const weeknames = ["日", "一", "二", "三", "四", "五", "六"];
//假定一次呼叫完成用时，单位秒，关闭突出显示呼叫信息
const callingTimeOnDuration = 5.4;
//延迟关闭突出呼叫信息
let timer_hideUnderway;

const model = {
  
  mixins: [BaseWebSocketModel],

  data() {
    return {
      deptFullTitle: "广西医科大学第二附属医院呼吸内镜中心",

      header: {
        logo: BlankImage,
        timestamp: 0,
        date: null,
        weekname: null,
        time: null,
      },

      queue: {
        calling: [],//呼叫中/检查中队列
        waiting: [],//等待队列
        past: [],//过号队列

        underway: []//呼叫中队列，用来放大/高亮
      },
      //突出呼叫信息
      underwayDialogStyle: {
        left: "20px",
        top: "20px"
      },
      //最近呼叫信息
      underwayExamLast: null,
      //检查项目字典列表
      examItems: [],
      //滚动
      rollingPanels: [],
    }
  },

  methods: {
    //读取系统时间，初次读取排队信息
    async buildHeader() {
      clearTimeout(timer_buildHeader);
      //
      const interval = 1000;
      //取服务器时间
      let millis;
      if(!this.header.timestamp) {
        //取服务器时间
        try {
          await request.get(ctx + "/info").then(res => {
            //系统时间
            millis = res.timestamp;
            //排队叫号接口信息
            apiInfo = res.service;
            //读取排队叫号
            this.readCalling();
          });

        } catch (err) { 
          console.error(err);
          millis = currDatetime().getTime();
        }
        this.header.timestamp = millis;
      } else {
        this.header.timestamp += interval;
      }

      const now = new Date(this.header.timestamp);;
      this.header.date = parseTime(now, "{y}年{m}月{d}日");
      this.header.time = parseTime(now, "{h}:{i}:{s}");
      try {
        this.header.weekname = `星期${weeknames[now.getDay()]}`;
      } catch (err) { console.error(err); }

      timer_buildHeader = setTimeout(this.buildHeader, interval);
    },
    //读取检查项目
    findExamItem() {
      request.get(examItemCtx).then(res => {
        this.examItems = res.data;

        this.$nextTick(this.prepareRollingPane);
      });
    },
    //读取正在叫号，检查中，等待中，过号数据
    readCalling() {
      clearTimeout(timer_readCalling);

      let ser = apiInfo.server.replace("http://", "");
      let ver = apiInfo.ver && apiInfo.ver.ws? ('/' + apiInfo.ver.ws) : '';
      this.socketUrl = `ws://${ser}${ver}/ws/queue/calling`;
      // console.log("gis-: ", this.socketUrl)
      this.openSocket();
    },
    //处理WebSocket错误
    handleSocketError(evt) {
    },
    //处理关闭事件
    handleSocketClosed(evt) {
      clearTimeout(timer_readCalling);
      
      timer_readCalling = setTimeout(this.readCalling, this.secondsReopenSocket * 1000);
    },
    //处理排队数据
    handleSocketReceived(evt) {
      // console.log("evt：", evt.data);
      let dat = evt.data;
      if(!dat) { return; }
      dat = JSON.parse(dat);

      this.queue.calling = dat.waitingList || [];
      this.queue.waiting = dat.queueingList || [];
      this.queue.past = dat.missedList || [];
      /*this.queue.underway = [];
      if (dat.calling) {
        if (JSON.stringify(dat.calling) == "{}") {
          this.queue.underway = [];
        } else {
          this.queue.underway = [dat.calling];
        }
      }*/
      //calling返回当前呼叫，避免当呼叫到来时，上一个呼叫未结束
      if(dat.calling && JSON.stringify(dat.calling) !== "{}") {
        this.queue.underway.push(dat.calling);
      }
      // 检查项目
      this.examItems = dat.examItemList;
      //
      this.timerHideUnderway();

      this.$nextTick(this.udateUnderwayDialogStyle);
      this.$nextTick(this.prepareRollingPane);
    },
    //是否正在叫号
    isUnderway(item) {
      return this.queue.underway && this.queue.underway.findIndex(r => r.callNo === item.callNo) !== -1;
    },
    //突出显示正在叫号
    udateUnderwayDialogStyle() {
      let css = this.underwayDialogStyle, exam = this.underwayExam;
      if(exam) {
        const bd = document.body, mxWidth = bd.clientWidth, mxHeight = bd.clientHeight;
        const dia = this.$refs.underwayDialog;
        let left = (mxWidth - dia.clientWidth) / 2, top = Math.max(16, ((mxHeight - dia.clientHeight) / 2 - 100));
        css.left = left + "px";
        css.top = top + "px";
      }
    },
    //定时关闭突显当前呼叫
    timerHideUnderway() {
      //当前有呼叫
      if(this.underwayExamLast) {
        return;
      }
      //
      clearTimeout(timer_hideUnderway);
      //正在呼叫
      if(!!(this.underwayExamLast = this.underwayExam)) {
        //console.log(`等待${this.underwayExamLast.callNo}呼叫结束`);
        //呼叫完后延后关闭
        let delay = callingTimeOnDuration;
        //最后一个呼叫，延后+2秒
        if(this.queue.underway.length === 1) {
          delay += 2;
        }
        timer_hideUnderway = setTimeout(this.hideUnderway, delay * 1000);
      }
    },
    //关闭突显当前呼叫
    hideUnderway() {
      //console.log(`${this.underwayExamLast.callNo}呼叫结束`);
      //最近呼叫和当前返回呼叫如是同一个，隐藏突显
      if(!this.underwayExamLast || !this.underwayExam || this.underwayExamLast.callNo === this.underwayExam.callNo) {
        //
        this.underwayExamLast = null;
        //
        this.queue.underway.shift();
        //
        if(this.queue.underway.length > 0) {
          //显示下一个
          //console.log("下一个突出");
          this.timerHideUnderway();
        } else {
          //console.log("关闭突出");
          this.$nextTick(this.udateUnderwayDialogStyle);
        }
      }
    },
    //
    prepareRollingPane() {
      clearTimeout(timer_rollingPane);
      //
      let panels = [];
      document.querySelectorAll(".qc-queue-grid-parag-body").forEach(e => panels.push(e));
      document.querySelectorAll(".rolling-pane .el-card__body").forEach(e => panels.push(e));
      this.rollingPanels = panels;

      this.rollPane();
    },
    //滚动
    rollPane() {
      clearTimeout(timer_rollingPane);
      //
      this.rollingPanels.forEach((e, i) => {
        //if(i != 1) { return true; }
        //console.log("%d %d", e.clientHeight, e.scrollHeight)
        let scrollTop = e.scrollTop;
        e.scrollTop = scrollTop + 1;
        //滚动位置无变化，重新滚动
        if(e.scrollTop === scrollTop) {
          e.scrollTop = 0;
        }
      });
      timer_rollingPane = setTimeout(this.rollPane, 90);
    },
    //
    clearAll() {
      clearTimeout(timer_readCalling);
      clearTimeout(timer_buildHeader);
      this.closeSocket()
    }
  },

  created() {
    this.buildHeader();

    // this.findExamItem();

    //this.readCalling();
  },

  /*mounted() {
    this.prepareRollingPane();
  },*/

  deactivated() {
    this.clearAll();
  },

  beforeDestroy() {
    this.clearAll();
  },

  computed: {
    underwayExam() {
      let items = this.queue.underway;
      return items && items.length? items[0] : null;
    }
  }

};
export default model;
