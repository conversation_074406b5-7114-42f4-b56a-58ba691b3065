import request,{postForm} from '@/utils/request'

const ctx = '/exammanagement/queue';

// 查询列表
export function find(query) {
  return postForm({
    url: ctx + '/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 查询详细
export function get(id) {
  return request({
    url: ctx + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: ctx + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: ctx + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request({
    url: ctx + '/del/' + id,
    method: 'delete'
  })
}

//
export function call(examInfoId, callRoomCode) {
  return request({
    method: "get",
    url: ctx + '/call',
    params: {examInfoId, callRoomCode},
    timeout: (30 * 1000)
  });
}

// 20230418
export function call2(examInfoId, callRoomCode) {
  return request({
    method: "get",
    url: ctx + '/call2',
    params: {examInfoId, callRoomCode},
    timeout: (30 * 1000)
  });
}


export function past(id) {
  return request({
    method: "get",
    url: ctx + '/past/' + id,
    timeout: (30 * 1000)
  });
}

//更改机房
export function changeEquipRoom(fromId, fromEquipRoomCode, toEquipRoomCode) {
  return postForm({url: (ctx + '/changeEquipRoom'), data: {fromId, fromEquipRoomCode, toEquipRoomCode}});
}
