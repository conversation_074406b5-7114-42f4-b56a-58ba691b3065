import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import {cloneDeep, mergeWith} from "lodash";

const model = {
  extends: BaseDialogModel,

  data() {
    return {
        fileNameArr:[],
        selectionElm:[],
    }
  },

  methods: {
    
    show(multMapExaminfoS){
        let vm = this;
        vm.fileNameArr = [];
        multMapExaminfoS.forEach(e => {
            let elm = cloneDeep(e);
            e["children"] = [];
            e["fileName"] = e.fileNames[0];
            for(let i=1;i<e.fileNames.length;i++){
                let elmTemp = cloneDeep(elm);
                elmTemp["fileName"] = e.fileNames[i];
                e.children.push(elmTemp);
            }
            vm.fileNameArr.push(e);
            console.log("flieNameArr",vm.fileNameArr);
        });
        vm.open();
    },

    submitForm(){
        let vm = this;
        let examMapFile = new Map();
        this.selectionElm.forEach(e=>{
            if(undefined==examMapFile.get(e.id)) examMapFile.set(e.id,{id:e.id,patientInfo:{name:e.patientInfo.name},fileNames:[]});
            examMapFile.get(e.id).fileNames.push(e.fileName);
            // ar.push({id:e.id,patientInfo:{name:e.patientInfo.name}});
            // examMapFile.set(e.id,ar);
        })
        console.log("examMapFile",examMapFile);
        let mapExaminfoS = [];
        for (let [key, value] of examMapFile) {
            if(value.fileNames.length>2){
                this.$modal.msgSuccess(value.patientInfo.name + "患者选择文件过多，请重新选择");
                return;
            }
            delete value.fileName;
            delete value.children;
            mapExaminfoS.push(value)
        }

        this.triggerBind("uploaderMatch",mapExaminfoS);
        vm.close();
    },

    handleSelectionChange(val){
        console.log("val",val);
        this.selectionElm = val;
        
    }
  }
};
export default model;