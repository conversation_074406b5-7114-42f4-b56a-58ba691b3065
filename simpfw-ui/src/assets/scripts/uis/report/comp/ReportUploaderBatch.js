import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {resbackBatch as resbackImageBatch} from "@/assets/scripts/pacs/image/api.imageTranster";

//检查信息
import * as api from "@/assets/scripts/uis/exammanagement/examinfo/api";
import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";
import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel"

import ReportUploaderMatch from "@/views/uis/report/comp/ReportUploaderMatch";

const model = {
  extends: BaseDialogModel,

  components: {LinksignPopup,ReportUploaderMatch},

  data() {
    return {
      uploadFileType:".pdf",
      params: null,
      processing: false,
      webGetFiles:false,
      qr: null,
      form:null,
      matchPattern:null,
      // matchAttr:null,
      mapExaminfoS:[],
      examItemName:null,
    }
  },

  methods: {
    closeForm(){
      this.triggerBind("reportUploadStateUpdate", false);
    },
    prepare(params,uploadFileType,deptConfig,examItemName) {
      this.uploadFileType = uploadFileType;
      this.params = params;
      this.processing = false;
      this.webGetFiles = false;
      this.examItemName = examItemName;
      // if(undefined!=deptConfig["matchPattern-"+params.examItemCodes]){
      //   console.log("upload2222",deptConfig,params);
      //   this.matchPattern = deptConfig["matchPattern-"+params.examItemCodes];
      // }else{
      //   this.matchPattern = deptConfig.matchPattern;
      // }
      this.matchPattern = deptConfig.matchPattern;


      //获取到必须填
      // var matchPatternAr = this.matchPattern.split("/");
      // if(undefined==matchPatternAr) this.$modal.msgSuccess("请配置科室批量上传匹配规则！");
      // matchPatternAr.forEach(e=>{
      //   var matchValue = e.split(":");
      //   if("attr"==matchValue[0]) this.matchAttr=matchValue[1];
      // });

      this.open();
      this.triggerBind("reportUploadStateUpdate", true);
    },

    importFiles(){
      this.webGetFiles = true;
    },

    submitForm(){
      if(!this.webGetFiles){
        this.$modal.msgSuccess("请选择文件夹");
        return;
      }
      this.processing = true;
      this.getUploadFiles();
    },

    uploadFolder(data){
      let vm = this;
      let fun;
      if(vm.uploadFileType==".jpg"){
        fun = api.batchReport;
      }else{
        fun = api.uploadReports;
      }
      fun(data).then(res => {
        if('qrnoauth' === res.errC) {
          if(null == vm.qr) {
            vm.qr = new QRSignRel(() => {
              vm.qr.activated = false;
              ///opt.firmed = true;
              cup(data);
            });
          }
          vm.qr.activated = true;
          vm.qr.refreshQrcode();
          return;
        }

        vm.$modal.msgSuccess(res.msg);

        vm.processing = false;
        vm.triggerBind("success");
        vm.close();
      }).catch(err => vm.processing = false);
    },

    getFileName(examinfo,matchPattern){
      //定义文件名匹配格式
      let matchPatternAr = matchPattern.split("/");

      let fileName="";
      for(let e of matchPatternAr){
        var matchValue = e.split(":");
        if("attr"==matchValue[0]||"attrO"==matchValue[0]) {
          let attrAr = matchValue[1].split(".");
          let obj = examinfo;
          attrAr.forEach(e=>{
            obj = obj[e];
          });
          if(undefined==obj||""==obj) {
            return null;
          }
          fileName+=obj;
        }else if("symbol"==matchValue[0]) fileName+=matchValue[1];
      }
      return fileName;
    },

    async getUploadFiles(){
      let vm = this;

      //获取文件
      const files = document.querySelector("#pdfFiles").files;
      if(!files || files.length === 0) {
        vm.processing = false;
        vm.close();
        vm.$modal.alert("该文件夹为空");
        return;
      }

      let form = new FormData();

      //获取患者，一次批量处理200个
      let params = {datesCreated: 1,resultStatusValues:[],status:0};
      params.resultStatusValues.push(StatusDict.regist);
      params.resultStatusValues.push(StatusDict.exam);
      params.resultStatusValues.push(StatusDict.report);

      vm.params.pageSize = 200;
      await api.find(vm.params).then(res => {
        vm.data = res.rows;
      }).catch();

      //过滤检查状态不符合患者
      const examinfoAr = vm.data.filter(function(examinfo) {
        if(!matchAnyStatus(examinfo, StatusDict.regist, StatusDict.exam)) return false;
        // if(undefined==this.matchAttr){
        //     return undefined != examinfo[this.matchAttr];
        // }
        return true;
      });

      if(0==examinfoAr.length){
        vm.processing = false;
        vm.close();
        vm.$modal.msgSuccess("没有需要上传患者");
        return;
      }

      //患者对应文件名map
      var mapExaminfoS = [];

      //患者依次和文件匹配
      for(var d of Array.from(examinfoAr)){
        var fileName = vm.getFileName(d,this.matchPattern)
        if(null==fileName) continue;

        var examinfo = {id:d.id,patientInfo:{name:d.patientInfo.name},fileNames:[]};
        Array.from(files).forEach(f => {
          //文件匹配校验
          if(f.name.indexOf(fileName)>-1&&f.name.includes(vm.uploadFileType)){
            form.append("files", f);
            examinfo.fileNames.push(f.name);
          }
        });
        if(0!=examinfo.fileNames.length) mapExaminfoS.push(examinfo);
      }

      if(0==mapExaminfoS.length){
        vm.processing = false;
        vm.close();
        vm.$modal.msgSuccess("没有文件匹配");
        return;
      }

      //调节图片插入顺序
      // let allFile = form.getAll("file");
      // form.delete("file");
      // let filePAr = [];
      // let fileLAr = [];
      // allFile.forEach(e=>{
      //   if(e.name.includes("BodyWater")) fileLAr.push(e);
      //   else filePAr.push(e);
      // })
      // filePAr.forEach(e=>{
      //   form.append("file", e);
      // });

      // fileLAr.forEach(e=>{
      //   form.append("file", e);
      // });

      //单个患者匹配到了超过2张影像
      let multMapExaminfoS = []
      vm.mapExaminfoS = [];
      for (var exam of mapExaminfoS) {
        if(exam.fileNames.length>1){
          multMapExaminfoS.push(exam);
        }else{
          vm.mapExaminfoS.push(exam);
        }
      }

      //form.append("ExamInfos", JSON.stringify(mapExaminfoS));
      vm.form = form;


      console.log("vm.mapExaminfoS",vm.mapExaminfoS,mapExaminfoS);
      if(multMapExaminfoS.length>0){
        vm.$refs.reportUploaderMatch.show(multMapExaminfoS);
        return;
      }


      form.append("ExamInfos", JSON.stringify(mapExaminfoS));

      vm.uploadFolder(form);

    },

    uploaderMatch(mapExaminfoSBack){
      let vm = this;

      mapExaminfoSBack.forEach(e=>{
        vm.mapExaminfoS.push(e);
      })



      vm.form.append("ExamInfos", JSON.stringify(vm.mapExaminfoS));

      vm.uploadFolder(vm.form);
    }

  }
};
export default model;
