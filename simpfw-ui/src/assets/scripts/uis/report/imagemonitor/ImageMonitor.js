import { mapGetters } from 'vuex';
import {cloneDeep} from "lodash";

import {undefinedOrNull, mergeWithNotNull} from "@/utils/common";
//WebSocket支持
import BaseWebSocketModel from "@/assets/scripts/pacs/BaseWebSocketModel";
//html调摄像头支持
import BaseUserMediaModel from "@/assets/scripts/pacs/BaseUserMediaModel";
//
import { imageFromCavas } from "@/assets/scripts/pacs/common";

//影像接口
import * as imapi from "@/assets/scripts/pacs/image/api";
//患者信息查询
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//房间信息
import {params as getEquipRoomParams} from "@/assets/scripts/pacs/equiproom/api";
//影像宽高比
import {ASPECT_WIDTH, ASPECT_HEIGHT, ASPECT_RATIO} from "@/assets/scripts/pacs/image/util";
//
import {StatusDict, matchAny} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//重新连接影像服务
let timer_reopenSocket;
//采集的图像尺寸
function imageDime(availWidth, availHeight) {
  //
  const maxWidth = ASPECT_WIDTH, maxHeight = ASPECT_HEIGHT;
  //
  let width = availWidth, height = width / ASPECT_RATIO;
  if(height > availHeight) {
    height = availHeight;
    width = availHeight * ASPECT_RATIO;
  }
  return {width, height};  
}
export {imageDime};
//静态/动态采集
const CollectMode = {Static: "STATIC", Dynamic: "DYNAMIC"}

const model = {
  mixins: [BaseUserMediaModel, BaseWebSocketModel],

  data() {
    return {
      examInfo: null,

      examInfoId: null,
      examInfoList: [],
      images: [],
      //呼叫检查的机房
      equipRoomParams: null,
      //浏览器调用摄像头
      userMediaEnabled: false,
      //后端获取摄像头影像
      socketEnabled: true,
      //
      isPopup: false,

      currImageObject: null,//当前影像数据用于浏览和采集
      captureImageWidth: ASPECT_WIDTH,
      captureImageHeight: ASPECT_HEIGHT,
      capturing: false,

      collectMode: CollectMode.Static,
      collectModesSupported: [CollectMode.Static],
      //执行动态采集，等待后端返回是否动态采集成功
      socketFreezing: false,
      //
      isDeactivated: false
    }
  },

  methods: {
    //
    show(exam) {
      //是否选择了检查
      if(!exam || !exam.id) {
        this.$modal.msgWarning("请选择检查。");
      //  return;
      }
      //
      this.setExamInfo(exam);
      //检查是否允许采集：登记完成、已检查、已报告的允许采集
      if(!!exam && exam.id && (!this.examInfo || !this.examInfo.id)) {
        this.$modal.msgWarning("该检查不支持采集，原因：" + (exam.resultStatus? exam.resultStatus.dictLabel : "未知") + "。");
        return;
      }
      //检查列表
      this.findExamInfo();
      //
      this.prepareImage();
    },
    //当前采集的检查
    setExamInfo(exam) {
      //如果是使用影像服务采集则断开摄像头
      //this.closeSocket();
      //显示指定重新连接
      //this.isConnectedSocked = false;
      //检查状态
      const collectable = !!exam && (!exam.resultStatus || !exam.resultStatus.dictValue || matchAny(exam, StatusDict.regist, StatusDict.exam, StatusDict.report));

      this.examInfo = collectable? exam : null;
      this.examInfoId = collectable && !!exam? exam.id : null;
      if(collectable && !!exam && !!exam.id) {
        //当前检查机房
        exam.equipRoom = this.currentEquipRoom;
      }
    },
    //读取房间信息，确定采集影像类型
    prepareImage() {
      let currRoom = this.currentEquipRoom;
      if(!currRoom || !currRoom.id) {
        //this.$modal.alert("请选择检查机房.");
        return;
      }
      //影像服务已连接
      if(this.isConnectedSocked) {
        //console.log("当前影像服务调用摄像头。");
        this.syncExamToSocket();
        return;
      }
      //浏览器摄像头已打开
      if(this.isConnectedUserMedia) {
        //console.log("当前浏览器调用摄像头。");
        return;
      }
      const examInfo = this.examInfo;
      if(!examInfo) { // || !examInfo.callInfo || !examInfo.callInfo.id
        this.$modal.msgWarning("请选择检查。");
        return;
      }
      /*//叫号机房或登记指定机房
      let callInfo = examInfo.callInfo, examRoom = examInfo.equipRoom;
      if((!callInfo || !callInfo.id) && !examRoom) {
        this.$modal.msgWarning("未明确检查房间|。");
        return;
      }
      //机房参数：设备本机存放文件地址、远程存储信息
      this.equipRoomParams = null;
      //读取叫号/检查机房信息
      let prom;
      if(!!callInfo) {
        prom = getCallInfo(callInfo.id).then(res => {
          const equipRoom = res.data.callRoom;
          return equipRoom || examRoom;// || Promise.resolve();
        });
      } else {
        prom = Promise.resolve(examRoom);
      }
      if(!prom) {
        this.$modal.msgWarning("未明确检查房间||。");
        return;
      }*/
      let prom = Promise.resolve(currRoom);
      //
      prom.then(res => {
        if(!res || !res.id) {
          this.$modal.msgWarning("未明确检查房间|||。");
          return;
        }
        //读取机房配置：影像服务地址，文件存储位置
        getEquipRoomParams(res.id).then(rm => {
          this.equipRoomParams = {storage: rm.storage, equipRoom: rm.equipRoom}
          let room = rm? rm.equipRoom : null;
          //使用影像服务采集或浏览器调用摄像头
          if(!!room && !!room.imageService) {
            this.socketEnabled = true;
            this.userMediaEnabled = false;
            //影像服务
            this.socketUrl = `ws://${room.imageService}/uis/image`;
            this.openSocket();
            //画布大小
            this.$nextTick(this.sizeImageViewport);
          } else {
            //浏览器打开摄像头
            this.$nextTick(() => {
              this.socketEnabled = false;
              this.userMediaEnabled = true;
              this.connectDevice();
              this.$nextTick(this.sizeImageViewport);
            });
          }
        });
      });
      //this.$nextTick(this.connectDevice);
      //this.openSocket("ws://127.0.0.1:12080/uis/image");
    },
    //视频显示
    getVideoViewport() { return this.$refs.videoViewport; },
    //图片画布
    getImageViewport() { return this.$refs.imageViewport; },
    getImageGraph() {
      const vp = this.getImageViewport();
      return !!vp? vp.getContext("2d") : null; 
    },
    //采集图片画布
    getImageCapture() { return this.$refs.imageCapture; },
    getCaptureGraph() {
      const vp = this.getImageCapture();
      return !!vp? vp.getContext("2d") : null; 
    },

    /**
     * 图像窗口尺寸
     */
    sizeImageViewport() {return;
      const viewport = this.socketEnabled? this.getImageViewport() : this.getVideoViewport();
      const wrap = viewport.parentNode;
      const width = wrap.clientWidth, height = width / ASPECT_RATIO;
      viewport.width = width;
      viewport.height = height;
      viewport.style.width = width + "px";
      viewport.style.height = height + "px";
    },

    //静态采集
    captureImage() {
      if(this.capturing) {
        return;
      }
      //重绘成希望的尺寸
      const imageCapture = this.getImageCapture();//imageViewport = this.getImageViewport(), 
      const imageObject = this.currImageObject, imageWidth = imageObject.width, imageHeight = imageObject.height;
      imageCapture.width = imageWidth;
      imageCapture.height = imageHeight;
      this.getCaptureGraph().drawImage(imageObject, 0, 0, imageObject.width, imageObject.height);

      let imageData = imageFromCavas(imageCapture);
      this.uploadImage(imageData);
    },
    //上传采集的图像
    uploadImage(imageData, fileUrl) {
      this.capturing = true;

      let exam = this.examInfo;
      let roomCode = this.currentEquipRoom.roomCode;
      imapi.saveImage(roomCode, exam.id, imageData, fileUrl).then(res => {
        this.capturing = false;

        //this.$modal.msgSuccess("已采集1张图片.");
        let img = res.data;
        //
        if(!exam.dicomStudy || !exam.dicomStudy.id) {
          imapi.getStudy({"examInfo.id": exam.id}).then(res => {
            this.examInfo.dicomStudy = res.data;
            if(!fileUrl) {
              this.syncExamToSocket();
            }
          });
        }
        //
        this.triggerBind("capture", exam, img);
      }).catch(err => this.capturing = false);
    },
    //加载采集的图像
    newImage(imageData) {
      if(!imageData) { return; }
      let img = new Image();
      img.onload = () => {this.drawImage(img);}
      img.src = imageData;//"data:image/jpeg;base64," + data;
    },
    //画图
    drawImage(img) {
      const viewport = this.getImageViewport(), imageWidth = img.width, imageHeight = img.height;
      /*if(img.width != viewport.width || img.height != viewport.height) {
        const width = img.width, height = img.height;
        viewport.width = width;
        viewport.height = height;

        viewport.style.width = width + "px";
        viewport.style.height = height + "px";
      }*/
      this.currImageObject = img;
      //允许最大宽、高度
      let availWidth, availHeight;
      const pane = viewport.parentNode.parentNode, toolbar = pane.querySelector(".buttons-pane");
      availWidth = pane.clientWidth;
      availHeight = pane.clientHeight - Math.max(toolbar.offsetHeight, toolbar.clientHeight);
      if(this.isPopup) {
        const diaHead = pane.parentNode.querySelector(".el-dialog__header");
        availHeight -= Math.max(diaHead.offsetHeight, diaHead.clientHeight);
      }
      //按比例计算绘制尺寸
      //默认填满宽度
      let drawWidth = availWidth, drawHeight = drawWidth * imageHeight / imageWidth;
      //console.log("imageWidth=%d, imageHeight=%d, availHeight=%d, drawWidth=%d, drawHeight=%d"
      //  , imageWidth, imageHeight, availHeight, drawWidth, drawHeight);
      //如高度超出，调整宽度
      if(drawHeight > availHeight) {
        drawHeight = availHeight;
        drawWidth = drawHeight * imageWidth / imageHeight;
        //console.log("adjestd imageWidth=%d, imageHeight=%d, availHeight=%d, drawWidth=%d, drawHeight=%d"
        //  , imageWidth, imageHeight, availHeight, drawWidth, drawHeight);
      }
      viewport.width = drawWidth;
      viewport.height = drawHeight;
      viewport.style.width = drawWidth + "px";
      viewport.style.height = drawHeight + "px";
      this.getImageGraph().drawImage(img, 0, 0, drawWidth, drawHeight);
    },
    //连接成功后发送机房参数、设备参数
    handleSocketOpen() {
      //
      this.handleSocketSend({action: "setparam", data: this.equipRoomParams});
      //当前静态/动态采集
      //this.socketFreezing = true;
      this.handleSocketSend({action: "getmode"});
      //
      this.syncExamToSocket();
    },
    //获取图片
    handleSocketReceived(event) {
      //console.log(this, "接收");
      const IMAGEDATA = "imagedata", DETACHEDIMAGE = "detached", MODE = "mode";
      let data = event.data, imageData;
      if("string" === typeof(data)) {
        let examMatched, dataName;
        let pos, msgo;
        if(0 === data.indexOf("{")) {
          msgo = JSON.parse(data);
          const currExamInfo = this.examInfo;
          if(!!msgo && !!currExamInfo) {
            examMatched = currExamInfo.examNo === msgo.examNo;
            dataName = msgo.name;
            data = msgo.data;
          }
        } else if(-1 !== (pos = data.indexOf(":"))) {
          examMatched = true;
          dataName = data.substring(0, pos);
          data = data.substring(pos + 1);
        }

        if(examMatched && dataName === IMAGEDATA) {//推送的影像
          imageData = "data:image/jpeg;base64," + data;
        } else if(examMatched && dataName === DETACHEDIMAGE) {//控制器抓取的影像
          let fileUrl = data;
          this.uploadImage(null, fileUrl);
          return;
        } else if(dataName === MODE) {//静态/动态
          this.collectMode = data.current;
          this.collectModesSupported = data.supported;
          //
          this.socketFreezing = false;
          return;
        }
      } else {
        imageData = (window.URL || window.webkitURL).createObjectURL(data);
      }

      if(!this.isDeactivated) {
        this.newImage(imageData);
      }
    },
    //socke断开，清空画布
    handleSocketClosed() {
      //console.log("断开");
      const grp = this.getImageGraph();
      if(!!grp) {
        grp.clearRect(0, 0, 9999, 9999);
      }
      //
      this.reopenSocket();
    },

    handleSocketError() {
      //console.log(arguments)
      this.setCollectMode(CollectMode.Static);
    },
    //意外断开
    reopenSocket() {
      clearTimeout(timer_reopenSocket);
      console.info("尝试重连影像服务....");
      //console.log(this.examInfo);
      if(!!this.examInfo) {
        timer_reopenSocket = setTimeout(this.openSocket, 8 * 1000);
      }
    },

    //读取患者检查
    findExamInfo() {
      let params = {pageSize: 999, pageNum: 1, status: 0, datesCreated: 1, "orderBy": "h.id asc"};
      //本机
      /*const room = this.currentEquipRoom;
      params.callInfo = {callRoom: {roomCode: room.roomCode}};
      */
      //工作状态, 已检查，已登记
      params.resultStatusValues = [StatusDict.regist, StatusDict.exam];

      eiapi.find(params).then(res => {
        let rows = res.rows, currExamInfo = this.examInfo;
        //当前书写的检查不再监控检查下拉列表
        if(!!currExamInfo && !!currExamInfo.id && -1 === rows.findIndex(e => e.id === currExamInfo.id)) {
          let exam = cloneDeep(currExamInfo);
          exam.isTemp = true;
          rows.unshift(exam);
          this.$modal.msgWarning("该检查登记时间为： " + exam.createTime);
        }
        this.examInfoList = rows;
      });
    },
    //选择患者检查
    changeExamInfo(id) {
      const exam = this.examInfoList.find(e => e.id === id);
      //this.examInfo = exam;
      this.setExamInfo(exam);
      //
      this.syncExamToSocket();
    },
    //患者检查下拉框显示
    selectOptionLabel(exam) {
      //姓名-排队号
      let lbl = [exam.patientInfo.name];
      if(!!exam.callInfo) {
        lbl.push("-");
        lbl.push(exam.callInfo.callNo);
      }
      return lbl.join("");
    },
    /**
     * 将当前报告发送至影像服务
     * 或告知影响服务当前无检查
     */
    syncExamToSocket() {
      //if(!this.socketEnabled) {
      //  return
      //}

      let exam = this.examInfo;
      //
      if(!this.isConnectedSocked && !!exam) {
        this.prepareImage();
        return;
      }
      //if(!!exam && !!exam.id) {
        let fx = () => {
          this.handleSocketSend({action: "setexam", data: exam});
        };
      //}
      //确保发送dicom信息
      if(!!exam && (!exam.dicomStudy || !exam.dicomStudy.id)) {
        imapi.getStudy({"examInfo.id": exam.id}).then(res => {
          this.examInfo.dicomStudy = res.data;

          fx();
        });
      } else {
        fx();
      }
    },
    resetExamToSocket() {
      this.examInfo = null;
      this.syncExamToSocket();
    },
    //弹出/嵌入视窗
    popup() {
      this.isPopup = !this.isPopup;

      this.$nextTick(() => {
        const diaWrap = this.$refs.imageMonitorViewportDialog.$el, dia = diaWrap.querySelector(".el-dialog");
        //console.log(dia);
        if(this.isPopup) {
          const re = document.body;
          re.appendChild(diaWrap);
          //
          const dime = {width: re.clientWidth * 0.9, height: re.clientHeight - 120};//imageDime(re.clientWidth * 0.9, re.clientHeight - 120);
          dia.style.width = (dime.width + 4 * 2 + 1 * 2) + "px";
          dia.style.height = dime.height + "px";
        } else {
          const wrap = this.$refs.imageMonitorWrap;
          wrap.appendChild(diaWrap);

          dia.style.width = "100%";
          dia.style.height = "unset";
        }
        this.sizeImageViewport();
        //
        if(this.isPopup) {
          dia.style.top = ((document.body.clientHeight - dia.clientHeight) / 2) + "px";
          dia.style.left = ((document.body.clientWidth - dia.clientWidth) / 2) + "px";
        }
      });
    },
    //当前静态/动态采集
    setCollectMode(mode) {
      //
      //this.collectMode = mode;
      //
      this.handleSocketSend({action: "setmode", data: mode});
    },
    //动态采集
    recordPicure() {
      let collectMode = CollectMode.Static;
      if(CollectMode.Dynamic !== this.collectMode) {
        this.socketFreezing = true;
        //
        collectMode = CollectMode.Dynamic;
      }
      //
      this.setCollectMode(collectMode);
    }
  },

  computed: {
    ...mapGetters(['currentEquipRoom']),
    //
    hasExamInfo() {
      return !!this.examInfoList && this.examInfoList.length > 0;
    },
    //
    isCollectStatic() {
      return CollectMode.Static === this.collectMode;
    },
    //
    isCollectDynamic() {
      return CollectMode.Dynamic === this.collectMode;
    },
    //
    dynamicCollectSupported() {
      return this.collectModesSupported.includes(CollectMode.Dynamic);
    }
  },

  watch: {
    'currentEquipRoom'(newv, oldv) {
      this.findExamInfo();
    }/*,
    examInfo(nv, ov) {
      this.syncExamToSocket();
    }*/
  },
  activated() {
    //console.log("activated");
    this.isDeactivated = false;
  },

  deactivated() {
    //console.log("deactivated")
    this.disconnectDevice();
    this.isDeactivated = true;
  },

  beforeDestroy() {
    this.resetExamToSocket();
  }
};
export default model;
