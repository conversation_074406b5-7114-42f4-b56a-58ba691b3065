import request from '@/utils/request'
//系统参数
import {getConfigKey} from '@/api/system/config';

//const ctx = '/queue/ticket';
const ctx = 'http://127.0.0.1:30010/v1';

//打印机参数配置
export function getPrinter() {
  return getConfigKey('pacs.smallticketPrinter');
}

export function print2(examInfo) {
  return request.post("/queue/ticket/getPrintInfo", examInfo)
  .then(res => {
      let retMap = res.data;
      if (retMap) {
        return request.post(ctx + '/printTicket', retMap);
      }
    })
  .catch(function (error) {
      // return Promise.resolve("排队小票打印失败");
  });
}
