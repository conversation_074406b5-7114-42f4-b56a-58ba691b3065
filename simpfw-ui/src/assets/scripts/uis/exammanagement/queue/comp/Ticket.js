import * as api from "./api.ticket";

export const QueueTicketPrint = {
  methods: {
    //打印排队小票
    printQueueTicket(examInfo) {
      try {
        //读小票打印配置参数，是否打印
        api.getPrinter().then(r => {
          //{"active":true}
          const cval = r && r.msg? JSON.parse(r.msg) : null;
          //检查 active 属性
          if(cval && cval.active) {
            api.print2(examInfo);
          }
        })
      } catch (err) {
        console.error(err);
      }
    }    
  }
};