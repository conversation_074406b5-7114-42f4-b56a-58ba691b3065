import {cloneDeep} from "lodash";

import {currDate, parseTime, currDatetime} from "@/utils/common";

import * as api from "../api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import {TransModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//叫号
import ExamCalling from "@/views/gis/exammanagement/queue/comp/ExamCalling";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

let timer_getList;

export default {
  extends: BaseGridModel,
  dicts: ['uis_exam_item'],
  mixins: [TransModel, ExamDataScope],
  components: { ExamCalling, Contextmenu },

  props: {
    refresh: {type: Number},
    //限定机房
    roomRestricted: {type: Boolean, default: false},
    //检查项目不可变
    examItemsFixed: {type: Boolean, default: false},
    //指定查询条件, 如 examInfo: {examAtPm: 1}
    filter: {type: Object},
    contextmenuItems: {type: Array}
  },

  data() {
    return {
      searchForm: {
          combo_props: [
              {value: "examInfo.patientInfo.name", label: "姓名"}  
            , {value: "examInfo.examNo", label: "检查号"}
            , {value: 'examInfo.patientInfo.registNo',label:'登记号'}
            , {value: 'examInfo.inpNo',label:'住院号'}
            , {value: 'callNo',label:'排队号'}
          ]
             
          , propName: "examInfo.patientInfo.registNo"
          , propValue: null
          , examItemCodes: null
        },
      currentItem: null,
      lastTimestampGetList: 0
    }
  },

  methods: {

    /**
     * 搜索
     */
    getList: function(opts) {
      clearTimeout(timer_getList);
      //
      this.checkValue()
      const millis = currDatetime().getTime();
      if((millis - this.lastTimestampGetList) < (1 * 1000)) {
        return;
      }
      this.lastTimestampGetList = millis;
      //
      this.currentItem = null;
      //
      this.handleCurrentChange(null);
      //房间调用，要求检查项目
      const filter = this.filter;
      if(this.roomRestricted && (!filter || !filter.examItemCodes || !filter.examItemCodes.length)) {
        console.warn("未提供房间可执行检查项目")
        //this.timerGetList();
        return;
      }
      //查询参数
      let sfm = this.searchForm, params = {pageSize: sfm.pageSize, pageNum: sfm.pageNum};
      //
      Object.assign(params, this.filter);
      //当天排队
      params.createTimeGe = parseTime(currDate());
      //
      params.createTimeLt = params.createTimeGe;

      //检查项目
      if(!this.examItemsFixed) {
        params.examItemCodes = sfm.examItemCodes
      }
      //未做检查排队
      //params.examInfo = params.examInfo || {};
      //params.examInfo.resultStatus = {dictValue: '0'};
      //默认工作精度包括"登记完成"和"检查中"
      if(!params.resultStatusCodes || params.resultStatusCodes.length === 0) {
        params.resultStatusCodes = ['0', '1'];
      }
      //根据下拉
      let propName = sfm.propName;
      if(propName) {
        let propsName = propName.split(/\./), prop = params;
        for(let i = 0, len = propsName.length; i < len; i ++) {
          let pnam = propsName[i];
          if((i + 1) < len) {
            prop[pnam] = prop[pnam] || {};
            prop = prop[pnam];
            continue;
          }
          prop[pnam] = sfm.propValue;
        }
      }
      //非自动刷新，显示加载图标
      if(!timer_getList || opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }
      api.find(params).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
        //
        this.timerGetList();
      }).catch(() => this.timerGetList);
    },
    //
    timerGetList() {
      clearTimeout(timer_getList);
      if(this.refresh) {
        timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },

    //单击
    handleCurrentChange(row, orow) {
      this.currentItem = row;
      //this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row.examInfo);
      //toCall(row.examInfo);
    },
    popRowContextmenu(row, col, evt) {
      if(this.contextmenuItems && this.contextmenuItems.length) {
        evt.preventDefault();
        evt.stopPropagation();

        this.$refs["tableContextmenu"].show(evt, row);
      }
    },
    handleContextmenu(item, row) {
      this.triggerBind("selectContextmenu", item, row)
    },
    //调用呼号
    toCall(row) {
      let examInfo = cloneDeep(row.examInfo);
      //row.examInfo = null;
      //delete row["examInfo"];
      examInfo['callInfo'] = row;
      this.$refs.examCalling.open(examInfo);
      //
      this.triggerBind("afterCall");
    },
    //呼叫下一个
    toCallNext() {
      let gridData = this.grid.data;
      if(!gridData || !gridData.length) {
        this.$modal.msg("没有检查了。");
        return;
      }
      let currentRow = this.currentItem, nxt;
      if(!currentRow) {
        nxt = gridData[0];
      } else {
        for(let i = 0; i < gridData.length; i ++) {
          let row = gridData[i];
          if(currentRow.id === row.id) {
            const nxti = i + 1;
            if(nxti < gridData.length) {
              nxt = gridData[nxti];
            }
            break;
          }
        }
      }
      //
      if(!nxt) {
        this.$modal.msg("没有检查了。");
        return;
      }
      this.$refs.dataGrid.setCurrentRow(nxt);
      this.toCall(nxt);
    },

    /**
     * 诊前准备或下午检查
     * @param props {'examAtPm':1}-下午检查状态码
     */
    handlePostpone(row, examAtPm) {
      this.handleTrans(row.examInfo, {examAtPm}).then(res => {
        if(res && 200 === res.code) {
          this.getList();
        }
      });
    },
    //标识过号
    handlePast(callInfo) {
      api.past(callInfo.id);
    },
    //当前选择的检查
    get() {
      return this.currentItem && this.currentItem.examInfo || null;
    },
    focusPropValue() {
        try { this.$nextTick(() => this.$refs.propValue.focus()) } catch (err) { console.error(err); }
    },

    //年龄列
    colFmt_age(row) {
      const pat = row.examInfo? row.examInfo.patientInfo : null;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //全选输入框文本
    handleFocusRegistCode($evt) {
      const el = $evt.target || $evt.srcElement;
      el.select();
    },
     //登记号补0
     checkValue(){
        let fm = this.searchForm
        if(fm.propName=='examInfo.patientInfo.registNo'){
            
            if(!!fm.propValue&&fm.propValue.length>0 && fm.propValue.length<10){
                fm.propValue = fm.propValue.padStart(10,'0');
            }
        }
    }
  },

  watch: {
    filter: {
      deep: true,
      immediate: true,
      handler() {
        if(this.examItemsFixed) {
          this.searchForm.examItemCodes = this.filter.examItemCodes;
        }
        this.getList();
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }
  },

  activated() {
    console.log("activated")
    this.timerGetList();
  },

  deactivated() {
    console.log("deactivated")
    clearTimeout(timer_getList);
  },

  beforeDestroy() {
    clearTimeout(timer_getList);
  }
};
