import { mapGetters } from 'vuex';

import {mergeWith as mergeWithDeep} from "lodash";

import {undefinedOrNull, mergeWithNotNull} from "@/utils/common";//triggerBind, 

import * as patapi from "@/assets/scripts/gis/exammanagement/patient/api";
import * as qapi from "@/assets/scripts/gis/exammanagement/queue/api";
//诊前准备
import {TransModel, ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//更新工作状态
import {StatusDict, examReachable} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//选择机房
import {EqiopRoomPicker} from "@/assets/scripts/pacs/equiproom/currentequiproom/EqiopRoomPicker";

//呼叫验证
function emptyValidForm() {
  return {
    medicalRecordNo: null,
    name: null,
    age: null,
    ageUnit: {},
    gender: {},
    birthDay: null,
  };
}
//检查信息
function emptyExamInfo() {
  return {
    id: null,
    patientInfo: {
      name: null,
      age: null,
      ageUnit: {},
      gender: {},
      birthDay: null,
      birthDay: null
    },
    examParts: null,
    clinicDiagnosis: null,
    allergyHistory: null,

    examPrerequire: null,

    callInfo: {
      callsNumber: null
    }
  };
}

const model = {

  props: {
    validRequired: {type: Boolean, default: false}
  },

  mixins: [ResultStatus, TransModel, EqiopRoomPicker],

  data() {
    return {
      visible: false,

      examPrerequireExists: false,

      validForm: emptyValidForm(),

      examForm: emptyExamInfo(),

      calling: false
    }
  },

  methods: {
    open(examInfo) {
      if(!examReachable(examInfo)) {
        return;
      }
      //是否已排号
      const callInfo = examInfo.callInfo;
      if(!callInfo || !callInfo.id) {
        this.$modal.alert("无法获取排队信息。");
        return;
      }
      //
      this.validForm = emptyValidForm();
      //
      let fm = emptyExamInfo();
      mergeWithDeep(fm, examInfo, null, mergeWithNotNull);
      Object.assign(this.examForm, fm);
      //读取排队信息
      fm = this.examForm;
      qapi.get(callInfo.id).then(res => {
        if(res) {
          fm.callInfo = res.data || callInfo;
          //不执行验证患者
          if(!this.validRequired) {
            //呼叫
            this.beforeCall().then(res => {
              //直接更新工作状态
              //this.doExam();
              return res;//? this.updateResultStatus(this.examForm, StatusDict.exam) : res;
            }).then(res => {
              this.afterUpdateResultStatus();
            });
          }
        }
      })
      //需验证患者
      if(this.validRequired) {
        this.visible = true;
      }
    },
    //需诊前准备
    examPrerequire() {
      let ret = this.handleTrans(this.examForm, {examAtPm: 1});//{examPrerequire: 0}
      //console.log(ret);
      if(ret) {
        Promise.resolve(ret).then(res => {
          if(res && 200 === res.code) {
            this.examForm.examPrerequire = 0;
            this.afterUpdateResultStatus();
          }
        });
      }
    },
    //开始检查
    toExam() {
      /*if(!this.examPrerequireExists) {
        this.doExam();
      } else {
        this.$modal.confirm("当前检查未准备就绪，是否进行检查？").then(this.doExam);
      }*/
      this.doExam();
    },
    //更新检查状态
    doExam() {
      let cfmsg;
      if(this.examPrerequireExists) {
        cfmsg = "当前检查未准备就绪，是否进行检查？";
      }
      this.confirmToUpdateResultStatus(this.examForm, StatusDict.exam, cfmsg)
        .then(res => {
          this.$modal.msgSuccess("操作成功");

          //if(res && 200 == res.code) {
            this.afterUpdateResultStatus();
          //}
        });
    },
    //呼叫前相关检查
    beforeCall() {
      //是否机房呼叫
      const room = this.currentEquipRoom, roomCode = room && room.roomCode || null;
      //非机房呼叫
      if(!roomCode) {
        //获取检查指定的机房
        const examRoom = this.examForm.equipRoom;
        if(examRoom && examRoom.roomCode) {
          return this.$modal.confirm("患者将在如下机房进行检查：" + examRoom.roomName)
            .then(() => this.doCall(examRoom.roomCode))
            .catch(err => this.toSelectEquipRoom);
        } else {
          return this.toSelectEquipRoom();
        }
      } else {
        return this.doCall(roomCode);
      }
    },
    //呼叫
    doCall(roomCode) {
      let fm = this.examForm, examInfoId = fm.id;//eiapi.call(fm)
      //提示呼叫中
      let mso = this.$message.warning({message: "叫号中..."
        , iconClass: "el-message__icon el-icon-microphone"
        , customClass:"el-message--warning"
        , duration: 30 * 1000});

      //调呼叫接口
      this.calling = true;
      return qapi.call(examInfoId, roomCode).then(res => {
        this.calling = false;
        mso.close();
        //重复呼叫
        if(res && res.errM) {
          this.$modal.alert(res.errM);
          return 0;
        }
        //
        this.$modal.msgSuccess("叫号结束。");

        if(res && 200 === res.code) {
          if(res.data) {
            fm.callInfo = res.data;
          }
        }

        return res;
      }).catch(err => {
        this.calling = false;
        mso.close();
        //this.$modal.alert("无法叫号，原因：" + err);
      });
    },
    //验证
    doValid() {
      const fm = this.validForm;
      if(!fm.medicalRecordNo) {
        this.$modal.alert("请输入验证的病历号。");
        return ;
      }
      patapi.find({medicalRecordNo: fm.medicalRecordNo}).then(res => {
        if(!res || 200 !== res.code || !res.rows || !res.rows.length) {
          this.$modal.alert("无该病历号匹配的患者信息。");
          return ;
        }
        this.validForm = res.rows[0];
      });
    }/*,
    examPrerequireExists() {
      const examPrerequire = this.examForm.examPrerequire;
      return 0 === examPrerequire;
    }*/
    , afterUpdateResultStatus() {
      this.triggerBind("refreshExamInfo");
    },
    //选择机房
    toSelectEquipRoom() {
      this.selectEquipRoom().then(rm => this.doCall(rm.roomCode)).catch(() => {});
      
      return Promise.resolve();
    }
  },

  watch: {
    "examForm.examPrerequire": {
      deep: true,
      handler(nval, oval) {
        this.examPrerequireExists = 0 === nval;
      }
    }
  },

  
  computed: {
    ...mapGetters(['currentEquipRoom']),
  
    buttonTextCall() {
      const callInfo = this.examForm.callInfo;
      return callInfo && callInfo.callsNumber > 0? "复呼" : "叫号";
    }
  }
};

export default model;