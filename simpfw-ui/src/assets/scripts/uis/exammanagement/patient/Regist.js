import {cloneDeep, mergeWith as mergeWithDeep} from "lodash";

import auth from '@/plugins/auth'

//import Treeselect from "@riophae/vue-treeselect";
//import "@riophae/vue-treeselect/dist/vue-treeselect.css";
//患者信息
import * as api from "@/assets/scripts/gis/exammanagement/patient/api";
//检查信息
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
//获取机房列表
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
//转拼音
import {toPinyin} from "@/api/common";
//选择科室
//import { treeselect as deptTreeselect } from "@/api/system/dept";
import DeptPicker from "@/views/system/dept/DeptPicker";

import {props as treeProps} from "@/assets/scripts/pacs/BaseTreeModel";

//选检查部位
//import ExamPartsTree from "@/views/pacs/comcfg/examparts/comp/ExamPartsTree";
import ExamPartsPicker from "@/views/pacs/comcfg/examparts/comp/ExamPartsPicker";
//选中用户
import UserPicker from "@/views/system/user/comp/UserPicker";
//患者列表右键菜单
import PatientSheet from "@/views/uis/exammanagement/patient/comp/PatientSheet";
import {tableContextmenuItems} from "@/assets/scripts/uis/exammanagement/patient/Index";
//检查状态
import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//检查编辑
import {
  ResultStatusModel as ResultStatus,
  TransModel,
  UndoModel
} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

//更改机房
//import ExamEquipRoom from '@/views/gis/exammanagement/patient/comp/ExamEquipRoom';
import EquipRoomStatus from "@/views/pacs/equiproom/Index";
//排队
import Queue from "@/views/gis/exammanagement/queue/comp/Queue";
//选择查询接口返回数据
import OrderPicker from "@/views/gis/exammanagement/patient/comp/OrderPicker";
//选择退费医嘱
import OrderRefund from "@/views/gis/exammanagement/patient/comp/OrderRefund";
//放射检查modality
import {DS_MODALITIES, OTOL_MODALITIES, RAD_MODALITIES, TCD_MODALITIES} from "@/assets/scripts/pacs/modalities";

import {
  calculateAge,
  currDatetime,
  dateAdd,
  getAge,
  mergeWithNotNull,
  parseTime,
  undefinedOrNull
} from "@/utils/common";
import {add as addNum} from "@/utils/NumberOps";
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";
//排队小票
import {QueueTicketPrint} from "@/assets/scripts/gis/exammanagement/queue/comp/Ticket";

import ReportUploader from "@/views/uis/report/comp/ReportUploader";

import ReportViewer from "@/views/uis/report/comp/ReportViewer";

import {getUserProfile} from "@/api/system/user";

import {PatiemtOptions} from "@/assets/scripts/pacs/exammanagement/patient/mixins";

import DateUtil from '@/utils/DateUtil';
import config from "@/assets/scripts/global/config";
import DesUtil from "@/utils/DesUtil";
import {PATIENTSHEETTYPE} from "@/assets/scripts/pacs/Const";

const BATCH_REGIST = "exam-info:batch-regist";

//
const IGNORECONFIRM = "ignoreConfirm";
//转换拼音
let timer_convToPinyin;
//
const RegistWay = {
  manual: 'W',
  registNo: 'I',
  inpNo: 'H',
  card: 'C',
  outpNo: 'O'
}
let RegistWayDef = RegistWay.card;
let ModalityCodeDef = "JC";//UIS_MODALITIES[1];
let itemCodeDef = "9";//UIS_MODALITIES[1];

const modalityMapItem = {
  DENT_CT: 'DENT_CT',
  DENT_DR: 'DENT_DR',
};

//
function emptyRegist() {
  return {
    registWay: RegistWayDef,

    patientInfo: {
      id: null,
      registNo: null,
      medicalRecordNo: null,
      qrCodeText: null,
      healthCardId: null,
      name: null,
      namePingyin: null,
      age: null,
      ageDisable: true, // lzw modify ,添加控件是否禁止
      birthday: null,
      birthPlace: null,
      cardNo: null,
      regNo: null,
      insuranceNo: null,
      assignedUnit: null,
      address: null,
      postcode: null,
      homePhone: null,
      phone: null,
      contactName: null,
      contactRelationship: null,
      contactAddress: null,
      contactPhone: null,

      healthCardType: {},
      gender: {dictValue: null},
      adoType: {},
      marriedStatus: {},
      nationality: {},
      nation: {},
      cardType: {},
      education: {},
      occupation: {},
      chargeType: {},
      ageUnit: {dictValue: 'Y'},
      vipFlag: '0'
    },

    id: null,
    examNo: null,
    examModality: {dictValue: ModalityCodeDef},
    inpType: {dictValue: null},
    examItem: {dictValue: itemCodeDef},
    //equipRoom: {},
    examParts: [],

    //examDoctor: {},
    reqDept: {deptName: null},
    reqDoctor: {},
    inpNo: null,
    inpTimes: null,
    inpWard: {},
    inpRoom: {},
    bedNo: null,
    examDoctorsName: null,
    examDoctorsCode: null,
    ordExecDoctorName: null,
    ordExecDoctorCode: null,

    examCost: null,
    examCostType: {dictValue: null},
    condParting: {dictValue: null},
    greenChannelFlag: null,
    appointTime: null,
    clinicDiagnosis: null,
    allergyHistory: null,
    clinicDisease: null,
    noteInfo: null,
    examPrerequire: null,
    reservedNoUsed: null,
    examAtPm: null,

    admNo: null,
    admSeriesNum: null,
    operationInfo: null,
    applyPath: null,
    ordId: null,ordName: null,arcimCode: null,ordBillStatus: null,
    ordPriorityCode: null,ordPriority: null,examPurpose: null,

    examParts_ids: [],
    examParts_names: null,

    greenChannelFlagValue: false,
    examPrerequireValue: false,
    reservedNoUsedValue: false,
    examAtPmValue: false,
    reportNo:null,
    imageNo:null,

    status: null,
    resultStatus: {
      dictValue: null
    },

    //排队信息：检查房间
    callInfo: {
      callRoom: {
        roomCode: null
      }
    },
    examAge: null,
    examAgeN: null,
    examAgeUnit: {dictValue: 'Y'},
    inspectionOrg: {dictValue: null, extend: {
        extendI1: null
    }},
  }
}

// const examItemDef = {
//   L0405: 'HGJDR',
//   L0401: '9',
//   "300413": 'TCD-desk',
// }

export {emptyRegist};

let timer_load;

const model = {

  name: "PatientRegist",

  dicts: ["uis_regist_way", "sys_yes_no", "uis_exam_modality", "uis_gender_type"
    , "uis_age_unit", "uis_exam_item", "comm_married_status", "uis_inp_type"
    , "comm_occupation", "uis_cond_parting", "uis_inp_ward", "comm_nation", "uis_patient_sens_grade","uis_exam_cost_type"
    , "common_organization_code"],

  components: { ExamPartsPicker, UserPicker, PatientSheet//, Treeselect
    //, ExamEquipRoom
    , EquipRoomStatus, Queue, OrderPicker, DeptPicker, OrderRefund,ReportUploader,ReportViewer },

  mixins: [ResultStatus, TransModel, UndoModel, ExamDataScope, QueueTicketPrint,PatiemtOptions],

  data() {
    return {

      user: {},

      IsEditFlag: false,

      treeSettings: treeProps,

      deptTreeData: [],

      searchForm: {
        registWay: RegistWayDef,
        registCode: null
      },

      registForm: emptyRegist(),
      //当前修改的登记信息副本，新建或取消时对比是否有更改未保存
      registFormCopy: null,

      registFormRules: {
        "patientInfo.name": { required: true, message: '请输入患者姓名', trigger: 'blur' },
        "patientInfo.gender.dictValue": { required: true, message: '请选择患者性别' },
        "examAgeN": { required: true, message: '请输入患者年龄', trigger: 'blur' },
        "patientInfo.registNo": { required: true, message: '请输入患者登记号', trigger: 'blur' },
        "examModality.dictValue": { required: true, message: '请选择检查类型' },
        "inpType.dictValue": { required: true, message: '请选择就诊类别' },
        "examItem.dictValue": { required: true, message: '请选择检查项目' },
        // "examParts_names": { required: true, message: '请选择检查部位' },
        "examCost": {  message: '请请输入检查费用', type: 'float' },
        //"registForm.imageNo": { required: true, message: '请选择检查项目' },
        //"registForm.reportNo": { required: true, message: '请选择检查项目' },
      },

      registFormOpts: {
        combo_equipRoom: [],
        combo_pinyin: []
      },

      loadRemoteProcessing: false,
      submitFormProcessing: false,
      registFormLoading: false,

      patientListActions: tableContextmenuItems(),//[{cmd: 'exam-queue-ticket::print', name: '打印排队小票'}]

      tab: {
        names: {
          examParts: "examParts",
        },
        current:  "examParts"
      },
      showOrdNames:null,
      cacheKey: "PatiemtOptions",
      risrCodeExamItemMap:{},
    };
  },

  methods: {

    getUser() {
      let vm = this;
      getUserProfile().then(response => {
        vm.user = response.data;
        // console.log("user2",this.user);
        // if(this.user.dept.deptCode){
        //   itemCodeDef = examItemDef[this.user.dept.deptCode];
        //   itemCodeDef = undefined==itemCodeDef?"9":itemCodeDef;
        //   this.registForm.examItem.dictValue = itemCodeDef;
        // }

        vm.getConfigKey("detpCustomizeConfig").then(response => {
          if(!response || !response.msg) { return; }
          const cfg = JSON.parse(response.msg);
          const deptCode = vm.user.dept.deptCode;
          const deptConfig = cfg[deptCode];
          if(undefined!=deptConfig&&undefined!=deptConfig.showOrdNames&&"1"===deptConfig.status) {
            vm.showOrdNames = deptConfig.showOrdNames.split("、");
          }
        });
      });
    },

    handleInspectionOrgChange(value) {
      // 找到选中的字典项
      const selectedDict = this.dict.type.common_organization_code.find(
        dict => dict.value === value
      );

      if (selectedDict && selectedDict.raw) {
        if (selectedDict.raw.extend) {
          // 确保 extend 对象存在
          if (!this.registForm.inspectionOrg.extend) {
            this.$set(this.registForm.inspectionOrg, 'extend', {});
          }

          // 设置 extendI1
          this.$set(
            this.registForm.inspectionOrg.extend,
            'extendI1',
            selectedDict.raw.extend.extendI1
          );
        } else if (this.registForm.inspectionOrg) {
          this.registForm.inspectionOrg.extend = null;
        }
      }
      console.log("登记表格", this.registForm)
    },

    processReportNo(str) {
      // 使用正则表达式找到最后一个数字子串
      // 这个正则表达式匹配一个或多个连续数字
      const regex = /(\d+)(?!.*\d)/;

      // 查找匹配项
      const match = str.match(regex);

      if (!match) {
        // 如果没有找到数字子串，直接返回原字符串
        return str;
      }

      // 获取匹配的数字子串
      const numberStr = match[0];
      // 获取匹配的起始位置
      const startIndex = match.index;

      // 将数字子串转换为数字并加1
      const incrementedNumber = parseInt(numberStr, 10) + 1;

      // 将增加后的数字转换回字符串，保持原来的位数（前导零）
      const incrementedNumberStr = incrementedNumber.toString().padStart(numberStr.length, '0');

      // 重新拼接字符串
      return str.substring(0, startIndex) + incrementedNumberStr + str.substring(startIndex + numberStr.length);
    },
    //保存
    submitForm(opt) {
      //console.log("保存登记信息", this.dict.type.common_organization_code)
      const vm = this, regFm = vm.registForm;
      vm.mainForm.registWay = vm.searchForm.registWay;
      vm.mainForm.registFormxamModality = vm.registForm.examModality.dictValue;
      vm.mainForm.registFormInpType = vm.registForm.inpType.dictValue;
      vm.mainForm.registFormExamItem = itemCodeDef = vm.registForm.examItem.dictValue;
      vm.mainForm.inspectionOrg = vm.registForm.inspectionOrg
      this.save();

      // if(auth.hasPermi("report:reportImage:upload")){
      //   if(this.registForm.imageNo==null||this.registForm.imageNo==""){
      //     this.$modal.msgWarning("请输入影像编号");
      //     return;
      //   }
      // }

      // if(auth.hasPermi("report:reportPdf:upload")){
      //   if(this.registForm.reportNo==null||this.registForm.reportNo==""){
      //     this.$modal.msgWarning("请输入报告编号");
      //     return;
      //   }
      // }

      vm.$refs.registForm.validate(valid => {
        if(!valid) {
          this.$modal.msgWarning("请输入/选择标星(*)的信息。");
          return;
        }

        if(RegistWay.manual==this.searchForm.registWay && !vm.registForm.examParts_names){
          this.$modal.msgWarning("手工登记需输入检查部位。");
          return;
        }
        //是否直接提交，跳过一些操作
        let firmed = false;
        if(opt && !(opt instanceof Event)) {
          firmed = opt.firmed;
        }
        //
        if(RegistWay.registNo === regFm.registWay && !vm.searchForm.registCode) {
          regFm.registWay = RegistWay.manual;
        }
        //年龄有效性
        let cfmM, pat = regFm, ageUnit = pat.examAgeUnit  ;
        if(!firmed && !!ageUnit) {
          switch(ageUnit.dictValue) {
            case "Y":
              if(pat.examAgeN > 150) {
                cfmM = "患者年龄超过150岁。";
              }
              break;
            case "M":
              if(pat.examAgeN >= 12) {
                cfmM = "患者年龄已达12个月，相当于" + Math.floor(pat.examAgeN / 12) + "岁。";
              }
              break;
            case "D":
              if(pat.examAgeN >= 365) {
                cfmM = "患者年龄已达365天，相当于" + Math.floor(pat.examAgeN / 365) + "岁。";
              } else if(pat.examAgeN >= 30) {
                cfmM = "患者年龄已达30天，相当于" + Math.floor(pat.examAgeN / 30) + "月。";
              } else if(pat.examAgeN >= 7) {
                cfmM = "患者年龄已达7天，相当于" + Math.floor(pat.examAgeN / 7) + "周。";
              }
              break;
            case "W":
              if(pat.examAgeN >= 5) {
                cfmM = "患者年龄已达5周，相当于" + Math.floor((7 * pat.examAgeN) / 30) + "月。";
              }
              break;
            case "H":
              if(pat.examAgeN >= 24) {
                cfmM = "患者年龄超过24小时，相当于" + Math.floor(pat.examAgeN / 24) + "天。";
              }
              break;
          }
        }

        let cfm = !!cfmM? vm.$modal.confirm(cfmM + "是否返回修改？", {
          confirmButtonText: '继续提交',
          cancelButtonText: '返回修改',
        }) : Promise.resolve(true);

        cfm.then(()=>{
          const pro = () => {
            //执行完成触发
            const cb = res => {
              this.submitFormProcessing = false;
              if(200 == res.code) {
                this.$modal.msgSuccess("操作成功");
                this.refreshModified(dat);
                const dat = res.data;
                if(dat) {
                  //
                  try {
                    const lastVer = cloneDeep(dat);
                    //告知检查信息有更新
                    this.$store.dispatch("lastEdited", lastVer);
                    //打印排队小票
                    if(!regFm.id) {
                      eiapi.examMatchFile(lastVer)
                      this.printQueueTicket(lastVer);
                    }
                  } catch (err) { console.error(err); }
                  //保存后进入新建
                  //if(!regFm.id) {
                    this.handleNew(IGNORECONFIRM);
                  //}
                  //regFm.id = dat.id;
                  //regFm.patientInfo.id = dat.patientInfo?dat.patientInfo.id : null;
                }
                //
                //this.refreshPatientSheet();
              } else {
                this.$modal.msgSuccess(res.msg || "操作失败");
              }
            };
            //
            regFm.greenChannelFlag = regFm.greenChannelFlagValue? 1 : null;
            regFm.examPrerequire = regFm.examPrerequireValue? 0 : null;
            regFm.reservedNoUsed = regFm.reservedNoUsedValue? 1: null;
            regFm.examAtPm = regFm.examAtPmValue? 1: null;

            //提交
            if(regFm.id) {
              //执行更新提交
              const cup = () => {
                this.submitFormProcessing = true;
                //数据加密
                let summitFm = cloneDeep(regFm);
                if(!!summitFm.patientInfo){
                  summitFm.patientInfo.phone = DesUtil.encode(summitFm.patientInfo.phone, config.securityKey)
                  summitFm.patientInfo.cardNo = DesUtil.encode(summitFm.patientInfo.cardNo, config.securityKey)
                }
                eiapi.update(summitFm).then(cb).catch(() => this.submitFormProcessing = false);
              };

              if(!firmed) {
                //医嘱登记的检查部位是否有更改
                const fmc = !!this.registFormCopy? JSON.parse(this.registFormCopy) : null;
                if(!!fmc && fmc.ordId) {
                  const uni = fmc.examParts.filter(ep => -1 === regFm.examParts.findIndex(e => ep.id === e.id)), ordId = regFm.ordId;
                  if(uni.length > 0 && ordId) {
                    //查询已执行医嘱
                    eiapi.loadRemote(RegistWay.registNo, regFm.patientInfo.registNo, "E", ordId).then(res => {
                      const ords = res.data;
                      if(!!ords && ords.length > 0) {
                        this.$refs.orderRefund.show(ords, uni);
                      } else {
                        cup();
                      }
                    });
                    return;
                  }
                }
              }
              //
              cup();
            } else {
              this.submitFormProcessing = true;
              if(!regFm.examDept) {
                // console.log("user",regFm,this.user);
                regFm["examDept"] = {};
                regFm.examDept["deptId"] = this.user.dept.deptId;
                regFm.examDept["deptCode"] = this.user.dept.deptCode;
              }
              //数据加密
              let summitFm = cloneDeep(regFm);
              if(!!summitFm.patientInfo){
                summitFm.patientInfo.phone = DesUtil.encode(summitFm.patientInfo.phone, config.securityKey)
                summitFm.patientInfo.cardNo = DesUtil.encode(summitFm.patientInfo.cardNo, config.securityKey)
              }
              eiapi.save(summitFm).then(cb).catch((err) => {
                if (err.data.isDBSaved) {
                  this.resetRegistForm();
                }
                this.submitFormProcessing = false;
              });
            }
            //
          };
          if(!regFm.id){
            let params = {status:0};
            params["patientInfo"] = {registNo: regFm.patientInfo.registNo}
            params["examItemCodes"] = [regFm.examItem.dictValue];
            params["examModalitiesCodes"] = [regFm.examModality.dictValue];

            let today = new Date();
            // 将小时、分钟、秒和毫秒设置为0，以获取当天的0点
            today.setHours(0, 0, 0, 0);
            params.createTimeGe = today;
            params.createTimeLt = today;

            eiapi.find(params).then(res => {
              if(0===res.rows.length){
                pro();
              }else{
               let examItem = vm.ctrlData.dict.uis_exam_item.find(element => element.value === regFm.examItem.dictValue);
               vm.$modal.confirm("该登记号今天已登记"+res.rows.length+"条 "+examItem.label+" 检查，是否继续登记").then(()=>{pro()})
              }
            });
          }else{
            pro();
          }

        });
      });
    },
    //勾选部位
    handleCheckExamParts(items) {
      let ei = this.registForm, examParts = items;
      //选中部位绑定的机房
      //let equipRoom;
      //if(examParts.length == 1 && (equipRoom = parts.equipRoom) && equipRoom.dictCode) {
      //  ei.equipRoom = cloneDeep(equipRoom);
      //}
      //
      /*if(undefinedOrNull(parts.examCosts)) {
        this.$modal.msg(`"${parts.partsName}"未设置检查费用，可进入"系统管理>其它设置>部位管理"进行设置`);
      }*/
      ei.examParts = examParts;
      ei.examParts_ids = examParts.map(p => p.id);
    },
    //双击取消部位
    uncheckExamParts(parts, pasive) {
      if(false !== pasive) {
        this.$refs["examPartsTree"].setChecked([parts.id], false);
        return;
      }

      let examParts = this.registForm.examParts;
      const idx = examParts.findIndex(p => p.id == parts.id);
      if(-1 != idx) {
        examParts.splice(idx, 1);
      }
    },
    //存入部位
    setExamInfo_examParts() {
      console.warn("未实现 " + setExamInfo_examParts)
    },

    /**
     * 读取检查信息/患者信息
     * @param examInfo 检查
     */
    handleEdit(examInfo) {
      let vm = this;
      const examInfoId = examInfo? examInfo.id : null;
      if(!examInfoId) {
        return;
      }

      //
      //var fm = this.registForm;
      //if(examInfoId == fm.id) {
      //  return;
      //}
      //添加一个标志。watch的时候不弹窗控制
      this.IsEditFlag = false

      this.resetRegistForm();

      this.registFormLoading = true;
      eiapi.get(examInfoId).then(res => {
        this.registFormLoading = false;
        const data = res.data;
        if(!data) {
          return;
        }
        //
        let cb = () => {
          data.patientInfo.ageDisable = false;
          if(!data.examAge) data.examAge = calculateAge(data.patientInfo.birthday,data.createTime);
          //数据解密
          if(!!data.patientInfo){
            data.patientInfo.phone = DesUtil.decode(data.patientInfo.phone, config.securityKey)
            data.patientInfo.cardNo = DesUtil.decode(data.patientInfo.cardNo, config.securityKey)
          }
          this.fillRegistForm(data);
          //暂存副本
          this.registFormCopy = JSON.stringify(this.registForm);
        };
        //
        if(2 === data.status) {
          this.$modal.confirm('该检查已被删除，是否继续编辑？').then(cb);
        } else {
          cb();
        }

      }).catch(() => this.registFormLoading = false);
    },
    //
    resetRegistForm() {
      //Object.assign(this.registForm, emptyRegist());
      this.registForm = emptyRegist();
      // 如果有上次的送检单位，则填入
      this.registForm.inspectionOrg = this.mainForm.inspectionOrg
      if (!this.registForm.ordExecDoctorCode && this.user) {
        this.registForm.ordExecDoctorCode = this.user.userName;
        this.registForm.ordExecDoctorName = this.user.nickName;
      }

      this.registFormCopy = null;
    },
    //
    fillRegistForm(dat) {
      if(dat) {
        let fm = this.registForm;
        //
        if(dat.examParts) {
          let examParts_ids = [], examParts_names = [];
          dat.examParts.forEach(p => {
            examParts_ids.push(p.id);
            examParts_names.push(p.partsName);
          });
          fm.examParts_ids = examParts_ids;
          fm.examParts_names = examParts_names.join(",");
        }
        dat.greenChannelFlagValue = !!dat.greenChannelFlag;
        dat.examPrerequireValue = 0 === dat.examPrerequire;
        dat.reservedNoUsedValue = !!dat.reservedNoUsed;
        dat.examAtPmValue = 1 === dat.examAtPm;
        fm.reportNo = dat.reportNo
        if (!fm.ordExecDoctorCode && this.user) {
          fm.ordExecDoctorCode = this.user.userName;
          fm.ordExecDoctorName = this.user.nickName;
        }
        mergeWithDeep(fm, dat, null, mergeWithNotNull);
        //Object.assign(this.registForm, fm);
        //console.log(this.registForm);
        this.convToPinyin();
      }
    },
    //新建
    handleNew(mix) {
      //
      const reregist = () => {
        this.searchForm.registCode = null;
        this.resetRegistForm();
        this.$router.push({name: this.$route.name});
      };
      //let prom = 'ignoreConfirm' === mix? Promise.resolve(true) : this.$modal.confirm("是否确定重新登记？");
      //prom.then(() => {
        if(IGNORECONFIRM === mix) {
            reregist();
        }
        //else if(JSON.stringify(this.registForm) != JSON.stringify(emptyRegist())){
        else if(this.checkModified()) {
          this.$modal.confirm("当前信息未保存，是否新建？").then(reregist)
        } else {
          reregist();
        }
        //this.$router.push({path: "/exammanagement/PatientList"});
      //});
    },
    /**
     * 取消登记/清空登记信息
     */
    handleCancel() {
      if(this.checkModified()) {
        this.$modal.confirm("有信息更新，是否需要保留之前的数据？").then(this.submitForm).catch(() => { this.handleNew(IGNORECONFIRM); });
      } else {
        this.handleNew(IGNORECONFIRM);
      }

      return;
      let item = this.registForm;
      //
      this.$modal.confirm("是否确定取消？").then(() => {
        if(item.id) {
          this.updateResultStatus(item, StatusDict.cancel).then(res => {
            if(res && 200 === res.code) {
              //重载表单
              this.refreshModified(item);
            }
          });
        } else {
          this.handleNew();
        }
      });
    },
    //
    handleDelete(mix) {
      let fm = this.registForm, item = !!mix && (mix instanceof Event)? fm : mix;
      if(item.id) {
        /*this.$modal.confirm("是否确定删除？").then(() => {
          return eiapi.del(item.id)
        }).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(item);
          }
        });*/
        this.$refs.patientSheet.verifyForDelete(item);
      }
    },
    undoDelete() {
      const fm = this.registForm;
      if(fm.id) {
        this.handleUndoDelete(fm).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    undoCancel() {
      const fm = this.registForm;
      if(fm.id) {
        this.handleUndoCancel(fm).then(res => {
          if(res && 200 === res.code) {
            this.refreshModified(fm);
          }
        });
      }
    },
    /**
     * 用户选择框
     * @param tar 触发选择的属性/表单元素
     * @param posts 过滤用户岗位
     */
    toPickUser(tar, posts) {
      this.$refs["userPicker"].showPicker({target: tar, posts});
    },
    /**
     * 选择的用户
     * @param tar 触发选择的属性/表单元素
     * @param usr 选择的用户
     */
    pickUser(tar, usr) {
      //console.log(usr);
      const fm = this.registForm;

      if(tar) {
        if('examDoctors' === tar || 'ordExecDoctor' === tar) {
          fm[`${tar}Code`] = usr.userName;
          fm[`${tar}Name`] = usr.nickName;
        } else {
          fm[tar] = usr;
        }
      }
    },
    //读取部门树信息
    //buildDeptTree() {
    //  deptTreeselect().then(res => {
    //    this.deptTreeData = res.data;
    //  });
    //},

    /**
     * 调用接口读取登记信息
     */
    loadRemote() {
      let vm= this;
      const sfm = this.searchForm, registWay = sfm.registWay;
      let registCode = sfm.registCode;
      if(!registWay || !registCode) {
        this.$modal.alert("请按登记方式输入.");
        return;
      }
      console.log("loadr",registWay);
      //登记号，增加补0到10位
      if(registCode.length<10&&registWay==RegistWay.registNo){
          registCode = registCode.padStart(10,'0');
          this.searchForm.registCode=registCode
      }
      this.resetRegistForm();
      this.IsEditFlag = true
      this.loadRemoteProcessing = true;

      eiapi.loadRemote(registWay, registCode).then(res => {
        this.loadRemoteProcessing = false;

        let items = res.data;
        if(!items || !items.length) {
          this.$modal.msgWarning("没有相关数据.");
          return;
        }

        if(undefined!=vm.showOrdNames&&vm.showOrdNames.length>0){
          var data = [];
          items.forEach(e=>{
            var incluV = null;
            vm.showOrdNames.forEach(i=>{
              if(e.ordName.indexOf(i) != -1 ){
                incluV = e;
              }
            });
            if(incluV) data.push(incluV);
          })

          items = data;
        }

        //触发一些更新，如数据字典，科室信息，检查部位
        const modified = res.modified;
        if(modified) {
          modified.forEach(mod => {
            if("dept" === mod) {
              //this.buildDeptTree();
            } else if(0 === mod.indexOf("dict::")) {
              this.dict.reloadDict(mod.substring(6));
            } else if("examParts" === mod) {
              //this.$refs["examPartsTree"].buildTree();
            }
          });
        }
        //
        if(items.length === 1) {
          // if(!exam.examItem) { exam.examItem = {dictValue: itemCodeDef}; }
          const examItemMap = vm.getExamItenByRisrCode(items[0])
          if(!!examItemMap) {
            //配置检查项目与RISR编码的映射关系
            items[0].examItem = {dictValue: examItemMap};
          }else{
            //没有配置检查项目与RISR编码的映射关系，且有批量登记权限
            if(auth.hasPermi(BATCH_REGIST)&&!!this.ctrlData&&!!this.ctrlData.dict) {
              let examIten = this.ctrlData.dict.uis_exam_item;
              if (examIten && Array.isArray(examIten)) {
                let examItenFil = examIten.filter(item => (item.raw && item.raw.extend && item.raw.extend.extendI1 === 1));
                if(examItenFil.length>0){
                  items[0].examItem = {dictValue: examItenFil[0].value};
                }
              }
            }

          }
          items[0].ordIds = items[0].ordId? [items[0].ordId] : [];
          this.pickOrd(items[0]);
          return ;
        }
        //alert(items.length);
        this.$refs.ordPicker.show(items);
      }).catch(() => this.loadRemoteProcessing = false);
    },

    getExamItenByRisrCode(exam){
      const risrCode = exam.arcimCode
      if(!risrCode) return null;
      // 获取对象的所有键
      const keys = Object.keys(this.risrCodeExamItemMap);

      // 过滤出包含 的键
      const filteredKeys = keys.filter(key => risrCode.includes(key));

      // 获取这些键对应的值
      const values = filteredKeys.map(key => this.risrCodeExamItemMap[key]);
      if(values.length>0){
        return values[0];
      }
      return null;
    },

    buildExam(exam){
      let vm = this;
      exam.medicalRecordNo = null;

      if(!exam.examModality) { exam.examModality = {dictValue: ModalityCodeDef}; }
      // if(!exam.examItem) { exam.examItem = {dictValue: itemCodeDef}; }

      //接口过来岁数为0，MT计算年龄
      if(0==exam.patientInfo.age){
        exam.patientInfo.age = getAge(parseTime(exam.patientInfo.birthday, "{y}-{m}-{d}")).age
        exam.patientInfo.ageUnit = {dictValue:getAge(parseTime(exam.patientInfo.birthday, "{y}-{m}-{d}")).ageUint};
      }
      if(!exam.examAge) exam.examAge = calculateAge(exam.patientInfo.birthday,DateUtil.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));


        let fm = emptyRegist();
        //
        if(exam.examParts) {
          let examParts_ids = [], examParts_names = [];
          exam.examParts.forEach(p => {
            examParts_ids.push(p.id);
            examParts_names.push(p.partsName);
          });
          fm.examParts_ids = examParts_ids;
          fm.examParts_names = examParts_names.join(",");
        }
        exam.greenChannelFlagValue = !!exam.greenChannelFlag;
        exam.examPrerequireValue = 0 === exam.examPrerequire;
        exam.reservedNoUsedValue = !!exam.reservedNoUsed;
        exam.examAtPmValue = 1 === exam.examAtPm;

        mergeWithDeep(fm, exam, null, mergeWithNotNull);

        const examItemMap = vm.getExamItenByRisrCode(exam)
        fm.examItem = {dictValue: examItemMap};
        // if(!examItemMap) fm.examModality = {dictValue: "Consumables"};
        //Object.assign(this.registForm, fm);
        //console.log(this.registForm);
        const pat = fm.patientInfo;
        const patName = !!pat? pat.name : null;

        //先不处理else 先不处理没有保存权限
        // if(!!patName) {
        //   toPinyin(patName).then(res => {
        //     const items = res.data || [];
        //     if(!!pat) { pat.namePingyin = items[0]; }


        //       // if(!fm.examDept) {
        //       //   // console.log("user",regFm,this.user);
        //       //   fm["examDept"] = {};
        //       //   fm.examDept["deptId"] = this.user.dept.deptId;
        //       //   fm.examDept["deptCode"] = this.user.dept.deptCode;
        //       // }
        //       //数据加密
        //       let summitFm = cloneDeep(fm);
        //       if(!!summitFm.patientInfo&&vm.patientPIIEncryptionOnOff){
        //         summitFm.patientInfo.phone = DesUtil.encode(summitFm.patientInfo.phone, config.securityKey)
        //         summitFm.patientInfo.cardNo = DesUtil.encode(summitFm.patientInfo.cardNo, config.securityKey)
        //       }
        //       // 成功先补处理 eiapi.save(summitFm).then(cb).catch(() => {});
        //       eiapi.save(summitFm).then().catch(() => {});

        //   })
        // }

        let summitFm = cloneDeep(fm);
        if(!!summitFm.patientInfo){
          summitFm.patientInfo.phone = DesUtil.encode(summitFm.patientInfo.phone, config.securityKey)
          summitFm.patientInfo.cardNo = DesUtil.encode(summitFm.patientInfo.cardNo, config.securityKey)
        }
        // 成功先补处理 eiapi.save(summitFm).then(cb).catch(() => {});
        return eiapi.save(summitFm);

    },

    pickOrdAr(examAr){
      let vm = this;
      if(Object.keys(vm.risrCodeExamItemMap) == 0){
        this.$modal.msgError("请先配置检查项目与RISR编码的映射关系！");
        return
      }
      console.log("pickOrdAr",examAr);
      if(!examAr) return
      const saveExamPromises = Array.from(examAr).map(
        exam => {return vm.buildExam(exam)}
      )
      Promise.allSettled(saveExamPromises).then(res => {
        console.log("res",res);
        if (!res || res.length === 0) {
          console.log("res不存在或为空，返回examAr");
          return examAr;
        }
        const filteredKeys = examAr.filter(exam => {
          return !res.some(r => r.status === "fulfilled" && r.value.data.ordId === exam.ordId);
        });
        if (filteredKeys.length > 0) {
          const ordNames = filteredKeys.map(exam => exam.ordName).join(', ');
          this.$modal.msgError("医嘱：" + ordNames + ` 登记异常，请检查！`);
          console.log("filteredKeys",filteredKeys);
        }
      }).catch(error => {
        console.error("error",error); // 这里不会执行
      });
      // examAr.forEach(exam => {
      //   vm.buildExam(exam).then().catch(() => {});
      // });
    },

    pickOrd(exam) {
      //不使用其病历号
      exam.medicalRecordNo = null;

      this.resetRegistForm();
      if(!exam.examModality) { exam.examModality = {dictValue: ModalityCodeDef}; }
      if(!exam.examItem) { exam.examItem = {dictValue: itemCodeDef}; }
      //接口过来岁数为0，MT计算年龄
      if(0==exam.patientInfo.age){
        exam.patientInfo.age = getAge(parseTime(exam.patientInfo.birthday, "{y}-{m}-{d}")).age
        exam.patientInfo.ageUnit = {dictValue:getAge(parseTime(exam.patientInfo.birthday, "{y}-{m}-{d}")).ageUint};
      }
      if(!exam.examAge) exam.examAge = calculateAge(exam.patientInfo.birthday,DateUtil.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
      this.fillRegistForm(exam);
    },
    //取消医嘱
    refundOrd(rows) {
      if(!!rows && rows.length > 0) {
        let fm = this.registForm, ordId = fm.ordId;
        if(ordId) {
          let sym = "@", ordsId = ordId.split(sym);
          for(let i = ordsId.length - 1; i >= 0; i --) {
            if(-1 !== rows.findIndex(r => r.ordId === ordsId[i])) {
              ordsId.splice(i, 1);
            }
            ordId = ordsId.join(sym);
            fm.ordId = ordId;
          }
        }
      }

      this.submitForm({firmed: true});
    },
    //页面加载
    prepareLoad() {
      //当前用户信息
      if (Object.keys(this.user).length === 0) {
        getUserProfile().then((res) => {
          this.user = res.data;
          this.resetRegistForm();
        });
      }
      clearTimeout(timer_load);

      timer_load = setTimeout(this.onLoad, 400);
    },
    onLoad() {
      let params = this.$route.params;
      //console.log(params);
      if(params && params.id) {
        this.handleEdit(params);
      }
      //
      this.layoutPage();
    },

    //改变年龄单位
    handleChangeAgeUnit () {
        this.handleChangeAge(this.registForm.examAgeN)
    },
    //改变年龄
    handleChangeAge (val) {
        const fm = this.registForm;
        fm.examAge = fm.examAgeN + fm.examAgeUnit.dictValue;
        //只有手工登记能输入年龄，其他不能输入。输入年龄时，动态改变生日
        //if (!fm.patientInfo.birthday) {
        const date = currDatetime()
        let bday = date
        switch (fm.examAgeUnit.dictValue) {
            case "Y":
                bday = dateAdd(date, -1 * parseInt(val), "y");
                break
            case "M":
                bday = dateAdd(date, -1 * parseInt(val), "m");
                break
            case "W":
                bday = dateAdd(date, -1 * parseInt(val), "w");
                break
            case "D":
                bday = dateAdd(date, -1 * parseInt(val), "d");
                break
            case "H":
                bday = dateAdd(date, -1 * parseInt(val), "h");
                break
        }


        fm.patientInfo.birthday = parseTime(bday);
        // }

    },
    //改变年龄
    //handleChangeAge(val) {
    //  const fm = this.registForm;
    //  if(!fm.patientInfo.birthday) {
    //    const date = currDatetime(), bday = dateAdd(date, -1 * parseInt(val), "y");
    //    fm.patientInfo.birthday = parseTime(bday);
    //  }
    //},

    //选择生日
    handleChangeBirthday(val) {
      const fm = this.registForm;
      if(undefinedOrNull(fm.patientInfo.age)) {
        const date = currDatetime(), age = date.getFullYear() - val.getFullYear();
        if(age > 0) {
          fm.patientInfo.age = age;
        }
      }
    },
    //全选输入框文本
    handleFocusRegistCode($evt) {
      const el = $evt.target || $evt.srcElement;
      el.select();
    },
    //点击患者列表菜单
    handlePatientListAction(cmd, item) {
      //console.log(cmd, item);
      switch(cmd) {
        case 'exam::edit':
          this.handleEdit(item);
          break;
        case 'exam::del':
          this.handleDelete(item);
          break;
        case 'exam::precond':
        case 'exam::at-pm':
          let props;
          if('exam::precond' == cmd) {
            props = {examPrerequire: 0};
          } else {
            props = {examAtPm: 1};
          }
          this.handleTrans(item, props).then(res => {
            if(res && 200 === res.code) {
              this.refreshModified(item);
            }
          });
          break;
        //case 'exam::equip-room-change':
        //  this.$refs.examEquipRoom.change(item);
        //  break;
        case 'report::write':
          let toRoute = "MTReportWriting";
          if(!!item.examModality && RAD_MODALITIES.includes(item.examModality.dictValue)) {
            toRoute = "RadReportWriting";
          }
          if(!!item.examModality && DS_MODALITIES.includes(item.examModality.dictValue)) {
            toRoute = "dsReportWriting";
          }else if (!!item.examModality && TCD_MODALITIES.includes(item.examModality.dictValue)) {
            toRoute = "ReportWriting";
          }

          if(!!item.examModality && OTOL_MODALITIES.includes(item.examModality.dictValue)) {
            toRoute = "MTReportWriting";
          }
          console.log("toRoute",toRoute);
          this.$router.push({name: toRoute, params: {report: item}});
          break;
        case 'exam::copy':
          document.execCommand('Copy','false',null);
          break;
        case 'exam::traceCase':
          this.$router.push({ name: "TraceCaseAddEdit", params: {"examNo": item.examNo}});
          break;
        case 'exam-queue-ticket::print':
          this.printQueueTicket(item);
          break;
        case 'report::upload':
          this.$refs.reportUploader.prepare(item);
          break;
        case 'report::viewer':
          this.$refs.reportViewer.view(item);
          break;
        case 'exam::dicomView':
          let toRoute2 = 'RadReportWriterImage'
          this.$router.push({name: toRoute2, query: {exam: item.id}});
          //console.log("ReportWriting.js-exam::traceCase-item: ", row);
          //this.$router.push({ name: "TraceCaseAddEdit", params: {"examNo": row.examNo}});
          break;
        default:
          console.warn("未实现：", cmd);
      }
    },

    // handleResbackImage() {
    //   const rep = this.reportForm;
    //   if(!rep || !rep.id) {
    //     this.$modal.alert("请选择报告。");
    //     return;
    //   }
    //   this.$refs.reportImageUploader.prepare(rep);
    // },

    //刷新患者列表
    refreshPatientSheet() {
      this.$refs.patientSheet.getList();
    },
    //更相关数据
    refreshModified(item) {
      const fm = this.registForm;
      if(item && fm.id == item.id) {
        this.handleEdit(item);
      }
      this.refreshPatientSheet();
    },
    //布局
    layoutPage() {
      try {
        const eptp = document.querySelector(".examPartsTreePane");
        if(eptp) {
          eptp.style.height = (document.querySelector(".patient-info-card .el-card__body").clientHeight - 4 * 2) + "px";
        }
      } catch (err) { console.error(err); }
    },
    //机房列表
    findEquipRoom() {
      findRoom({}).then(res => {
        const equipRooms = res && res.rows || [];
        this.registFormOpts.combo_equipRoom = equipRooms;
        this.applyDataCtrl(DataCtrlDict.ItemType.room, equipRooms);
      });
    },
    //选择检查部位
    pickExamParts() {
      const fm = this.registForm, examMod = fm.examModality, examItem = fm.examItem, inpType = fm.inpType;
      const params = {modalityCode: (examMod? examMod.dictValue : null)
        , examItem: examItem
        , inpTypesCode: (inpType? inpType.dictValue : null)};
      //
      if(params.inpTypesCode) {
        params.inpType = this.dict.type.uis_inp_type.find(t => t.raw.dictValue === params.inpTypesCode).raw;
      }

      this.$refs.examPartsPicker.findData(params);
    },

    isOuterOrg() { //是否外院
      return this.inspectionOrg
      && this.inspectionOrg.dictValue
      && (!this.inspectionOrg.extend || (this.inspectionOrg.extend && this.inspectionOrg.extend.extendI1 !== 1));
    },
    getRegistNo() {
      if (this.isOuterOrg() && this.registForm.patientInfo.registNo) {
        return this.inspectionOrg.dictValue + '-' + this.registForm.patientInfo.registNo;
      }
      return this.registForm.patientInfo.registNo;
    },
    removePrefix(str, prefix) {
      const regex = new RegExp(`^${prefix}-`); // 构造正则，匹配“prefix-”开头的内容
      return str.replace(regex, ''); // 替换匹配到的部分为空
    },
    //查询登记号是否已登记
    findPatient() {
      let vm = this;
      const fm = this.registForm;
      if(!fm.patientInfo || !fm.patientInfo.registNo) {
        return;
      }
      api.find({registNo: this.getRegistNo()}).then(res => {
        //console.log(res);
        if(res.total === 1&&RegistWay.manual==this.searchForm.registWay) {
          const row = res.rows[0];

          row.age = getAge(parseTime(row.birthday, "{y}-{m}-{d}")).age
          row.ageUnit.dictValue = getAge(parseTime(row.birthday, "{y}-{m}-{d}")).ageUint

          this.dict.type.uis_age_unit.forEach(res => {
              if (res.value == row.ageUnit.dictValue) {
                row.ageUnit.dictLabel = res.label
              }
          })

          const m = "<div>根据该登记号找到患者信息如下，是否使用？</div>"
            + "<div>" + row.name
            + "，" + (row.gender? row.gender.dictLabel : "")
            + "，" + (row.age) + (row.ageUnit? row.ageUnit.dictLabel : "") + "</div>"
          this.$confirm(m, "确认", {
            dangerouslyUseHTMLString: true
          }).then(() => {
            if(RegistWay.manual==this.searchForm.registWay){
              fm.examAge = row.age + row.ageUnit.dictValue;
            }
            //fm.examAge = calculateAge(row.birthday);
            //数据解密
            if(!!row){
              row.phone = DesUtil.decode(row.phone, config.securityKey)
              row.cardNo = DesUtil.decode(row.cardNo, config.securityKey)
            }
            mergeWithDeep(fm.patientInfo, row, null, mergeWithNotNull);
            this.convToPinyin();
          });
        } else if(res.total > 1) {
          this.$modal.msgWarning(`找到${res.total}条该登记号的患者信息，请核查登记信息。`);
        }
      });
    },
    //选择科室
    pickDept() {
      this.$refs.deptPicker.findData();
    },
    //选择科室
    handleCheckDept(dept) {
      this.registForm.reqDept = cloneDeep(dept);
    },
    delayConvToPinyin() {
      clearTimeout(timer_convToPinyin);
      timer_convToPinyin = setTimeout(this.convToPinyin, 1000);
    },
    //转换拼音
    convToPinyin() {
      clearTimeout(timer_convToPinyin);

      const fm = this.registForm, pat = fm.patientInfo;
      const patName = !!pat? pat.name : null;
      this.registFormOpts.combo_pinyin = [];
      if(!!pat && pat.namePingyin) {
        this.registFormOpts.combo_pinyin.push(pat.namePingyin);
      }
      if(!!patName) {
        toPinyin(patName).then(res => {
          const items = res.data || [];
          this.registFormOpts.combo_pinyin = items;
          if(!!pat) { pat.namePingyin = items[0]; }
        })
      }
    },
    //登记信息是否有更改
    checkModified() {
      return !!this.registFormCopy && JSON.stringify(this.registForm) !== this.registFormCopy;
    },

    registCodeInput(number){
      if(this.searchForm.registWay==RegistWay.card&&15==number.length){
        this.loadRemote();
      }
    },
     calculateAgeTest(birthdate) {
      const birthday = new Date(birthdate);
      const now = new Date("2024-11-10 15:09");
      let age = {};

      // 计算年份差
      age.years = now.getFullYear() - birthday.getFullYear();

      // 如果当前日期还没到今年的生日，则年份差减1
      if (now.getMonth() < birthday.getMonth() ||
          (now.getMonth() === birthday.getMonth() && now.getDate() < birthday.getDate())) {
          age.years--;
      }

      // 计算月份差
      age.months = now.getMonth() - birthday.getMonth();
      // 如果当前月份还没到出生月份，则月份差减1
      if (age.months < 0) {
          age.months += 12;
          //age.years--;
      }

      // 计算日期差
      age.days = now.getDate() - birthday.getDate();
      // 如果当前日期还没到出生日，则日期差减1
      if (age.days < 0) {
          // 获取上个月的最后一天
          const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
          age.days += lastDayOfMonth;
          age.months--;
      }

      // 计算小时差
      age.hours = now.getHours() - birthday.getHours();
      // 如果当前小时还没到出生小时，则小时差减1
      if (age.hours < 0) {
          age.hours += 24;
          age.days--;
      }

      // 计算分钟差
      age.minutes = now.getMinutes() - birthday.getMinutes();
      // 如果当前分钟还没到出生分钟，则分钟差减1
      if (age.minutes < 0) {
          age.minutes += 60;
          age.hours--;
      }

      console.log(`您现在的年龄是：${age.years}岁 ${age.months}月 ${age.days}天 ${age.hours}小时 ${age.minutes}分钟`);
      return age;
    }
  },

  activated() {
    this.prepareLoad();
    this.$refs.registCodeInput.focus();
  },

  created() {
    let vm = this;

    vm.getConfigKey("risrCodeExamItemMap").then(response => {
      if(!response || !response.msg) { return; }
      vm.risrCodeExamItemMap = JSON.parse(response.msg);
      console.log("risrCodeExamItemMap",vm.risrCodeExamItemMap);
    });

    vm.findEquipRoom();
    vm.getUser();
    // this.$refs.registCodeInput.focus();
  },

  mounted() {
    this.prepareLoad();
    //
    window.addEventListener("resize", this.layoutPage);
    this.$refs.registCodeInput.focus();

    this.mainForm = this.read();
    console.log("regist-mainForm-read",this.read());
    if(undefined!=this.mainForm.registWay) {
      this.searchForm.registWay = this.mainForm.registWay
      RegistWayDef = this.mainForm.registWay;
    }

    if(undefined!=this.mainForm.registFormxamModality) {
      this.registForm.examModality.dictValue = this.mainForm.registFormxamModality
    }

    if(undefined!=this.mainForm.registFormExamItem) {
      this.registForm.examItem.dictValue = this.mainForm.registFormExamItem
    }

    if(undefined!=this.mainForm.registFormInpType) {
      this.registForm.inpType.dictValue = this.mainForm.registFormInpType
    }

    if (!this.registForm.inspectionOrg) {
      this.registForm.inspectionOrg = this.mainForm.inspectionOrg;
    }

    if (!this.registForm.reportNo) {
      this.registForm.reportNo = this.mainForm.originalReportNo
    }

  },

  watch: {
    //选择的部位更改
    "registForm.examParts": {
      deep: true,
      handler(newVal, oldVal) {
        let examParts_names = [], examCost = 0;
        if(newVal && newVal.length) {
          newVal.forEach(p => {
            //拼接部位名称
            examParts_names.push(p.partsName);
            //费用计算
            let examCost0 = parseFloat(p.examCosts);
            if(!isNaN(examCost0)) {
              examCost = addNum(examCost, examCost0);
            }
          });
        }

        const eifm = this.registForm;
        eifm.examParts_names = examParts_names.join(",");
        //费用，医嘱登记的检查不计算
        if(!eifm.ordId) {
          eifm.examCost = examCost;
        }

        if(eifm.examParts_names==''){
            eifm.examParts_names=null
        }
        if(eifm.examCost=='0'){
            eifm.examCost=null//用于新建时， this.registForm, emptyRegist() 对比
        }
      }
    },
    //登记号改变
    "registForm.patientInfo.registNo": {
        deep: true,
        handler (newVal, oldVal) {
            //console.log(newVal, oldVal, this.IsEditFlag)
            if (oldVal != newVal && this.IsEditFlag && newVal) {
                this.findPatient()
                this.IsEditFlag = false
            }
        }
    },

    /**
     * 勾选/取消勾选"绿色通道"
     */
    "registForm.greenChannelFlagValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.greenChannelFlag = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"诊前检查"
     */
    "registForm.examPrerequireValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examPrerequire = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"启用保留号"
     */
    "registForm.reservedNoUsedValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.reservedNoUsed = nv? 1 : null;
      }
    },

    /**
     * 勾选/取消勾选"下午检查"
     */
    "registForm.examAtPmValue": {
      deep: true,
      handler(nv, ov) {
        this.registForm.examAtPm = nv? 1 : null;
      }
    },

    /**
     * 切换登记方式
     */
    "searchForm.registWay": {
      deep: true,
      handler(nv, ov) {
        RegistWayDef = nv;
        //this.$modal.confirm("是否清空'基本信息'和'检查信息'所有数据？").then(this.resetRegistForm);
        this.resetRegistForm();
        if (nv === RegistWay.manual) {
          this.registForm.patientInfo.ageDisable = false
          this.$refs.registNoInput.focus();
        }else{
          this.$refs.registCodeInput.focus();
        }
      }
    },

    /**
     * 检查类型字典取值后执行
     */
    "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        let items = [];
        if(nv && nv.length) {
          //层级
          nv.forEach(e => {
            items.push(e);
            let d = e.raw;
            if(d.children && d.children.length) {
              d.children.forEach(c => {
                items.push({value: c.dictCode, label: ("--" + c.dictLabel), raw: c});
              });
            }
          });
        }
        //
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, items);
      }
    },

    "ctrlData.dict.uis_exam_modality":{
      deep: true,
      handler(nv, ov) {
        if(0!=nv.length){
          let incluS = nv.filter(e=>e.value==this.mainForm.registFormxamModality);
          this.registForm.examModality.dictValue = incluS.length>0?incluS[0].value:nv[0].value;
          ModalityCodeDef = this.registForm.examModality.dictValue;
        }
      }
    },

    "registForm.examModality":{
      deep: true,
      handler(nv, ov) {
        let modalityValue = modalityMapItem[nv.dictValue];
        if(undefined!=modalityValue){
          this.ctrlData.dict.uis_exam_item.forEach(e=>{
            if(e.value==modalityValue) this.registForm.examItem.dictValue = modalityValue;
          });
        }
      }
    },



    /**
     * 就诊类型字典取值后执行
     */
    "dict.type.uis_inp_type": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    "ctrlData.dict.uis_exam_item":{
      deep: true,
      handler(nv, ov) {
        if(undefined!=nv&&0!=nv.length){
          let incluS = nv.filter(e=>e.value==this.mainForm.registFormExamItem);
          this.registForm.examItem.dictValue = incluS.length>0?incluS[0].value:nv[0].value;
          itemCodeDef = this.registForm.examItem.dictValue;
        }
      }
    },

    "registForm.examItem.dictValue": {
      deep: true,
      handler(nv, ov) {
        if (nv) {
          eiapi.getMaxReportNo(nv)
            .then(res => {
              if (!this.registForm.id) {
                this.registForm.reportNo = res.data;
                if (res.data) {
                  this.registForm.reportNo = this.processReportNo(res.data);
                }
              }
            });
        }
      }
    },

    "registForm.examAge":{
      deep: true,
      handler(nv, ov) {
        if(!!nv){const examAge = nv;
          const regex = /\d+/;
          const match = examAge.match(regex);
          this.registForm.examAgeN = match ? parseInt(match[0], 10) : null;

          const regexUnit = /[YMWDHm]/;
          const matchUnit = examAge.match(regexUnit);
          this.registForm.examAgeUnit = {dictValue:matchUnit ? matchUnit[0] : "Y"};
        }

      }
    },
  },

  computed: {
    getOriginRegistNo: {
      get() {
        if(this.isOuterOrg() && this.registForm.patientInfo.registNo) {
          let registNo = this.removePrefix(this.registForm.patientInfo.registNo, this.inspectionOrg.dictValue);
          this.registForm.patientInfo.registNo = registNo;
          return registNo;
        }
        return this.registForm.patientInfo.registNo;
      },
      set(value) {
        this.registForm.patientInfo.registNo = value;
      }
    },
    inspectionOrg: {
      get() {
        console.log("registForm is: ", this.registForm)
        let inspectionOrg = this.registForm.inspectionOrg ? this.registForm.inspectionOrg : null;
        console.log("inspectionOrg: ", inspectionOrg)
        return inspectionOrg;
      },
      set(value) {
        // 当清除按钮点击时，value 会是 null
        if (!this.registForm.inspectionOrg) {
          this.$set(this.registForm, 'inspectionOrg', {dictValue: null, extend: {extendI1: null}});
        }
        this.$set(this.registForm.inspectionOrg, 'dictValue', value);

        // 如果清除了值，也清除extend信息
        if (value === null && this.registForm.inspectionOrg.extend) {
          this.$set(this.registForm.inspectionOrg.extend, 'extendI1', null);
        }
      }
    },

    inspectionOrgCode() {
      if (this.isOuterOrg()) {
        return this.inspectionOrg.dictValue + '-';
      }
      return "";
    },

    patientSheetType() {
      return PATIENTSHEETTYPE ;
     },
    /**
     * 勾选/取消勾选绿色通道
     */
    regNoReadonly() {
      const sfm = this.searchForm, rfm = this.registForm;
      return RegistWay.manual !== sfm.registWay || !!rfm.id;
    },
    //是否可删除
    deleteEnabled() {
      const fm =  this.registForm;
      return fm.id && 2 !== fm.status;
    },
    //是否可撤销删除
    undoDeleteEnabled() {
      const fm =  this.registForm;
      return fm.id && 2 === fm.status;
    },
    //是否可撤销取消
    cancelEnabled() {
      const fm =  this.registForm;
      return fm.id && (!fm.resultStatus || !fm.resultStatus.dictValue || StatusDict.cancel !== fm.resultStatus.dictValue);
    },
    //是否可撤销取消
    undoCancelEnabled() {
      const fm =  this.registForm;
      return fm.id && !!fm.resultStatus && StatusDict.cancel === fm.resultStatus.dictValue;
    },
    //是否可编辑
    editEnabled() {
      const fm = this.registForm, resultStatus = fm.resultStatus, resultStatusCode = !!resultStatus? resultStatus.dictValue : null;
      return !resultStatusCode || /^[012]$/.test(resultStatusCode);
    },
    //是否禁止编辑年龄
    ageDisabled() {
      const sfm = this.searchForm, rfm = this.registForm;
      //非人工登记、非编辑记录时禁止编辑
      return RegistWay.manual !== sfm.registWay && !rfm.id;
    },
    //是否禁止号码输入框
    registCodeInputDisabled() {
      const sfm = this.searchForm, rfm = this.registForm;
      //非人工登记、非编辑记录时禁止编辑
      return RegistWay.manual === sfm.registWay ;
    }
  }

};

export default model;
