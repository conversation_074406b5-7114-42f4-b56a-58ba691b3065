import {cloneDeep, mergeWith} from "lodash";

import auth from '@/plugins/auth'

import {mergeWithNotNull, undefinedOrNull, fmt_exam_age, currDate} from "@/utils/common";
//接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api.js";
//
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
//编辑
import {EditModel, UndoModel, TransModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from '@/components/Contextmenu';
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth"
//查询偏好配置
import PatientListSearchOptions from '@/views/pacs/report/comp/PatientListSearchOptions';
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import ReportUploader from "@/views/uis/report/comp/ReportUploader";
import ReportUploaderBatch from "@/views/uis/report/comp/ReportUploaderBatch";

import ReportViewer from "@/views/uis/report/comp/ReportViewer";

import {StatusDict, matchAny as matchAnyStatus} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import {ResultStatusModel as ResultStatus} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

import {getUserProfile} from "@/api/system/user";

import {SearchOptions} from "@/assets/scripts/pacs/report/mixins";

//扫码认证
import {default as LinksignPopup} from "@/views/pacs/linksign/Popup";

import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel"

//操作权限
import {default as PermiCheck, PERMI_AUDIT, PERMI_SECOND_AUDIT, PERMI_THIRD_AUDIT} from "@/directive/permission/mixins";

//科室信息
import {treeselect as deptTreeselect} from "@/api/system/dept";

import {mapGetters} from 'vuex';

import {PatiemtOptions} from "@/assets/scripts/pacs/exammanagement/patient/mixins";

import DateUtil from '@/utils/DateUtil';

import {PATIENTSHEETTYPE} from "@/assets/scripts/pacs/Const";
import UserPicker from "@/views/system/user/comp/UserPicker.vue";
import {actions} from "@/assets/starter/globalactons";

//表单标识
const formSimp = "simp";

const AgeUnits = {'Y': '岁', 'M': '月', 'D': '天', 'H': '小时', 'm': '分'}

let timer_getList = {};

export default {
  extends: BaseGridModel,
  mixins: [EditModel, UndoModel, TransModel, SearchOptions, ExamDataScope, ResultStatus, PermiCheck, PatiemtOptions],
  dicts: ["uis_exam_result_status", 'uis_exam_item', "uis_inp_type", "uis_exam_modality", "uis_gender_type", , "uis_exam_result_prop"],

  components: {
    UserPicker,
    Contextmenu,
    OperationAuth,
    PatientListSearchOptions,
    ReportUploader,
    ReportViewer,
    ReportUploaderBatch,
    LinksignPopup
  },

  props: {
    //定时刷新秒数
    refresh: {type: Number},
    //表格行右键菜单
    actions: {type: Array, default: () => []},
    //固定查询条件
    filter: {type: Object},//e.g. {status:0,datesCreated:1}
    //限制
    restrict: {type: Object},
    //普通列表：0 上传列表：1 审核列表：2
    sheetType: {type: String, default: PATIENTSHEETTYPE.normal},
  },

  data() {
    return {
      // Add this to your existing data properties
      rememberFilters: false,
      savedFilters: null,
      // ... other data properties
      //当前查询表单
      lastSearchForm: formSimp,
      deptTreeData: [],
      searchFormVisible: false,

      user: {},

      createTime_props: [{label: "今天", value: 0},
        {label: "昨天", value: 1},
        {label: "三天", value: 3},
        {label: "七天", value: 7},
        {label: "自定义时间", value: -1}],
      createTimeValue: 7,


      searchForm: {
        combo_props: [{value: "patientInfo.name", label: "姓名"}
          , {value: "examNo", label: "检查号"}
          , {value: 'patientInfo.registNo', label: '登记号'}
          , {value: 'inpNo', label: '住院号'}
          , {value: 'reportNo', label: '报告编号'}
          , {value: 'imageNo', label: '影像编号'}]

        , patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        }

        , inpTypeValues: []
        , id: null,
        examNo: null,
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModality: {dictCode: null},
        examDoctor: {nickName: null},
        reportDoctor: {nickName: null},
        auditDoctor: {nickName: null},
        operDoctor: {nickName: null},
        reqDoctor: {
          nickName: '',
          userName: ''
        }

        , propName: "patientInfo.registNo"
        , propValue: null
        , resultStatusValues: null
        , examItemCodes: null
        , equipExamItemCode: null
        , examResultProp: {dictCode: null}
        , createTimeGe: currDate()
        , createTimeLt: currDate()
        , pageSize: 17
      },

      searchFormSimp: {
        combo_props: [{value: "patientInfo.name", label: "姓名"}
          , {value: "examNo", label: "检查号"}
          , {value: 'patientInfo.registNo', label: '登记号'}
          , {value: 'inpNo', label: '住院号'}
          , {value: 'reportNo', label: '报告编号'}
          , {value: 'imageNo', label: '影像编号'}]

        , patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        }

        , inpTypeValues: []
        , id: null,
        examNo: null,
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModality: {dictCode: null},
        examDoctor: {nickName: null},
        reportDoctor: {nickName: null},
        auditDoctor: {nickName: null},
        operDoctor: {nickName: null}

        , propName: "patientInfo.registNo"
        , propValue: null
        , resultStatusValues: null
        , examItemCodes: null
        , equipExamItemCode: null
        , examResultProp: {dictCode: null}
        , pageSize: 17
      },

      currentSelection: null,
      qr: null,
      //定时刷新列表
      uploading: false,
      auditing: false,
      params: null,
      cacheKey: "PatiemtOptions",
      examItemConfig: null,
      showCustomDatePickers: false,
      startDate: '',
      endDate: '',
      patientMainForm: null,
    }
  },

  methods: {

    setCreateTimeValue(value){
      this.createTimeValue = value
      this.refreshTime(value)
    },
    reportable(row) {
      return matchAnyStatus(row, StatusDict.regist, StatusDict.exam);
    },
    goToOcrConfirm(row) {
      this.$router.push({
        name: 'OcrConfirm',
        params: {examInfo: row}
      });
    },
    disabledUpload() {
      let examItemCodes = this.searchFormSimp.examItemCodes;
      if (Array.isArray(examItemCodes) && examItemCodes.length == 1) {
        return false;
      }
      return true
    },

    //查询
    search(opt) {
      if (!!opt && (opt instanceof Event)) {
        let el = 13 === opt.which ? (opt.target || opt.currentTarget) : null;
        if (!!el && "INPUT" !== el.nodeName) {
          return;
        }
      }

      this.lastSearchForm = formSimp === opt ? opt : null;
      this.getList(new MouseEvent("click"));
    },


    toggleSearchForm() {
      this.searchFormVisible = !this.searchFormVisible;
    },

    //勾选触发
    handleSelectionChange(rows) {
      this.currentSelection = rows;
    },

    /**
     * 用户选择框
     * @param {Object} opts - Options for user picker
     */
    toPickUser(opts) {
      let tar = opts.target || 'reqDoctor';
      let fm = this.searchForm;

      let names, codes;
      if (tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }

      let selectedUsers = [];
      if (!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({userName: e, nickName: names[i]});
        });
      }
      opts.selectedUsers = selectedUsers;

      this.$refs.userPicker.showPicker(opts);
    },

    /**
     * Handle user selection from picker
     * @param {string} tar - Target field
     * @param {Object|Array} users - Selected user(s)
     */
    pickUser(tar, users) {
      let fm = this.searchForm;
      let names = [], codes = [];

      if (!!users.length) {
        users.forEach((e) => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }

      names = names.join(",");
      codes = codes.join(",");

      if (tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }

      // Trigger search after selection
      this.searchFormChange();
    },

    batchAudit() {
      let vm = this;
      if (undefined == this.currentSelection || 0 == this.currentSelection.length) {
        this.$modal.msgSuccess("请选择检查！");
        return;
      }
      let form = new FormData();
      this.currentSelection.forEach(e => form.append("examInfos", e.id));

      let fun;
      if (vm.hasPermi("report:reportImage:upload")) {
        //fun = eiapi.batchAudit;
        fun = eiapi.batchAudit;
      } else {
        //批量审核上传的pdf报告
        fun = eiapi.batchAuditPdf;
      }


      const cup = (data) => {
        this.auditing = true;
        fun(form).then(res => {
          this.auditing = false;
          if ('qrnoauth' === res.errC) {
            if (null == vm.qr) {
              vm.qr = new QRSignRel(() => {
                this.qr.activated = false;
                this.qr.openDig = false;
                ///opt.firmed = true;
                cup(data);
              });
            }
            this.qr.openDig = true;
            return;
          }

          vm.$modal.msgSuccess(res.msg);
          vm.$refs.dataGrid.clearSelection();
          vm.currentSelection = null;
          vm.getList();
        }).catch(err => {
          this.$refs.dataGrid.clearSelection();
          this.currentSelection = null;
          this.auditing = false;
        });
      };
      cup(form);
    },

    selectable(row, index) {
      if (matchAnyStatus(row, StatusDict.report)) {
        return true;
      } else {
        return false;
      }
    },


    getUser() {
      let vm = this;
      getUserProfile().then(response => {
        this.user = response.data;
        //this.getList();
      });
    },

    getImageName(row) {
      if (undefined != row.dicomStudies) return null;
      if (undefined != row.dicomStudies[0].seriesSet[0] && undefined != row.dicomStudies[0].seriesSet[0].imagesSet[0])
        return row.dicomStudies[0].seriesSet[0].imagesSet[0].fileName;

      return null;
    },

    /**
     * 搜索
     */
    getList: function (opts) {
      let vm = this;
      // if ("delay"==opts ) {
      //   clearTimeout(this.timer_getList); // 仅在非手动触发时清除定时器
      // }
      //
      //this.checkValue()
      this.handleCurrentChange(null);

      if (!this.patientMainForm) {
        this.readMainForm();
      }

      let params;
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      if (formSimp === this.lastSearchForm) {
        //简单查询
        //查询参数
        let sfm = this.searchFormSimp;
        params = {
          pageSize: sfm.pageSize, pageNum: sfm.pageNum, resultStatusValues: [],
          createTimeGe: sfm.createTimeGe,
          createTimeLt: sfm.createTimeLt
        };
        //父组件指定参数
        if (this.filter) {
          mergeWith(params, this.filter, true, mergeWithNotNull);
        }
        //偏好查询设置, 覆盖父组件
        //let searchOpts = this.$refs.searchOpt.readOpts();
        //mergeWith(params, searchOpts);

        // if(undefined!=sfm.resultStatusValues&&sfm.resultStatusValues.length>0 )
        if (undefined != sfm.resultStatusValues && sfm.resultStatusValues.length > 0) params.resultStatusValues = cloneDeep(sfm.resultStatusValues);
        //检查进度限制
        const rst = this.restrict;
        if ((!params.resultStatusValues || params.resultStatusValues.length === 0)
          && rst && rst.resultStatusValues) {
          params.resultStatusValues = rst.resultStatusValues
        }

        // if(undefined!=this.dept) {
        //   console.log("this.dept",this.dept);
        //   params["examDept"] = {};
        //   params["examDept"]["deptId"] = this.dept.deptId;
        // }

        //检查项目
        params.examItemCodes = sfm.examItemCodes

        //当天预约检查
        // params.appointExamDateGe = parseTime(currDate());
        // //
        // params.appointExamDateLt = params.appointExamDateGe;
        // if(0==params.resultStatusValues.length&&0==params.examItemCodes.length){
        //   params.appointWithCreated = true;
        // }
        params.appointWithCreated = true;

        //根据下拉
        let propName = sfm.propName, propValue = sfm.propValue;
        if (!!propName && !!propValue) {
          let propsName = propName.split(/\./), prop = params;
          for (let i = 0, len = propsName.length; i < len; i++) {
            let pnam = propsName[i];
            if ((i + 1) < len) {
              prop[pnam] = {};
              prop = prop[pnam];
              continue;
            }
            prop[pnam] = propValue;
          }
          //查询不限定时间
          params.appointWithCreated = null;
          delete params.appointWithCreated;
        }

        // var date = new Date();
        // date.setDate(date.getDate() - this.createTimeValue);
        // date = new Date(new Date(date.toLocaleDateString()).getTime());
        // params.createTimeGe = date;
        // params.createTimeLt = new Date();


      } else {
        //过滤器查询
        params = cloneDeep(this.searchForm);
        delete params["combo_props"];
        delete params["propName"];

        // if(params.examItemCodes) {
        //   params.examItemCodes.forEach((c, i) => {
        //     params["examItemCodes[" + i + "]"] = c;
        //   });
        //   delete params["examItemCodes"];
        // }
      }

      //已删除状态
      let pos;
      if (!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
        params.resultStatusValues.splice(pos, 1);
        //params.resultStatusAsStatus = "2";
        params.status = 2;
      } else {
        params.status = 0;
      }

      if (this.patientSheetType.pendingReport === this.sheetType) {
        //待报告列表
        params.resultStatusValues.push(StatusDict.regist);
        params.resultStatusValues.push(StatusDict.exam);
        // if(undefined==vm.searchFormSimp.equipExamItemCode){
        //   if(undefined!=vm.ctrlData.dict.uis_exam_item) {
        //     vm.searchFormSimp.equipExamItemCode=vm.ctrlData.dict.uis_exam_item[0].value
        //   }
        // }
        // params["examItemCodes"] = vm.searchFormSimp.equipExamItemCode;
      } else if (this.patientSheetType.pendingAudit == this.sheetType) {
        //待审核列表
        if (this.hasPermi(PERMI_AUDIT)) {
          params.resultStatusValues.push(StatusDict.report);
          params.resultStatusValues.push(StatusDict.exam);
        }
        if (this.hasPermi(PERMI_SECOND_AUDIT)) {
          params.resultStatusValues.push(StatusDict.audit);
        }
        if (this.hasPermi(PERMI_THIRD_AUDIT)) {
          params.resultStatusValues.push(StatusDict.second_audit);
        }


        // if(undefined==vm.searchFormSimp.equipExamItemCode){
        //   if(undefined!=vm.ctrlData.dict.uis_exam_item) {
        //     vm.searchFormSimp.equipExamItemCode=vm.ctrlData.dict.uis_exam_item[0].value
        //   }
        // }
        // params["examItemCodes"] = vm.searchFormSimp.equipExamItemCode;
      }


      //点击按钮触发
      if (opts && (opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }
      //
      //params.status=0;
      //params.status = null;
      //
      //
      // if(this.user.dept) {
      //   params["examDept"] = {deptId:null};
      //   params.examDept.deptId = this.user.dept.deptId;
      // }

      if (undefined == this.examModality || 0 == this.examModality.length) return;

      params["examModalitiesCodes"] = [];
      this.examModality.forEach(e => {
        params["examModalitiesCodes"].push(e.dictValue);
      });


      this.params = params;

      let searChparams = cloneDeep(params);
      if (!searChparams.examItemCodes || 0 == searChparams.examItemCodes.length) {
        if (!!vm.loginUsrExamItem) {
          searChparams["examItemCodes"] = [];
          vm.loginUsrExamItem.forEach(e => {
            searChparams["examItemCodes"].push(e.dictValue);
          });
        }
      }
      eiapi.find(searChparams).then(res => {
        this.loading = false;
        this.grid.pager.total = res.total;
        this.grid.data = res.rows;
        this.triggerBind("updatePatientNum", {sheetType: this.sheetType, rowsNum: this.grid.data.length});
        for (var i = 0; i < this.grid.data.length; i++) {
          // var pdfPath = this.grid.data[i].reportUrlPdf
          // if(null!=pdfPath){
          //   this.grid.data[i]["reportUrlPdfName"] = pdfPath.substring(pdfPath.lastIndexOf('/')+1);
          // }
          let fileNames = '';
          let fileNameJs = this.grid.data[i]["reportUploadFilename"];
          var obj = JSON.parse(fileNameJs);

          // 遍历对象的属性
          for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
              fileNames += (key + ',');
            }
          }
          this.grid.data[i]["reportUploadFilenameStr"] = fileNames;


          var exam = this.grid.data[i];
          var imageNum = 0;
          if (undefined != exam.dicomStudies) {
            exam.dicomStudies.forEach(stu => {
              if (undefined != stu.seriesSet) {
                stu.seriesSet.forEach(e => imageNum += e.imageNumber);
              }
            });
          }

          exam["imageNum"] = imageNum;
          // var jpgPath = this.grid.data[i].reportUrlJpg
          // if(null!=jpgPath){
          //   this.grid.data[i]["reportUrlJpgName"] = jpgPath.substring(jpgPath.lastIndexOf('/')+1);
          // }
        }

        // if ("delay"==opts) {
        //   this.delayGetList(); // 仅在非手动触发时重新启动定时器
        // }
      }).catch(() => {
        // if ("delay"==opts) {
        //   this.delayGetList(); // 仅在非手动触发时重新启动定时器
        // }
      });
    },

    ordExecute(examInfo) {
      eiapi.ordExecute(examInfo).then(res => {
        this.getList();
        this.$modal.msgSuccess("执行成功");
      }).catch();
    },

    //读取部门树信息
    buildDeptTree() {
      deptTreeselect().then(res => {
        this.deptTreeData = res.data;
      });
    },

    //轮询
    delayGetList() {
      clearTimeout(timer_getList[this.sheetType]); // 确保清除之前的定时器
      if (this.refresh) {
        timer_getList[this.sheetType] = setTimeout(() => {
          this.getList("delay");
          this.delayGetList(); // 在完成一次查询后重新设置定时器
        }, this.refresh * 1000);
      }
    },

    getExamItemConfig() {
      if (!this.examItemConfig) {
        this.$modal.msgSuccess("检查项目未获取成功，请重试!");
        return;
      }

      if (!this.searchFormSimp.examItemCodes) {
        this.$modal.msgSuccess("请选择检查项目!");
        return;
      }
      return this.examItemConfig[this.searchFormSimp.examItemCodes[0]];
    },

    reportOperate(opt, row) {
      let examItemConfig = this.getExamItemConfig();
      if (undefined == examItemConfig) {
        this.$modal.msgSuccess("请配置科室配置!");
        return;
      }
      switch (opt) {
        case "uploadPdf":
          this.$refs.reportUploader.prepare(row, ".pdf", examItemConfig);
          break;
        case "reloadPdf":
          this.$confirm('确认重新上传报告？')
            .then(_ => {
              this.$refs.reportUploader.prepare(row, ".pdf", examItemConfig);
            });
          break;
        case "uploadJpg":
          this.$refs.reportUploader.prepare(row, ".jpg", examItemConfig);
          break;
        case "reloadJpg":
          this.$confirm('确认重新上传报告？')
            .then(_ => {
              this.$refs.reportUploader.prepare(row, ".jpg", examItemConfig);
            });
          break;
        // case 'view':
        //   this.$refs.reportViewer.view(row);
        //   break;
        default:
          break;
      }
    },
    help(){
      let url;
      if(process.env.VUE_APP_USR_HELP_DOC && process.env.VUE_APP_USR_HELP_DOC.startsWith("http") ){
        url=process.env.VUE_APP_USR_HELP_DOC
      }else{
        url = window.location.protocol + "//"+window.location.hostname + process.env.VUE_APP_USR_HELP_DOC ;
      }
      //  window.open(url, '_blank');
      this.$showPdf(url, '帮助文档');
    },

    getReportDataSourceEdit(opt, row) {
      let resultStatus, resultStatusCode;
      switch (opt) {
        case "uploadPdf":
          resultStatus = row.resultStatus, resultStatusCode = resultStatus ? resultStatus.dictValue : null;
          if (auth.hasPermi("report:reportPdf:upload") && (!undefinedOrNull(resultStatusCode) && StatusDict.regist === resultStatusCode || StatusDict.exam === resultStatusCode)) {
            return true;
          }
          return false;
        case "reloadPdf":
          resultStatus = row.resultStatus, resultStatusCode = resultStatus ? resultStatus.dictValue : null;
          if (auth.hasPermi("report:reportPdf:upload") && (!undefinedOrNull(resultStatusCode) && StatusDict.report == resultStatusCode)) {
            return true;
          }
          return false;
        case "uploadJpg":
          resultStatus = row.resultStatus, resultStatusCode = resultStatus ? resultStatus.dictValue : null;
          if (auth.hasPermi("report:reportImage:upload") && (!undefinedOrNull(resultStatusCode) && StatusDict.regist === resultStatusCode || StatusDict.exam === resultStatusCode)) {
            return true;
          }
          return false;
        case "reloadJpg":
          resultStatus = row.resultStatus, resultStatusCode = resultStatus ? resultStatus.dictValue : null;
          if (auth.hasPermi("report:reportImage:upload") && (!undefinedOrNull(resultStatusCode) && StatusDict.report == resultStatusCode)) {
            return true;
          }
          return false;
        //报告
        //return row.reportDataSource==0?false:true;
        case 'view':
          resultStatus = row.resultStatus, resultStatusCode = resultStatus ? resultStatus.dictValue : null;
          if (!undefinedOrNull(resultStatusCode) && StatusDict.report === resultStatusCode
            || StatusDict.audit === resultStatusCode
            || StatusDict.reaudit === resultStatusCode
            || StatusDict.print === resultStatusCode) {
            return false;
          }
          return true;
        //return row.reportDataSource==2?false:true;
        default:
          break;
      }
    },

    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if (1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    //删除
    undoDelete(row) {
      this.handleUndoDelete(row).then(res => {
        if (res && 200 === res.code) {
          this.getList();
        }
      });
    },
    //延迟检查
    handlePostpone(row) {
      let props = {examAtPm: 1};
      this.handleTrans(row, props).then(res => this.getList());
    },

    focusPropValue() {
      try {
        this.$nextTick(() => this.$refs.propValue.focus())
      } catch (err) {
        console.error(err);
      }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age ? (pat.age + (pat.ageUnit ? pat.ageUnit.dictLabel : '')) : (pat ? pat.age : '');
    },

    //年龄列
    // colFmt_exam_age(row) {
    //   const pat = row.patientInfo;
    //   return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    // },

    //
//年龄问题，以检查日期和出生日期计算
    colFmt_exam_age(row, column, cellValue, index) {
      return fmt_exam_age(row, column, cellValue, index);
    },

    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if (!!row && !!row.callInfo && !!row.callInfo.callRoom && !!row.callInfo.callRoom.roomName) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom ? row.equipRoom.roomName : null;
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //打开查询设置
    prepareSearchOpt() {
      this.$refs.searchOpt.open();
    },
    //登记号补0
    checkValue() {
      let fm = this.searchFormSimp
      if (fm.propName == 'patientInfo.registNo') {

        if (!!fm.propValue && fm.propValue.length > 0 && fm.propValue.length < 10) {
          fm.propValue = fm.propValue.padStart(10, '0');
        }
      }
    },
    //
    colStyle({row, column, rowIndex, columnIndex}) {

      if ('unexecuteOrdId' === column.property) {
        let clz = ["table-cell-unexecuteOrdId-status"];
        if (!undefinedOrNull(row.ordId) && !undefinedOrNull(row.unexecuteOrdId)) {
          clz.push("table-cell-unexecuteOrdId-status-" + 1);
        }
        return clz.join(" ");
      }

      if ('resultStatus.dictLabel' !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.resultStatus, stav = sta ? sta.dictValue : null;
      if (!undefinedOrNull(stav)) {
        clz.push("table-cell-result-status-" + stav);
      }

      return clz.join(" ");
    },

    reportImageUploads() {
      let vm = this;
      let reportExamItemName = null;
      if (undefined != vm.ctrlData.dict.uis_exam_item) {
        vm.ctrlData.dict.uis_exam_item.forEach(e => {
          if (!!vm.searchFormSimp.examItemCodes && e.value == vm.searchFormSimp.examItemCodes[0]) reportExamItemName = e.label
        })
      }
      this.$refs.reportUploaderBatch.prepare(this.params, ".jpg", this.getExamItemConfig(), reportExamItemName);
    },

    reportPdfUploads() {
      let vm = this;
      let reportExamItemName = null;
      if (undefined != vm.ctrlData.dict.uis_exam_item) {
        vm.ctrlData.dict.uis_exam_item.forEach(e => {
          if (!!vm.searchFormSimp.examItemCodes && e.value == vm.searchFormSimp.examItemCodes[0]) reportExamItemName = e.label
        })
      }
      this.$refs.reportUploaderBatch.prepare(this.params, ".pdf", this.getExamItemConfig(), reportExamItemName);
    },

    reportUploadStateUpdate(state) {
      this.uploading = state;
    },

    refreshTime(value) {
      const currentDate = new Date();
      const pastDate = new Date();
      pastDate.setDate(currentDate.getDate() - this.createTimeValue);
      const startOfDay = new Date(pastDate.toLocaleDateString()).getTime();

      this.searchFormSimp.createTimeGe = new Date(startOfDay);
      this.searchFormSimp.createTimeLt = (value === 1) ? new Date(startOfDay) : currentDate;
    },

    searchFormChange() {
      this.patientMainForm.examItemCodes = this.searchFormSimp.examItemCodes;
      this.patientMainForm.createTimeValue = this.createTimeValue;
      this.patientMainForm.resultStatusValues = this.searchFormSimp.resultStatusValues;
      this.save();
      // if (-1==value) {
      //   this.showCustomDatePickers = true;
      // } else {
      //   this.showCustomDatePickers = false;

      //   var date = new Date();
      //   date.setDate(date.getDate() - this.createTimeValue);
      //   date = new Date(new Date(date.toLocaleDateString()).getTime());
      //   this.searchForm.createTimeGe = date;
      //   this.searchForm.createTimeLt = new Date();

      //   if(1==value) this.searchForm.createTimeLt = date;
      //   this.getList();
      // }

      this.showCustomDatePickers = (this.createTimeValue === -1);

      if (!this.showCustomDatePickers) {
        this.refreshTime(this.createTimeValue);
        this.getList();
      } else {
        this.searchFormSimp.createTimeGe = null;
        this.searchFormSimp.createTimeLt = null;
      }
    },

    changeDatePicker(value) {
      console.log("changeDatePicker:", value);
      this.searchFormSimp.createTimeGe = value[0];
      this.searchFormSimp.createTimeLt = value[1];
      this.getList();
    },

    isRegist() {
      return this.patientSheetType.regist == this.sheetType;
    },
    isPendingReport() {
      return this.patientSheetType.pendingReport == this.sheetType;
    },
    isPendingAudit() {
      return this.patientSheetType.pendingAudit == this.sheetType;
    },
    isNormal() {
      return this.patientSheetType.normal == this.sheetType;
    },

    readMainForm() {
      this.mainForm = this.read();
      this.patientMainForm = this.mainForm;
      if (this.patientMainForm) {
        if (this.patientMainForm.examItemCodes) {
          this.searchFormSimp.examItemCodes = this.patientMainForm.examItemCodes;
        }

        if (this.patientMainForm.resultStatusValues) {
          this.searchFormSimp.resultStatusValues = this.patientMainForm.resultStatusValues;
        }

        if (this.patientMainForm.createTimeValue !== undefined) {
          this.createTimeValue = this.patientMainForm.createTimeValue;
          this.searchFormChange();
        }
      }
    },

    //清空申请医生
    clearReqDoctor() {
      if (this.searchForm.reqDoctor) {
        this.searchForm.reqDoctor.userName = '';
        // nickName is already cleared by the input's clear button
      }
      // Trigger search after clearing
      this.searchFormChange();
    },

    /**
     * Handle remember filters checkbox change
     */
    handleRememberFiltersChange(value) {
      if (value) {
        // Save current filters
        this.saveFilters();
      } else {
        // Clear saved filters
        this.savedFilters = null;
        localStorage.removeItem('patientSheetFilters');
      }
    },

    /**
     * Save current filters
     */
    saveFilters() {
      // Create a copy of the current filters
      this.savedFilters = {
        searchForm: cloneDeep(this.searchForm),
        createTimeValue: this.createTimeValue
      };

      // Save to localStorage
      localStorage.setItem('patientSheetFilters', JSON.stringify(this.savedFilters));
    },

    /**
     * Load saved filters
     */
    loadFilters() {
      try {
        const savedFilters = localStorage.getItem('patientSheetFilters');
        if (savedFilters) {
          this.rememberFilters = true;
          this.savedFilters = JSON.parse(savedFilters);

          // Apply saved filters
          if (this.savedFilters.searchForm) {
            this.searchForm = cloneDeep(this.savedFilters.searchForm);
          }

          if (this.savedFilters.searchFormSimp) {
            this.searchFormSimp = cloneDeep(this.savedFilters.searchFormSimp);
          }

          if (this.savedFilters.createTimeValue !== undefined) {
            this.createTimeValue = this.savedFilters.createTimeValue;
          }

          // 确保在下一个 tick 设置 rememberFilters，以便 Vue 能正确捕获变化
          this.$nextTick(() => {
            this.rememberFilters = true;
          });
        }
      } catch (e) {
        console.error('Failed to load saved filters', e);
      }
    },
  },

  mounted() {
    this.cacheKey = this.sheetType;
    this.lastSearchForm = this.rememberFilters ? null : formSimp;
    this.readMainForm();

    this.getList();

    this.getConfigKey("examIemConfigKey").then(response => {
      if (response && response.msg) {
        this.examItemConfig = JSON.parse(response.msg);
      }
    });
    // this.delayGetList(); // 确保 mounted 时正确启动定时器
    actions.registerAction(this.cacheKey+'.patientsheet.select.time_range',this.setCreateTimeValue)
  },


  watch: {
    "searchForm": {
      deep: true,
      handler() {
        if (this.rememberFilters) {
          this.rememberFilters = false;
          this.handleRememberFiltersChange(false);
        }
      }
    },
    /**
     * 检查类型字典取值后执行
     */
    "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        let items = [];
        if (nv && nv.length) {
          const conv = d => {
            return {value: d.dictValue, label: ("--" + d.dictLabel), raw: d};
          };
          //层级
          nv.forEach(e => {
            if (e.raw.parent && e.raw.parent.dictCode) {
              return true;
            }
            items.push(e);
            //
            let d = e.raw;
            if (d.children && d.children.length) {
              d.children.forEach(c => {
                items.push(conv(c));
              });
            }
            //
            nv.forEach(c => {
              if (c.raw.parent && c.raw.parent.dictCode === d.dictCode) {
                items.push(conv(c.raw));
              }
            });
          });
        }
        //
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, items);
      }
    },

    // "ctrlData.dict.uis_exam_modality":{
    //   deep: true,
    //   handler(nv, ov) {
    //     if(!!nv) this.getList();
    //   }
    // },

    /**
     * 就诊类别字典取值后执行
     */
    "dict.type.uis_inp_type": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        if (nv && Array.isArray(nv) && (!this.isRegist())) {
          nv = nv.filter(item => !(item.raw && item.raw.extend && item.raw.extend.extendI1 === 1));
        }
        console.log("nv", nv);
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    },

    "examModality": {
      deep: true,
      handler(nv, ov) {
        if (!!nv) this.getList();
      }
    },

  },

  activated() {
    // this.delayGetList();
    //this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(timer_getList[this.sheetType]); // 确保组件停用时清除定时器
  },

  beforeDestroy() {
    clearTimeout(timer_getList[this.sheetType]); // 确保组件销毁时清除定时器
  },

  computed: {
    ...mapGetters(['examModality', 'loginUsrExamItem']),
    combo_resultStatus() {
      const dict = this.dict.type.uis_exam_result_status, rst = this.restrict;
      if (!rst || !rst.resultStatusValues || !rst.resultStatusValues.length) {
        return dict;
      }
      return dict.filter(d => -1 !== rst.resultStatusValues.findIndex(v => v === d.value));
    },
    patientSheetType() {
      return PATIENTSHEETTYPE;
    },
  },

  created() {
    let vm = this;
    this.getUser();
    console.log("created patientSheet.vue");
    // Load saved filters if available
    this.loadFilters();this.delayGetList();
  },

};
