import request,{postForm,postRequestAndGetArrayBuffer} from '@/utils/request'

const ctx = '/exammanagement/examInfo';

// 查询列表
export function find(query) {
  return postForm({
    url: ctx + '/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 查询详细
export function get(id) {
  return request({
    url: ctx + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: ctx + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: ctx + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request.put(ctx + '/del/' + id)
}

// 撤销删除
export function undoDel(id) {
  return request.put(ctx + '/undoDel/' + id)
}

// 修改工作状态
export function updateResultStatus(data) {
  return request({
    url: ctx + '/updateResultStatus',
    method: 'put',
    data: data
  })
}
//保存报告
export function saveReport(data) {
  return request({
    url: ctx + '/report/save',
    method: 'put',
    data: data
  })
}

//读取接口获取患者登记信息
export function loadRemote(way, code, ordStatus, ordId) {
  if(ordId) {
    ordId = ordId.replace(/@/g, ",");
  }
  return request({
    url: ctx + '/loadRemote/' + way + '/' + code,
    method: 'get',
    params: {ordStatus, ordId}
  })
}

//诊前准备就绪
export function updateExamPrerequire(data) {
  return request.put((ctx + '/updateExamPrerequire'), data);
}

//检查时段
export function updateExamPeriod(data) {
  return request.put((ctx + '/updateExamPeriod'), data);
}
//获取审核签名
export function getUserSignInfo(data) {
  return request.put(ctx + '/report/getUserSignInfo', data);
}

//打印报告
export function printReport(data) {
  return request.put(ctx + '/report/printReport', data);
}

//保存审核文件
export function saveAuditFile(pdf,data) {
  let form = new FormData();
  form.append("file", pdf);
  form.append("data", JSON.stringify(data));
  let url = ctx + '/report/saveAuditFile';
  return request({
    url: url,
    method: "post",
    data:form,
    timeout: (12 * 1000)
  })
}

// 审核，传入报告id
export function doAudit(data) {
  let form = new FormData();
  //form.append("file", pdf);
  form.append("data", JSON.stringify(data));
  let url = ctx + '/report/doAudit';
  return request({
    url: url,
    method: "post",
    data:form,
    timeout: (12 * 1000)
  })
}
//审核
export function auditReport(data) {
  return request.put(ctx + '/report/audit', data);
}
//复核
export function reauditReport(data) {
  return request.put(ctx + '/report/reaudit', data);
}
//召回
export function withdrawAuditReport(data) {
  return postForm({url: ctx + '/report/withdrawAudit', data: data});
}

//签字
export function signReport(data) {
  return request.put(ctx + '/report/sign', data);
}

//复制
export function copy(data) {
  return request.post(ctx + '/copy', data);
}

//拆分
export function split(data) {
  return request.post(ctx + '/split', data);
}
//保存报告文件：pdf，jpg
export function uploadReportDoc(data) {
  return request({
    url: ctx + "/report/uploadReportDoc",
    method: "post",
    data,
    timeout: (60 * 1000)
  })
}

//匹配图像
export function examMatchingImg(data) {
  return request({
    url: ctx + '/examMatchingImg',
    method: 'put',
    data: data
  })
}

export function withdrawAuditHistReason() {
  return request.get(ctx + '/report/withdrawAuditHistReason');
}

export function viewReportDoc(data, mode = 0) {
  return request.post(ctx + "/report/view/" + mode, data, {timeout: (60 * 1000)});
}

export function viewUploadReportDoc(data) {
  return request.post(ctx + "/report/getReport/0/pdf", data, {timeout: (60 * 1000)});
}

export function viewUploadOrgReportDoc(data) {
  return request.post(ctx + "/report/getReport/1/pdf", data, {timeout: (60 * 1000)});
}

export function viewUploadOrgReportPng(data) {
  return request.post(ctx + "/report/getReport/1/png", data, {timeout: (60 * 1000)});
}

//上传pdf格式报告
// export function uploadReport(form) {
//   const url = ctx + "/report/uploadReport";
//   return request.post(url, form);
// }

//上传pdf格式报告
export function batchReport(form) {
  const url = ctx + "/report/batchReport";
  return request.post(url, form);
}

//上传pdf格式报告
export function uploadReports(form) {
  const url = ctx + "/report/uploadReports";
  return request.post(url, form);
}

//上传ocr pdf报告
export function uploadOCRReports(form) {
  const url = ctx + "/report/uploadOCRReports";
  return request.post(url, form, );
}

//批量并上传pdf报告
export function uploadAuditReports(form) {
  const url = ctx + "/report/uploadAuditReports";
  return request.post(url, form);
}

//批量审核报告
export function batchAudit(form) {
  const url = ctx + "/report/batchAudit";
  return request.post(url, form);
}

//批量审核pdf报告
export function batchAuditPdf(form) {
  const url = ctx + "/report/batchAuditPdf";
  return request.post(url, form);
}

//执行医嘱
// 新增
export function ordExecute(data) {
  return request({
    url: ctx + '/ordExecute',
    method: 'post',
    data: data
  })
}

/**
 * 获取报告pdf
 * @param {*} data
 */
export function getReportPdf(data) {
  return postRequestAndGetArrayBuffer(ctx + '/report/pdf', data);
  // let url = ctx + 'report/pdf',
  // return request({
  //   url: ctx + '/report/pdf',
  //   method: 'post',
  //   headers: {
  //       responseType: 'arraybuffer'
  //   },
  //   data: data
  // })
}

// 根据参数键名查询参数值
export function getReportInfo() {
  return request({
    url: ctx +'/report/getReportInfo',
    method: 'get'
  })
}


//获取pdf内容信息
/*export function getPdfMsg(form) {
  const url = ctx + "/report/getPdfMsg";
  return request.post(url, form);
}*/

//获取多个pdf内容信息
export function ocrMatch(form) {
  const url = ctx + "/report/ocrMatch";
  // return request.post(url, form);
  return request({
    url: url,
    method: "post",
    data: form,
    timeout: (120 * 1000)
  })
}

export function getFile(form) {
  const url = ctx + "/report/getFile";
  return request.post(url, form);
}

export function getVirtualPrinterFile(form) {
  const url = ctx + "/report/getVirtualPrinterFile";
  return request.post(url, form);
}

//查询一定条件下文件是否已存在
export function searchFileExist(form) {
  const url = ctx + '/report/searchFileExist';
  return request.post(url, form);
}

//查询一定条件下文件是否已存在
export function deleteFilesByIds(form) {
  const url = ctx + '/report/deleteFilesByIds';
  return request.post(url, form);
}

//根据examUid查询历史文件信息
export function getFileInfosByExamUid(form) {
  const url = ctx + '/report/getFileInfosByExamUid';
  return request.post(url, form);
}

//根据examUid查询历史文件信息
export function getUnmatchFileInfos(form) {
  const url = ctx + '/report/getUnmatchFileInfos';
  // return request.post(url, form);
  return request({
    url: url,
    method: "post",
    data: form,
    headers: { repeatSubmit: false }
  })
}

// 状态回退
export function resultStatusRollback(data) {

  return request({
   url: ctx + '/report/resultStatusRollback',
   method: 'post',
   data: data
  })
 }

 //保存报告文件：pdf，jpg
export function getReportImageData(data) {
  return request({
    url: ctx + "/report/getReportImageData",
    method: "post",
    data,
    timeout: (60 * 1000)
  })
}

 // 报告数据清空
 export function reportDataClear(data) {

  return request({
   url: ctx + '/report/reportDataClear',
   method: 'post',
   data: data
  })
 }
