import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";

import {StatusDict, examReachable} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
//注册路由
export const EditModel = {
  methods: {
    //读取编辑
    handleUpdate(row) {
      this.$router.push({name: "PatientRegist", params: row});
    }
  }  
};
//诊前准备/下午检查转变
export const TransModel = {
  methods: {
    /**
     * 诊前准备或下午检查
     * @param props {'examPrerequire':0}-诊前准备状态码, {'examAtPm':1}-下午检查状态码
     */
    handleTrans(row, props) {
      const resultStatus = row.resultStatus;
      //检查不是“登记完成”状态的不支持诊前准备
      if(!examReachable(row)) {
        return ;
      }
      //
      return this.$modal.confirm("是否确定？")
        .then(() => {
          props.id = row.id;
          //指令
          if("examPrerequire" in props) {
            return eiapi.updateExamPrerequire(props);
          } else if("examAtPm" in props) {
            return eiapi.updateExamPeriod(props);
          }
        })
        .then(res => {
          if(res && 200 === res.code) {
            this.$modal.msgSuccess("操作完成");
          }
          return res;
        });
    }
  }
};

//撤销操作
export const UndoModel = {
  methods: {
    handleUndoDelete(row) {
      return eiapi.undoDel(row.id).then(res => {
        if(res && 200 === res.code) { 
          this.$modal.msgSuccess("执行成功。");
        } else {
          this.$modal.msgError(res && res.msg || "执行失败。");
        }
        return res;
      }); 
    },
    handleUndoCancel(row) {
     return ResultStatusModel.updateResultStatus.call(this, row, StatusDict.regist);
    }
  }
};

//更新检查状态
export const ResultStatusModel = {
  methods: {
    //是否确定更改检查状态
    confirmToUpdateResultStatus(row, status, cfmsg = "是否确定？") {
      return this.$modal.confirm(cfmsg).then(() => this.updateResultStatus(row, status));
    },
    //更改检查状态
    updateResultStatus(row, status) {
      const nrow = {id: row.id, resultStatus: {dictValue: status}};
      return eiapi.updateResultStatus(nrow).then(res => {
        //console.log(res);
       /* if(200 == res.code) {
          this.$modal.msgSuccess("操作成功");
          //this.getList();
        } else {
          this.$modal.msgError(res.msg || "操作失败");
        }*/
        //
        return res;
      }).catch(() => this.$modal.msgError("操作出错"));
    }
  }
};
