
// 设备工作量
import EquipReport from '@/views/uis/exammanagement/workreport/equip';
// 职工工作量
import StaffReport from '@/views/uis/exammanagement/workreport/staff';
import StaffPartReport from '@/views/uis/exammanagement/workreport/staff_part';
import PartReport from '@/views/uis/exammanagement/workreport/part';
import ItemReport from '@/views/uis/exammanagement/workreport/item';
import PropReport from '@/views/uis/exammanagement/workreport/prop';
import EquipPropReport from '@/views/uis/exammanagement/workreport/equip_prop';

//逻辑
export default {
  name: "WorkReport",


  components: {
    StaffReport,
    StaffPartReport,
    EquipReport,
    PartReport,
    ItemReport,
    PropReport,
    EquipPropReport
  },


  data() {
    return {
      activeName:"prop",
      reportType:"prop",
      tabs: [
        {name: "report_doctor_name", acname: "staff", label: "报告医生工作量统计"},
        // {name: "audit_doctor_name", acname: "staff", label: "审核医生工作量统计"},
        // {name: "reaudit_doctor_name", acname: "staff", label: "复审医生工作量统计"},
        // {name: "consultants_name", acname: "staff", label: "会诊医生工作量统计"},
        // {name: "regist_user_name", acname: "staff", label: "登记员工作量统计"},
        // {name: "month", acname: "staff_part", label: "医生月份工作量统计"},
        // {name: "part", acname: "part", label: "检查部位工作量统计"},
        // {name: "item", acname: "item", label: "检查项目工作量统计"},
        {name: "prop", acname: "prop", label: "检查阳性工作量统计"},
        {name: "equip", acname: "equip", label: "设备工作量统计"},
        {name: "equip_prop", acname: "equip_prop", label: "设备阳性率统计"},
        {name: "oper_doctor_name", acname: "staff", label: "操作者工作量统计"},
      ],
    };
  },

  methods: {
    
    handleClick1(vm) {

      this.activeName = vm.$attrs["id"]
      this.reportType = vm.$attrs["reportType"]
    },
  }
};
