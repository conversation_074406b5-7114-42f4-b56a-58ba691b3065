import store from "@/store/index";

import {cloneDeep} from "lodash";

//检查信息
import * as eiapi from "@/assets/scripts/gis/exammanagement/workreport/api";

import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';

import {EditModel, TransModel, UndoModel} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import JsonExcel from 'vue-json-excel'
import Vue from 'vue'
Vue.component('downloadExcel', JsonExcel)

function currMonth(){
  const dt = new Date();
  return new Date(dt.getFullYear(), dt.getMonth());
}

//表单标识
const formSimp = "simp";
//右键菜单
const TableContexmenuItems = [
        {cmd: 'exam::copy', name: '复制'},
        {cmd: 'exam::edit', name: '更改信息'}
        , {cmd: 'exam::del', name: '删除检查', permissions: ['exam-info:delete'], assert: (ctx, exam) => {if(!!exam && 2 === exam.status) {ctx.cmd = 'exam::undel';ctx.name = '删除恢复';} else {ctx.cmd = 'exam::del';ctx.name = '删除检查';}}}
        //, {cmd: 'exam::equip-room-change', name: '更改机房'}
        //, {cmd: 'exam::precond', name: '诊前准备'}
        , {cmd: 'exam::at-pm', name: '延迟检查'}//下午检查
        , {cmd: 'report::write', name: '打开报告'}
      ];
//export {TableContexmenuItems};
function tableContextmenuItems() {
  const userPermissions = store.state.user.permissions, superPerm = "*:*:*";
  if(userPermissions.includes(superPerm)) {
    return TableContexmenuItems;
  }

  return TableContexmenuItems.filter(e => !e.permissions || e.permissions.length === 0 || userPermissions.includes(e.permissions[0]));
};
export {tableContextmenuItems};

//逻辑
export default {
  name: "staff_part",

  props:['para'],

  extends: BaseGridModel,

  mixins: [EditModel, TransModel, UndoModel],


  dicts: ["uis_exam_item", "uis_inp_type", "uis_exam_modality", "uis_gender_type", "uis_exam_result_status"],

  data() {
    return {
      tabs: [
        {name: "report_doctor_name", label: "报告医生工作量统计"},
        {name: "audit_doctor_name", label: "审核医生工作量统计"},
        {name: "reaudit_doctor_name", label: "复审医生工作量统计"},
        {name: "consultants_name", label: "会诊医生工作量统计"},
        {name: "regist_user_name", label: "登记员工作量统计"},
        {name: "PartReport", label: "检查部位工作量统计"}
      ],


      searchFormVisible: false,
      /**
       * 搜索框
       */
      searchForm:{
        id: null,
        examNo: null,
        examItemCodes: [],
        callInfo: {
          callNo: null
        },

        inpType: {dictCode: null},
        reqDept: {deptId: null},
        examModalitys: {dictCode: null},
        examDoctor: {userName: null},

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: {dictValue: null},
        },


        inpTypeValues: []
      },

      /**
       * 当前
       */
      currentTableRow: null,

      tableAction: tableContextmenuItems(),

      deptTreeData: [],


      searchFormSimp: {
        textFieldName: null,
        textField: null,

        doctorName: null,
        doctorType: "auditDoctor",
        // reportType: "month",

        reportDoctor: {userName: null},
        auditDoctor: {userName: null},
        reauditDoctor: {userName: null},
        consultantsCode: null,
        creator: {userName: null},
        examResultProp: {dictValue: null},

        dateFieldName: "auditTime",
        dateFieldGe: currMonth(),
        dateFieldLt: currMonth(),
        
        inpTypeValues: [],
        examDevicesCode: [],
        resultStatusValues: [],
        equipRoomsCode: [],
        examModalityPara:[]
      },
      //当前查询表单
      lastSearchForm: formSimp,
      //
      combo: {
        searchTextFields: [
          {name: "examNo", label: "检查号"},
          {name: "patientInfo.registNo", label: "登记号"},
          {name: "patientInfo.name", label: "姓名"},
          {name: "inpNo", label: "住院号"},
          {name: "patientInfo.healthCardId", label: "就诊卡号"},
        ],
        searchDateFields: [
          {name: "examTime", label: "检查时间"},
          // {name: "applyTime", label: "预约时间"},
          {name: "auditTime", label: "审核时间"},
          {name: "createTime", label: "登记时间"}
        ],
        //设备型号列表
        devices: [],
        //房间列表
        equipRooms: [],
        // 统计类型
        searchReportType:[
          {name: "report_doctor_name", label: "报告医生", val: "reportDoctor"},
          {name: "audit_doctor_name", label: "审核医生", val: "auditDoctor"},
          {name: "reaudit_doctor_name", label: "复审医生", val: "reauditDoctor"},
          {name: "consultants_name", label: "会诊医生", val: "consultants_name"},
          {name: "regist_user_name", label:"登记员", val:"creator"}
        ],
        
        searchReportTypeMap:{
          "report_doctor_name":"reportDoctor",
          "audit_doctor_name":"auditDoctor",
          "reaudit_doctor_name":"reauditDoctor",
          "consultants_name":"",
          "regist_user_name":"creator"
        }
      },
      fields: {
      },
      exportName: '医生工作量统计',
      exportSheet: 'Sheet1',
      dataArr:[]
    };
  },

  methods: {
    fetchData() {
      let data=[];
      
      this.fields['日期'] = 'c1';
      this.fields['医生'] = 'c2';
      for (let index = 0; index < this.grid.data[0].parts.length; index++) {
        const element = this.grid.data[0].parts[index];
        this.fields[element.part] = 'd' + index;
      }
      this.fields['其他'] = 'o1';

      for (let index = 0; index < this.grid.data.length; index++) {
        const element = this.grid.data[index];
        let tmp = 
        {
            c1: null,
            c2: null,
            o1: null,
        };
        tmp.c1 = element.day;
        tmp.c2 = element.name;
        for (let index1 = 0; index1 < element.parts.length; index1++) {
          const element1 = element.parts[index1];
          tmp[this.fields[element1.part]] = element1.num
        }
        tmp.o1 = element.other;
        data[index] = tmp;
      }
      return data;
      },

    
    handleClick1(vm) {
      // this.searchFormSimp.reportType = vm.$attrs["id"]
      // if(vm.$attrs["id"] == "PartReport"){
      
      // }
    },

    /**
     * 搜索
     */
    getList(mix) {
      let params;
      //
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      if(formSimp ===  this.lastSearchForm) {
        
        params = cloneDeep(this.searchFormSimp);
        // params.reportType = this.para
        //
        if(!!params.textField) {
          params[params.textFieldName] = params.textField;
        }
        delete params.textFieldName;
        delete params.textField;
        //
        if(!!params.dateFieldGe || !!params.dateFieldLt) {
          params[params.dateFieldName + 'Ge'] = params.dateFieldGe || null;

          var tmpDate = new Date(Date.parse(params.dateFieldGe));
          tmpDate.setMonth(tmpDate.getMonth() + 1)
          tmpDate.setHours(0)
          params[params.dateFieldName + 'Lt'] = tmpDate || null;
        }
        // delete params.dateFieldName;
        delete params.dateFieldGe;
        delete params.dateFieldLt;
        if (params.doctorType && params.doctorType.length > 0 && params.doctorName && params.doctorName.length > 0){
          if(params.doctorType == "consultants_name"){
            params.consultantsCode = params.doctorName
          }
          else{
            params[params.doctorType].userName = params.doctorName
          }
        }
        // else if (params.doctorName && params.doctorName.length > 0){
        //   if(params.reportType == "consultants_name"){
        //     params.consultantsCode = params.doctorName
        //   }
        //   else{
        //     params[this.combo.searchReportTypeMap[params.reportType]].userName = params.doctorName
        //   }
        // }
      } else {
        params = cloneDeep(this.searchForm);
      }
      // if (params.doctorName == null || params.doctorName == ""){
      //   this.$modal.msg("请输入医生代码");
      //   return;
      // }

      if(params.examItemCodes) {
        params.examItemCodes.forEach((c, i) => {
          params["examItemCodes[" + i + "]"] = c;
        });
        delete params["examItemCodes"];
      }
      //
      //if(!(mix instanceof Event) && (mix instanceof Object)) {
      //  if(mix.page) { params.pageNum = mix.page; }
      //  if(mix.limit) { params.pageSize = mix.limit; }
      //}
      //已删除状态
      let pos;
      if(!!params.resultStatusValues && params.resultStatusValues.length > 0
        && -1 !== (pos = params.resultStatusValues.findIndex(e => e == "-2"))) {
          params.resultStatusValues.splice(pos, 1);
          //params.resultStatusAsStatus = "2";
          params.status = 2;
      } else {
        params.status = 0;
      }
      this.loading = true;

      eiapi.partFind(params).then(res => {
        this.loading = false;
        this.grid.data = res.rows;
        if (this.grid.data == undefined || this.grid.data[0] == undefined){
          this.grid.data = [{parts:[]}]
        }
      });
    }, 

    /**
     * 查看检查记录
     * @param item 选择的检查记录
     */
    // selectTableRow(item) {
    //   var vm = this;
    //   //
    //   vm.currentTableRow = item;
    // },

    //查询
    search(opt) {
      // this.lastSearchForm = formSimp === opt? opt : null;
      this.getList();
    },

    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },


  },

  mount(){
    console.log(this.props)
  },

  created() {
    this.grid.data[0] = {parts:[]}
  }
};
