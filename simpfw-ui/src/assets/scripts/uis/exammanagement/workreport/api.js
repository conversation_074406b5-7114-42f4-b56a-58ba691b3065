import request,{postForm} from '@/utils/request'

const ctx = '/exammanagement/workReport';

// 查询列表
export function find(query) {
  return postForm({
    url: ctx + '/list',
    data: query,
    headers: {repeatSubmit: false}
  })
}
// 查询部位工作列表
export function partFind(query) {
  return postForm({
    url: ctx + '/part_list',
    data: query,
    headers: {repeatSubmit: false}
  })
}
// 查询总数
export function findTotal(query) {
  return postForm({
    url: ctx + '/total',
    data: query,
    headers: {repeatSubmit: false}
  })
}

// 查询详细
export function get(id) {
  return request({
    url: ctx + '/get?id=' + id,
    method: 'get'
  })
}

// 新增
export function save(data) {
  return request({
    url: ctx + '/save',
    method: 'post',
    data: data
  })
}

// 修改
export function update(data) {
  return request({
    url: ctx + '/update',
    method: 'put',
    data: data
  })
}

// 删除
export function del(id) {
  return request.put(ctx + '/del/' + id)
}

// 撤销删除
export function undoDel(id) {
  return request.put(ctx + '/undoDel/' + id)
}

// 修改工作状态
export function updateResultStatus(data) {
  return request({
    url: ctx + '/updateResultStatus',
    method: 'put',
    data: data
  })
}
//保存报告
export function saveReport(data) {
  return request({
    url: ctx + '/report/save',
    method: 'put',
    data: data
  })
}

//读取接口获取患者登记信息
export function loadRemote(way, code) {
  return request({
    url: ctx + '/loadRemote/' + way + '/' + code,
    method: 'get'
  })
}

//诊前准备就绪
export function updateExamPrerequire(data) {
  return request.put((ctx + '/updateExamPrerequire'), data);
}

//检查时段
export function updateExamPeriod(data) {
  return request.put((ctx + '/updateExamPeriod'), data);
}

//审核
export function auditReport(data) {
  return request.put(ctx + '/report/audit', data);
}
//复核
export function reauditReport(data) {
  return request.put(ctx + '/report/reaudit', data);
}
//召回
export function withdrawAuditReport(data) {
  return postForm({url: ctx + '/report/withdrawAudit', data: data});
}

//签字
export function signReport(data) {
  return request.put(ctx + '/report/sign', data);
}

//复制
export function copy(data) {
  return request.post(ctx + '/copy', data);
}

//拆分
export function split(data) {
  return request.post(ctx + '/split', data);
}
//保存报告文件：pdf，jpg
export function uploadReportDoc(data) {
  return request({
    url: ctx + "/report/uploadReportDoc",
    method: "post",
    data,
    timeout: (30 * 1000)
  })
}