
import BaseGridModel from "@/assets/scripts/uis/BaseGridModel";

import * as api from "@/assets/scripts/uis/exammanagement/examprecond/api";

let model = {
  name: "ExamPrecond",
  extends: BaseGridModel,
  dicts: [],
  data() {
    return {
      searchForm: {
        examInfo: {
          id: null, 
          ordId: null, 
          patientInfo: {
            name: null
          }
        }
      }
    };
  },

  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      api.find(this.searchForm).then(res => {
        this.loading = false;
        this.grid.total = res.total;
        this.grid.data = res.rows;
      });
    },
    /** 修改 */
    handleEdit(row) {
      this.$router.push({path: "/exammanagement/PatientRegist", query: {"examInfo.id": row.examInfo.id}});
    },
    /** 准备就绪 */
    handleUpdate(row) {
      this.$modal.confirm('是否确认准备就绪？').then(() => {
        const nrow = {id: row.id, status: 1};
        return api.update(nrow);
      }).then(res => {
        console.log(res);

        if(200 === res.code) {
          this.getList();
          this.$modal.msgSuccess("已准备就绪");
        }
      }).catch(err => console.error(err));
    }
  }
};

export default model;