import Examnn from "@/views/gis/exammanagement/patient/comp/Exammn";

import * as api from "@/assets/scripts/gis/exammanagement/examinfo/api";

let model = {
  name: "ExamPrecond",

  components: {Examnn},

  methods: {
    handleUpdate(row) {
      this.$modal.confirm('是否确认准备就绪？').then(() => {
        const nrow = {id: row.id, examPrerequire: 1};
        return api.updateExamPrerequire(nrow);
      }).then(res => {
        if(200 === res.code) {
          this.$refs.examnn.getList();
          this.$modal.msgSuccess("已准备就绪");
        }
      }).catch(err => console.error(err));
    }    
  }
};

export default model;