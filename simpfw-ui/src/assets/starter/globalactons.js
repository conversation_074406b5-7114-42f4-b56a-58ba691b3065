import _ from "lodash"
let _actions = {

}
export const actions = {
    // 验证用户是否具备某权限
    registerAction(key, fn) {
        if (_.isString(key) && _.isFunction(fn)) {
            _actions[key] = fn
        }
    },

    getAction(key) {
        return _actions[key]
    },

    removeAction(key) {
        delete _actions[key]
    },

    getActions() {
        return _actions;
    },

    triggerActions(key, ...args) {
        _actions[key] && _actions[key](...args)
    }

}