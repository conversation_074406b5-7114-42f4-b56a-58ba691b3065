import router from '@/router'
function messageFromWindow(event, message) {
    console.log('收到来自别的窗口信息', event, message)
    if (message && message.type && 'main-go-to-router' == message.type) {
        if (message.to) {
            if ('InterfaceMonitoring' == message.to) {
                router.push({
                    name: message.to,
                    query: { "name": "interfaceMonitoring", "sheetType": "interfaceMonitoring", "refresh": "3" }
                });
                MT.actions.getAction("ocr.select.time_range")(message.params.dateRange.type)
            } else {
                router.push({
                    name: message.to,
                });
                MT.actions.triggerActions("normal.patientsheet.select.time_range", message.params.dateRange.type)
            }
        }

    }
}
export default {
    // 验证用户是否具备某权限
    start() {
        // 如果是electron环境变量
        if (window.electron) {
            console.log("当前环境是MT客户端")
            window.electron.ipcRenderer.on('message-from-window', messageFromWindow)
        }
    },

}