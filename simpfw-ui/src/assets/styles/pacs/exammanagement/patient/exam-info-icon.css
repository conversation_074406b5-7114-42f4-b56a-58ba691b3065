/* .result-status-icon-0{
  color: #58B5E4;
} */
.result-status-icon-1,.result-status-icon-2{
  color: #4fc6f5;
}
.result-status-icon-3, .result-status-icon-4{

  color: #7B79EC;
}
.result-status-icon-0 ,.result-status-icon-5, .result-status-icon-6{
  color: #85D04A;
}

div >>> .table-cell-result-status-0
, div >>> .el-table__row:hover .table-cell-result-status-0
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-0

, div >>> .table-cell-result-status-6
, div >>> .el-table__row:hover .table-cell-result-status-6
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-6
{
  background-color: #85D04A !important;
  color: white;
}

div >>> .table-cell-unexecuteOrdId-status-1
{
  color: red;
}


 div >>> .table-cell-result-status-5
, div >>> .el-table__row:hover .table-cell-result-status-5
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-5
{
  background-color: #228b22 !important;
  color: white;
}

div >>> .table-cell-result-status-1
, div >>> .el-table__row:hover .table-cell-result-status-1
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-1

, div >>> .table-cell-result-status-2
, div >>> .el-table__row:hover .table-cell-result-status-2
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-2
{
  background-color: #4fc6f5 !important;
  color: white;
}
div >>> .table-cell-result-status-3
, div >>> .el-table__row:hover .table-cell-result-status-3
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-3

, div >>> .table-cell-result-status-4
, div >>> .el-table__row:hover .table-cell-result-status-4
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-4

, div >>> .table-cell-result-status-11
, div >>> .el-table__row:hover .table-cell-result-status-11
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-11

, div >>> .table-cell-result-status-12
, div >>> .el-table__row:hover .table-cell-result-status-12
, div >>> .el-table--striped .el-table__body .el-table__row--striped .table-cell-result-status-12
{
  background-color: #7B79EC !important;
  color: white;
}
/*div >>> .el-table__row:hover .table-cell-result-status
, div >>> .current-row .table-cell-result-status{
  color: unset !important;
}*/
