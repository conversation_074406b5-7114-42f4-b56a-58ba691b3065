html,body,#app{
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.qc-container{
  font-size: 1.6em;
  overflow: hidden;
}
.qc-header, .qc-footer-inner{
  padding: 0;
  background-color: #0E2E55;
  color: white;
}
.qc-header-inner >>> .el-col{
  height: 100px;
}
.qc-header-logo{
  white-space: pre;
}
.qc-header-title{
  text-align: center;
  /*line-height: 2.4em;*/
  font-size: 1.4em;
  font-family: "Microsoft YaHei";
  display: flex;
  align-items: center;
  justify-items: center;
  height: 100%;
}
.qc-header-title span{
  flex-grow: 1;
}

.qc-header-datetime {
  float: right;
  width: 10em;
  height: 100%;
  padding-left: 32px;
  font-size: 0.9em;
}
.qc-header-datetime::after{
  clear: both;
}
.qc-header-datetime>div{
  height: 50%;
  line-height: 50px;
}

.qc-footer-inner{
  padding: 0 16px;
  line-height: 2em;
}

.qc-container >>> .el-main{
  padding: 0px;
  overflow: hidden;
}
.qc-container .nested-card >>> .el-card__header{
  text-align: center;
  color: white;
  line-height: 42px;
}
.qc-container .nested-card >>> .el-card__body{
  overflow: hidden;
  color: white;
}
/*.qc-container .nested-card .qc-queue-grid-row:nth-child(even){
  background-color: #DEEEFC;
}*/
.waiting-parag{
  background-color: #0277B2;
}
.waiting-parag:nth-child(even){
  background-color: #018FD7;
}
.qc-queue-grid-parag-title{
  text-align: center;
  background-color: #3291C1;
  line-height: 42px;
}
.waiting-parag:nth-child(even) .qc-queue-grid-parag-title{
  background-color: #31A4DF;
}
.qc-queue-grid-row, .qc-queue-item{
  /*padding: 6px;*/
  line-height: 2em;
}
/*.qc-queue-grid-row{
  width: 32%;
  margin: 0 4px;
  float: left;
}
.qc-queue-grid-row:last-child::after{
  clear: both;
}*/
.qc-queue-grid-row{
  text-align: center;
}
.qc-queue-grid-row-ing {
  font-size: 1.4em;
  color: #3D9970;
}
.qc-queue-item{
  text-align: left;
  /*background-color: #DEEEFC;*/
  overflow: hidden;
}
.past-card .qc-queue-item{
  display: inline-block;
  width: 50%;
}
.past-card .qc-queue-item:nth-child(2n) {
  width: 49%;
  margin-left: 4px;
}
.qc-queue-item span{
  margin-left: 1em;
  white-space: nowrap;
  word-break: keep-all;
}

.qc-queue-underway{
  position: absolute;
  top: 20px;
  left: 20px;
  margin: 0 auto;
  width: 800px;
  /*border: 4px solid #0E2D59;*/
  border-radius: 16px;
  background-color: white;
}
.qc-queue-underway-top{
  text-align: center;
  line-height: 2em;
  margin-top: 1em;
  font-size: 2em;
}
.qc-queue-underway-main{
  text-align: center;
  font-size: 3em;
  margin: 1em auto 1.5em;
}
.qc-queue-underway-bottom{
  padding-right: 1em;
  text-align: right;
  line-height: 2em;
}

.waiting-card >>> .el-card__header{
  background-color: #0B7CB5;
}
.waiting-card >>> .el-card__body{
  background-color: #018FD7;
  display: flex;
  padding: 0;
}
.waiting-parag{
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}
.qc-queue-grid-parag-body{
  flex-grow: 1;
  overflow: hidden;
}
.calling-card >>> .el-card__header{
  background-color: #539C47;
}
.calling-card >>> .el-card__body{
  background-color: #388E2A;
}
.past-card >>> .el-card__header{
  background-color: #4D564C;
}
.past-card >>> .el-card__body{
  background-color: #384137;
}
