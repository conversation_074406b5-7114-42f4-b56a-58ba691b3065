.regist-container{
  background-color: #FFF;
}
div.searchFormPane{
  padding: 4px 4px;
}
div.examPartsTreePane, div.examPartsPane{
  height: 200px; 
  overflow: auto;
}

div.examPartsPane li{
  cursor: default;
  padding: 2px 4px;
}
div.examPartsPane li:hover{
  background-color: #EFEFEF;
}

div.simp-date-picker{
  width: 100%;
}
div.simp-date-picker >>> .el-input__inner{
  padding-left: 15px;
}

.nested-card-alone .app-container{
  padding: 0;
}

.form-element-group{
  background-color: #F2F5F8;
  padding: 1px;
  display: inline-block;
}
.form-element-group >>> .el-input__inner{
  border-width: 0;
  background-color: transparent;
}
.form-element-group >>> .el-select .el-input__inner{
  background-color: #FFF;
}

.patient-info-card, .exam-info-card{
  padding-right: 16px;
  border-width: 0;
}

.regist-wrap{

}
.regist-form-pane{
  width: 60%;
}
.regist-form-pane>>>.el-form-item__content .el-input__inner{
  padding-left: 4px;
  padding-right: 4px;
}
.regist-tabs-pane{

}

.regist-form-pane .foot-tools>>>.el-checkbox{
  margin-right:  16px;
}