
main.pcq-body-pane{
  padding: 0;
}
main.pcq-body-pane th{
  font-weight: normal;
}
div.box-body-mh{
  min-height: 160px;
}
aside.pcq-form-pane{
  margin: 0;
  padding: 0;
}
aside.pcq-form-pane .el-radio, aside.pcq-form-pane .el-checkbox{
  margin-right: 8px;
}

div.el-message-box-loading .el-button--small {
  padding-top: 8px;
  padding-bottom: 8px;
}
div.el-message-box-loading .el-message-box__btns {
  text-align: center;
}
button.el-button--primary-second {
  color: #409EFF;
  background-color: #ECF5FF;
}

div.pcq-box .el-form-item__label, div.pcq-box .el-checkbox__label {
  font-size: 1.2em;
}

/*div.qa-diag-outline-pane{
  position: relative;
}*/
div.qa-diag-outline-pane>.el-row{
  margin-left: 0!important;
  margin-right: 0!important;
}
div.qa-diag-outline-toggler {
  display: none;
  position: absolute;
  top: 0;
  z-index: 9;
  height: 16px;
  text-align: center;
}
div.qa-diag-outline-toggler .el-button{
  padding: 0px 16px;
  border-radius: 4px 4px 0 0;
}

.inner-container-aside{
  border-radius: 0 8px 0 0;
  margin-right: 8px;
}

.inner-container-aside-collapse{
  display: none;
  margin-left: auto;
}

.data-container{
  padding: 8px 0 0 8px;
  height: 100%;
  background-color: #FFF;
  border-radius: 8px 0 0 0;
}

.exam-search-form >>> .el-input__inner{
  padding-left: 6px;
  padding-right: 6px;
}
.exam-search-form .el-input__inner{
  font-size: 0.9em;
}

.search-form-toggler{
  width: 32px;
  height: 32px;
}

.inner-container-aside div.nested-card .el-card__header{
  padding: 16px 0 16px 8px;
}

.exam-search-form-simp>.el-select, .exam-search-form-simp>.el-input{
  margin-left: 4px;
  width: 100px;
}
.exam-search-form-simp>.el-button{
  margin-left: 4px;
}
.exam-search-form-simp>.svg-icon{
  vertical-align: middle;
}
