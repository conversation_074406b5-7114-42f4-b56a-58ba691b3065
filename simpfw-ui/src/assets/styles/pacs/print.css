@page {
  size: A4 portrait;
  margin: 0;
}

*.a4page{
  width: 210mm;
  height: 296.8mm;
  padding: 0 16mm;
  margin: 0cm auto;
}

@media screen {
  .a4page {
    border: 1px #D3D3D3 solid;
    border-radius: 2px;
    background: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  }
}

.page-break{
  page-break-before: always;
}

@media print{
  div.printingdialog, div.printingdialog div.el-dialog{
    display: unset;
    /*height: 100%;*/
    width: 100%;
    border-width: 0;
    box-shadow: none;
    margin-top: 0 !important;
    overflow: hidden;
    border-radius: 0px !important;
  }
  /*分页问题*/
  div.printingdialog{
    position: relative;
  }
  /*div.printingdialog  div.el-dialog{
    background-color: red;
  }*/
  div.printingdialog div.el-dialog__header{
    display: none;
  }
  div.printingdialog div.el-dialog__body{
    height: 100% !important;
    /*width: 100% !important;*/
    padding: 0 !important;
  }
  div.printingdialog .a4page{
    /*height: 100%;
    width: unset;*/
    border-width: 0;
    box-shadow: none;
    overflow: hidden;
    margin: 0;
  }

  div.v-modal{
    /*opacity: 1;
    background-color: white;*/
    display: none;
  }
  
  .hide-print, .rightPanel-container, #app{
    display: none !important;
  }
}