.report-previewer-wrap{
  transform-origin: top left;
}
.report-previewer-wrap + .report-previewer-wrap{
  margin-top: 16px;
}
.report-previewer-page.a4page{
  padding: 0 0mm 0 1mm;
}
.report-previewer-form{
  height: 98%;
}

.report-previewer-form >>> .el-form-item__label{
  text-align: left;
  padding-right: 0px;
}
.report-preview-title {
  margin: 8px auto;
  padding: 0;
  text-align: center;
  font-weight:600;
  color:rgb(0, 0, 0);
  font-family: songti;
}
.report-preview-subject{
  /* margin: 8px auto; */
  margin-top: 25px;
  padding: 0;
  text-align: center;
  font-weight: 600;
  font-family: songti;
}
.report-preview-header{
  padding-top:30px;
}
.report-preview-header-inner{
  position: relative;
  /*margin-top: 40px;*/
  height: 70px;
}
.report-preview-logo{
  position: absolute;
  left: 110px;
  top: -10px;
  width: 68px;
  height: 68px;
}

.report-preview-title{
  font-size: 24px;
}

.report-preview-logo img{
  width: 100%;
  height: 100%;
}
.report-preview-title{
  font-size: 24px;
}
.report-preview-subject{
  margin: 6px auto;
  font-size: 20px;
  color: #FF1717;
}
.report-previewer-wrap{
  position: relative;
}
.report-preview-footer{
/*  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;*/
  margin-bottom: 16px;
}
.report-header , .report-footer{
    margin-top: 10px;
}

.report-preview-hinfo >>> .el-form-item__label
, .report-preview-hinfo >>> .el-form-item__content{
  line-height: 1.1;
  font-weight: bold;
}


.report-preview-hline{
  border-bottom: 2px solid rgb(0, 0, 0);
  margin-bottom: 5px;
}
.report-para-title{
 /* font-family:sans-serif; */
  color:rgb(0, 0, 0);
  font-family: songti;
  font-size: 1.2em;
  font-weight: 300;
  line-height: 2;
}
.text-block-pre{
  /* font-family:sans-serif; */
  font-family: songti;
  margin: 0 1.2em;
  line-height: 1.3;
  color:rgb(39, 39, 39);
  white-space: pre-wrap;
}
.report-preview-image{
  margin: 0 auto;
  padding: 5px 0;
  text-align: center;
  min-height: 110px;
  /*display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;*/
}

.cornerstone-element-container{
  display: inline-block;
  margin: 0 3px 4px;
  /*width: 400px;
  height: 320px;*/
  text-align: center;
}
/*.report-preview-image-1 .cornerstone-element-container{
  width: 400px;
}
.report-preview-image-2 .cornerstone-element-container{
  width: 49%;
}
.report-preview-image-3 .cornerstone-element-container{
  width: 33%;
}
.report-preview-image-4 .cornerstone-element-container{
  width: 24%;
}*/
.cornerstone-element, .cornerstone-element canvas{
  width: 100%;
  height: 100%;
}
.keepline{
  white-space: nowrap;
  word-break: keep-all;
}

.signDoctorItem label, .signDoctorItem img{
  vertical-align: middle;
}
.signDoctorItem img{
  height: 32px;
}

.report-preview-tx-exam-desc{
  min-height: 60%;
}
.report-preview-main{
  overflow: hidden;
}


.report-flex-item-fill{
    flex-grow:1/*0.8*/
}
.report-preview-tx-exam-desc{
  min-height: 60%;
}

.report-preview-main{
  overflow: hidden;
}
.report-header-label{
    color:rgb(0, 0, 0);
    font-weight: 300;
    /* font-family:sans-serif; */
    font-family: songti;
}

.report-header-value{
    color:rgb(39, 39, 39);
    /* font-weight:bold; */
    /* font-family:sans-serif; */
    font-family: songti;
}
/*导出jpg、pdf样式*/
/*.convertingdialog-pdf{
  visibility: hidden;
}*/
.convertingdialog-pdf.a4page{
  padding: 0;
}
.convertingdialog-pdf .report-previewer-form{
  padding: 0 6mm 0 12mm;
}
.convertingdialog-pdf, .convertingdialog-pdf .report-previewer-form{
  background-color: white;
}
/*打印样式*/
@media print{
  .el-dialog__wrapper.popupdialog{
    display: none;
  }
  .el-dialog__wrapper.printingdialog{
    display: unset;
  }

  .report-preview-logo{
    left: 110px;
  }

  .report-previewer-wrap + .report-previewer-wrap {
    margin-top: 0;
    margin-bottom: -1px;
  }

  .report-previewer-wrap{
    transform: scale(1) !important;
  }
}
