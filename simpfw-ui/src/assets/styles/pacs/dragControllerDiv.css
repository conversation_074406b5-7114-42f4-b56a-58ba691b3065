 /* 拖拽相关样式 */
    /*包围div样式*/
    .box {
        width: 100%;
        height: 100%;
        /* margin: 1% 0px; */
        /* overflow: hidden; */
        /* box-shadow: -1px 9px 10px 3px rgba(0, 0, 0, 0.11); */
    }
    /*左侧div样式*/
    .left {
        width: calc(22% - 10px);  /*左侧初始化宽度*/   
        height: 100%;
        background: #FFFFFF;
        float: left;
        /* overflow: auto; */
    }
    /*拖拽区div样式*/
    .resize {
        cursor: col-resize;
        float: left;
        position: relative;
        top: 45%;
        background-color: #d6d6d6;
        border-radius: 5px;
        margin-top: -10px;
        margin-left: -10px;
        width: 10px;
        height: 50px;
        background-size: cover;
        background-position: center;
        /*z-index: 99999;*/
        font-size: 32px;
        color: white;
    }
    /*拖拽区鼠标悬停样式*/
    .resize:hover {
        color: #444444;
    }
    /*右侧div'样式*/
    .mid {
        float: left;
        width: 78%;   /*右侧初始化宽度*/
        height: 100%;
        /* background: rgb(205, 0, 0);
        box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11); */
        padding-left: 10px;
        /* overflow-y: auto; */
    }

    