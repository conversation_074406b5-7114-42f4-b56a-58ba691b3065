div.cornerstone-elements-wrap{
  margin: 8px 8px;
  background-color: #F4F9FC;
  border-radius: 8px;
  overflow: hidden;
}
div.cornerstone-elements-scroller{
  width: 24px;
  height: 110px;
  line-height: 110px;
  text-align: center;
  cursor: default;
  user-select: none;
}
div.cornerstone-elements-scroller:hover{
  background-color: #D8EBFF;
}
div.cornerstone-elements-pane{
  padding: 8px 0px; 
  overflow: auto; 
  white-space: nowrap; 
  word-break: keep-all;
  height: 120px;
  
}
div.cornerstone-elements-pane-emtpy{
  background: url(../../images/no-image.png) center center no-repeat;
}
.buttons-pane-gap{
  text-align: right;
}

div.cornerstone-element-container{
  display: inline-block;
  width: 147px;
  height: 83px;
  border: 1px solid #DDD;
  background-color: #DDD;
  position: relative;
  vertical-align: middle;
}
div.cornerstone-element-container{
  margin-left: 12px;
}
div.cornerstone-element-container:first-child{
  margin-left: 0px;
}
div.cornerstone-element{
  width: 100%;
  height: 100%;
}
div.cornerstone-element-no{
  position: absolute;
  z-index: 9;
  right: 4px;
  bottom: 4px;
  padding: 2px 6px;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 4px;
}
div.cornerstone-element-cls{
  position: absolute;
  z-index: 9;
  left: -1px;
  bottom: 8px;
  padding: 2px 6px;
  width: 18px;
  height: 18px;
}

div.cornerstone-element-info{
  position: absolute;
  z-index: 9;
  left: -5px;
  bottom: -15px;
  padding: 2px 6px;
  width: 147px;
  height: 18px;
}

div.cornerstone-element-cls i{
  font-size: 20px;
  color: white;
}
div.cornerstone-element-cls:hover i{
  color: red;
}
div.exami-mage-checkbox{
  position: absolute;
  z-index: 2;
  left: 4px;
  top: 4px;
  display: inline-block;
  vertical-align: middle;
}
div.exami-mage-checkbox >>> .el-checkbox__inner{
  border-color: #999;
  width: 18px;
  height: 18px;
}
div.exami-mage-checkbox >>> .el-checkbox__inner::after {
  top: 3px;
  left: 7px;
}
.cornerstone-element-video{
  text-align: center;
  background-color: #000;
}
.cornerstone-element-video i{
  margin-top: 16px;
  font-size: 54px;
  color: #FFF;/*#409EFF*/
}

