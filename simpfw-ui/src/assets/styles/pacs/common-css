ul,li{
  margin: 0;
  padding: 0;
  list-style-type: none;
}

div.flex-container{
  display: flex;
}
div.flex-container-column{
  flex-direction: column;
}
div.flex-item-fill{
  flex-grow: 1;
}

div.popupdialog .el-dialog{
  width: 800px;
}
div.popupdialog .el-dialog__header{
  padding: 4px;
  background-image: linear-gradient(to bottom,#dfe1e5 0,#b9bbbf 100%);
}
div.popupdialog .el-dialog__headerbtn{
  top: 8px;
}
div.popupdialog .el-dialog__body{
  padding: 4px;
}
div.popupdialog .el-dialog__footer{
  padding: 4px;
  background-color: #d1d7df;
}
div.popupdialog .el-dialog__footer .dialog-footer button + button{
  margin-left: 4px;
}

div.form-item-col-3{
  width: 70%;
}
div.form-item-col-1{
  width: 30%;
}

.form-el-w160{
  width: 160px;
}

.el-button+.el-button{
  margin-left: unset;
}
div.nested-card{
  border-radius: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}
div.nested-card .el-card__header{
  padding: 4px 7px;
  min-height: unset;
  /*background-image: linear-gradient(to bottom,#dfe1e5 0,#b9bbbf 100%); */
  background-color: #003366;
  color: #FFF;
}
div.nested-card .el-card__body{
  padding: 4px;
  flex-grow: 1;
}
div.nested-card-tools{
  float: right;
}

*.hide {
  display: none;
}

section.content-main {
  padding: 0px;
  background-color: #fff;
  overflow: auto;
}

section.content-main .data-body {
  padding: 0;
}
div.content-wrap{
  overflow: auto;
  height: 100%;
}

div.pane-head {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

div.pane-head-label {
  line-height: 30px;
  font-size: 16px;
}

header.main-header {
  /*background-color: #2a579a;*/
  background-color: #167092;
}

body.skin-blue .main-header .logo {
  background-color: transparent;
}

body.skin-blue .main-header .logo:hover,
body.skin-blue .main-header .navbar .sidebar-toggle:hover {
  /*background-color: #204f93;*/
  background-color: #167092;
}

body.skin-blue .main-header .navbar {
  background-color: transparent;
}

body.skin-blue .wrapper,
body.skin-blue .main-sidebar,
body.skin-blue .left-side {
  /*background-color: #e3e7ec;*/
  background-color: #167092;
}

/*.skin-blue .main-sidebar{
      width:200px;
  }*/

body.skin-blue .user-panel>.info,
body.skin-blue .user-panel>.info>a {
  /*color: #555;*/
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu>li:hover>a,
body.skin-blue .sidebar-menu>li.active>a,
body.skin-blue .sidebar-menu>li.menu-open>a {
  background-color: transparent;
  color: #000;
  color: #EAF6FB;
  font-weight: bold;
  background-color: #3B70B8;
}

body.skin-blue .sidebar-menu>li.treeview>a {
  background-color: #3B70B8;
}

body.skin-blue .sidebar-menu>li>.treeview-menu {
  background-color: #3B68AA;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a,
body.skin-blue .sidebar a {
  color: #555;
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a {
  padding: 8px 6px 8px 28px;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a i {
  margin-right: 4px;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a:hover,
body.skin-blue .sidebar-menu .treeview-menu>li>a.router-link-active {
  color: #000;
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a.router-link-active {
  background-color: #fff;
  background-color: #2885EB;
  border-left: 2px solid #A0CEFD;
  padding-left: 26px;
}

ul.treeview-menu {
  padding-left: 0;
}

header.main-header .logo .logo-lg {
  font-weight: 700;
}

body.skin-blue .main-header .navbar .sidebar-toggle {
  height: 50px;
  overflow: hidden;
}

form.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

div.form-h-item .el-form-item__label {
  width: 5.2em;
}

div.form-row {
  margin: 4px 0;
}

div.form-h-item .el-form-item__content {
  width: 160px;
}

div.form-h-item .el-form-item__label {
  width: 5.2em;
}

div.form-h-item .el-form-item__content {
  width: 160px;
}

div.el-table th.el-table__cell {
  background-color: #f2f2f2;
}
div.el-table--medium .el-table__cell{
  
}
div.el-table .table_cell_opt .el-link{
  color: blue;
}

div.content-pane {
  margin: 4px;
}

div.paginationClass {
  margin-top: 4px;
}

div.el-tabs__header {
  margin-bottom: 0px;
}

div.el-tabs--card>.el-tabs__header .el-tabs__nav {
  border-left: none;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item {
  background-color: #f4f6f8;
  color: #23508e;
  height: 32px;
  line-height: 32px;
}

/*div.el-tabs--card>.el-tabs__header .el-tabs__item{
      border-bottom-width:0;
  }*/
div.el-tabs--card>.el-tabs__header .el-tabs__item,
div.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: #e4e7ed;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item.is-active.is-closable {
  background-color: #eaedf1;
  color: #23508e;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item .el-icon-close {
  width: 14px;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item.is-closable:hover {
  padding-left: 20px;
  padding-right: 20px;
}

div.pane-head .el-col:last-child {
  text-align: right;
}

button.tab-button {
  position: relative;
  bottom: -9px;
  margin-left: 0 !important;
  float: left;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

div.search-form-pane {
  max-height: 144px;
  overflow: hidden;
  overflow-y: auto;
}

/*弹出框*/
div.checkFeedbackDialog .el-form-item {
  margin-bottom: 0px;
}

div.editFormDialog div.el-form-item {
  margin-bottom: 8px;
}

div.box-noborder {
  border-width: 0px;
}

div.checkFeedbackDialog .box-body {
  padding: 0;
}

div.checkFeedbackDialog .box-footer {
  margin-top: 8px;
  padding: 2px 4px;
  border: 1px solid #f4f4f4;
  border-radius: 6px;
}

div.align-center {
  text-align: center;
}

/*表格*/
.current-row>td {
  background: rgba(0, 158, 250, 0.219) !important;
}

.el-table__header th{
  font-weight: normal;
}
.el-table__body tr:hover>td {
  background-color: rgba(0, 158, 250, 0.219) !important;
}

button.el-button--mini, button.el-button--small{
  font-size:14px;
}
/*#mainTabContainer .el-tabs__item{
  font-size:16px;
}*/
div.el-table--mini, div.el-table--small{
  font-size:14px;
}

/*表格无内容*/
*.empty-body{
   padding:1.2em 0;
   text-align:center
}

/*消息*/
div.message-dark{
  min-width: 8em;
  background-color: #303133;
  border-color: #303133;
  transform-origin: center bottom 0px;
}
div.message-dark .el-message__content {
  color: #fff;
}

i.el-message__closeBtn{
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  font-size: 24px;
}

button [class*=" el-icon-"], button [class^=el-icon-] {
  font-size: 0.95em;
}

/*信息*/
.el-icon-success-ok{
  color: green;
}
.el-icon-error-nok{
  color: red;
}

/* */
div.draw-pane {
  display: flex;
  flex-direction: column;

  width: 30%;
  min-width: 300px;

  background: white;
  border-left: 1px solid #DDD;

}
div.draw-pane, div.draw-pane *{
  font-size: 16px;
}
div.draw-pane-draw{
  transition: all 0.3s linear;
  transform: translateX(100%);

  position: absolute;
  top: 40px;
  right: 0;
  z-index: 99;
  bottom: 29px;
  box-shadow: 4px 0px 4px 4px #999;
}
div.draw-pane-min{
  bottom: unset;
}
div.draw-pane-show{
  transform: translateX(0%);
}
div.draw-pane-form-adapt .el-form-item{
  display: inline-block;
  margin-bottom: 4px;
  width: 49%;
  vertical-align: top;
}
div.draw-pane-form-adapt .el-form-item:hover{
  background-color: #ecf5ff;
  border-radius: 2px;
}
div.draw-pane-form-adapt .draw-pane-form-adapt-sitem{
  width: 100%;
}
div.draw-pane-form-adapt .draw-pane-form-adapt-sitem-block .el-form-item__content{
  clear: left;
  margin-left: 0!important;
  white-space: pre-wrap;
  padding: 4px;
}
div.draw-pane-form-adapt .el-form-item .el-form-item__content
, div.draw-pane-form-adapt .el-form-item .el-form-item__label{
  line-height: 1.6;
  font-weight: normal;
}
div.draw-pane-form-adapt .el-form-item .el-form-item__label{
  color: #999;
}
div.draw-pane-form-adapt .el-form-item .el-form-item__content{
  word-break: break-all;
}
div.draw-pane-header{
  padding:4px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid #DDD;
}
div.draw-pane-header button{
  /*padding: 4px;*/
  border-width: 0;
  font-weight: bold;
}
div.draw-pane-header-l {
  flex-grow: 1;
  text-align: left;
}
*.draw-pane-header-txt{
  display: inline-block;
  margin-top: 8px;
}
div.draw-pane-header-r {
  
}
div.draw-pane-content{
  flex-grow: 1;
  overflow: auto;
  padding: 4px 0;
}
/*tr.grid-row .el-button{
  padding: 2px;
  font-size: 18px;
}*/
button.layout-button{
  padding-left: 0;
  padding-right: 0;
  width: 1px;
  overflow: hidden;
  visibility: hidden;
}

div.el-form-item, div.el-form-item--small.el-form-item{
  margin-bottom: 2px;
}

/*表格操作列*/
td.button-col .el-button{
  padding-left: 8px;
  padding-right: 8px;
}
td.button-col .el-button + .el-button{
  margin-left: 4px;
}
div.el-tooltip__popper.is-light{
  background-color: #fdf5e6;
}
div.el-tooltip__popper.is-light[x-placement] div.popper__arrow::after {
  border-top-color: #fdf5e6;
}

/*右键菜单*/
div.contextmenu-pane {
  position: absolute;
  z-index: 99;
  overflow: hidden;
  width: 150px;
  background-color: #FFFFFF;
  border: 1px solid #DDD;
  box-shadow: 2px 2px 2px #999;
}
div.contextmenu-pane ul, div.contextmenu-pane li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  cursor: default;
}
div.contextmenu-pane li {
  margin: 2px;
  text-align: left;
  border-radius: 3px;
}
div.contextmenu-pane li:hover {
  background-color: #EFEFEF;
}
div.contextmenu-pane li button, div.contextmenu-pane li button:hover {
  padding: 4px 0 4px 16px;
  width: 100%;
  text-align: left;
  color: inherit;
}

/*主内容区高度*/
.hasTagsView .inner-container{
  height: calc(100vh - 84px);
}
.inner-container .el-header, .inner-container .el-main, .inner-container .el-aside
,.nested-container .el-header, .nested-container .el-main, .nested-container .el-aside {
  margin: 0;
  padding: 0;
}

/*Tabs, 标签位于右侧*/
div.tabs-wrap .tabs-hor{
  min-height: 200px;
}
div.tabs-wrap .tabs-hor .el-tabs__item{
  padding: 10px 2px;
  width: 3em;
  text-align: center;
  word-break: break-all;
  white-space: normal;
  height: unset;
  line-height: 1.2;
  background-color: #EEE;
  color: #999;
  border-bottom: 1px solid #999;
}
div.tabs-wrap .tabs-hor .is-active{
  background-color: #409EFF;
  color: white;
}
div.tabs-wrap .tabs-hor.el-tabs--right .el-tabs__header.is-right {
  margin-left: 0;
}

/*列表*/
ul.ulist, ul.ulist li {
  padding: 0;
  margin: 0;
  list-style: none;
}
ul.ulist .el-button{
  margin: 0;
  padding: 2px 4px;
}

/*清除左右浮动*/
*.clearfix::after{
  clear: both;
}

div.fieldset-legend{
  margin: 4px auto;
  padding: 4px 8px;
  border-bottom:  1px solid #DDD;
}