@import url("print.css");

ul,li{
  margin: 0;
  padding: 0;
  list-style-type: none;
}
p{
  margin: 0;
  padding: 0;
}
*.hei100{
  height: 100%;
}
*.hei90{
  height: 90%;
}
*.hei50{
  height: 50%;
}
*.scroll-auto{
  overflow: auto;
}
*.hide {
  display: none;
}
*.w100{
  width: 100%;
}

.al-right{
  text-align: right;
}
.al-center{
  text-align: center;
}

*.flex-container{
  display: flex;
}
*.flex-container-column{
  display: flex;
  flex-direction: column;
}
*.flex-item-fill{
  flex-grow: 1;
}
*.flex-item-fill-2{
  flex-grow: 2;
}

div.popupdialog{
  border-radius: 12px;
}
div.popupdialog .el-dialog__header{
  padding: 16px 30px;
}
div.popupdialog .el-dialog__headerbtn{
  top: 12px;
  right: 32px;
  padding: 4px 4px;
  font-size: 24px;
}
/*div.popupdialog .el-dialog__headerbtn .el-dialog__close{
  color: #FFF;
}*/
div.popupdialog .el-dialog__title, div.popupdialog .dialog-slot-title{
  font-size: 18px;
  font-weight: bold;
}
div.popupdialog .el-dialog__title::before, div.popupdialog .dialog-slot-title::before{
  content: "";
  display: inline-block;
  margin-right: 4px;
  height: 16px;
  background: #018fd7;
  border-radius: 3px;
  overflow: hidden;
  border: 2px solid #018fd7;
  vertical-align: middle;
}
div.popupdialog .el-dialog__body{
  padding: 4px 16px;
}
div.popupdialog .el-dialog__footer{
  padding: 4px 30px 12px;
}
div.popupdialog .el-dialog__footer .buttons-left{
  float: left;
}
div.popupdialog .el-dialog__footer .buttons-left::after{
  clear: both;
}
div.popupdialog .el-dialog__footer .dialog-footer button + button{
  margin-left: 4px;
}
div.popupdialog-noheader .el-dialog__header{
  display: none;
}
div.popupdialog div.dialog-slot-title{
  padding-right: 48px;
}
div.popupdialog .dialog-slot-title-tools .el-button{
  padding: 4px 8px;
  vertical-align: top;
}
div.popupdialog .dialog-slot-title-tools .el-button + .el-button{
  margin-left: 4px;
}

div.form-item-col-3{
  width: 70%;
}
div.form-item-col-1{
  width: 30%;
}

.form-el-w160{
  width: 160px;
}

.el-button+.el-button{
  margin-left: unset;
}
div.nested-card{
  border-radius: 0;
  border-width: 0 1px;
  display: flex;
  flex-direction: column;
  /*height: 100%;*/
}
div.nested-card .el-card__header{
  padding: 4px 8px;
  min-height: unset;
  /*background-image: linear-gradient(to bottom,#dfe1e5 0,#b9bbbf 100%); */
  /*background-color: #2980B9;#003366
  color: #FFF;*/
  border-bottom-width: 0;
}
div.nested-card .nested-card-header-title{
  /*position: relative;*/
  padding-left: 0px;
}
div.nested-card .nested-card-header-title::before{
  content: "";
  /*position: absolute;
  left: 0px;
  top: 0px;*/
  width: 4px;
  height: 16px;
  background: #018fd7;
  border-radius: 3px;
  display: inline-block;
  margin-right: 4px;
  vertical-align: middle;
}
div.nested-card .el-card__body{
  padding: 4px;
  flex-grow: 1;
}
.nested-card-alone .el-card__body{
  overflow: auto;
}
div.nested-card-tools{
  float: right;
}

section.content-main {
  padding: 0px;
  background-color: #fff;
  overflow: auto;
}

section.content-main .data-body {
  padding: 0;
}
div.content-wrap{
  overflow: auto;
  height: 100%;
}

div.pane-head {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

div.pane-head-label {
  line-height: 30px;
  font-size: 16px;
}

header.main-header {
  /*background-color: #2a579a;*/
  background-color: #167092;
}

ul.treeview-menu {
  padding-left: 0;
}

header.main-header .logo .logo-lg {
  font-weight: 700;
}

form.search-form-inline .el-form-item {
  margin-bottom: 0px;
}
*.el-form-item__label{
  font-weight: normal;
}
div.form-h-item .el-form-item__label {
  width: 5.2em;
}

div.form-row {
  margin: 4px 0;
}

div.form-h-item .el-form-item__content {
  width: 160px;
}

div.form-h-item .el-form-item__label {
  width: 5.2em;
}

div.form-h-item .el-form-item__content {
  width: 160px;
}
.form-item-thin .el-form-item__label{
  padding-right: 0!important;
}

div.el-table th.el-table__cell {
  background: linear-gradient(180deg, #F2F9FE 0%, #DCF0FF 100%);
}

div.el-table .table_cell_opt .el-link{
  color: blue;
}

div.content-pane {
  margin: 4px;
}

div.paginationClass {
  margin-top: 4px;
}
div.pane-head .el-col:last-child {
  text-align: right;
}

button.tab-button {
  position: relative;
  bottom: -9px;
  margin-left: 0 !important;
  float: left;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

div.search-form-pane {
  max-height: 144px;
  overflow: hidden;
  overflow-y: auto;
}

/*弹出框*/
div.checkFeedbackDialog .el-form-item {
  margin-bottom: 0px;
}

div.editFormDialog div.el-form-item {
  margin-bottom: 8px;
}

div.box-noborder {
  border-width: 0px;
}

div.checkFeedbackDialog .box-body {
  padding: 0;
}

div.checkFeedbackDialog .box-footer {
  margin-top: 8px;
  padding: 2px 4px;
  border: 1px solid #f4f4f4;
  border-radius: 6px;
}

div.align-center {
  text-align: center;
}

/*表格*/
.current-row>td {
  background: rgba(0, 158, 250, 0.219) !important;
}

.el-table__header th{
  font-weight: normal;
}
.el-table__body tr:hover>td {
  background-color: rgba(0, 158, 250, 0.219) !important;
}

/*表格无内容*/
*.empty-body{
   padding:1.2em 0;
   text-align:center
}

/*消息*/
div.message-dark{
  min-width: 8em;
  background-color: #303133;
  border-color: #303133;
  transform-origin: center bottom 0px;
}
div.message-dark .el-message__content {
  color: #fff;
}

i.el-message__closeBtn{
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  font-size: 24px;
}

button [class*=" el-icon-"], button [class^=el-icon-] {
  font-size: 0.95em;
}

/*信息*/
.el-icon-success-ok{
  color: green;
}
.el-icon-error-nok{
  color: red;
}

/*tr.grid-row .el-button{
  padding: 2px;
  font-size: 18px;
}*/
button.layout-button{
  padding-left: 0;
  padding-right: 0;
  width: 1px;
  overflow: hidden;
  visibility: hidden;
}

.tight-form .el-form-item{
  margin-bottom: 4px;
}

/*表格操作列*/
td.button-col .el-button{
  padding: 4px;
}
td.button-col .el-button + .el-button{
  margin-left: 4px;
}
td.button-col button [class*=" el-icon-"], td.button-col button [class^=el-icon-]{
  font-size: 1.2em;
}
td.button-col .el-button:focus{
  color: inherit;
}

div.el-tooltip__popper.is-light{
  background-color: #fdf5e6;
}
div.el-tooltip__popper.is-light[x-placement] div.popper__arrow::after {
  border-top-color: #fdf5e6;
}

/*右键菜单*/
div.contextmenu-pane {
  position: absolute;
  z-index: 99;
  overflow: hidden;
  width: 150px;
  padding: 6px 0;
  background-color: #FFFFFF;
  border: 1px solid #DDD;
  border-radius: 8px;
  box-shadow: 0 0 16px #DDD;
}
div.contextmenu-pane ul, div.contextmenu-pane li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  cursor: default;
}
div.contextmenu-pane li {
  margin: 2px;
  text-align: left;
  border-radius: 3px;
}
div.contextmenu-pane li:hover {
  background-color: #EFEFEF;
}
div.contextmenu-pane li button, div.contextmenu-pane li button:hover {
  padding: 4px 0 4px 16px;
  width: 100%;
  text-align: left;
  color: inherit;
}
div.contextmenu-pane li button, div.contextmenu-pane li button:focus{
  color: inherit;
}
.contextmenu-item-disabled{
  color: #DDD;
}

/*主内容区高度*/
.inner-container, .inner-container aside{
  background-color: rgba(255, 255, 255, 0.85);
}
.inner-container-split{
  background-color: #e7edf3;
}
.hasTagsView .inner-container{
  height: calc(100vh - 84px);
}
.inner-container .el-header, .inner-container .el-main, .inner-container .el-aside
,.nested-container .el-header, .nested-container .el-main, .nested-container .el-aside {
  margin: 0;
  padding: 0;
}

/*Tabs, 标签位于右侧*/
div.tabs-wrap .tabs-hor{
  min-height: 200px;
}
.tabs-wrap .tabs-hor-along
, .tabs-wrap .tabs-hor-along .el-tabs__content
, .tabs-wrap .tabs-hor-along .el-tabs__content .el-tab-pane{
  height: 100%;
}
div.tabs-wrap .tabs-hor .el-tabs__item{
  padding: 10px 2px;
  width: 3em;
  text-align: center;
  word-break: break-all;
  white-space: normal;
  height: unset;
  line-height: 1.2;
  background-color: #EEE;
  color: #999;
  border-bottom: 1px solid #999;
}
div.tabs-wrap .tabs-hor .is-active{
  background-color: #409EFF;
  color: white;
}
.tabs-hor .el-tabs__header{
  background-color: #F7F7F7;
}
div.tabs-wrap .tabs-hor.el-tabs--right .el-tabs__header.is-right {
  margin-left: 0;
}
.el-tabs__active-bar.is-bottom{
  top: 0;
  bottom: unset;
}

/*列表*/
ul.ulist, ul.ulist li {
  padding: 0;
  margin: 0;
  list-style: none;
}
ul.ulist .el-button{
  margin: 0;
  padding: 2px 4px;
}
ul.ulist-select li{
  padding: 4px;
  cursor: default;
}
ul.ulist-select li:hover{
  background-color: #EFEFEF;
}
ul.ulist-card li{
  display: inline-block;
  margin: 2px 2px;
  padding: 4px 8px;
  cursor: default;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #d9ecff;
  /*background-color: #CCFFFF;*/
}
ul.ulist-card li:hover{
  /*background-color: #99CCFF;*/
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}
.clear{
  clear: both;
}
/*清除左右浮动*/
*.clearfix::after{
  clear: both;
}

div.fieldset-legend{
  margin: 4px auto;
  padding: 4px 8px;
  border-bottom:  1px solid #DDD;
}

.row-gap{
  padding: 4px 0;
}

.el-tree-node.is-current > .el-tree-node__content{
  font-weight: bold;
}

div.buttons-pane{
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}
.buttons-pane-gap .el-button + .el-button
, .buttons-pane-gap .el-input + .el-button{
  margin-left: 4px;
}
.eo-space-r-4{
  margin-right: 4px;
}

.btrans-tree-wrap .el-tree{
  background-color: transparent;
}
.aside-btrans{
  background-color: transparent;
}
.aside-tree-wrap{
  padding: 4px 0 4px 4px;
  height: 100%;
  background-color: #f4f9fc;
  border-radius: 8px 0 0 8px;
}
.aside-tree-wrap .el-tree{
  background-color: transparent;
}

.scrollpane{
  height: 100%;
  width: 100%;
}
.scrollpane-h .el-scrollbar__wrap{
  overflow-x: hidden;
}
.scrollpane-v .el-scrollbar__wrap{
  overflow-y: hidden;
}

.el-button--primary{
  background-color: #018FD7;
  border-color: #018FD7;
}

.el-tabs__header {
  margin-bottom: 0;
}

.tab-ver .el-tabs__header{
  padding: 0 8px;
  background-color: rgba(255, 255, 255, 0.85);
}
.tab-ver-cp .el-tabs__item{
  padding: 0 8px;
}
.tab-ver-flex{
  display: flex;
  flex-direction: column;
}
.tab-ver-flex>.el-tabs__content{
  flex-grow: 1;
}
.tab-body-p4{
  padding: 4px;
}

div.el-date-editor--noprefix .el-input__prefix .el-input__icon {
  font-size: 0;
  display: none;
}
div.el-date-editor--noprefix .el-input__inner {
  padding-left: 6px;
  padding-right: 6px;
}

div.search-form-v{
  padding: 4px 0;
}

/*滚动条*/
/*::-webkit-scrollbar { width: 6px; height: 6px; }
::-webkit-scrollbar-track-piece { background:rgba(0, 0, 0, .1); }
::-webkit-scrollbar-thumb:vertical { height: 6px; background-color:rgba(0, 0, 0, .4); }
::-webkit-scrollbar-thumb:horizontal { width: 6px; background-color:rgba(0, 0, 0, .5); }*/

::-webkit-scrollbar {
  z-index: 99;
  width: 6px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.0);
}

::-webkit-scrollbar-thumb {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.4);
  transition: all .2s;
  height: 20px;
  border: 0px solid rgba(0, 0, 0, 0.2);
}

:hover::-webkit-scrollbar-thumb {
  transition: all .2s;
}
::-webkit-scrollbar-button {
  display: none;
}
::-webkit-scrollbar-corner {
  display: none;
}

/**/
.state-icon-err { color: red; }
.state-icon-ok { color: green; }


div.foot-tools{
  padding: 10px 0;
  text-align: center;
  /*background-color: #D1D7DF;*/
}
div.foot-tools .el-button, div.foot-tools button+button{
  margin-left: 4px;
}
div.foot-tools label + .el-button{
  margin-left: 16px;
}

.select-multi-sing .el-select__tags .el-tag--info:nth-child(2){
  display: none;
}
.select-multi-sing .el-tag__close{
  display: none;
}

.el-input-group-thin .el-input-group__append{
  padding-left: 12px;
  padding-right: 12px;
}
.el-input-group-thin .el-input-group__append:hover{
  background-color: #018FD7;
}
.el-input-group-thin .el-input-group__append:hover i{
  color: #FFF;
}

.icon-button-toolbar{
  border-bottom: 2px solid #44626f;
}
.icon-button{
  display: inline-block;
  height: 100%;
  text-align: center;
}
svg.svg-icon.button-icon{
  width: 18px;
  height: 18px;
}
.icon-button:hover{
 color: green;
}
.icon-button-active{
 color: green;
}
.el-notification-button{
  margin: 0 4px;
  cursor: pointer;
  background-color: #018FD7;
  color: white;
  padding: 2px 4px;
}
.el-notification-button:hover{
  text-decoration: underline;
}