<template>
  <div>
    <i-frame :src="url" />
  </div>
</template>
<script>
import iFrame from "@/components/iFrame/index";
export default {
  name: "Swagger",
  components: { iFrame },

  computed: {
    url() {
      let baseUrl = process.env.VUE_APP_BASE_API;
      if("/" !== baseUrl.substring(baseUrl.length - 1)) {
        baseUrl += "/";
      }
      return baseUrl + "swagger-ui/index.html";
    }
  }
};
</script>
