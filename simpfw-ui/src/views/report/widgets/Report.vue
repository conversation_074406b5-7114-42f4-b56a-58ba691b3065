<template>
  <div class="report-container">
    <iframe name="report-container" id="rh-report-container" :width=width :height=height :src="initReportPath"></iframe>
  </div>
</template>

<script>
//检查状态变更
import {
  StatusDict,
  matchAny as matchAnyStatus,
} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

export default {
  components: {},
  name: 'Report',
  data() {
    return {
      callQ: new Map(),
    };
  },
  methods: {
    createDeferredPromise() {
      let resolve;
      let reject;
      const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
      });

      return {promise, resolve, reject};
    },
    callReportFn(fn, args) {
      let id = Date.now().toString()
      const {promise, resolve, reject} = this.createDeferredPromise()
      this.callQ.set(id, {promise, resolve, reject})
      // @ts-ignore
      this.sendMessage({
        type: 'report-system-call-fn',
        call: fn,
        args: args,
        id: id,
      });
      return promise;
    },
    /**
     * 加载报告
     * @param reportPath
     * @param id
     */
    loadReport(reportPath, id, defaultValue, option) {
      let initValue = defaultValue || {};
      return this.callReportFn("_ctx.services.preview.loadReportOrTemplate", [reportPath, id, initValue, option || {}]);
    },
    /**
     * 保存报告
     */
    save(path, id) {
      return this.callReportFn("_ctx.services.preview.save", [{
        path: path,
        id: id,
        savePdf: true,
        saveReport: true,
      }]);
    },
    /**
     * 签名
     * @param signImg
     */
    sign(signInfo, exam) {
      // console.log("给当前报告上审核签名",signImg)
      // return this.callReportFn("_ctx.services.preview.setWidgetContentValue", [
      //   "auditDoctorSign", {
      //     src: "data:image/png;base64," + signInfo.signImage,
      //     dateLabel: "审核日期："+signInfo.signDate,
      //   }
      // ]);
      // console.log("给当前报告上审核签名",signImg)
      let params = {
        // "auditDoctorSign": {
        //   src: "data:image/png;base64," + signInfo.signImage,
        //   dateLabel: "审核日期：" + signInfo.signDate,
        // },

        auditTime: {
          text: signInfo.signDate,
        },
      };

      if (!!signInfo.signImage) {

        if (matchAnyStatus(exam, StatusDict.audit)) {
          params["secondAuditDoctorSign"] = {
            src: "data:image/png;base64," + signInfo.signImage,
            dateLabel: "审核日期：" + signInfo.signDate,
          }
        } else if (matchAnyStatus(exam, StatusDict.second_audit)) {
          params["thirdAuditDoctorSign"] = {
            src: "data:image/png;base64," + signInfo.signImage,
            dateLabel: "审核日期：" + signInfo.signDate,
          }
        } else {
          // TODO临时方案，后面召回的时候要清一二三审的值
          params["auditDoctorSign"] = {
            src: "data:image/png;base64," + signInfo.signImage,
            dateLabel: "审核日期：" + signInfo.signDate,
          }
          params["secondAuditDoctorSign"] = {
            src: "" ,
            dateLabel: "",
          }
          params["thirdAuditDoctorSign"] = {
            src: "" ,
            dateLabel: "",
          }
        }
      }

      params["auditDoctorTag"] = {
        text: "审核医师：",
      };

      params["QRImageText1"] = {
        text: "微信",
      };

      params["QRImageText2"] = {
        text: "扫码",
      };

      params["QRImageText3"] = {
        text: "查看",
      };

      params["QRImageText4"] = {
        text: "报告",
      };

      params["operDoctorName"] = {
        text: !!exam && !!exam.operDoctor && !!exam.operDoctor.nickName ? ("操作者：" + exam.operDoctor.nickName) : "",
      };

      params["auditTimeText"] = {
        text: !!signInfo.signDate ? ("审核日期：" + signInfo.signDate) : "",
      };

      params["QRImage"] = {
        src: !!signInfo.QRImage ? ("data:image/png;base64," + signInfo.QRImage) : "",
      };

      // if(!!signInfo.QRImage){
      //   params.QRImage =  {
      //     src: "data:image/png;base64," + signInfo.QRImage,
      //   };
      // }else{
      //   params.QRImage =  {
      //     src: null,
      //   };
      // }
      console.log("sign", params);
      return this.callReportFn("_ctx.services.preview.setContentValues", [
        params
      ]);
    },
    /**
     * 导出PDF
     */
    getPdf() {
      return this.callReportFn("_ctx.services.preview.print", [{}]);
    },
    print() {
      return this.callReportFn("_ctx.services.preview.print", [{}]);
    }
  },
  props: {
    initReportPath: {
      type: String,
      default: 'http://192.168.2.224:5173/#/report/preview?path=/%E7%A9%BA%E7%99%BD.rtpl&op=write&showOpButton=false'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  mounted() {
    var self = this;
    (function (win, doc) {
      var ifr = doc.getElementById('rh-report-container').contentWindow;
      var sendMessage = function () {
        if (win.postMessage) {
          if (win.addEventListener) {
            win.addEventListener("message", function (e) {
              console.log('报表系统回调了report-call', e)
              if (e.data && e.data.type && e.data.type === 'report-call-response') {
                console.log('报表系统回调了report-call', e.data)
                if (e.data.id) {
                  let id = e.data.id
                  let p = self.callQ.get(id)
                  if (p) {
                    p.resolve(e.data.res)
                    self.callQ.delete(id)
                  }
                }
              }
            }, false);
          }
          return function (data) {
            ifr.postMessage(data, '*');
          };
        }
      };
      // win.sendMessage = sendMessage();
      self.sendMessage = sendMessage();
    })(window, document);
    console.log('查看报告组件加载完成')
  }
};

</script>
<style scoped>
.report-container {
  width: 100%;
  height: 100%;
}
</style>
