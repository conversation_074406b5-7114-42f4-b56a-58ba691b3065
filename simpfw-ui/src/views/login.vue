<template>
  <div class="loginWrap">
    <div class="login">
      <div class="loginFormWrap" v-show="loginAction.CHPASSWD == currLoginAction">
        <el-form ref="passwdForm" :model="passwdForm" :rules="passwdRules" class="login-form">
          <h3 class="title">{{appTitle}}</h3>

          <div class="login-form-body">
            <el-form-item prop="username">
              <el-input
                v-model="passwdForm.username"
                type="text"
                auto-complete="off"
                placeholder="账号"
              >
                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
              </el-input>
            </el-form-item>
            <el-form-item prop="oldPassword">
              <el-input
                v-model="passwdForm.oldPassword"
                type="password"
                auto-complete="off"
                placeholder="旧密码">
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
              </el-input>
            </el-form-item>
            <el-form-item prop="newPassword">
              <el-input
                v-model="passwdForm.newPassword"
                type="password"
                auto-complete="off"
                placeholder="新密码">
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
              </el-input>
            </el-form-item>
            <el-form-item prop="confirmPassword">
              <el-input
                v-model="passwdForm.confirmPassword"
                type="password"
                auto-complete="off"
                placeholder="确认密码">
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
              </el-input>
            </el-form-item>
          </div>

          <div class="login-form-buttons">
            <el-button-group>
              <el-button type="primary" style="width: 60%" icon="el-icon-refresh"
               @click="chpasswd">更改密码</el-button>
              <el-button type="primary" style="width: 40%" icon="el-icon-key"
               @click="setLoginAction(loginAction.LOGIN)">返回登录</el-button>
            </el-button-group>
          </div>
        </el-form>
      </div>
      <div class="login-wall" :class="{'login-wall-chpasswd': (loginAction.CHPASSWD == currLoginAction)}"></div>
      <div class="loginFormWrap" v-show="loginAction.LOGIN == currLoginAction">
        <div class="loginModeFunc" :class="{loginModeFuncAccount: (loginMode.linksign == activeLoginMode)}"
        @click="switchLoginMode"></div>

        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <h3 class="title">{{appTitle}}</h3>

          <!-- <div>
            <el-form-item>
              <el-select v-model="loginForm.equipRoom.roomCode" clearable placeholder="选择机房" style="width: 100%">
                <template slot="prefix"><i class="el-icon-place"></i></template>
                <el-option v-for="dict in combo.equipRoom"
                  :key="dict.roomCode"
                  :label="dict.roomName"
                  :value="dict.roomCode"
                />              
              </el-select>
            </el-form-item>            
          </div>

          <div>
            <el-form-item>
              <el-select v-model="loginForm.examItemCode" clearable placeholder="选择检查项目" style="width: 100%">
                <template slot="prefix"><i class="el-icon-place"></i></template>
                <el-option v-for="dict in dict.type.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />              
              </el-select>
            </el-form-item>            
          </div> -->

          <div class="login-form-body" v-show="loginMode.regular == activeLoginMode || loginMode.linksignOTP == activeLoginMode || loginMode.linksignSMS == activeLoginMode">
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                auto-complete="off"
                :placeholder="usernamePlaceholder"
                @keyup.enter.native="changeFocus('input_password')"
              >
                <span slot="prepend" v-if="loginMode.linksignSMS == activeLoginMode">+86</span>
                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" v-else />
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                id="input_password"
                v-model="loginForm.password"
                :type="vericodeFieldType"
                auto-complete="off"
                :placeholder="passwordPlaceholder"
                @keyup.enter.native="handleLogin"
                :style="{width: (loginMode.regular == activeLoginMode? '100%' : '60%')}"
              >
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
              </el-input>
              <div style="display: inline-block; width: 39%; text-align: right;" v-show="loginMode.regular !== activeLoginMode">
                <el-button type="primary" @click="aboutlinksignOtp" v-show="loginMode.linksignOTP == activeLoginMode">如何获取</el-button>
                <el-button type="primary" @click="aqrsms" v-show="loginMode.linksignSMS == activeLoginMode">获取短信</el-button>
              </div>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaOnOff">{{captchaOnOff}}
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="验证码"
                style="width: 63%"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>

            <el-form-item v-show="loginMode.linksignOTP == activeLoginMode || loginMode.linksignSMS == activeLoginMode">
              <el-select v-model="loginForm.authTime" placeholder="授权时长" style="width: 100%">
                <template slot="prefix"><i class="el-icon-timer"></i></template>
                <el-option v-for="dict in combo.authTime"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />              
              </el-select>
            </el-form-item>            

            <div class="login-form-bar-ass" v-show="loginMode.regular == activeLoginMode">
              <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
              <el-button type="text" style="padding: 0" @click="setLoginAction(loginAction.CHPASSWD)">修改密码</el-button>
            </div>
          </div>

          <div class="login-form-qr" v-show="loginMode.linksign == activeLoginMode">
            <Linksign :qr="qr" />
          </div>

          <div class="login-form-buttons">
            <!-- 账号登录按钮 -->
            <el-button-group v-show="loginMode.regular == activeLoginMode">
              <el-button type="primary" style="width: 40%" icon="el-icon-key"
                :loading="loading"
                @click.native.prevent="handleLogin"
              >
                {{!loading? '登 录' : '登 录 中...'}}
              </el-button>

              <el-tooltip class="item" effect="dark" content="扫码登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-full-screen"
                 @click="setLoginMode(loginMode.linksign)"></el-button>
              </el-tooltip>
              
              <el-tooltip class="item" effect="dark" content="动态令牌" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-c-scale-to-original"
                 @click="setLoginMode(loginMode.linksignOTP)"></el-button>
              </el-tooltip>
              
              <el-tooltip class="item" effect="dark" content="短信授权" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-mobile-phone"
                 @click="setLoginMode(loginMode.linksignSMS)"></el-button>
              </el-tooltip>
            </el-button-group>

            <!-- 扫码认证按钮 -->
            <el-button-group v-show="loginMode.linksign == activeLoginMode">
              <el-tooltip class="item" effect="dark" content="账号登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-key"
                 @click="setLoginMode(loginMode.regular)"></el-button>
              </el-tooltip>

              <el-button type="primary" style="width: 40%" icon="el-icon-refresh"
               @click="refreshQr()">{{qr && qr.refreshButtonText || '刷新二维码'}}</el-button>
              
              <el-tooltip class="item" effect="dark" content="动态令牌" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-c-scale-to-original"
                 @click="setLoginMode(loginMode.linksignOTP)"></el-button>
              </el-tooltip>
              
              <el-tooltip class="item" effect="dark" content="短信授权" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-mobile-phone"
                 @click="setLoginMode(loginMode.linksignSMS)"></el-button>
              </el-tooltip>
            </el-button-group>

            <!-- 动态口令 -->
            <el-button-group v-show="loginMode.linksignOTP == activeLoginMode">
              <el-tooltip class="item" effect="dark" content="账号登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-key"
                 @click="setLoginMode(loginMode.regular)"></el-button>
              </el-tooltip>

              <el-tooltip class="item" effect="dark" content="扫码登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-full-screen"
                 @click="setLoginMode(loginMode.linksign)"></el-button>
              </el-tooltip>
              
              <el-button type="primary" style="width: 40%" icon="el-icon-c-scale-to-original"
                :loading="loading"
                @click.native.prevent="cfmotp"
              >
                {{!loading? '登 录' : '登 录 中...'}}
              </el-button>
              
              <el-tooltip class="item" effect="dark" content="短信授权" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-mobile-phone"
                 @click="setLoginMode(loginMode.linksignSMS)"></el-button>
              </el-tooltip>
            </el-button-group>
            
            <!-- 短信验证 -->
            <el-button-group v-show="loginMode.linksignSMS == activeLoginMode">
              <el-tooltip class="item" effect="dark" content="账号登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-key"
                 @click="setLoginMode(loginMode.regular)"></el-button>
              </el-tooltip>

              <el-tooltip class="item" effect="dark" content="扫码登录" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-full-screen"
                 @click="setLoginMode(loginMode.linksign)"></el-button>
              </el-tooltip>
              
              <el-tooltip class="item" effect="dark" content="动态令牌" placement="top">
                <el-button type="primary" plain style="width: 20%" icon="el-icon-c-scale-to-original"
                 @click="setLoginMode(loginMode.linksignOTP)"></el-button>
              </el-tooltip>
              
              <el-button type="primary" style="width: 40%" icon="el-icon-mobile-phone"
                :loading="loading"
                @click.native.prevent="cfmsms"
              >
                {{!loading? '登 录' : '登 录 中...'}}
              </el-button>
            </el-button-group>
          </div>
        </el-form>
      </div>
      <!--  底部  -->
      <div class="el-login-footer">
        <span class="copyright-ann">Copyright © 2022- 软航科技 All Rights Reserved.</span>
      </div>
    </div>
  </div>
</template>

<script>
import model from "@/assets/scripts/login";
export default model;
</script>

<style rel="stylesheet/scss" lang="scss">
.loginWrap{
  height:100%;
  background: linear-gradient(to bottom,#2392C1 0,#335698 100%);
}
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  /*background-image: url("../assets/images/login-background.jpg");
  background-size: cover;*/
  /*background: url("../assets/images/app-bg-pattern.png") 0 0 no-repeat transparent;*/
}
.title {
  margin: 0px auto 32px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  margin-top: 60px;
  padding: 0px 60px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.copyright-ann{
  font-size: 16px;
}
.login-code-img {
  height: 38px;
}

.login-form-qr{
  width: 240px;
  height: 240px;
  margin: 0 auto 16px;
}

.login-form-buttons .el-button-group{
  width: 100%;
}
.login-form-buttons .el-button{
  width: 50%;
  padding-left: 8px;
  padding-right: 8px;
}
.login-wall{
  width: 570px;
  height: 446px;
  border-radius: 16px 0 0 16px;
  background: url("../assets/images/login-wall.png") 0 0 no-repeat transparent;
  /*box-shadow: 0 0 48px 0px rgba(255, 255, 255, .4);*/
}
.login-wall-chpasswd{
  background-position: 100% 0;
  border-radius: 0 16px 16px 0;
}
.loginFormWrap{
  position: relative;
  width: 420px;
  height: 500px;
  border-radius: 6px;
  background: #ffffff;
}
.loginModeFunc{
  position: absolute;
  top: 8px;
  right: 8px;
  width: 50px;
  height: 50px;
  background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAKlBMVEVHcEyHquiGqueGq+iGq+eHrOmGq+eFqueGqueNsO6JrOeHq+eUru2Fqub6x9OvAAAADXRSTlMAXLyHqEiY9eQSKXELCCPK/wAAAcRJREFUWMPt1jFLw1AUBeBTQgs1ix0cC/IGFyn4B7Jo14KL1MVBkbu5uDgFxMUsgmsGwc2pYH9AoJPo4FKFgnL/i0OfbdL2mXcQRKpnKn18PYXb3FdEOpdVALHqEEAwf6qL3iwjiHgS8AQnPKnxZK7GgwTtaVqWHLfbO5YMc8dXluTTtMQmUNW33HFrOUldRETuXeRBRER2iy1NVdV1F3lWVdWtIqnMkPb2ZC4ugqxIctN3kpAnyHgS8gQRTwKeIMqRy7OzUw9S40YJAFh1kcckSZLkfmmfl++QA0sajcaaJe+NaRYtpfDr1RcvIIi8tuVsDUuQ8STkCTKehDzBYExGxpjNvfnAkXj8ie/wzz9RVX1JxxkAeLKvXaRZHN+5vRtco5zcNxyZqfEiFZ4Ua/xIhSeFGk9SFZEjVX0VkcN+v99R1Q0RKZno5+rr2Ybd8h/BJ7nhSe9HyK/7YtVYdWiMaY3F6MIYU2a6fqtvtoYkC2rA14CvAV/jsZ/2k2liP4KSe3/5ST1N0zS9Zkjo/G/pTsaTkCeIeBLwBBFPAp4g4kmNJzjhSY0nuP2rj9gkHZ7c8WQlpgm6PKnyBE2eVHiCjCfhB4/kuLSh1hUaAAAAAElFTkSuQmCC") 50% no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
div.loginModeFuncAccount{
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkBAMAAACCzIhnAAAAHlBMVEVHcEz///+LrOuFq+eGq+eGqueHrOiFqub////C1PLgc7nMAAAAB3RSTlMAgB7rl81ZAcX/fAAAAXZJREFUWMOV1jFqw0AQRuEhhNR7BENC0psEt8ZNDpDCbcgRYpupp/vnBLpuCslysLRavek/niQGNFbsNi9f3piPg5nFTTz6itmb6ZY5riEXMwWKuO9NY+bNTz/WmNed/5rGzKdvrTnPfjaNmaN/t8mTX0xjZuebNnnwk2nMuNuKcTeNGUT6DCPBiQonwYnKSOqrckeCExVOghMVToITdZwkJ+o4SU6uC01IcDIsNCLBSb/QjAQnKpwEJyqcBCfqOElO6pk6SU6qmQWSnNQySyQ5qWQWSXIyn1kmyclspkGSE5Xmj28ywUkts0SCk/nM9ewBmeG4IpnhhAOZ4VCsT1c7R+uTlaMXZvYNkrMHvJqZyTdQMwOJOk6Sk+tlQ0hyMlw2iCQn04Vuk+BkkhHPrCD9Qr9vCOkXektInzkjku7uJ0TUcZL4waSOvr6U8CPfrcBaEpz8y4hnxDPiGfGMeEY8I54Rz4hnxDPiGfGMeEY8I54Rz2ASfzwte7EhQL3+AAAAAElFTkSuQmCC");
}
div.login-form-bar-ass{
  display: flex;
  justify-content: space-between;
  margin: 0 0 25px 0;
}
</style>
