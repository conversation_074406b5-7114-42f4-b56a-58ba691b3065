<template>
    <div class="flex-container image-collection-pane">
        <div class="flex-item-fill flex-container flex-container-column image-collection-viewer">
            <div class="flex-item-fill image-collection-viewer-main">

            </div>
            <div class="image-collection-bottom">
                <el-button type="primary" @click="attachImage" :disabled="!socketEnabled">报告采图</el-button>
                <el-button type="primary" @click="detachImage" :disabled="!socketEnabled">后端采图</el-button>
                <el-button type="primary" @click="attachVideo" :disabled="!socketEnabled">报告录像</el-button>
                <el-button type="primary" @click="detachVideo" :disabled="!false">后端录像</el-button>
                <span>采集倒计时0秒</span>
                <span>加时5秒</span>
                <el-button @click="openSettings">设置</el-button>
            </div>
        </div>
        <div class="flex-container flex-container-column image-collection-list">
            <h3 class="image-collection-list-top">患者影像</h3>
            <div class="flex-item-fill image-collection-list-items">

            </div>
            <div class="image-collection-bottom">
                <el-button>全选</el-button>
                <el-button>全不选</el-button>
            </div>
        </div>

        <settings ref="settingDialog" />
        <CurrentEquipRoom />
    </div>
</template>

<script>
import "@/assets/styles/pacs/common.css";
import "@/assets/styles/pacs/ImageCollection.css";

import model from "@/assets/scripts/pacs/ImageCollection";

export default model;
</script>
