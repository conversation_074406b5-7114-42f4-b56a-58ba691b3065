<template>
    <el-dialog title="本地参数设置" :visible.sync="opened" append-to-body
     class="popupdialog">
      <el-form ref="form" label-width="100px">
        <el-tabs type="card">
          <el-tab-pane label="本地设置[F]">
            <el-row>
              <el-col :span="16">
                <el-form-item label="设备类型" prop="deviceType">
                  <el-select v-model="form.deviceType" placeholder="-请选择-">
                    <el-option
                      v-for="item in form.combo_deviceTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item prop="log">
                  <el-checkbox v-model="form.log">记录系统操作日志</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="16">
                <el-form-item label="端口号" prop="portNumber">
                  <el-input v-model="form.portNumber" placeholder="端口号" />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item prop="autoStartup">
                  <el-checkbox v-model="form.autoStartup">自动启动服务</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="22">
                <el-form-item label="视频播放程序" prop="mediaPlayer">
                  <el-input v-model="form.mediaPlayer" placeholder="视频播放程序" />
                </el-form-item>
              </el-col>

              <el-col :span="2">
                  <el-button>...</el-button>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="22">
                <el-form-item label="文件检测目录" prop="scanDir">
                  <el-input v-model="form.scanDir" placeholder="文件检测目录" />
                </el-form-item>
              </el-col>

              <el-col :span="2">
                  <el-button>...</el-button>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="22">
                <el-form-item label="图片存储目录" prop="storeDir">
                  <el-input v-model="form.storeDir" placeholder="图片存储目录" />
                </el-form-item>
              </el-col>

              <el-col :span="2">
                  <el-button>...</el-button>
              </el-col>
            </el-row>

          </el-tab-pane>
          <el-tab-pane label="DICOM设置[D]"></el-tab-pane>
          <el-tab-pane label="模拟设置[R]"></el-tab-pane>
          <el-tab-pane label="显示器设置[V]"></el-tab-pane>
        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </div>
    </el-dialog>
</template>

<script>
import "@/assets/styles/pacs/common.css";

import model from "@/assets/scripts/pacs/ImageCollectionSettings";

export default model;
</script>
