<template>
  <div ref="imageView" class="rw-image-view-wrap" style="min-height: 300px;" oncontextmenu="return false">
    <div class="rw-image-view-toolbar icon-button-toolbar">
      <div v-for="t in tools" :key="t.name" v-if="!t.hidden"
       class="icon-button" :class="{'icon-button-active': (toolActive === t.name)}"
       @click="activeCornerstoneTool(getViewElement(), t.name)">
        <svg-icon :icon-class="t.icon" class-name="button-icon"/>
        <div>{{t.title}}</div>
      </div>
    </div>
    <div class="rw-image-view-main">
      <div class="rw-image-view-element" :style="imageStyle"></div>
    </div>
  </div>
</template>

<script>
import {loadAndDisplay} from "@/assets/scripts/pacs/image/util";
//影像工具：测量，放大
import cmixins from "@/assets/scripts/cornerstone/cornerstone-mixins";

export default {
  props: {
    scaleZoom: {
      type: Object,
      default () {
          return {
              max: 5, 
              min: 0.2
          }
      }
    },
    changeUrl:{
      default(){
        return null
      }
    }
  },

  mixins: [cmixins],

  data() {
    return {
      imageStyle: null
    }
  },
  methods: {
    show() {
      const ele = this.getViewElement();
      if(1 === this.enableCornerstone(ele)) {
        this.enableCornerstoneTools(ele);
      }

      this.loadImage();
    },

    getViewElement() {
      return this.$refs.imageView.querySelector(".rw-image-view-element");
    },

    loadImage() {
      const imageUrl = this.changeUrl;
      if(!imageUrl) {
        return;
      }

      try {
        const ele = this.getViewElement();
        const p = loadAndDisplay(ele, imageUrl);
        p.then(img => {
          //可见区域
          const wrp = ele.parentNode, availWidth = wrp.clientWidth, availHeight = wrp.clientHeight;
          //影像尺寸
          const imageWidth = img.width, imageHeight = img.height;
          //console.log("availWidth=%d, availHeight=%d, imageWidth=%d, imageHeight=%d", availWidth, availHeight, imageWidth, imageHeight);
          //绘制区域
          let viewportWidth = Math.min(availWidth, imageWidth)
            , viewportHeight = viewportWidth * imageHeight / imageWidth;
            /*if(viewportHeight > availHeight) {
              viewportHeight = availHeight;
              viewportWidth = viewportHeight * imageWidth / imageHeight;
            }*/

            this.imageStyle = {width: viewportWidth + "px", height: viewportHeight + "px"};
            this.$nextTick(() => cornerstone.resize(ele, true));
        });
      } catch (err) { console.error(err); }

    }
  },
  mounted() {
    this.show();
  },
  watch: {
    'changeUrl': {
      handler(newv, oldv) {
         this.show();
      }
      
    }
  }
}


</script>

<style scoped>
.rw-image-view-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  user-select: none;
}
.rw-image-view-toolbar, .rw-image-view-element {
  background-color: #000;
  white-space: nowrap;
}
.rw-image-view-toolbar {
  width: 100%;
  padding: 8px 16px
}
.rw-image-view-toolbar .icon-button{
  margin: 0 8px;
}
.rw-image-view-main {
  flex-grow: 1;
  width: 100%;
}
    
</style>
