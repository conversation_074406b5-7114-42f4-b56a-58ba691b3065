<template>
  <div class="app-container">
    <el-row>
      <!--数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <!-- <el-tree
            :data="tree.data"
            :props="tree.props"
            :expand-on-click-node="false"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          /> -->
          <AreaTree ref="areaTree" @onChangeArea="handleNodeClick" />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="地区名称">
            <el-input
              v-model="queryForm.regionName"
              placeholder="地区名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">清空</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>

          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="regionCode"
         stripe highlight-current-row>
          <el-table-column prop="regionCode" label="区域代码" width="260" />
          <el-table-column prop="regionName" label="区域名称" width="200" />
          <el-table-column prop="superiorRegion.regionCode" label="上级区域代码" width="260" />
          <el-table-column prop="superiorRegion.regionName" label="上级区域名称" width="200" />
          <el-table-column label="操作" align="center" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="grid.pager.pageNum"
          :limit.sync="grid.pager.pageSize"
          @pagination="getList"
        />
      </el-col>
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editForm.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="地区代码" prop="regionCode">
              <el-input v-model="editForm.regionCode" placeholder="请输入地区代码" />
            </el-form-item>
          </el-col>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="地区名称" prop="regionName">
              <el-input v-model="editForm.regionName" placeholder="请输入地区名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级代码">
              <el-input v-model="editForm.superiorRegion.regionCode" :readonly="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级名称">
              <el-input v-model="editForm.superiorRegion.regionName" :readonly="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/syscfg/area";
export default model;
</script>
