<template>
  <div class="app-container">
    <el-row>
      <!--树-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <AreaTree ref="areaTree" @onChangeArea="handleNodeClick" />
        </div>
      </el-col>
      <!--数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch">
          <el-form-item label="院区名称">
            <el-input
              v-model="queryForm.hospitalName"
              placeholder="院区名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">清空</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>

          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="hospitalCode"
         stripe highlight-current-row>
          <el-table-column prop="hospitalCode" label="院区代码" width="260" />
          <el-table-column prop="hospitalName" label="院区名称" width="200" />
          <el-table-column prop="hospitalGradeLabel" label="院区等级" width="260" />
          <el-table-column prop="hospitalTel" label="联系方式" width="200" />
          <el-table-column label="操作" align="center" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="grid.pager.pageNum"
          :limit.sync="grid.pager.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editForm.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="院区代码" prop="hospitalCode">
              <el-input v-model="editForm.hospitalCode" placeholder="请输入院区代码" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="院区名称" prop="hospitalName">
              <el-input v-model="editForm.hospitalName" placeholder="请输入院区名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮编">
              <el-input v-model="editForm.hospitalPostCode" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="editForm.hospitalTel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="院区等级">
              <el-select v-model="editForm.hospitalGradeCode" placeholder="-选择-" clearable>
                <el-option
                  v-for="dict in dict.type.hospital_grade"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="所属地区">
              <treeselect v-model="editForm.region.regionCode" :options="areaTreeData" :normalizer="areaTreeSelectNormalizer" placeholder="-选择-" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="院区地址">
              <el-input v-model="editForm.hospitalAddress" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/syscfg/hospital";
export default model;
</script>
