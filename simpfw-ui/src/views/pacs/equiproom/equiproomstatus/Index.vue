<template>
  <div class="app-container">
    <el-row>

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
          &nbsp;
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="dictLabel" label="机房名称" />
          <el-table-column prop="extend.extendS3" label="上午状态">
            <template slot-scope="scope">
              <el-button type="text" @click="updateStatus(scope.row,'extend.extendS3')">{{colBtn(scope.row,'extend.extendS3')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="extend.extendS4" label="下午状态">
            <template slot-scope="scope">
              <el-button type="text" @click="updateStatus(scope.row,'extend.extendS4')">{{colBtn(scope.row,'extend.extendS4')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="extend.extendI1" label="全天状态">
            <template slot-scope="scope">
              <el-button type="text" @click="updateStatus(scope.row,'extend.extendI1')">{{colBtn(scope.row,'extend.extendI1')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="永久状态">
            <template slot-scope="scope">
              <el-button type="text" @click="updateStatus(scope.row,'status')">{{colBtn(scope.row,'status')}}</el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-col>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/equiproom/equiproomstatus";
export default model;
</script>
