<template>
    <div style="height: 100%">
      <el-container class="inner-container inner-container-split" style="height: 100%">
        <el-main>
          <div class="data-container flex-container-column">
            <div class="search-form-v">
              <el-form :model="searchForm" :inline="true" class="exam-search-form-simp">
                <el-input v-model="searchForm.patientInfo.name" placehoder="请输入" />              
                <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
              </el-form>
            </div>
    
            <div class="flex-item-fill" style="overflow: auto;">
    
              <el-table v-loading="loading" :data="grid.data" row-key="id" height="100%"
              stripe highlight-current-row>
    
                <el-table-column prop="patientInfo.name" label="患者姓名" width="120" :formatter="colFmt_object" />
                <el-table-column prop="examNo" label="检查号" width="160" />
                <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="80" :formatter="colFmt_dictData" />
                <el-table-column prop="patientInfo.age" label="年龄" width="80" :formatter="colFmt_age" />
                <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
                :formatter="colFmt_object" />
                <el-table-column prop="inpNo" label="住院号" width="100" />
                <el-table-column prop="inpWard.dictLabel" label="住院病区" min-width="120" 
                :formatter="colFmt_object"
                show-overflow-tooltip />
                <el-table-column prop="bedNo" label="床号" width="80" />
                <el-table-column prop="examModality.dictLabel" label="检查类型" width="100" :formatter="colFmt_dictData" />
                <el-table-column prop="examModality.dictLabel" label="预约时间" width="120" />
    
                <el-table-column label="操作" fixed="right" align="center" width="120" class-name="button-col">
                  <template slot-scope="scope">
                    <el-button title="编辑"
                      icon="el-icon-edit"
                      @click="handleUpdate(scope.row)"
                    ></el-button>
                  <el-button
                    title="删除"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
    
            <pagination
              v-show="grid.total>0"
              :total="grid.total"
              :page.sync="searchForm.pageNum"
              :limit.sync="searchForm.pageSize"
              @pagination="getList"
            />
          </div>
        </el-main>
      </el-container>
    </div>
</template>

<script>
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
export default {
  name: "AppointmentList",

  extends: BaseGridModel,

  data() {
    return {
        searchForm: {
            patientInfo: {
                name: null
            }
        }
    }
  }
};
</script>
    