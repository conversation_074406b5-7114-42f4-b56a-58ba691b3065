<template>
<el-dialog title="选择机房" :visible.sync="opened" class="popupdialog" width="400px"
 :close-on-press-escape="closable" destroy-on-close><!--  :show-close="closable" -->
  <el-form label-width="100px" class="tight-form">
    <h3 class="form-fieldset-legend">请选择检查的机房</h3>
    <el-row>
      <el-col :span="24">
        <el-form-item label="检查机房">
          <el-select v-model="equipRoom.roomCode" style="min-width: 160px">
            <el-option
              v-for="dict in ctrlData.room"
              :key="dict.roomCode"
              :label="dict.roomName"
              :value="dict.roomCode"
            />
          </el-select>              
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <div slot="footer" class="dialog-footer">
    <div class="foot-tools">
      <el-row>
        <el-col :span="24">
          <el-button type="primary" @click="makeSelect">确定</el-button>
          <el-button @click="close">关闭</el-button> <!--  v-show="closable" -->
        </el-col>
      </el-row>
    </div>
  </div>

</el-dialog>
</template>

<style scoped>
.form-fieldset-legend{
  margin-left: 32px;
  font-size: 1.1em;
}  
</style>

<script>
import { mapGetters } from 'vuex';
import {setRoom, getRoom, clearRoom} from "@/utils/equip-room"

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
//temp-只选择，不设置为当前所在机房
const MODE = {
  TEMP: "temp"
};

const model = {

  extends: BaseDialogModel,

  mixins: [ExamDataScope],

  props: {
    mode: {type: String}
  },

  data() {
    return {
      equipRoom: {}
      , combo_equipRoom: []
    }
  },

  methods: {
    //显示选择窗口
    prepare() {
      if(!this.forTemp) {
        const room = this.currentEquipRoom;
        //console.log(room);
        if(room && room.roomCode) {
          return;
        }
      }

      this.open();
    },
    //确定选择
    makeSelect() {
      let equipRoom = this.equipRoom, equipRoomCode = equipRoom? equipRoom.roomCode : null;
      if(!equipRoomCode) {
        this.$modal.alert("请选择房间。");
      }
      equipRoom = this.combo_equipRoom.find(e => e.roomCode === equipRoomCode)
      setRoom(equipRoom);
      this.$store.commit("SET_EQUIPROOM", equipRoom);
      //
      this.$modal.msgSuccess("已选择" + equipRoom.roomName);

      this.close();
    },
    //读取机房列表
    findEquipRoom() {
      findRoom({}).then(res => {
        const equipRooms = res && res.rows || [];
        this.combo_equipRoom = equipRooms;
        this.applyDataCtrl(DataCtrlDict.ItemType.room, equipRooms);
      });
    }
  },

  mounted() {
    if(!this.forTemp) {
      this.prepare();
    }

    this.findEquipRoom();
  },

  computed: {
    //登录的房间
    ...mapGetters(['currentEquipRoom']),

    closable() {
      return this.forTemp || !!this.currentEquipRoom && !! this.currentEquipRoom.roomCode;
    },

    forTemp() {
      return this.mode === MODE.TEMP;
    }
  }
};
export default model;
</script>
