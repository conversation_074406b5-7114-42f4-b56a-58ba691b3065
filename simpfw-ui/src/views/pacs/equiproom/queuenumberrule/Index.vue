<template>
  <div class="app-container">
    <el-row>

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="18">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>
          <el-col :span="6">
            <right-toolbar :showSearchEnabled="false" @queryTable="getList"></right-toolbar>
          </el-col>            
        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="ruleType.dictLabel" label="规则分类" width="120" :formatter="colFmt_dictData" />
          <el-table-column prop="ruleTypeSpec.dictLabel" label="检查类型" width="120" :formatter="colFmt_dictData" />
          <el-table-column prop="rulePrefix" label="前缀" width="80" />
          <el-table-column prop="rulePattern" label="首排号" width="120" />
          <el-table-column prop="reservedNo" label="预留号" min-width="200" />
          <el-table-column prop="priority" label="优先级" min-width="80" />
          <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
          <el-table-column label="操作" align="center" width="120" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editFormOptions.visible" width="620px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editFormOptions.rules" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则分类" prop="ruleType.dictValue">
              <el-select v-model="editForm.ruleType.dictValue" clearable @change="onChangeRuleType">
                <el-option
                  v-for="dict in dict.type.uis_queue_number_rule_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>              
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="ruleTypeSpec.dictValue">
              <el-select v-model="editForm.ruleTypeSpec.dictValue" clearable>
                <el-option
                  v-for="dict in editFormOptions.combo_queueNumberRuleTypeSpec"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="规则前缀">
              <el-input v-model="editForm.rulePrefix" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="首个排号">
              <el-input v-model="editForm.rulePattern" placeholder="例如0016,从16开始长度为4位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="预留号" prop="reservedNo">
                <el-input v-model="editForm.reservedNo" placeholder="多个预留号用','间隔,如'1,2,3'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级">
               <el-input v-model="editForm.priority" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/equiproom/queuenumberrule";
export default model;
</script>
