<template>
<div>
  <div class="stat-header">
    <div class="stat-header-s">
      <strong>{{dimeTitle}}的工作进度（{{gridData.length}}条）</strong>
      <div class="stat-header-t">
        <el-button size="small" icon="el-icon-refresh" circle @click="stat"></el-button>
      </div>
      <div class="clear"></div>
    </div>
  </div>

  <div class="stat-main">
    <el-table :data="gridData" row-key="dime">
      <el-table-column prop="dimeLabel" :label="dimeTitle" min-width="100" />
      <el-table-column prop="numQueue" label="未检查" min-width="100" />
      <el-table-column prop="numExam" label="正在检查" min-width="100" />
      <el-table-column prop="numReport" label="完成检查" min-width="100" />
      <el-table-column prop="numPostpone" label="延迟检查" min-width="100" />
      <el-table-column prop="total" label="总人数" min-width="100" />
    </el-table>
  </div>

  <div class="stat-chart">
    <div ref="chartPane"></div>
  </div>
</div>

</template>

<style scoped>
.stat-header{
  margin-top: 8px;
}
.stat-header-s{
  padding: 8px 16px;
}
.stat-header-s::before{
  content: "";
  width: 4px;
  height: 16px;
  background: #018fd7;
  border-radius: 3px;
  display: inline-block;
  margin-right: 4px;
  vertical-align: middle;
}
.stat-header-s strong{
  margin-top: 4px;
}
.stat-header-t{
  float: right;
  text-align: right;
}
.stat-main{
  margin: 0 16px 0;
}
.stat-chart{
  margin: 0 16px 1.8em;
  min-width: 600px;
  min-height: 200px;
  background-color: #F5FBFF;
}
.stat-chart>div{
  width: 95%;
  margin: 0 auto;
}
</style>

<script>
import * as echarts from 'echarts'

import * as api from "@/assets/scripts/pacs/equiproom/examresultstatusstat/api";

const model = {
  props: ["dime"],

  data() {
      return {
        gridData: []
      }
  },

  methods: {
    stat() {
      api.stat(this.dime).then(res => {
        this.gridData = res && res.data || [];

        this.buildChart();
      });
    },

    buildChart() {
      const pane = this.$refs.chartPane;
      pane.style.height = (pane.clientWidth * 0.2) + "px";

      let chart = echarts.getInstanceByDom(pane);
      if(!chart) {
        chart = echarts.init(pane);
      }

      //
      const data = this.gridData;
      //
      const legendData = [], xsisdata = ["未检查", "正在检查", "完成检查", "延迟检查"], seriesData = [];
      data.forEach(e => {
        const legend = e.dimeLabel;
        legendData.push(legend);
        seriesData.push({
            name: legend,
            type: 'bar',
            barGap: 0,
            barWidth: 30,
            data: [e.numQueue, e.numExam, e.numReport, e.numPostpone]
          });
      });

      const option = {
        title: {
          text: null
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          show: false,
          data: legendData
        },
        grid: {
          left: '16px',
          right: '32px',
          bottom: '16px',
          top: '16px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          minInterval: 1,
          data: xsisdata,
          axisTick: {show: false},
          axisLine: {show: false},
          axisLabel: {show: true}
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          axisLine: {show: false},
          splitLine: {show: true, lineStyle: {color: ['#CBE8FA']}}
        },
        series: seriesData
      };
      console.log(option);
      chart.setOption(option)
    }
  },

  created() {
    this.stat();
  },

  computed: {
    dimeTitle() {
      switch(this.dime) {
        case "examItem":
          return "检查项目";
        case "equipRoom":
          return "检查机房";
      }
      return null;
    }
  }
};
export default model;
</script>
