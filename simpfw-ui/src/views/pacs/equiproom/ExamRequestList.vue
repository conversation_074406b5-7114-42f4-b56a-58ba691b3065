<template>
<div style="width: 1024px;margin: 0 auto">
    <h1 style="text-align:center">阴阳性统计报表</h1>
    <h2 style="text-align:center">检查类型：US</h2>
    <div>检查时间：2022/07/01~2022/08/18 统计时间：2022/08/20 11:24</div>
    <div>
        <el-table :data="gridData" show-summary>
          <el-table-column label="机房" prop="roomName" min-width="160" align="left" />
          <el-table-column label="患者总数" prop="total" width="160" align="left" />
          <el-table-column label="阳性患者数" prop="numY" width="160" align="left" />
          <el-table-column label="阳性率"  prop="rateY" width="160" align="left" />
          <el-table-column
            label="操作"
            align="center"
            width="80"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-document"
                v-hasPermi="['system:user:edit']"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
    </div>
</div>
</template>

<script>
const model = {
    data() {
        return {
            gridData: [
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
                {roomName: "6号检查室", total: 22, numY: 1, rateY: ((100 / 22).toFixed(2) + '%')},
            ]
        }
    }
};
export default model;
</script>
