<template>
  <div :class="{'app-container': editable}">
    <el-row>

      <!--数据-->
      <el-col :span="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <span class="buttons-pane-gap">
              <el-button type="primary" plain
                icon="el-icon-plus"
                @click="handleAdd" v-if="editable">新增</el-button>

              <el-button type="primary"
                icon="el-icon-refresh"
                @click="getList">刷新</el-button>
            </span>
          </el-col>
        </el-row>


        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="roomName" label="机房名称" />
          <el-table-column prop="workAm" label="上午状态">
            <template slot-scope="scope">
              <el-button type="text" :style="{color:scope.row.workAm===1?'red': 'green'}"
               @click="updateStatus(scope.row,'workAm')">{{workLabel(scope.row,'workAm')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="workPm" label="下午状态">
            <template slot-scope="scope">
              <el-button type="text" :style="{color:scope.row.workPm===1?'red': 'green'}"
               @click="updateStatus(scope.row,'workPm')">{{workLabel(scope.row,'workPm')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="workDay" label="全天状态">
            <template slot-scope="scope">
              <el-button type="text" :style="{color:scope.row.workDay===1?'red': 'green'}"
               @click="updateStatus(scope.row,'workDay')">{{workLabel(scope.row,'workDay')}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="workPerm" label="永久状态">
            <template slot-scope="scope">
              <el-button type="text" :style="{color:scope.row.workPerm===1?'red': 'green'}"
               @click="updateStatus(scope.row,'workPerm')">{{workLabel(scope.row,'workPerm')}}</el-button>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="120" class-name="button-col" v-if="editable">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-col>
    </el-row>
    
    <el-dialog title="编辑房间" :visible.sync="editFormOptions.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editFormOptions.rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="房间名称" prop="roomName">
              <el-input v-model="editForm.roomName" />              
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间号码" prop="roomCode">
              <el-input v-model="editForm.roomCode" />              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查项目">
              <el-select v-model="editForm.examItemCode" clearable multiple collapse-tags>
                <el-option
                  v-for="dict in ctrlData.dict.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>              
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查设备">
              <el-select v-model="editForm.device.id" clearable>
                <el-option
                  v-for="item in combo.devices" 
                  v-show="!item.equipRoom || !item.equipRoom || !!editForm.id && editForm.id===item.equipRoom.id"
                  :key="item.id"
                  :label="item.modality"
                  :value="item.id"
                />
              </el-select>              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上午状态">
              <el-checkbox v-model="editForm.workAmFlag" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下午状态">
              <el-checkbox v-model="editForm.workPmFlag" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="全天状态">
              <el-checkbox v-model="editForm.workDayFlag" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="永久状态">
              <el-checkbox v-model="editForm.workPermFlag" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="采集服务">
              <el-input v-model="editForm.imageService" placeholder="ip:端口，如127.0.0.1:11080" />
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/equiproom";
export default model;
</script>
