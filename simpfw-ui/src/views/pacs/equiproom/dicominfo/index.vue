<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <span class="buttons-pane-gap">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                @click="handleAdd"
              >新增</el-button>

              <el-button type="primary"
                icon="el-icon-refresh"
                @click="getList">刷新</el-button>
            </span>
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="deviceNo" label="设备编号" min-width="160" />
          <el-table-column prop="scpAe" label="SCP AE" width="160" />
          <el-table-column prop="scpIp" label="SCP IP" width="120" />
          <el-table-column prop="scpPort" label="SCP PORT" width="90" />
          <el-table-column prop="scuAe" label="SCU AE" width="160" />
          <el-table-column prop="scuIp" label="SCU IP" width="120" />
          <el-table-column prop="scuPort" label="SCU PORT" width="90" />
          <el-table-column prop="modalityCode" label="检查类型" width="120" />
          <el-table-column prop="modality" label="设备名称" min-width="160" />
          <el-table-column prop="dataSource.dictLabel" label="dataSource" min-width="160" />
          <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
          <el-table-column prop="deviceCode" label="设备型号代码" width="120" />
          <el-table-column prop="device" label="设备型号名称" min-width="160" />
          <el-table-column label="操作" fixed="right" align="center" width="120" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="grid.pager.pageNum"
          :limit.sync="grid.pager.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    
    <el-dialog title="编辑" :visible.sync="editFormOptions.visible" width="800px" custom-class="popupdialog" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editFormOptions.rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="deviceNo">
                <el-input v-model="editForm.deviceNo" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查项目">
              <el-select v-model="editForm.examItemCode" clearable multiple collapse-tags>
                <el-option
                  v-for="dict in ctrlData.dict.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="modalityCode">
                <el-input v-model="editForm.modalityCode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查类型名称" prop="modality">
                <el-input v-model="editForm.modality" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="所属科室">
              <Treeselect v-model="editForm.dept.deptId" :options="deptTreeData" :show-count="true" placeholder="选择" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查机房" prop="equipRoom.roomCode">
              <el-select v-model="editForm.equipRoom.roomCode" clearable>
                <el-option
                  v-for="dict in editFormOptions.combo_equipRoom"
                  v-show="!dict.device || !dict.device.id || !!editForm.id && editForm.id === dict.device.id"
                  :key="dict.roomCode"
                  :label="dict.roomName"
                  :value="dict.roomCode"
                />
              </el-select>              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="传输类型">
              <el-checkbox-group v-model="editForm.transmissionTypes">
                <el-checkbox label="0">接收</el-checkbox>
                <el-checkbox label="1">提取</el-checkbox>
              </el-checkbox-group>            
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="SCP AE" class="form-textfield">
                <el-input v-model="editForm.scpAe" :readonly="scpActivated" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SCP IP" class="form-textfield">
               <el-input v-model="editForm.scpIp" :readonly="scpActivated" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SCP PORT" class="form-textfield">
               <el-input v-model="editForm.scpPort" :readonly="scpActivated" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="SCU AE" class="form-textfield">
                <el-input v-model="editForm.scuAe" :readonly="scuActivated" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SCU IP" class="form-textfield">
               <el-input v-model="editForm.scuIp" :readonly="scuActivated" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SCU PORT" class="form-textfield">
               <el-input v-model="editForm.scuPort" :readonly="scuActivated" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="型号代码">
                <el-input v-model="editForm.deviceCode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号名称">
              <el-input v-model="editForm.device" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="dataSource">
              <el-select v-model="editForm.dataSource.dictValue" clearable class="w100">
                  <el-option
                    v-for="dict in ctrlData.dict.data_source"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="存储路径">
              <el-input v-model="editForm.path" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="第二存储路径">
              <el-input v-model="editForm.pathSecond" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="是否序列">
              <el-radio-group v-model="editForm.isSequence">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>            
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用">
              <el-radio-group v-model="editForm.status">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>            
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/equiproom/dicominfo";
export default model;
</script>
