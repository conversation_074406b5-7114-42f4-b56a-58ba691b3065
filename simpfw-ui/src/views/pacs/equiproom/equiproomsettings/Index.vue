<template>
  <div class="app-container">
    <el-row :gutter="20">

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="examItem.dictLabel" label="检查项目" width="160" :formatter="colFmt_dictData" />
          <el-table-column prop="equipRoom.roomName" label="检查机房" width="160" :formatter="colFmt_dictData" />
          <el-table-column prop="examParts" label="检查部位" min-width="200" :formatter="colFmt_examParts" />
          <el-table-column prop="amNum" label="上午人数" width="80" />
          <el-table-column prop="pmNum" label="下午人数" width="80" />
          <el-table-column prop="waitNum" label="等待人数" width="80" />
          <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
          <el-table-column label="操作" align="center" width="120" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="grid.pager.pageNum"
          :limit.sync="grid.pager.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editForm.visible" width="600px" custom-class="popupdialog" append-to-body>
      <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查机房">
              <el-select v-model="editForm.equipRoom.roomCode">
                <el-option
                  v-for="dict in editFormOpts.combo_equipRoom"
                  :key="dict.roomCode"
                  :label="dict.roomName"
                  :value="dict.roomCode"
                />
              </el-select>              
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查项目">
              <el-select v-model="editForm.examItem.dictCode" clearable>
                <el-option
                  v-for="dict in dict.type.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.raw.dictCode"
                />
              </el-select>              
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上午人数">
                <el-input v-model="editForm.amNum" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下午人数">
               <el-input v-model="editForm.pmNum" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="等待人数">
                <el-input v-model="editForm.waitNum" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查部位">
              <div style="height: 200px; overflow: auto">
                <ExamPartsTree ref="examPartsTree" :checkedKeys="editForm.examParts_ids" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/equiproom/equiproomsettings";
export default model;
</script>
