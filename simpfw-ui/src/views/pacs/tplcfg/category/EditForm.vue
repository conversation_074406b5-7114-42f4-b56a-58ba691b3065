<template>
  <el-dialog
    :title="editForm.title"
    :visible.sync="editForm.visible"
    append-to-body
  >
    <el-container style="height: 100%">
      <div class="box" ref="box">
        <div class="left hei100 scroll-auto">
          <TemplateTree
            :wrapClass="{ 'aside-tree-wrap': true }"
            :priflag="true"
            :menu="true"
            ref="tplTree"
            @dblclick="handleNodeClick"
          />
        </div>
        <div class="resize" title="收缩侧边栏">⋮</div>
        <div class="mid hei100">
          <el-main>
            <el-form
              ref="editForm"
              :model="editForm"
              :rules="editFormRules"
              label-width="80px"
              ><!-- :rules="rules" -->
              <el-row>
                <el-col :span="24">
                  <el-form-item v-if="!editForm.isEdit" label="当前类型">
                    <el-radio-group
                      v-model="editForm.personalFlag"
                      @input="setTemplateVis"
                    >
                      <el-radio
                        v-for="dict in dict.type
                          .uis_report_template_private_flag"
                        :key="dict.value"
                        :label="dict.value"
                        >{{ dict.label }}</el-radio
                      >
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="父类模板" prop="parent.id">
                    <!-- {{editForm.parent.cateName}} -->
                    <Treeselect
                      v-model="editForm.parent.id"
                      :flat="true"
                      :options="cateTreeData"
                      :show-count="true"
                      :filter-node-method="filterTemplate"
                      @select="selectParent"
                      @input="change"
                      placeholder="选择"
                      style="max-width: 240px; font-weight: 100"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="分类名称" prop="cateName">
                    <el-input
                      v-model="editForm.cateName"
                      placeholder="请输入分类名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="状态">
                    <el-checkbox v-model="editForm.status">启用</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row
                v-if="
                  undefined == selectNodeLevel || 0 == selectNodeLevel
                    ? true
                    : false
                "
              >
                <el-col :span="24">
                  <el-form-item label="检查类型" prop="examModalityCodes">
                    <el-select v-model="editForm.examModalityCodes" clearable>
                      <el-option
                        v-for="dict in ctrlData.dict.uis_exam_modality"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="1 == selectNodeLevel ? true : false">
                <el-col :span="24">
                  <el-form-item label="检查项目" prop="examItemCodes">
                    <el-select v-model="editForm.examItemCodes" class="w100">
                      <el-option
                        v-for="dict in ctrlData.dict.uis_exam_item"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="selectNodeLevel > 1 ? true : false">
                <el-col :span="24">
                  <el-form-item label="检查部位" prop="examPartsIds">
                    <div style="height: 200px; overflow: auto">
                      <ExamPartsTree
                        ref="examPartsTree"
                        :checkedKeys="editForm.examPartsIds"
                        :expandedKeys="editForm.examPartsIds"
                        @onChecked="handleCheckedExamParts"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-main>
          <el-footer height="unset">
            <div class="foot-tools">
              <el-button type="primary" @click="submitEditForm"
                >确 定</el-button
              >
              <el-button @click="cancelEdit">取 消</el-button>
            </div>
          </el-footer>
        </div>
      </div>
    </el-container>

    <!-- <div slot="footer" class="dialog-footer">
    <el-button type="primary" @click="submitEditForm">确 定</el-button>
    <el-button @click="cancelEdit">取 消</el-button>
  </div> -->
  </el-dialog>
</template>

<style scoped src="@/assets/styles/pacs/dragControllerDiv.css"></style>

<script>
import model from "@/assets/scripts/pacs/tplcfg/category/EditForm";
export default model;
</script>
