<template>
  <div class="app-container">
    <el-row>

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
            <el-button
              type="primary"
              
              icon="el-icon-search"
              size="mini"
              @click="getList"
            >刷新</el-button>
            <el-radio-group v-model="personalFlag" style="margin-left: 10px;" >
              <el-radio v-for="dict in dict.type.uis_report_template_private_flag" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="cateTreeData" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="id" label="分类编码" width="120" />
          <el-table-column prop="cateName" label="分类名称" width="200" />
          <el-table-column prop="parent.id" label="父类编码" width="120" />
          <el-table-column prop="parent.cateName" label="父类模板" width="200" />
          <el-table-column prop="examParts" label="检查部位" min-width="200" :formatter="colFmt_examParts" />
          <el-table-column prop="personalFlag" label="模板类型" min-width="200" :formatter="colFmt_personalFlag" />
          <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
          <el-table-column label="操作" align="center" width="160" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-plus"
                @click="handleAdd(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="!queryForm.treeEnabled && grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="queryForm.pageNum"
          :limit.sync="queryForm.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    
    <EditForm ref="editFormDialog" @refresh="getList"></EditForm>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/tplcfg/category";
export default model;
</script>
