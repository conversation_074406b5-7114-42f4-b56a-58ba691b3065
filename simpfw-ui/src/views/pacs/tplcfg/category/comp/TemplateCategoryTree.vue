<template>
<el-tree
  ref="tree" node-key="uid"
  :data="tree.data"
  :props="tree.props"
  :expand-on-click-node="true"
  :default-expand-all="defaultExpandAll"
  @node-click="handleNodeClick"
  @node-contextmenu="handleNodeContextmenu"
  :filter-node-method="filterTemplate"
  :default-expanded-keys="expandedKeys"
  :current-node-key="currentNodeKey"
>
  <span slot-scope="{ node, data }" :class="node.data.className">
    <i :class="node.data.icon" v-if="!!node.data.icon"></i>
    <el-tooltip v-if="2!=node.data.data.personalFlag&&node.data.isTemplate&node.data.data.templateType==1"  effect="light" placement="right" :disabled="!htooltip">
      <div style="width: 400px;" slot="content">
          <h2 style="font-weight: bold;">内容:</h2>
          <span style="font-size: medium; white-space: pre-wrap;"> {{ node.data.data.examDesc }}</span>
      </div>
      <span>{{ node.label }}</span>
  </el-tooltip>
    <el-tooltip v-else-if="2!=node.data.data.personalFlag&&node.data.isTemplate"  effect="light" placement="right" :disabled="!htooltip">
        <div style="width: 400px;" slot="content">
            <h2 style="font-weight: bold;">检查所见(印象):</h2>
            <span style="font-size: medium; white-space: pre-wrap;"> {{ node.data.data.examDesc }}</span>
            <h2 style="font-weight: bold;">检查诊断(提示):</h2>
            <span style="font-size: medium; white-space: pre-wrap;"> {{ node.data.data.examDiagnosis }}</span>
        </div>
        <span>{{ node.label }}</span>
    </el-tooltip>
    <!-- 结构化模板 -->
    <el-tooltip v-if="2==node.data.data.personalFlag&&node.data.isTemplate"  effect="light" placement="right" :disabled="!htooltip">
        <div style="width: 600px;" slot="content">
            <el-radio-group v-model="radio" v-show="false">
              <el-radio :label="3">备选项</el-radio>
              <el-radio :label="6">备选项</el-radio>
              <el-radio :label="9">备选项</el-radio>
            </el-radio-group>

            <div v-for="(textLine,index) in node.data.data.examDescAr" :key="index">
              <span v-for="(textGroup,indexG) in textLine" :key="indexG" style="font-size: medium; white-space: pre-wrap;">
                <span v-if="textGroup.isStr==0?true:false">{{textGroup.str}}</span> 
                <el-radio-group v-if="textGroup.isStr==1?true:false"  v-model="textGroup.select" @change="val=> change()">
                    <el-radio v-for="(textRadio,indexR) in textGroup.str" :key="indexR" :label="indexR" style="margin: 1px;font-weight:bolder">{{textRadio}}</el-radio>
                </el-radio-group>
                <el-checkbox-group v-if="textGroup.isStr==2?true:false"  v-model="textGroup.select" @change="val=> change()" style="display:inline-block">
                    <el-checkbox v-for="(textRadio,indexR) in textGroup.str" :key="indexR" :label="indexR" style="margin: 1px;font-weight:bolder">{{textRadio}}</el-checkbox>
                </el-checkbox-group>
              </span>
            </div>
          </div>
        <span>{{ node.label }}</span>
    </el-tooltip>

    <span v-if="!node.data.isTemplate">{{ node.label }}</span>
  </span>
</el-tree>
</template>

<script>
import model from "@/assets/scripts/pacs/tplcfg/category/comp/TemplateCategoryTree";
export default model;
</script>
<style >
div.el-tooltip__popper.is-light {
    background-color: rgb(249, 249, 240)
}
</style>