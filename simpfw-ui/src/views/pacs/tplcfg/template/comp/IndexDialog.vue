 <template>
<el-dialog title="编辑模板" :visible.sync="opened" >
  <TemplateIndex ref="tplIndex" @refresh="bubbleRefresh" />
</el-dialog>
</template>
<script>

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {default as TemplateIndex} from "@/views/pacs/tplcfg/template/Index";

export default {
  extends: BaseDialogModel,

  components: {TemplateIndex},

  methods: {
    bubbleRefresh(item) {
      this.triggerBind("refresh", item);

      this.close();
    },

    handleAdd(dat) {
      this.open();
      this.$nextTick(() => {
        this.$refs.tplIndex.handleAdd(dat);
      });
    }
  },

  mounted() {
    //this.$refs.tplIndex.buildCateTree();
  }
};

</script>
