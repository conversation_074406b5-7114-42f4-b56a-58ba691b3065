<template>
  <div :class="wrapClass">
    <div class="priflag-bar" v-if="priflag">
      <el-radio-group v-model="privateFlag" @input="setTemplateVis">
        <el-radio v-for="dict in dict.type.uis_report_template_private_flag" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
      </el-radio-group>
    </div>
    <TemplateCategoryTree ref="cateTree"
     :defaultExpandAll="false"
     :fakeRoot="false" :default-expand-all="false" :htooltip="htooltip"
     :menu="menu"
     @built="loadTemplates"
     @onChangeArea="handleNodeClick"
     @nodeDblclick="handleNodeDblclick"
     @nodeContextmenu="showNodeContextmenu" />
     <!-- 右键 -->
    <Contextmenu ref="tplTreeContextmenu" :items="[{cmd: 'edit', name: '编辑'}]" @select="clickNodeContextmenu" />

  </div>
</template>

<style>
.priflag-bar{
  padding: 4px 8px;
  text-align: center;
  background-color: #EFEFEF;
}
span.node-template{
  color: #003366;
  /*background-color: #f4f9fc;
  border-radius: 4px;*/
  user-select: none;
}
</style>

<script>
import model from "@/assets/scripts/pacs/tplcfg/template/comp/TemplateTree";
export default model;
</script>
