<template>
<div style="height: 100%">
  <el-container style="height: 100%">
    <div class="box" ref="box">
        <div class="left hei100 scroll-auto">
            <TemplateTree :wrapClass="{'aside-tree-wrap': true}" :priflag="true" :menu="true" ref="tplTree"   @dblclick="handleNodeClick" />
        </div>
        <div class="resize" title="收缩侧边栏">
            ⋮
        </div>
        <div class="mid hei100 ">
            <el-main>
                <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="110px" class="tight-form"><!-- :rules="rules" -->
                  <el-row>
                    <el-col :span="18">
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="当前模板" prop="personalFlag">
                            <el-radio-group v-model="editForm.personalFlag"  @input="setTemplateVis" >
                              <el-radio v-for="dict in dict.type.uis_report_template_private_flag" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
                            </el-radio-group>
                            </el-form-item>
                        </el-col>             
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="模板类型" prop="templateType">
                            <el-radio-group v-model="editForm.templateType"   >
                              <el-radio v-for="dict in dict.type.pacs_template_type" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
                            </el-radio-group>
                            </el-form-item>
                        </el-col>             
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="模板分类" prop="category.id">
                            <Treeselect  v-model="editForm.category.id" :flat="true"
                            :options="cateTreeData" :show-count="true"
                            placeholder="选择" style="max-width: 240px;font-weight: 100;" /> 
                          </el-form-item>
                        </el-col>             
                      </el-row>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="模板名称" prop="templateName">
                            <el-input v-model="editForm.templateName" :readonly="!editable" :title="!editable? '请选择模板分类或模板' : null" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <div v-if="editForm.templateType=='2'">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="检查所见(印象)">
                              <el-input type="textarea" v-model="editForm.examDesc" name="examDesc" :rows="8" :readonly="!editable" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="检查诊断(提示)">
                              <el-input type="textarea" v-model="editForm.examDiagnosis" name="examDiagnosis" :rows="4" :readonly="!editable" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="术后医嘱">
                              <el-input type="textarea" v-model="editForm.operationSuggestion" name="operationSuggestion" :rows="4" :readonly="!editable" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="提示语">
                              <el-input v-model="editForm.prompts" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="阴阳性">
                              <el-select v-model="editForm.examResultProp.dictValue" clearable>
                                <el-option
                                  v-for="dict in dict.type.uis_exam_result_prop"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>              
                            </el-form-item>
                          </el-col>
          
                          <!-- <el-col :span="12">
                            <el-form-item label="检查项目">
                              <el-select v-model="editForm.examItem_dictValue" clearable multiple collapse-tags>
                                <el-option
                                  v-for="dict in dict.type.uis_exam_item"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>              
                            </el-form-item>
                          </el-col> -->
                          
                        </el-row>
                        <el-row >
                          <el-col :span="24">
                            <el-form-item label="检查部位">
                              <div style="height: 200px; overflow: auto">
                                <ExamPartsTree ref="examPartsTree" :checkedKeys="editForm.examPartsIds" :expandedKeys="editForm.examPartsIds" @onChecked="handleCheckedExamParts" />
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                    </div>
                    <div v-else-if="editForm.templateType=='1'">
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="模板内容">
                            <el-input type="textarea" v-model="editForm.examDesc" name="examDesc" :rows="8" :readonly="!editable" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <!-- <el-row>
                        <el-col :span="12">
                            <el-form-item label="检查项目">
                              <el-select v-model="editForm.examItem_dictValue" clearable multiple collapse-tags>
                                <el-option
                                  v-for="dict in dict.type.uis_exam_item"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>              
                            </el-form-item>
                          </el-col>
                      </el-row> -->
                      <el-row >
                          <el-col :span="24">
                            <el-form-item label="检查部位">
                              <div style="height: 200px; overflow: auto">
                                <ExamPartsTree ref="examPartsTree" :checkedKeys="editForm.examPartsIds" :expandedKeys="editForm.examPartsIds" @onChecked="handleCheckedExamParts" />
                              </div>
                            </el-form-item>
                          </el-col>
                        </el-row>
                    </div>
                    </el-col>
                 
                    <el-col :span="6">
                      <div>关键短语</div>
                      <PhraseSelect :targets="['examDesc', 'examDiagnosis', 'operationSuggestion']" @change="updateFormField" />
                    </el-col>
                  </el-row>
                </el-form>
              </el-main>
              <el-footer height="unset">
                <div class="foot-tools">
                  <el-button type="primary" @click="submitEditForm" :loading="loading" :disabled="!editable" :title="!editable? '请选择模板分类或模板' : null">保 存</el-button>
                  <el-button type="danger" @click="handleDelete(editForm)" v-show="!!editForm.id">删 除</el-button>
                  <el-button @click="cancelEdit">重 置</el-button>
                </div>
              </el-footer>
        </div>
    </div>
  </el-container>
</div>
</template>

<style scoped src="@/assets/styles/pacs/dragControllerDiv.css"></style>

<script>
import model from "@/assets/scripts/pacs/tplcfg/template";
export default model;
</script>
