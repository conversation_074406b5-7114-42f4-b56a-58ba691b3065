<template>
  <ul class="ulist ulist-card clearfix">
    <li v-for="p in dataset" :key="p.thesaurusCode" @click="onSelect(p.thesaurusName, $event)">
      <span>{{p.thesaurusName}}</span>
    </li>
  </ul>
</template>

<!-- <style scoped>
ul.ulist, ul.ulist li {
  padding: 0;
  margin: 0;
  list-style: none;
}
ul.ulist .el-button{
  margin: 0;
  padding: 2px 4px;
}
</style> -->

<script>
import BaseFormInputInsertor from "@/assets/scripts/pacs/BaseFormInputInsertor";

import * as api from "@/assets/scripts/pacs/tplcfg/phrase/api";

let model = {
  name: "PraseSelect",

  extends: BaseFormInputInsertor,

  methods: {

    loadData() {
      api.find({}).then(res => {
        this.dataset = res.rows;
      });
    },
  }
};

export default model;
</script>
