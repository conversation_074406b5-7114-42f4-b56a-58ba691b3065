<template>
  <div class="app-container">
    <el-row>

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="characterCode" label="符号编码" width="260" />
          <el-table-column prop="characterName" label="符号名称" min-width="200" />
          <el-table-column prop="status" label="是否启用" width="100" :formatter="colFmt_Status" />
          <el-table-column label="操作" width="120" align="center" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"/>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"/>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editForm.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="符号编码" prop="characterCode">
              <el-input v-model="editForm.characterCode" placeholder="请输入符号编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="符号名称" prop="character">
              <el-input v-model="editForm.characterName" placeholder="请输入符号名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查项目">
              <el-select v-model="editForm.examItem_dictValue" clearable multiple collapse-tags>
                              <el-option
                                v-for="dict in dict.type.uis_exam_item"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="是否启用">
              <el-checkbox v-model="editForm.status">启用</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/tplcfg/symbol";
export default model;
</script>
