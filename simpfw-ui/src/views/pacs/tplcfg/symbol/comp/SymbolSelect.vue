<template>
  <ul class="ulist ulist-card clearfix">
    <li v-for="p in dataset" :key="p.characterCode" @click="onSelect(p.characterName, $event)">
      {{p.characterName}}
    </li>
  </ul>
</template>

<style scoped>

</style>

<script>
import BaseFormInputInsertor from "@/assets/scripts/pacs/BaseFormInputInsertor";

import * as api from "@/assets/scripts/pacs/tplcfg/symbol/api";

//
import {DataCtrlDict} from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";


let model = {
  name: "SymbolSelect",

  extends: BaseFormInputInsertor,
  mixins: [ExamDataScope],
  methods: {

    loadData() {
      api.find({status: 0}).then(res => {
        this.dataset = res.rows;
      });
    },
  },

  watch:{
    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      }
    }
  }
};

export default model;
</script>
