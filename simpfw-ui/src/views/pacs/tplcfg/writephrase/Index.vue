<template>
<div style="height: 100%">
  <el-container style="height: 100%">
    <el-aside class="btrans-tree-wrap">
        <WritePhraseTree ref="writePhraseTree" @onChangeArea="handleNodeClick" />
    </el-aside>
    <el-main>

      <el-row :gutter="10" class="mb8">
        <el-col :span="24">
          <el-button type="primary" plain icon="el-icon-plus" size="mini"
            @click="handleAdd">新增</el-button>
            <el-button type="primary" icon="el-icon-search" size="mini"
            @click="getList">刷新</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="grid.data" row-key="id"
       stripe highlight-current-row>
        <el-table-column prop="phraseContent" label="书写词库" min-width="260" :show-overflow-tooltip="true" />
        <el-table-column prop="contentItem.dictLabel" label="词库类别" width="100"  :formatter="colFmt_dictData" />
        <el-table-column prop="examItem.dictLabel" label="检查项目" width="200" :formatter="colFmt_dictData" />
        <el-table-column prop="parent.id" label="父类编码" width="80">
          <template slot-scope="scope">{{scope.row && scope.row.parent && scope.row.parent.id}}</template>
        </el-table-column>
        <el-table-column prop="parent.phraseContent" label="父类名称" width="200" show-overflow-tooltip>
          <template slot-scope="scope">{{scope.row && scope.row.parent &&  scope.row.parent.phraseContent}}</template>
        </el-table-column>
        <el-table-column prop="status" label="是否启用" width="80" :formatter="colFmt_Status" />
        <el-table-column label="操作" align="center" width="160" class-name="button-col">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)" />
            <el-button
              icon="el-icon-delete"
              @click="handleDelete(scope.row)" />
            <el-button
              icon="el-icon-plus"
              @click="handleAdd(scope.row)" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="grid.pager.total>0"
        :total="grid.pager.total"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        @pagination="getList"
      />
    </el-main>
  </el-container>
  
  <el-dialog :title="editForm.title" :visible.sync="editForm.visible"
   class="popupdialog" width="600px" append-to-body>
    <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="父类编码">
            <el-input v-model="editForm.parent.id" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父类名称">
            <el-input v-model="editForm.parent.phraseContent" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="词库类别">
            <el-select v-model="editForm.contentItem.dictCode">
              <el-option
                v-for="dict in dict.type.uis_report_content_item"
                :key="dict.value"
                :label="dict.label"
                :value="dict.raw.dictCode"
              />
            </el-select>              
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查项目">
            <el-select v-model="editForm.examItem_dictCode" clearable multiple collapse-tags>
              <el-option
                v-for="dict in dict.type.uis_exam_item"
                :key="dict.value"
                :label="dict.label"
                :value="dict.raw.dictCode"
              />
            </el-select>              
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="书写词库" prop="phraseContent">
            <el-input type="textarea" v-model="editForm.phraseContent" :rows="4" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="是否启用">
            <el-checkbox v-model="editForm.statusFlag" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitEditForm">确 定</el-button>
      <el-button @click="cancelEdit">取 消</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import model from "@/assets/scripts/pacs/tplcfg/writephrase";
export default model;
</script>
