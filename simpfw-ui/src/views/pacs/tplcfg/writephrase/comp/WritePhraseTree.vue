<template>
  <el-tree
    :data="affectedTreeData"
    :props="tree.props"
    :expand-on-click-node="false"
    ref="tree"
    default-expand-all
    @node-click="handleNodeClick"
  />
</template>

<script>
import {cloneDeep} from "lodash";

import BaseTreeModel from "@/assets/scripts/pacs/BaseTreeModel";

import * as api from "@/assets/scripts/pacs/tplcfg/writephrase/api";

let model = {
  extends: BaseTreeModel,

  props: {
    filter: {type: Object}
    , examItem: {type: Object} 
    , formFieldCode: {type: String}
  },

  methods: {
    //读取书写词库
    buildTree() {
      api.treeselect(this.filter).then(res => {
        this.tree.data = res.data;
      });
    },

    //根据检查项目和书写内容（检查所见/检查诊断/术后医嘱）
    affectTreeData(origData, tarData) {
      if(!origData) { return; }
      let examItemCode = this.examItem? this.examItem.dictValue : null
        , formFieldCode = this.formFieldCode;
      switch(this.formFieldCode) {
        case 'examDesc':
          formFieldCode = '01';
          break;
        case 'examDiagnosis':
          formFieldCode = '02';
          break;
        case 'operationSuggestion':
          formFieldCode = '03';
          break;
      }
      //console.log(examItemCode, formFieldCode);
      if((origData instanceof Object) && !(origData instanceof Array)) {
        origData = [origData];
      }

      origData.forEach(e => {
        const examItem = e.data.examItem, contentItem = e.data.contentItem;
        //console.log(e);
        if((!examItemCode || !examItem || !examItem.length || !!examItem && !!examItem.find(ei => ei.dictValue === examItemCode))
          && (!formFieldCode || !contentItem || !contentItem.dictCode || !!contentItem && contentItem.dictValue === formFieldCode)) {
            let ochd = e.children;
            if(!ochd || !ochd.length) {
              tarData.push(e);
            } else {
              let nod = cloneDeep(e), chd = [];
              nod.children = chd;
              tarData.push(nod);
              this.affectTreeData(ochd, chd);
            }
        }
      });
    }
  },

  computed: {
    //当前应用的树数据
    affectedTreeData() {
      let origData = this.tree.data, items;
      if((!this.examItem || !this.examItem.dictCode) && !this.formFieldCode) {
        items = origData;
      } else {
        items = [];
        this.affectTreeData(origData, items);
      }
      return [{label: "全部", children: items}];
    }
  }
};

export default model;

</script>
