<template>
  <WritePhraseTree :filter="{status: 0}" :examItem="examItem" :formFieldCode="formFieldCode" @onChangeArea="selectNode" />
</template>

<script>

import WritePhraseTree from "@/views/pacs/tplcfg/writephrase/comp/WritePhraseTree";
import BaseFormInputInsertor from "@/assets/scripts/pacs/BaseFormInputInsertor";

let model = {
  extends: BaseFormInputInsertor,

  props: {
    examItem: {type: Object}
    , formFieldCode: {type: String}
  },

  components: { WritePhraseTree },

  methods: {
    //选择节点触发
    selectNode(node) {
      //console.log(node);
      if(!node.id) {
        return;
      }
      this.onSelect(node.label);
    }
  },

  mounted() {
    //console.log(this.target);
    //console.log(this.examItem);
  }
};

export default model;
</script>
