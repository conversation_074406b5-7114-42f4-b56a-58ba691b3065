<template>
<div class="qr-code-wrap" v-loading="qr && qr.loading" element-loading-background="rgba(0, 0, 0, 0.2)">
  <img :src="qr && qr.data" @error="qr && qr.refreshQrcodeFail()" />
  <div class="qr-code-wrap-mask-invalid" @click="qr && qr.refreshQrcode()"
   v-if="qr && !qr.autoCheckQrAuthStatus">
    <div class="qr-code-wrap-mask-invalid-ico"><i class="el-icon-refresh-right"></i></div>
    <div class="qr-code-wrap-mask-invalid-txt">已过期</div>
  </div>
</div>
</template>

<style scoped>
.qr-code-wrap, .qr-code-wrap img{
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  text-align: center;
}
.qr-code-wrap-mask-invalid{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0, 0, 0, 0.6);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: default;
}
.qr-code-wrap-mask-invalid-ico{
  font-size: 32px;
}
.qr-code-wrap-mask-invalid-txt{
  font-size: 1.4em;
}
</style>

<script>

//import QRSign from "@/assets/scripts/uis/linksign/QRSign"

export default {
  name: "QRDialog",

  props: {
      qr: {type: Object, default: null},
  },

  data() {
    return {};
  },

  mounted() {
      
  },

  methods: {
    /**
     * 退出系统
     */
    logout() {
      //LoginHelper.logout();
    }

    , abandQR() {
      this.$emit("abandQR");
    }
  }
};

</script>

