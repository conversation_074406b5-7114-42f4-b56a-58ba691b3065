<template>
  <!-- 扫码登录窗口 -->
  <el-dialog custom-class="linksign-qrcode-dialog"
   :before-close="abandQR"
   :close-on-click-modal="false"
   :visible.sync="opened"
   >
    <div slot="title" class="linksign-qrcode-tip">{{qr && qr.naviText}}</div>
    <div>
      <div class="linksign-qrcode-wrap" v-loading="qr && qr.loading" element-loading-background="rgba(0, 0, 0, 0.2)">
        <img :src="qr && qr.data" @load="qr && qr.refreshQrcodeSuccess()" @error="qr && qr.refreshQrcodeFail()" />
      </div>
    </div>
    <div slot="footer">
      <el-button-group style="width:100%;">
        <el-button type="primary" style="width:50%;" @click="qr && qr.refreshQrcode()" :loading="qr && qr.loading">{{qr && qr.refreshButtonText}}</el-button>
        <el-button type="danger" style="width:50%;" @click="logout">退出登录</el-button>
      </el-button-group>
    </div>
  </el-dialog>
</template>

<script>
import '@/assets/styles/pacs/linksign/QRDialog.css';

import QRSign from "@/assets/scripts/pacs/linksign/QRSign"

export default {

  props: {
      qr: {type: QRSign, default: null},
  },

  data() {
    return {};
  },

  mounted() {
      
  },

  methods: {
    /**
     * 退出系统
     */
    logout() {
      
    }

    , abandQR() {
      this.$emit("abandQR");
    }
  }
};

</script>

