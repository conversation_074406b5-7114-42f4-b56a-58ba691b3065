<template>
  <!-- 扫码登录窗口 -->
  <el-dialog custom-class="linksign-qrcode-dialog"
   :before-close="abandQR"
   :close-on-click-modal="false"
   :visible.sync="opened"
   append-to-body
   >


<el-container>
    <el-header>{{title}}</el-header>
    <span v-show="qr.oauthWindowURL!=null">
      <el-main>
      <div style="width: 600px;height: 600px;" class="linksign-qrcode-wrap" v-show="'ca' === loginForm.mode">

        <iframe  style="width: 500px;height: 600px;" :src="qr.oauthWindowURL"></iframe>

      </div>
    </el-main>
    </span>
    <span v-show="qr.oauthWindowURL==null">
    <el-main>
    <div class="linksign-qrcode-wrap" v-show="'ca' === loginForm.mode">
        <Linksign :qr="qr" />
        <el-button type="primary" style="width:35%;margin-left:32%;" @click="refreshQrcode">{{refreshButtonText}}</el-button>
      </div>
    <div v-show="'pin' === loginForm.mode">
        <el-form :model="loginForm" :rules="loginRule"
         status-icon
         ref="certAuthForm"
         label-width="0px"
         class="login-page" size="medium">
        <!-- <h3 class="title">{{title}}</h3> -->
            <el-form-item >
              <el-radio v-for="(type,index) in loginForm.userTypes" :key="index" v-model="loginForm.userType" :label="type.code">{{type.name}}</el-radio>
            </el-form-item>
            <div>
            <el-form-item prop="username" >
                <el-input type="text"
                    v-model="loginForm.username"
                    auto-complete="off"
                    placeholder="用户ID或工号"
                    @keyup.enter.native="handleSubmit"

                ></el-input>
            </el-form-item>

            <el-form-item prop="password" >
                <el-input type="password"
                    v-model="loginForm.password"
                    auto-complete="off"
                    placeholder="口令"
                    @keyup.enter.native="handleSubmit"

                ></el-input>
            </el-form-item>

            <!-- <el-form-item prop="verifyCode" >
                <el-input type="text"
                    v-model="loginForm.verifyCode"
                    auto-complete="off"
                    placeholder="验证码"
                    style="width:49%"
                ></el-input>
                <el-tooltip :disabled="!!loginForm.username && !!loginForm.password" content="请输入'用户ID或工号'及'口令'" placement="top"><span style="display: inline-block; width: 49%;">
                <el-button type="primary" @click="requestVerify"
                    :disabled="requestVerifyDisabled"
                    style="width:100%">获取验证码{{verifyCodeTimeoutCounter}}</el-button>
                </span></el-tooltip>
            </el-form-item> -->

            <!-- <el-form-item style="width:100%;margin-bottom:12px;" size="medium">
              <el-button-group style="width:100%;">

                <el-button type="primary" style="width:50%;" @click="setLoginMode('pin')" :loading="logining">口令认证</el-button>
                <el-button type="success" style="width:50%" @click="setLoginMode('ca')" >扫码认证<i class="el-icon-full-screen"></i></el-button>
              </el-button-group>
            </el-form-item> -->
          </div>
        </el-form>

    </div>
  </el-main>
  <el-footer>
      <el-button-group style="width:100%; margin-bottom:12px;">
            <!-- <el-button type="primary" style="width:20%;" @click="setLoginMode('up')">返回</el-button> -->
            <el-button type="primary" style="width:50%;" @click="setLoginMode('pin')" :loading="logining">口令认证</el-button>
            <el-button type="success" style="width:50%" @click="setLoginMode('ca')" >扫码认证<i class="el-icon-full-screen"></i></el-button>
      </el-button-group>
    </el-footer>
    <!-- <div slot="footer">
      <el-button-group style="width:100%;">
        <el-button type="primary" style="width:50%;" @click="refreshQrcode">{{refreshButtonText}}</el-button>
        <el-button type="danger" style="width:50%;" @click="cancel">取消扫码</el-button>
      </el-button-group>
    </div> -->
  </span>
  </el-container>
  </el-dialog>
</template>

<script>
import model from "@/assets/scripts/pacs/linksign/Popup";
export default model;
</script>

