<template>
  <el-form label-width="100px" class="tight-form exam-view-form">
    <el-row style="height: 50%; width: 100%">  
      <el-form-item label="检查所见" class="hei100 fhta">
        <el-input name="examDiagnosis" v-model="examInfo.examDiagnosis" readonly type="textarea" :rows="6"/>
      </el-form-item>
    </el-row>

  <el-row style="height: 50%; width: 100%">  
    <el-form-item label="检查诊断" class="hei100 fhta">
      <el-input name="examDiagnosis" v-model="examInfo.examDiagnosis"  readonly type="textarea" :rows="6"/>
    </el-form-item>
  </el-row>

  </el-form>

</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>

<script>
import {mergeWith as mergeWithDeep} from "lodash";

import {mergeWithNotNull} from "@/utils/common";

import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import {emptyRegist} from "@/assets/scripts/uis/exammanagement/patient/Regist";

const model = {
  name: "ReportViewer",
  extends: BaseDialogModel,

  data() {
    return {
      examInfo: emptyRegist()
    }
  },

  methods: {
    view(exam) {
      let tpl = emptyRegist();
      //this.examInfo = tpl;
      //mergeWithNotNull(tpl, exam);
      if (exam == undefined){
        // this.$modal.msg("请选择一个患者");
        return;
      }
      console.log(exam.resultStatus.dictCode);
      if(exam.resultStatus.dictCode < 213){
        this.examInfo =emptyRegist();
        return;
      }
      eiapi.get(exam.id).then(res => {
        mergeWithDeep(tpl, res.data, null, mergeWithNotNull);
        this.examInfo = tpl;
      })
      this.open();
    }
  }
};
export default model;
</script>

<style scoped>
.exam-view-form >>> .el-form-item__content{
  font-weight: bold;
}
</style>