<template>
<!-- <el-dialog title="查看申请单" :visible.sync="opened" close-on-press-escape close-on-click-modal> -->
  <el-form label-width="100px" class="tight-form exam-view-form">

    <el-row>
      <el-col :span="8">
        <el-form-item label="检查类型:">{{examInfo.examModality.dictLabel}}</el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="就诊类型:">{{examInfo.inpType.dictLabel}}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="检查项目:">{{examInfo.examItem.dictLabel}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="16">
        <el-form-item label="检查部位:">{{!!examInfo.examParts? examInfo.examParts.map(e => e.partsName).join(",") : null}}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="检查机房:">{{!!examInfo.callInfo && examInfo.callInfo.callRoom? examInfo.callInfo.callRoom.roomName : null}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="检查医师:">{{!!examInfo.examDoctor? examInfo.examDoctorsName : null}}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="申请科室:">{{!!examInfo.reqDept? examInfo.reqDept.deptName : null}}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="申请医师:">{{examInfo.reqDoctorName}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="检查费用:">{{examInfo.examCost}}</el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="绿色通道:">{{examInfo.greenChannelFlag? '是' : '否'}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="临床诊断:">{{examInfo.clinicDiagnosis}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="过敏史:">{{examInfo.allergyHistory}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="患者病史:">{{examInfo.clinicDisease}}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="注意事项:">{{examInfo.noteInfo}}</el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!--<div slot="footer" class="dialog-footer">
    <div class="foot-tools">
      <el-row>
        <el-col :span="24">
          <el-button type="primary">合格</el-button>
          <el-button type="primary">不合格</el-button>
          <el-button @click="close">关闭</el-button>
        </el-col>
      </el-row>
    </div>
  </div>-->

<!-- </el-dialog> -->
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>

<script>
import {mergeWith as mergeWithDeep} from "lodash";

import {mergeWithNotNull} from "@/utils/common";

import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import {emptyRegist} from "@/assets/scripts/uis/exammanagement/patient/Regist";

const model = {
  name: "ExamViewer",
  extends: BaseDialogModel,

  data() {
    return {
      examInfo: emptyRegist()
    }
  },

  methods: {
    view(exam) {
      let tpl = emptyRegist();
      //this.examInfo = tpl;
      //mergeWithNotNull(tpl, exam);
      if (exam == undefined){
        // this.$modal.msg("请选择一个患者");
        return;
      }
      eiapi.get(exam.id).then(res => {
        mergeWithDeep(tpl, res.data, null, mergeWithNotNull);
        this.examInfo = tpl;
      })
      this.open();
    }
  }
};
export default model;
</script>

<style scoped>
.exam-view-form >>> .el-form-item__content{
  font-weight: bold;
}
</style>