<template>
  <div style="height: 100%">
    <el-container class="inner-container inner-container-split" style="height: 100%">
      <el-aside class="inner-container-aside" :class="{'inner-container-aside-collapse': !searchFormVisible}"  width="200px">
        <el-form label-position="left" label-width="5em" style="height:100%" class="tight-form exam-search-form">
          <el-card class="nested-card hei100 card-pane" :body-style="{overflow: 'hidden',display: 'flex', 'flex-direction': 'column'}">
            <div slot="header">
              <span class="nested-card-header-title">过滤器</span>
              <!-- <div class="nested-card-tools">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="search"></el-button>
              </div> -->
            </div>

            <div class="flex-item-fill" style="overflow: auto">
              <el-scrollbar class="scrollpane scrollpane-h"><!--  -->
              <div>
                <el-form-item label="检查日期" class="el-form-item-date-label"></el-form-item>
                <el-form-item label-width="0" class="el-form-item-alcenter el-form-item-date-input">
                  <el-date-picker
                    v-model="searchForm.examTimeGe"
                    type="date"
                    placeholder="开始日期"
                    class="el-date-editor--noprefix"
                    style="width:45%" :clearable="false">
                  </el-date-picker>
                  -
                  <el-date-picker
                    v-model="searchForm.examTimeLt"
                    type="date"
                    placeholder="结束日期"
                    class="el-date-editor--noprefix"
                    style="width: 45%" :clearable="false">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label-width="时间选择" class="form-item-thin el-form-item-srow">

                </el-form-item>
                <el-form-item label="姓名">
                  <el-tooltip content="请输入*或空格进行模糊查询, 如: 张*三, *某四" placement="top-start" :open-delay="1000">
                    <el-input v-model="searchForm.patientInfo.name" placeholder="请输入*进行模糊查询" clearable />
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="住院号">
                  <el-input v-model="searchForm.inpNo" clearable />
                </el-form-item>
                <el-form-item label="登记号">
                  <el-input v-model="searchForm.registNo" clearable :readonly="searchForm.pinPatient"></el-input>
                </el-form-item>
                 <el-form-item label="就诊类型">
                  <el-select v-model="searchForm.inpTypeValues" placeholder="全部"
                   multiple collapse-tags class="select-multi-sing">
                    <el-option
                      v-for="dict in dict.type.uis_inp_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="申请科室">
                  <Treeselect v-model="searchForm.reqDept.deptId" :options="deptTreeData" :show-count="true" placeholder="选择" />
                </el-form-item>

                <el-form-item label="检查类别" class="el-form-item-srow el-form-item-modalities">
                  <el-select v-model="searchForm.examModality.dictValue" clearable>
                    <el-option
                      v-for="dict in dict.type.uis_exam_modality"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>

                 <el-form-item label="检查号">
                    <el-input v-model="searchForm.examNo" clearable></el-input>
                  </el-form-item>
                 <el-form-item label="性别">
                  <el-select v-model="searchForm.patientInfo.gender.dictValue" clearable>
                      <el-option
                        v-for="dict in dict.type.uis_gender_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="就诊号">
                    <el-input v-model="searchForm.outpNo" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="检查医生">
                    <el-input v-model="searchForm.examDoctor.nickName" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="报告医生">
                    <el-input v-model="searchForm.reportDoctor.nickName" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="审核医生">
                    <el-input v-model="searchForm.auditDoctor.nickName" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="检查所见">
                    <el-input v-model="searchForm.examDesc" clearable placeholder="多关键字以逗号或空格间隔"></el-input>
                  </el-form-item>
                  <el-form-item label="检查诊断">
                    <el-input v-model="searchForm.examDiagnosis" clearable placeholder="多关键字以逗号或空格间隔"></el-input>
                  </el-form-item>
              </div>
            </el-scrollbar><!--  -->
            </div>
            <div style="padding: 4px 0; text-align: center;">
              <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
            </div>
          </el-card>
        </el-form>
      </el-aside>

      <el-main>
        <div class="data-container flex-container-column">
          <p> {{currentTableRowStr == undefined ? "选中一个患者以显示信息" :  currentTableRowStr}}</p>
          <div class="search-form-v">
            <el-form :model="searchFormSimp" :inline="true" class="exam-search-form exam-search-form-simp"><!-- class="tight-form"-->
              <svg-icon icon-class="list-border" class-name="search-form-toggler"  @click="toggleSearchForm"/>
              <el-select v-model="searchFormSimp.textFieldName" clearable>
                <el-option v-for="item in combo.searchTextFields"
                  :key="item.name"
                  :label="item.label"
                  :value="item.name"
                />
              </el-select>
              <el-input v-model="searchFormSimp.textField" placehoder="请输入" />
              <el-select v-model="searchFormSimp.dateFieldName" clearable>
                <el-option v-for="item in combo.searchDateFields"
                  :key="item.name"
                  :label="item.label"
                  :value="item.name"
                />
              </el-select>
              <el-date-picker
                v-model="searchFormSimp.dateFieldGe"
                type="date"
                placeholder="开始日期"
                class="el-date-editor--noprefix"
                :clearable="true">
              </el-date-picker>
              -
              <el-date-picker
                v-model="searchFormSimp.dateFieldLt"
                type="date"
                placeholder="结束日期"
                class="el-date-editor--noprefix"
                :clearable="true">
              </el-date-picker>
              <!-- <el-select v-model="searchFormSimp.inpTypeValues" placeholder="就诊类型"
               multiple collapse-tags class="select-multi-sing">
                <el-option
                  v-for="dict in dict.type.uis_inp_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
                  <el-select v-model="searchFormSimp.equipRoomsCode" multiple collapse-tags placeholder="检查房间" clearable class="select-multi-sing">
                    <el-option
                      v-for="dict in combo.equipRooms"
                      :key="dict.roomCode"
                      :label="dict.roomName"
                      :value="dict.roomCode"
                    />
                  </el-select>
              <el-select v-model="searchFormSimp.examDevicesCode" placeholder="设备型号"
               multiple collapse-tags class="select-multi-sing">
                <el-option v-for="item in combo.devices"
                  :key="item.deviceCode"
                  :label="item.device"
                  :value="item.deviceCode"
                />
              </el-select>
              <el-select v-model="searchFormSimp.resultStatusValues" placeholder="检查进度"
               multiple collapse-tags class="select-multi-sing">
                <el-option
                  v-for="dict in dict.type.uis_exam_result_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
                <el-option label="已删除" value="-2" />
              </el-select> -->
              <el-button type="primary" icon="el-icon-search" @click="search('simp')">查询</el-button>
            </el-form>
          </div>

          <div class="flex-item-fill" style="overflow: auto;">

            <el-table v-loading="loading" :data="grid.data" row-key="id" height="100%"
            stripe highlight-current-row
            @row-click="selectTableRow"
            @cell-dblclick="dblclickTableRow"
            @row-contextmenu="showTableAction">

              <el-table-column prop="id" label="检查流水" width="80" />
              <el-table-column prop="patientInfo.name" label="患者姓名" width="100" :formatter="colFmt_object" />
              <el-table-column prop="examNo" label="检查号" width="120" />
              <el-table-column prop="examItem.dictLabel" label="检查项目" width="100" />
              <el-table-column prop="examParts.partsName" label="检查部位" width="180" :formatter="colFmt_object" show-overflow-tooltip  />
              <el-table-column prop="examDiagnosis" label="检查结论" width="180" :formatter="colFmt_object" show-overflow-tooltip  />

              <el-table-column prop="examCost" label="费用" width="100" />
              <el-table-column prop="callInfo.callNo" label="排队号" width="70" :formatter="colFmt_object" />
              <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50" :formatter="colFmt_dictData" />
              <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" />
              <el-table-column prop="equipRoom.roomName" label="检查房间" width="100" :formatter="colFmt_equipRoom" />
              <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
              :formatter="colFmt_object" />
              <el-table-column prop="inpNo" label="住院号" width="180" />
              <el-table-column prop="reqDept.deptName" label="申请科室" min-width="140"
              :formatter="colFmt_object"
              show-overflow-tooltip />
              <el-table-column prop="bedNo" label="床号" width="80" />
              <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="100">
                <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                  <i class="el-icon-error state-icon-err" v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                  <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue" v-else></i>
                  <span style="margin-left: 4px">{{ 2 === scope.row.status? '已删除' : (!!scope.row.resultStatus? scope.row.resultStatus.dictLabel : null) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="reqDoctor.nickName" label="申请医生" min-width="100" :formatter="colFmt_object" />
              <el-table-column prop="examDoctorsName" label="检查医生" min-width="100" />
              <el-table-column prop="reportDoctor.nickName" label="报告医生" width="100" :formatter="colFmt_object"  />
              <el-table-column prop="auditDoctor.nickName" label="审核医生" width="100" :formatter="colFmt_object"  />
              <el-table-column prop="accessionNumber" label="accessionNumber" min-width="200" />
              <el-table-column prop="consultantsName" label="会诊医生" min-width="100" />
              <el-table-column prop="recordersName" label="记录人员" min-width="100" />
              <el-table-column prop="creator.nickName" label="登记人员" min-width="120" />
              <el-table-column prop="createTime" label="登记时间" min-width="160" />
              <el-table-column prop="examTime" label="检查时间" min-width="160" />
              <el-table-column prop="signTime" label="审核时间" min-width="160" />
              <el-table-column prop="reportTime" label="报告时间" min-width="160" />
              <el-table-column prop="examModality.dictLabel" label="检查类型" width="100" :formatter="colFmt_dictData" />

              <!-- <el-table-column label="操作" fixed="right" align="center" width="120" class-name="button-col">
                <template slot-scope="scope">
                  <el-button title="查看"
                    icon="el-icon-tickets"
                    @click="handleDetail(scope.row)"
                  ></el-button>
                  <el-button title="编辑"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                  ></el-button>
                <el-button v-if="2 === scope.row.status"
                  title="撤销删除"
                  icon="el-icon-refresh-left"
                  @click="callUndoDelete(scope.row)"
                  v-hasPermi="['exam-info:delete']"
                  ></el-button>
                <el-button v-else
                  title="删除"
                  icon="el-icon-delete"
                  @click="verifyForDelete(scope.row)"
                  v-hasPermi="['exam-info:delete']"
                  ></el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>

          <pagination
            v-show="grid.total>0"
            :total="grid.total"
            :page.sync="searchForm.pageNum"
            :limit.sync="searchForm.pageSize"
            @pagination="getList"
          />
        </div>
      </el-main>
    </el-container>

    <Contextmenu ref="tableContextmenu" :items="tableAction" @select="handleTableAction" />

    <!-- <ExamViewer ref="examViewer" /> -->
    <!-- <ExamEquipRoom ref="examEquipRoom" @change="search" /> -->
    <OperationAuth ref="operAuth" @success="handleDelete" />
  </div>
  </template>

  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>

  <script>
  //import '@/assets/styles/uis/common.css';
  //import '@/assets/styles/uis/exammanagement/patient/Index.css';

  import model from "@/assets/scripts/pacs/exammanagement/workstation/Patient";
  export default model;
  </script>


