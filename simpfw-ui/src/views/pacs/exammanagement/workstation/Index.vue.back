<template>



    <el-row style="height: 100%; width: 100%">
      <el-col :span="12" style="height: 100%;">
#TODO显示图像q
        <iframe src="http://localhost:3000/viewer?StudyInstanceUIDs=*******.4.1.25403.************.3824.20170125095722.1" style="height: 100%; width: 100%"></iframe>
      </el-col>

      <el-col style="height: 100%" :span="12">
   
        <div style="height: 60%; width: 100%">  
          <Patient ref="patient" @dblClickRow="selectPatient" />
        </div>
        <div style="height: 40%; width: 100%">
          <el-tabs v-model="currentTab" class="tab-ver tab-ver-cp tab-ver-flex hei100" @tab-click="handleTabClick">
            <el-tab-pane label="所有图像" :name="tabsName.Study" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <Study ref="allStudy" @dispatchAction="handleAction" style="height: 100%; width: 100%"></Study>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="未匹配图像" :name="tabsName.StudyNone" style="height: 100%; width: 100%">
              <div class="tab-body-p4 hei100" style="height: 100%; width: 100%">
                <StudyNone ref="noneStudy" @dispatchAction="handleAction" style="height: 100%; width: 100%"></StudyNone>
              </div>
            </el-tab-pane>
            
            
          </el-tabs>
        </div> 
      </el-col>
    </el-row>





</template>


<script>

import model from "@/assets/scripts/uis/exammanagement/workstation/Index";
export default model;
</script>
