<template>
  <div class="app-container">
    <div>
      <el-form :model="searchForm" ref="searchForm" size="small" :inline="true">
        <el-form-item label="关键字">
          <el-input
            v-model="searchForm.title"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="grid.data" row-key="id"
        stripe highlight-current-row>
        <el-table-column prop="title" label="标题" width="300" />
        <el-table-column prop="memo" label="召回原因" show-overflow-tooltip />
        <el-table-column label="操作" align="center" class-name="button-col">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            ></el-button>
            <el-button
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="grid.pager.total>0"
        :total="grid.pager.total"
        :page.sync="grid.pager.pageNum"
        :limit.sync="grid.pager.pageSize"
        @pagination="getList"
      />
    </div>
    
    <el-dialog title="编辑" :visible.sync="editForm.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="标题" prop="title">
              <el-input v-model="editForm.title" />
            </el-form-item>
          </el-col>
        </el-row>

          <el-row>
          <el-col :span="24">
            <el-form-item label="召回原因" prop="memo">
              <el-input v-model="editForm.memo" type="textarea" rows="8" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";

import * as api from "@/assets/scripts/pacs/report/reportWithdrawMemo/api";

function  emptyForm() {
  return {
    visible: false,
    title: null,
    memo: null
  };
}

export default {
  extends: BaseGridModel,

  data() {
    return {
      searchForm: {
        title: null
      },

      editForm: emptyForm(),
      editFormRules: {
        "title": { required: true, message: '请输入标题' },
        "memo": { required: true, message: '召回原因' }
      }
    };
  },

  methods: {
    getList() {
      let fm = this.searchForm;
      api.find(fm).then(res => {
        this.grid.data = res.rows;
      });
    },

    handleAdd() {
      let fm = this.editForm = emptyForm();
      fm.visible = true;
    },

    handleUpdate(row) {
      this.editForm = emptyForm();;
      
      api.get(row.id).then(res => {
        const dat = res.data;
        this.editForm = {
          id: dat.id
          , title: dat.title
          , memo: dat.memo
          , visible: true
        };
      })
    },

    cancelEdit() {
      this.editForm.visible = false;
    },

    submitEditForm() {
      const fm = this.editForm;

      this.$refs.editForm.validate(v => {
        if(!v) {
          return;
        }

        let prom = fm.id? api.update(fm) : api.save(fm);
        prom.then(() => {
          this.getList();
          this.editForm = emptyForm();
        });
      });
    },

    doDelete(row) {
      const id = row.id;
      if(id) {
        return api.del(id);
      } else {
        return Promise.resolve(false);
      }
    }
  }
};
</script>
