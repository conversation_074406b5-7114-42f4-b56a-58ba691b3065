<template>
  <el-container ref="patientListWrap" class="nested-container" style="height: 100%">
    <el-aside
      class="inner-container-aside"
      :class="{ 'inner-container-aside-collapse': !searchFormVisible }"
      width="200px"
    >
      <el-form
        label-position="left"
        label-width="5em"
        style="height: 100%"
        class="tight-form exam-search-form"
        @keyup.enter.native="search"
      >
        <el-card
          class="nested-card hei100 card-pane"
          :body-style="{
            overflow: 'hidden',
            display: 'flex',
            'flex-direction': 'column',
          }"
        >
          <div slot="header">
            <span class="nested-card-header-title">过滤器</span>
            <div class="nested-card-tools">
              <el-checkbox v-model="rememberFilters" @change="handleRememberFiltersChange"
                >记住选择</el-checkbox
              >
            </div>
          </div>

          <div class="flex-item-fill" style="overflow: auto">
            <el-scrollbar class="scrollpane scrollpane-h"
              ><!--  -->
              <div>
                <el-form-item
                  label="登记日期"
                  class="el-form-item-date-label"
                ></el-form-item>
                <el-form-item
                  label-width="0"
                  class="el-form-item-alcenter el-form-item-date-input"
                >
                  <el-date-picker
                    v-model="searchForm.createTimeGe"
                    type="date"
                    placeholder="开始日期"
                    class="el-date-editor--noprefix"
                    style="width: 45%"
                    :clearable="true"
                  >
                  </el-date-picker>
                  -
                  <el-date-picker
                    v-model="searchForm.createTimeLt"
                    type="date"
                    placeholder="结束日期"
                    class="el-date-editor--noprefix"
                    style="width: 45%"
                    :clearable="true"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  label-width="时间选择"
                  class="form-item-thin el-form-item-srow"
                >
                </el-form-item>
                <el-form-item label="姓名">
                  <el-tooltip
                    content="请输入*或空格进行模糊查询, 如: 张*三, *某四"
                    placement="top-start"
                    :open-delay="1000"
                  >
                    <el-input
                      v-model="searchForm.patientInfo.name"
                      placeholder="请输入*进行模糊查询"
                      clearable
                    />
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="住院号">
                  <el-input v-model="searchForm.inpNo" clearable />
                </el-form-item>
                <el-form-item label="登记号">
                  <el-input
                    v-model="searchForm.patientInfo.registNo"
                    clearable
                    :readonly="searchForm.pinPatient"
                  ></el-input>
                </el-form-item>
                <el-form-item label="就诊类型">
                  <el-select
                    v-model="searchForm.inpTypeValues"
                    placeholder="全部"
                    multiple
                    collapse-tags
                    class="select-multi-sing"
                  >
                    <el-option
                      v-for="dict in ctrlData.dict.uis_inp_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="申请科室">
                  <Treeselect v-model="searchForm.reqDept.deptId" :options="deptTreeData" :show-count="true"
                    placeholder="选择" />
                </el-form-item> -->

                <el-form-item
                  label="检查类别"
                  class="el-form-item-srow el-form-item-modalities"
                >
                  <el-select v-model="searchForm.examModality.dictValue" clearable>
                    <el-option
                      v-for="dict in ctrlData.dict.uis_exam_modality"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="检查项目"
                  class="el-form-item-srow el-form-item-modalities"
                >
                  <el-select v-model="searchForm.examItemCodes" clearable>
                    <el-option
                      v-for="dict in ctrlData.dict.uis_exam_item"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="检查进度"
                  class="el-form-item-srow el-form-item-resultStatusValues"
                >
                  <el-select
                    v-model="searchForm.resultStatusValues"
                    clearable
                    multiple
                    collapse-tags
                    class="select-multi-sing"
                    placeholder="检查进度"
                  >
                    <el-option
                      v-for="dict in combo_resultStatus"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                    <el-option label="已删除" value="-2" />
                  </el-select>
                </el-form-item>

                <el-form-item label="检查号">
                  <el-input v-model="searchForm.examNo" clearable></el-input>
                </el-form-item>
                <el-form-item label="性别">
                  <el-select v-model="searchForm.patientInfo.gender.dictValue" clearable>
                    <el-option
                      v-for="dict in dict.type.uis_gender_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="就诊号">
                  <el-input v-model="searchForm.outpNo" clearable></el-input>
                </el-form-item>
                <el-form-item label="申请医生">
                  <el-input
                    v-model="searchForm.reqDoctor.nickName"
                    clearable
                    class="input-field-narr"
                    @clear="clearReqDoctor"
                  >
                    <el-button
                      slot="append"
                      size="mini"
                      icon="el-icon-user-solid"
                      @click="toPickUser({ multiple: false, target: 'reqDoctor' })"
                    ></el-button>
                  </el-input>
                </el-form-item>
                <el-form-item label="检查医生">
                  <el-input v-model="searchForm.examDoctor.nickName" clearable></el-input>
                </el-form-item>
                <el-form-item label="报告医生">
                  <el-input
                    v-model="searchForm.reportDoctor.nickName"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核医生">
                  <el-input
                    v-model="searchForm.auditDoctor.nickName"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="操作者">
                  <el-input v-model="searchForm.operDoctor.nickName" clearable></el-input>
                </el-form-item>
                <el-form-item label="检查所见">
                  <el-input
                    v-model="searchForm.examDesc"
                    clearable
                    placeholder="多关键字以逗号或空格间隔"
                  ></el-input>
                </el-form-item>
                <el-form-item label="检查诊断">
                  <el-input
                    v-model="searchForm.examDiagnosis"
                    clearable
                    placeholder="多关键字以逗号或空格间隔"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="阴阳性"
                  class="el-form-item-srow el-form-item-examResultProp"
                >
                  <el-select v-model="searchForm.examResultProp.dictValue" clearable>
                    <el-option
                      v-for="dict in dict.type.uis_exam_result_prop"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </div> </el-scrollbar
            ><!--  -->
          </div>
          <div style="padding: 4px 0; text-align: right">
            <el-button type="primary" icon="el-icon-search" @click="search"
              >查询</el-button
            >
          </div>
        </el-card>
      </el-form>
    </el-aside>

    <el-main>
      <div class="data-container flex-container-column">
        <div class="searchFormBar">
          <el-form class="exam-search-form exam-search-form-simp">
            <el-tooltip effect="dark" content="详细查询" placement="top">
              <svg-icon
                icon-class="list-border"
                class-name="search-form-toggler"
                @click="toggleSearchForm"
              />
            </el-tooltip>

            <el-select
              v-model="searchFormSimp.propName"
              style="width: 90px"
              @change="focusPropValue"
            >
              <el-option
                v-for="dict in searchFormSimp.combo_props"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-input
              ref="propValue"
              v-model="searchFormSimp.propValue"
              @keyup.enter.native="getList"
              @blur="checkValue"
              clearable
              style="width: 115px"
            />
            <el-select
              v-model="searchFormSimp.resultStatusValues"
              @change="searchFormChange"
              style="width: 100px"
              clearable
              multiple
              collapse-tags
              class="select-multi-sing"
              placeholder="检查进度"
            >
              <el-option
                v-for="dict in combo_resultStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-select
              v-model="createTimeValue"
              style="width: 90px"
              @change="createTimeValueSearchChange"
            >
              <el-option
                v-for="dict in createTime_props"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-date-picker
              style="width: 200px"
              v-if="createTimeValue == -1 ? true : false"
              @change="changeDatePicker"
              v-model="startDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>

            <el-select
              v-model="searchFormSimp.examItemCodes"
              @change="searchFormChange"
              style="width: 180px"
              clearable
              collapse-tags
              placeholder="检查项目"
            >
              <el-option
                v-for="dict in ctrlData.dict.uis_exam_item"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

            <!-- <el-select v-model="searchFormSimp.equipExamItemCode" v-show="isPendingReport()||isPendingAudit()" style="width: 105px;" collapse-tags class="select-multi-sing" placeholder="检查项目"
            @change="searchFormChange">
                <el-option
                  v-for="dict in ctrlData.dict.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select> -->
            <el-tooltip class="item" effect="dark" content="查询" placement="bottom">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="search('simp')"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                disabledUpload() ? '请仅选择一个检查项目后上传' : '点击批量上传文件'
              "
              placement="bottom"
            >
              <el-button
                v-hasPermi="['report:reportImage:upload']"
                v-show="isPendingReport()"
                :disabled="disabledUpload()"
                :loading="uploading"
                type="primary"
                icon="el-icon-upload2"
                @click="reportImageUploads"
              >
                批量上传影像
              </el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                disabledUpload() ? '请仅选择一个检查项目后上传' : '点击批量上传文件'
              "
              placement="bottom"
            >
              <el-button
                v-hasPermi="['report:reportPdf:upload']"
                v-show="isPendingReport()"
                :disabled="disabledUpload()"
                :loading="uploading"
                type="primary"
                icon="el-icon-upload2"
                @click="reportPdfUploads"
                >批量上传报告
              </el-button>
            </el-tooltip>
            <!-- <el-button v-hasPermi="['report:reportImage:upload','report:reportPdf:upload']" v-show="sheetType==2?true:false" :disabled="searchForm.equipExamItemCode=='NYST'" :loading="auditing" type="primary" @click="batchAudit">批量审核</el-button> -->
            <!-- <el-tooltip class="item" effect="dark" content="偏好配置" placement="bottom">
              <el-button type="primary" icon="el-icon-s-tools" @click="prepareSearchOpt"></el-button></el-tooltip> -->
          </el-form>
        </div>
        <div class="flex-item-fill scroll-auto" style="overflow: auto">
          <el-table
            ref="dataGrid"
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            stripe
            highlight-current-row
            height="100%"
            style="min-height: 200px"
            :cell-class-name="colStyle"
            @row-contextmenu="showAction"
            @current-change="handleCurrentChange"
            @row-dblclick="handleRowDblClick"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              v-if="isPendingAudit()"
              :reserve-selection="true"
              :row-key="(row) => row.id"
              highlight-current-row="true"
              :selectable="selectable"
            ></el-table-column>
            <el-table-column
              prop="patientInfo.name"
              label="患者姓名"
              width="80"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="patientInfo.gender.dictLabel"
              label="性别"
              width="50"
              :formatter="colFmt_dictData"
            />
            <!-- <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" /> -->
            <el-table-column
              prop="examAge"
              label="年龄"
              width="60"
              :formatter="colFmt_exam_age"
            />
            <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="80">
              <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                <!-- <i class="el-icon-error state-icon-err"
                    v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue"
                    v-else></i> -->
                <span style="margin-left: 4px">{{
                  2 === scope.row.status
                    ? "已删除"
                    : !!scope.row.resultStatus
                    ? scope.row.resultStatus.dictLabel
                    : null
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileInfos.length"
              label="文件数"
              align="center"
              width="60"
            >
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.fileInfos &&
                    scope.row.fileInfos.length > 0 &&
                    reportable(scope.row)
                  "
                  class="clickable-link"
                  style="cursor: pointer; color: #409eff"
                  @click="goToOcrConfirm(scope.row)"
                >
                  {{ scope.row.fileInfos.length }}
                </span>
                <span v-else>{{
                  scope.row.fileInfos ? scope.row.fileInfos.length : 0
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="patientInfo.registNo"
              label="登记号"
              min-width="100"
              :formatter="colFmt_object"
            />
            <!-- <el-table-column prop="examParts.partsName" label="检查部位" width="100" :formatter="colFmt_object" show-overflow-tooltip  /> -->
            <!-- <el-table-column prop="examDiag" label="检查结论" width="120" show-overflow-tooltip /> -->
            <el-table-column prop="examItem.dictLabel" label="检查项目" width="130" />
            <el-table-column
              prop="imageNo"
              v-hasPermi="['report:reportImage:upload']"
              label="影像编号"
              width="140"
            />
            <el-table-column
              prop="reportUploadFilenameStr"
              :show-overflow-tooltip="true"
              v-hasPermi="['report:reportPdf:upload']"
              label="报告文件名"
              width="140"
            />
            <el-table-column
              prop="reportUploadFilenameStr"
              :show-overflow-tooltip="true"
              v-hasPermi="['report:reportImage:upload']"
              label="影像文件名"
              width="140"
            >
              <template slot-scope="scope">
                {{ getImageName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="reportNo"
              v-hasPermi="['report:reportPdf:upload']"
              label="报告编号"
              width="140"
            />
            <!-- <el-table-column prop="reportUrlJpgName" v-hasPermi="['report:reportImage:upload']" label="影像文件名" width="140" /> -->
            <el-table-column prop="inpNo" label="住院号" width="120" />
            <el-table-column prop="examNo" label="检查号" width="120" />
            <el-table-column prop="imageNum" label="图像数量" width="80" />
            <el-table-column
              prop="examModality.dictLabel"
              label="检查类型"
              width="100"
              :formatter="colFmt_dictData"
            />
            <!-- <el-table-column prop="callInfo.callNo" label="排队号" width="70" :formatter="colFmt_object" /> -->
            <el-table-column prop="unexecuteOrdId" label="医嘱状态" width="100">
              <template slot-scope="scope" v-if="!!scope.row.ordId">
                <span style="margin-left: 4px">{{
                  undefined == scope.row.unexecuteOrdId || "" == scope.row.unexecuteOrdId
                    ? "已执行"
                    : "未执行"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="unexecuteOrdId" label="发送状态" width="100">
              <template slot-scope="scope" v-if="!!scope.row.statusOfSendReport">
                <span style="margin-left: 4px">{{
                  1 == scope.row.statusOfSendReport || 3 == scope.row.statusOfSendReport
                    ? "成功"
                    : "失败"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="creator.nickName" label="登记人员" min-width="160" />
            <el-table-column prop="createTime" label="登记时间" min-width="160" />
            <el-table-column prop="examTime" label="检查时间" min-width="160" />
            <el-table-column prop="examSerialNo" label="检查流水号" width="140" />
            <el-table-column
              prop="equipRoom.roomName"
              label="检查房间"
              width="100"
              :formatter="colFmt_equipRoom"
            />

            <el-table-column
              label="操作"
              v-if="isPendingReport() || isPendingAudit()"
              fixed="right"
              align="center"
              class-name="small-padding"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('uploadPdf', scope.row) && isPendingReport()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('uploadPdf', scope.row)"
                  >上传报告
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('reloadPdf', scope.row) && isPendingAudit()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('reloadPdf', scope.row)"
                  >已上传(重传)
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('uploadJpg', scope.row) && isPendingReport()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('uploadJpg', scope.row)"
                  >上传影像
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('reloadJpg', scope.row) && isPendingAudit()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('reloadJpg', scope.row)"
                  >已上传(重传)
                </el-button>
                <!-- <el-button size="mini" type="text" icon="el-icon-plus" @click="reportOperate('upload', scope.row)">上传报告</el-button> -->
                <!-- <el-button size="mini" type="text" :disabled="getReportDataSourceEdit('view', scope.row)" v-show="!limitUpload" icon="el-icon-view"
                  @click="reportOperate('view', scope.row)">查看报告</el-button> -->
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              v-else-if="isRegist() || isNormal()"
              fixed="right"
              align="center"
              class-name="small-padding"
              width="120"
            >
              <template slot="header" slot-scope="scope">
                <span>操作</span>
                <!-- <el-button type="info" size="mini" @click="showHelp">
                  <i class="el-icon-question"></i>
                </el-button> -->
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-question"
                  title="帮助文档"
                  style="margin-right: 5px"
                  @click="help()"
                ></el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-setting"
                  v-show="!!scope.row.ordId && !!scope.row.unexecuteOrdId ? true : false"
                  @click="ordExecute(scope.row)"
                  >执行
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 右键菜单 -->
          <Contextmenu ref="tableContextmenu" :items="actions" @select="handleAction" />
          <!-- 操作密码验证 -->
          <OperationAuth ref="operAuth" @success="handleDelete" />
          <!-- 查询偏好设置 -->
          <PatientListSearchOptions
            ref="searchOpt"
            cacheKey="exam::patientSheetSearchOptions"
            @change="getList"
          />

          <!-- 上传检查影像 -->
          <ReportUploader ref="reportUploader" />
          <ReportUploaderBatch
            ref="reportUploaderBatch"
            @reportUploadStateUpdate="reportUploadStateUpdate"
          />

          <!-- 报告预览 -->
          <!-- <ReportViewer ref="reportViewer" :viewLoadReport="false"/> -->

          <!-- 扫码 -->
          <LinksignPopup :qr="qr" />

          <!-- 选择医生 -->
          <UserPicker ref="userPicker" @pick="pickUser" />
        </div>

        <pagination
          small
          layout="total, prev, pager, next"
          :total="grid.pager.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="getList"
        />
      </div>
    </el-main>
  </el-container>
</template>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style
  scoped
  src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"
></style>
<style scoped>
.searchFormBar {
  margin-bottom: 4px;
}

.searchFormBar >>> .el-button {
  margin-left: 4px;
  padding: 10px;
}
</style>

<script>
import { cloneDeep, mergeWith } from "lodash";

import auth from "@/plugins/auth";

import {
  mergeWithNotNull,
  undefinedOrNull,
  fmt_exam_age,
  currDate,
} from "@/utils/common";
//接口
import * as eiapi from "@/assets/scripts/uis/exammanagement/examinfo/api.js";
//
import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";
//编辑
import {
  EditModel,
  UndoModel,
  TransModel,
} from "@/assets/scripts/gis/exammanagement/examinfo/mixins";
//右键菜单
import Contextmenu from "@/components/Contextmenu";
//操作验证
import OperationAuth from "@/views/system/common/OperationAuth";
//查询偏好配置
import PatientListSearchOptions from "@/views/pacs/report/comp/PatientListSearchOptions";
//
import { DataCtrlDict } from "@/assets/scripts/pacs/comcfg/examdatactrl/index";
import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

import ReportUploader from "@/views/uis/report/comp/ReportUploader";
import ReportUploaderBatch from "@/views/uis/report/comp/ReportUploaderBatch";

import ReportViewer from "@/views/uis/report/comp/ReportViewer";

import {
  StatusDict,
  matchAny as matchAnyStatus,
} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";
import { ResultStatusModel as ResultStatus } from "@/assets/scripts/gis/exammanagement/examinfo/mixins";

import { getUserProfile } from "@/api/system/user";

import { SearchOptions } from "@/assets/scripts/pacs/report/mixins";

//扫码认证
import { default as LinksignPopup } from "@/views/pacs/linksign/Popup";

import QRSignRel from "@/assets/scripts/pacs/linksign/QRSignRel";

//操作权限
import {
  default as PermiCheck,
  PERMI_AUDIT,
  PERMI_SECOND_AUDIT,
  PERMI_THIRD_AUDIT,
} from "@/directive/permission/mixins";

//科室信息
import { treeselect as deptTreeselect } from "@/api/system/dept";

import { mapGetters } from "vuex";

import { PatiemtOptions } from "@/assets/scripts/pacs/exammanagement/patient/mixins";

import DateUtil from "@/utils/DateUtil";

import { PATIENTSHEETTYPE } from "@/assets/scripts/pacs/Const";
import UserPicker from "@/views/system/user/comp/UserPicker.vue";

//表单标识
const formSimp = "simp";

const AgeUnits = { Y: "岁", M: "月", D: "天", H: "小时", m: "分" };

export default {
  extends: BaseGridModel,
  mixins: [
    EditModel,
    UndoModel,
    TransModel,
    SearchOptions,
    ExamDataScope,
    ResultStatus,
    PermiCheck,
    PatiemtOptions,
  ],
  dicts: [
    "uis_exam_result_status",
    "uis_exam_item",
    "uis_inp_type",
    "uis_exam_modality",
    "uis_gender_type",
    ,
    "uis_exam_result_prop",
  ],

  components: {
    UserPicker,
    Contextmenu,
    OperationAuth,
    PatientListSearchOptions,
    ReportUploader,
    ReportViewer,
    ReportUploaderBatch,
    LinksignPopup,
  },

  props: {
    //定时刷新秒数
    refresh: { type: Number },
    //表格行右键菜单
    actions: { type: Array, default: () => [] },
    //固定查询条件
    filter: { type: Object }, //e.g. {status:0,datesCreated:1}
    //限制
    restrict: { type: Object },
    //普通列表：0 上传列表：1 审核列表：2
    sheetType: { type: String, default: PATIENTSHEETTYPE.overdue },
  },

  data() {
    return {
      // Add this to your existing data properties
      rememberFilters: false,
      savedFilters: null,
      // ... other data properties
      //当前查询表单
      lastSearchForm: formSimp,
      deptTreeData: [],
      searchFormVisible: false,

      user: {},

      createTime_props: [
        { label: "今天", value: 0 },
        { label: "昨天", value: 1 },
        { label: "三天", value: 3 },
        { label: "七天", value: 7 },
        { label: "自定义时间", value: -1 },
      ],
      createTimeValue: 7,

      searchForm: {
        combo_props: [
          { value: "patientInfo.name", label: "姓名" },
          { value: "examNo", label: "检查号" },
          { value: "patientInfo.registNo", label: "登记号" },
          { value: "inpNo", label: "住院号" },
          { value: "reportNo", label: "报告编号" },
          { value: "imageNo", label: "影像编号" },
        ],

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: { dictValue: null },
        },

        inpTypeValues: [],
        id: null,
        examNo: null,
        callInfo: {
          callNo: null,
        },

        inpType: { dictCode: null },
        reqDept: { deptId: null },
        examModality: { dictCode: null },
        examDoctor: { nickName: null },
        reportDoctor: { nickName: null },
        auditDoctor: { nickName: null },
        operDoctor: { nickName: null },
        reqDoctor: {
          nickName: "",
          userName: "",
        },

        propName: "patientInfo.registNo",
        propValue: null,
        resultStatusValues: null,
        examItemCodes: null,
        equipExamItemCode: null,
        examResultProp: { dictCode: null },
        createTimeGe: null,
        createTimeLt: null,
        pageSize: 17,
      },

      searchFormSimp: {
        combo_props: [
          { value: "patientInfo.name", label: "姓名" },
          { value: "examNo", label: "检查号" },
          { value: "patientInfo.registNo", label: "登记号" },
          { value: "inpNo", label: "住院号" },
          { value: "reportNo", label: "报告编号" },
          { value: "imageNo", label: "影像编号" },
        ],

        patientInfo: {
          name: null,
          medicalRecordNo: null,
          inpNo: null,
          gender: { dictValue: null },
        },

        inpTypeValues: [],
        id: null,
        examNo: null,
        callInfo: {
          callNo: null,
        },

        inpType: { dictCode: null },
        reqDept: { deptId: null },
        examModality: { dictCode: null },
        examDoctor: { nickName: null },
        reportDoctor: { nickName: null },
        auditDoctor: { nickName: null },
        operDoctor: { nickName: null },

        propName: "patientInfo.registNo",
        propValue: null,
        resultStatusValues: null,
        examItemCodes: null,
        equipExamItemCode: null,
        examResultProp: { dictCode: null },
        pageSize: 17,
      },

      currentSelection: null,
      qr: null,
      //定时刷新列表
      timer_getList: null,
      uploading: false,
      auditing: false,
      params: null,
      cacheKey: "PatiemtOptions",
      examItemConfig: null,
      showCustomDatePickers: false,
      startDate: "",
      endDate: "",
      patientMainForm: null,
    };
  },

  methods: {
    reportable(row) {
      return matchAnyStatus(row, StatusDict.regist, StatusDict.exam);
    },
    goToOcrConfirm(row) {
      this.$router.push({
        name: "OcrConfirm",
        params: { examInfo: row },
      });
    },
    disabledUpload() {
      let examItemCodes = this.searchFormSimp.examItemCodes;
      if (Array.isArray(examItemCodes) && examItemCodes.length == 1) {
        return false;
      }
      return true;
    },

    //查询
    search(opt) {
      if (!!opt && opt instanceof Event) {
        let el = 13 === opt.which ? opt.target || opt.currentTarget : null;
        if (!!el && "INPUT" !== el.nodeName) {
          return;
        }
      }

      this.lastSearchForm = formSimp === opt ? opt : null;
      this.getList();
    },

    toggleSearchForm() {
      this.searchFormVisible = !this.searchFormVisible;
    },

    //勾选触发
    handleSelectionChange(rows) {
      this.currentSelection = rows;
    },

    /**
     * 用户选择框
     * @param {Object} opts - Options for user picker
     */
    toPickUser(opts) {
      let tar = opts.target || "reqDoctor";
      let fm = this.searchForm;

      let names, codes;
      if (tar in fm) {
        names = fm[tar].nickName;
        codes = fm[tar].userName;
      } else {
        names = fm[`${tar}Name`];
        codes = fm[`${tar}Code`];
      }

      let selectedUsers = [];
      if (!!codes) {
        codes = codes.split(",");
        names = names.split(",");
        codes.forEach((e, i) => {
          selectedUsers.push({ userName: e, nickName: names[i] });
        });
      }
      opts.selectedUsers = selectedUsers;

      this.$refs.userPicker.showPicker(opts);
    },

    /**
     * Handle user selection from picker
     * @param {string} tar - Target field
     * @param {Object|Array} users - Selected user(s)
     */
    pickUser(tar, users) {
      let fm = this.searchForm;
      let names = [],
        codes = [];

      if (!!users.length) {
        users.forEach((e) => {
          names.push(e.nickName);
          codes.push(e.userName);
        });
      } else {
        names.push(users.nickName);
        codes.push(users.userName);
      }

      names = names.join(",");
      codes = codes.join(",");

      if (tar in fm) {
        fm[tar].nickName = names;
        fm[tar].userName = codes;
      } else {
        fm[`${tar}Name`] = names;
        fm[`${tar}Code`] = codes;
      }

      // Trigger search after selection
      this.searchFormChange();
    },

    batchAudit() {
      let vm = this;
      if (undefined == this.currentSelection || 0 == this.currentSelection.length) {
        this.$modal.msgSuccess("请选择检查！");
        return;
      }
      let form = new FormData();
      this.currentSelection.forEach((e) => form.append("examInfos", e.id));

      let fun;
      if (vm.hasPermi("report:reportImage:upload")) {
        //fun = eiapi.batchAudit;
        fun = eiapi.batchAudit;
      } else {
        //批量审核上传的pdf报告
        fun = eiapi.batchAuditPdf;
      }

      const cup = (data) => {
        this.auditing = true;
        fun(form)
          .then((res) => {
            this.auditing = false;
            if ("qrnoauth" === res.errC) {
              if (null == vm.qr) {
                vm.qr = new QRSignRel(() => {
                  this.qr.activated = false;
                  this.qr.openDig = false;
                  ///opt.firmed = true;
                  cup(data);
                });
              }
              this.qr.openDig = true;
              return;
            }

            vm.$modal.msgSuccess(res.msg);
            vm.$refs.dataGrid.clearSelection();
            vm.currentSelection = null;
            vm.getList();
          })
          .catch((err) => {
            this.$refs.dataGrid.clearSelection();
            this.currentSelection = null;
            this.auditing = false;
          });
      };
      cup(form);
    },

    selectable(row, index) {
      if (matchAnyStatus(row, StatusDict.report)) {
        return true;
      } else {
        return false;
      }
    },

    getUser() {
      let vm = this;
      getUserProfile().then((response) => {
        this.user = response.data;
        //this.getList();
      });
    },

    getImageName(row) {
      if (undefined != row.dicomStudies) return null;
      if (
        undefined != row.dicomStudies[0].seriesSet[0] &&
        undefined != row.dicomStudies[0].seriesSet[0].imagesSet[0]
      )
        return row.dicomStudies[0].seriesSet[0].imagesSet[0].fileName;

      return null;
    },

    getOverdueDaysAgoMidnight(overdueDay) {
      const date = new Date();
      date.setDate(date.getDate() - overdueDay);
      date.setHours(0, 0, 0, 0);
      return date;
    },

    /**
     * 搜索
     */
    getList: function (opts) {
      let vm = this;
      // clearTimeout(this.timer_getList);
      //
      //this.checkValue()
      this.handleCurrentChange(null);
      this.refreshTime(this.createTimeValue);
      if (!this.patientMainForm) {
        this.readMainForm();
      }

      let params;
      this.searchFormSimp.pageNum = this.searchForm.pageNum;
      this.searchFormSimp.pageSize = this.searchForm.pageSize;
      console.log(this.searchFormSimp);
      if (formSimp === this.lastSearchForm) {
        //简单查询
        //查询参数
        let sfm = this.searchFormSimp;
        params = {
          pageSize: sfm.pageSize,
          pageNum: sfm.pageNum,
          resultStatusValues: [],
          createTimeGe: sfm.createTimeGe,
          createTimeLt: sfm.createTimeLt,
        };
        //父组件指定参数
        if (this.filter) {
          mergeWith(params, this.filter, true, mergeWithNotNull);
        }
        //偏好查询设置, 覆盖父组件
        //let searchOpts = this.$refs.searchOpt.readOpts();
        //mergeWith(params, searchOpts);

        // if(undefined!=sfm.resultStatusValues&&sfm.resultStatusValues.length>0 )
        if (undefined != sfm.resultStatusValues && sfm.resultStatusValues.length > 0)
          params.resultStatusValues = cloneDeep(sfm.resultStatusValues);
        //检查进度限制
        const rst = this.restrict;
        if (
          (!params.resultStatusValues || params.resultStatusValues.length === 0) &&
          rst &&
          rst.resultStatusValues
        ) {
          params.resultStatusValues = rst.resultStatusValues;
        }

        // if(undefined!=this.dept) {
        //   console.log("this.dept",this.dept);
        //   params["examDept"] = {};
        //   params["examDept"]["deptId"] = this.dept.deptId;
        // }

        //检查项目
        params.examItemCodes = sfm.examItemCodes;

        //当天预约检查
        // params.appointExamDateGe = parseTime(currDate());
        // //
        // params.appointExamDateLt = params.appointExamDateGe;
        // if(0==params.resultStatusValues.length&&0==params.examItemCodes.length){
        //   params.appointWithCreated = true;
        // }
        params.appointWithCreated = true;

        //根据下拉
        let propName = sfm.propName,
          propValue = sfm.propValue;
        if (!!propName && !!propValue) {
          let propsName = propName.split(/\./),
            prop = params;
          for (let i = 0, len = propsName.length; i < len; i++) {
            let pnam = propsName[i];
            if (i + 1 < len) {
              prop[pnam] = {};
              prop = prop[pnam];
              continue;
            }
            prop[pnam] = propValue;
          }
          //查询不限定时间
          params.appointWithCreated = null;
          delete params.appointWithCreated;
        }

        // var date = new Date();
        // date.setDate(date.getDate() - this.createTimeValue);
        // date = new Date(new Date(date.toLocaleDateString()).getTime());
        // params.createTimeGe = date;
        // params.createTimeLt = new Date();
      } else {
        //过滤器查询
        params = cloneDeep(this.searchForm);
        delete params["combo_props"];
        delete params["propName"];

        // if(params.examItemCodes) {
        //   params.examItemCodes.forEach((c, i) => {
        //     params["examItemCodes[" + i + "]"] = c;
        //   });
        //   delete params["examItemCodes"];
        // }
      }

      //已删除状态
      let pos;
      if (
        !!params.resultStatusValues &&
        params.resultStatusValues.length > 0 &&
        -1 !== (pos = params.resultStatusValues.findIndex((e) => e == "-2"))
      ) {
        params.resultStatusValues.splice(pos, 1);
        //params.resultStatusAsStatus = "2";
        params.status = 2;
      } else {
        params.status = 0;
      }

      //点击按钮触发
      if (!this.timer_getList || (opts && opts instanceof MouseEvent)) {
        this.loading = true;
        //
        this.grid.data = [];
      }

      if (undefined == this.examModality || 0 == this.examModality.length) return;

      params["examModalitiesCodes"] = [];
      this.examModality.forEach((e) => {
        params["examModalitiesCodes"].push(e.dictValue);
      });

      this.params = params;

      let searChparams = cloneDeep(params);
      if (!searChparams.examItemCodes || 0 == searChparams.examItemCodes.length) {
        if (!!vm.loginUsrExamItem) {
          searChparams["examItemCodes"] = [];
          // vm.loginUsrExamItem.forEach((e) => {
          //   searChparams["examItemCodes"].push(e.dictValue);
          // });
        }
      }

      // 装配逾期条件
      // 审核状态不等于终审 & 检查项目设置了逾期天数
      let ec = searChparams["examItemCodes"];
      // 登记，检查，报告
      searChparams["resultStatusValues"] = [
        StatusDict.regist,
        StatusDict.exam,
        StatusDict.report,
      ];
      // let result = _.filter(searChparams["resultStatusValues"], n => {
      //   StatusDict
      // });
      searChparams["examModalitiesCodes"] = [];
      console.log("有检查项目", ec);
      if (ec && typeof ec == "string" && this.examItemConfig) {
        let config = this.examItemConfig[ec];
        console.log("只有一个检查项目", config);
        if (config) {
          //
          let od = config["reportOverdueDays"];
          if (od) {
            // 登记时间大于空
            // searChparams["createTimeGe"] = null;
            // 登记时间小于超期最后时间
            let odate = this.getOverdueDaysAgoMidnight(od);
            let oldOdate = searChparams["createTimeLt"];
            if (!oldOdate || odate < oldOdate) {
              searChparams["createTimeLt"] = odate;
            }
            // 登记时间小于今天减去 超时提醒时间
          } else {
            // 登记时间大于今天的
            searChparams["createTimeGe"] = this.getOverdueDaysAgoMidnight(-3);
            searChparams["createTimeLt"] = null;
          }
        }
      } else {
        searChparams["examItemCodes"] = "xxx";
      }
      console.log("获取预期列表", searChparams);
      eiapi
        .find(searChparams)
        .then((res) => {
          this.loading = false;
          this.grid.pager.total = res.total;
          this.grid.data = res.rows;
          this.triggerBind("updatePatientNum", {
            sheetType: this.sheetType,
            rowsNum: this.grid.data.length,
          });
          for (var i = 0; i < this.grid.data.length; i++) {
            // var pdfPath = this.grid.data[i].reportUrlPdf
            // if(null!=pdfPath){
            //   this.grid.data[i]["reportUrlPdfName"] = pdfPath.substring(pdfPath.lastIndexOf('/')+1);
            // }
            let fileNames = "";
            let fileNameJs = this.grid.data[i]["reportUploadFilename"];
            var obj = JSON.parse(fileNameJs);

            // 遍历对象的属性
            for (var key in obj) {
              if (obj.hasOwnProperty(key)) {
                fileNames += key + ",";
              }
            }
            this.grid.data[i]["reportUploadFilenameStr"] = fileNames;

            var exam = this.grid.data[i];
            var imageNum = 0;
            if (undefined != exam.dicomStudies) {
              exam.dicomStudies.forEach((stu) => {
                if (undefined != stu.seriesSet) {
                  stu.seriesSet.forEach((e) => (imageNum += e.imageNumber));
                }
              });
            }

            exam["imageNum"] = imageNum;
            // var jpgPath = this.grid.data[i].reportUrlJpg
            // if(null!=jpgPath){
            //   this.grid.data[i]["reportUrlJpgName"] = jpgPath.substring(jpgPath.lastIndexOf('/')+1);
            // }
          }

          this.delayGetList();
        })
        .catch(this.delayGetList);
    },

    ordExecute(examInfo) {
      eiapi
        .ordExecute(examInfo)
        .then((res) => {
          this.getList();
          this.$modal.msgSuccess("执行成功");
        })
        .catch();
    },

    //读取部门树信息
    buildDeptTree() {
      deptTreeselect().then((res) => {
        this.deptTreeData = res.data;
      });
    },

    //轮询
    delayGetList() {
      if (this.refresh) {
        // this.timer_getList = setTimeout(this.getList, this.refresh * 1000);
      }
    },

    getExamItemConfig() {
      if (!this.examItemConfig) {
        this.$modal.msgSuccess("检查项目未获取成功，请重试!");
        return;
      }

      if (!this.searchFormSimp.examItemCodes) {
        this.$modal.msgSuccess("请选择检查项目!");
        return;
      }
      return this.examItemConfig[this.searchFormSimp.examItemCodes[0]];
    },

    reportOperate(opt, row) {
      let examItemConfig = this.getExamItemConfig();
      if (undefined == examItemConfig) {
        this.$modal.msgSuccess("请配置科室配置!");
        return;
      }
      switch (opt) {
        case "uploadPdf":
          this.$refs.reportUploader.prepare(row, ".pdf", examItemConfig);
          break;
        case "reloadPdf":
          this.$confirm("确认重新上传报告？").then((_) => {
            this.$refs.reportUploader.prepare(row, ".pdf", examItemConfig);
          });
          break;
        case "uploadJpg":
          this.$refs.reportUploader.prepare(row, ".jpg", examItemConfig);
          break;
        case "reloadJpg":
          this.$confirm("确认重新上传报告？").then((_) => {
            this.$refs.reportUploader.prepare(row, ".jpg", examItemConfig);
          });
          break;
        // case 'view':
        //   this.$refs.reportViewer.view(row);
        //   break;
        default:
          break;
      }
    },
    help() {
      let url;
      if (
        process.env.VUE_APP_USR_HELP_DOC &&
        process.env.VUE_APP_USR_HELP_DOC.startsWith("http")
      ) {
        url = process.env.VUE_APP_USR_HELP_DOC;
      } else {
        url =
          window.location.protocol +
          "//" +
          window.location.hostname +
          process.env.VUE_APP_USR_HELP_DOC;
      }
      //  window.open(url, '_blank');
      this.$showPdf(url, "帮助文档");
    },

    getReportDataSourceEdit(opt, row) {
      let resultStatus, resultStatusCode;
      switch (opt) {
        case "uploadPdf":
          (resultStatus = row.resultStatus),
            (resultStatusCode = resultStatus ? resultStatus.dictValue : null);
          if (
            auth.hasPermi("report:reportPdf:upload") &&
            ((!undefinedOrNull(resultStatusCode) &&
              StatusDict.regist === resultStatusCode) ||
              StatusDict.exam === resultStatusCode)
          ) {
            return true;
          }
          return false;
        case "reloadPdf":
          (resultStatus = row.resultStatus),
            (resultStatusCode = resultStatus ? resultStatus.dictValue : null);
          if (
            auth.hasPermi("report:reportPdf:upload") &&
            !undefinedOrNull(resultStatusCode) &&
            StatusDict.report == resultStatusCode
          ) {
            return true;
          }
          return false;
        case "uploadJpg":
          (resultStatus = row.resultStatus),
            (resultStatusCode = resultStatus ? resultStatus.dictValue : null);
          if (
            auth.hasPermi("report:reportImage:upload") &&
            ((!undefinedOrNull(resultStatusCode) &&
              StatusDict.regist === resultStatusCode) ||
              StatusDict.exam === resultStatusCode)
          ) {
            return true;
          }
          return false;
        case "reloadJpg":
          (resultStatus = row.resultStatus),
            (resultStatusCode = resultStatus ? resultStatus.dictValue : null);
          if (
            auth.hasPermi("report:reportImage:upload") &&
            !undefinedOrNull(resultStatusCode) &&
            StatusDict.report == resultStatusCode
          ) {
            return true;
          }
          return false;
        //报告
        //return row.reportDataSource==0?false:true;
        case "view":
          (resultStatus = row.resultStatus),
            (resultStatusCode = resultStatus ? resultStatus.dictValue : null);
          if (
            (!undefinedOrNull(resultStatusCode) &&
              StatusDict.report === resultStatusCode) ||
            StatusDict.audit === resultStatusCode ||
            StatusDict.reaudit === resultStatusCode ||
            StatusDict.print === resultStatusCode
          ) {
            return false;
          }
          return true;
        //return row.reportDataSource==2?false:true;
        default:
          break;
      }
    },

    //表格行右键菜单
    showAction(row, col, evt) {
      evt.preventDefault();
      evt.stopPropagation();

      this.$refs["tableContextmenu"].show(evt, row);
    },
    handleAction(item, row) {
      this.triggerBind("dispatchAction", item.cmd, row);
    },
    //单击
    handleCurrentChange(row, orow) {
      this.triggerBind("selectRow", row);
    },
    //双击
    handleRowDblClick(row) {
      this.triggerBind("dblClickRow", row);
    },
    //指定编辑操作或默认编辑操作
    handleEdit(row) {
      if (1 !== this.triggerBind("editRow", row)) {
        //mixins
        this.handleUpdate(row);
      }
    },
    //执行删除
    doDelete(row) {
      return eiapi.del(row.id);
    },
    //删除
    undoDelete(row) {
      this.handleUndoDelete(row).then((res) => {
        if (res && 200 === res.code) {
          this.getList();
        }
      });
    },
    //延迟检查
    handlePostpone(row) {
      let props = { examAtPm: 1 };
      this.handleTrans(row, props).then((res) => this.getList());
    },

    focusPropValue() {
      try {
        this.$nextTick(() => this.$refs.propValue.focus());
      } catch (err) {
        console.error(err);
      }
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age
        ? pat.age + (pat.ageUnit ? pat.ageUnit.dictLabel : "")
        : pat
        ? pat.age
        : "";
    },

    //年龄列
    // colFmt_exam_age(row) {
    //   const pat = row.patientInfo;
    //   return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    // },

    //
    //年龄问题，以检查日期和出生日期计算
    colFmt_exam_age(row, column, cellValue, index) {
      return fmt_exam_age(row, column, cellValue, index);
    },

    //检查机房，如已呼叫取呼叫机房，否则取登记时分配机房
    colFmt_equipRoom(row) {
      //叫号房间
      if (
        !!row &&
        !!row.callInfo &&
        !!row.callInfo.callRoom &&
        !!row.callInfo.callRoom.roomName
      ) {
        return row.callInfo.callRoom.roomName;
      }

      return !!row && !!row.equipRoom ? row.equipRoom.roomName : null;
    },
    //删除验证
    verifyForDelete(row) {
      this.$refs.operAuth.prepare(row);
    },
    //执行删除
    handleDelete(row) {
      this.doDelete(row).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    //打开查询设置
    prepareSearchOpt() {
      this.$refs.searchOpt.open();
    },
    //登记号补0
    checkValue() {
      let fm = this.searchFormSimp;
      if (fm.propName == "patientInfo.registNo") {
        if (!!fm.propValue && fm.propValue.length > 0 && fm.propValue.length < 10) {
          fm.propValue = fm.propValue.padStart(10, "0");
        }
      }
    },
    //
    colStyle({ row, column, rowIndex, columnIndex }) {
      if ("unexecuteOrdId" === column.property) {
        let clz = ["table-cell-unexecuteOrdId-status"];
        if (!undefinedOrNull(row.ordId) && !undefinedOrNull(row.unexecuteOrdId)) {
          clz.push("table-cell-unexecuteOrdId-status-" + 1);
        }
        return clz.join(" ");
      }

      if ("resultStatus.dictLabel" !== column.property) {
        return "";
      }
      //
      let clz = ["table-cell-result-status"];
      let sta = row.resultStatus,
        stav = sta ? sta.dictValue : null;
      if (!undefinedOrNull(stav)) {
        clz.push("table-cell-result-status-" + stav);
      }

      return clz.join(" ");
    },

    reportImageUploads() {
      let vm = this;
      let reportExamItemName = null;
      if (undefined != vm.ctrlData.dict.uis_exam_item) {
        vm.ctrlData.dict.uis_exam_item.forEach((e) => {
          if (
            !!vm.searchFormSimp.examItemCodes &&
            e.value == vm.searchFormSimp.examItemCodes[0]
          )
            reportExamItemName = e.label;
        });
      }
      this.$refs.reportUploaderBatch.prepare(
        this.params,
        ".jpg",
        this.getExamItemConfig(),
        reportExamItemName
      );
    },

    reportPdfUploads() {
      let vm = this;
      let reportExamItemName = null;
      if (undefined != vm.ctrlData.dict.uis_exam_item) {
        vm.ctrlData.dict.uis_exam_item.forEach((e) => {
          if (
            !!vm.searchFormSimp.examItemCodes &&
            e.value == vm.searchFormSimp.examItemCodes[0]
          )
            reportExamItemName = e.label;
        });
      }
      this.$refs.reportUploaderBatch.prepare(
        this.params,
        ".pdf",
        this.getExamItemConfig(),
        reportExamItemName
      );
    },

    reportUploadStateUpdate(state) {
      this.uploading = state;
    },

    refreshTime(value) {
      if (value == -1) {
        this.searchFormSimp.createTimeGe = null;
        this.searchFormSimp.createTimeLt = null;
      } else {
        const currentDate = new Date();
        const pastDate = new Date();
        pastDate.setDate(currentDate.getDate() - this.createTimeValue);
        const startOfDay = new Date(pastDate.toLocaleDateString()).getTime();

        this.searchFormSimp.createTimeGe = new Date(startOfDay);
        this.searchFormSimp.createTimeLt =
          value === 1 ? new Date(startOfDay) : currentDate;
      }
    },

    createTimeValueSearchChange(value) {
      // this.showCustomDatePickers = value === -1;

      // if (!this.showCustomDatePickers) {
      // } else {
      //   this.searchFormSimp.createTimeGe = null;
      //   this.searchFormSimp.createTimeLt = null;
      //   this.getList();
      // }
      this.refreshTime(value);
      this.getList();
      this.save();
    },

    searchFormChange(value) {
      this.patientMainForm.examItemCodes = this.searchFormSimp.examItemCodes;
      this.patientMainForm.createTimeValue = this.createTimeValue;
      this.patientMainForm.resultStatusValues = this.searchFormSimp.resultStatusValues;
      this.save();
      // if (-1==value) {
      //   this.showCustomDatePickers = true;
      // } else {
      //   this.showCustomDatePickers = false;

      //   var date = new Date();
      //   date.setDate(date.getDate() - this.createTimeValue);
      //   date = new Date(new Date(date.toLocaleDateString()).getTime());
      //   this.searchForm.createTimeGe = date;
      //   this.searchForm.createTimeLt = new Date();

      //   if(1==value) this.searchForm.createTimeLt = date;
      //   this.getList();
      // }

      // if (!this.showCustomDatePickers) {
      //   this.refreshTime(value);
      //   this.getList();
      // } else {
      //   this.searchFormSimp.createTimeGe = null;
      //   this.searchFormSimp.createTimeLt = null;
      // }
      this.getList();
    },

    changeDatePicker(value) {
      console.log("changeDatePicker:", value);
      this.searchFormSimp.createTimeGe = value[0];
      this.searchFormSimp.createTimeLt = value[1];
      this.getList();
    },

    isRegist() {
      return this.patientSheetType.regist == this.sheetType;
    },
    isPendingReport() {
      return this.patientSheetType.pendingReport == this.sheetType;
    },
    isPendingAudit() {
      return this.patientSheetType.pendingAudit == this.sheetType;
    },
    isNormal() {
      return this.patientSheetType.normal == this.sheetType;
    },

    readMainForm() {
      console.log("获取主搜索");
      this.mainForm = this.read();
      this.patientMainForm = this.mainForm;
      if (this.patientMainForm) {
        if (this.patientMainForm.examItemCodes) {
          this.searchFormSimp.examItemCodes = this.patientMainForm.examItemCodes;
        }

        if (this.patientMainForm.resultStatusValues) {
          this.searchFormSimp.resultStatusValues = this.patientMainForm.resultStatusValues;
        }

        if (this.patientMainForm.createTimeValue !== undefined) {
          this.createTimeValue = this.patientMainForm.createTimeValue;
          this.searchFormChange(this.createTimeValue);
        }
      }
    },

    //清空申请医生
    clearReqDoctor() {
      if (this.searchForm.reqDoctor) {
        this.searchForm.reqDoctor.userName = "";
        // nickName is already cleared by the input's clear button
      }
      // Trigger search after clearing
      this.searchFormChange();
    },

    /**
     * Handle remember filters checkbox change
     */
    handleRememberFiltersChange(value) {
      if (value) {
        // Save current filters
        this.saveFilters();
      } else {
        // Clear saved filters
        this.savedFilters = null;
        localStorage.removeItem("patientSheetFilters");
      }
    },

    /**
     * Save current filters
     */
    saveFilters() {
      // Create a copy of the current filters
      this.savedFilters = {
        searchForm: cloneDeep(this.searchForm),
        createTimeValue: this.createTimeValue,
      };

      // Save to localStorage
      localStorage.setItem("patientSheetFilters", JSON.stringify(this.savedFilters));
    },

    /**
     * Load saved filters
     */
    loadFilters() {
      try {
        const savedFilters = localStorage.getItem("patientSheetFilters");
        if (savedFilters) {
          this.rememberFilters = true;
          this.savedFilters = JSON.parse(savedFilters);

          // Apply saved filters
          if (this.savedFilters.searchForm) {
            this.searchForm = cloneDeep(this.savedFilters.searchForm);
          }

          if (this.savedFilters.searchFormSimp) {
            this.searchFormSimp = cloneDeep(this.savedFilters.searchFormSimp);
          }

          if (this.savedFilters.createTimeValue !== undefined) {
            this.createTimeValue = this.savedFilters.createTimeValue;
          }

          // 确保在下一个 tick 设置 rememberFilters，以便 Vue 能正确捕获变化
          this.$nextTick(() => {
            this.rememberFilters = true;
          });
        }
      } catch (e) {
        console.error("Failed to load saved filters", e);
      }
    },
  },

  mounted() {
    this.cacheKey = "OverdueReportList";
    this.lastSearchForm = this.rememberFilters ? null : formSimp;
    this.readMainForm();

    this.getList();

    this.getConfigKey("examIemConfigKey").then((response) => {
      if (response && response.msg) {
        this.examItemConfig = JSON.parse(response.msg);
      }
    });
  },

  watch: {
    searchForm: {
      deep: true,
      handler() {
        if (this.rememberFilters) {
          this.rememberFilters = false;
          this.handleRememberFiltersChange(false);
        }
      },
    },
    /**
     * 检查类型字典取值后执行
     */
    "dict.type.uis_exam_modality": {
      deep: true,
      handler(nv, ov) {
        let items = [];
        if (nv && nv.length) {
          const conv = (d) => {
            return { value: d.dictValue, label: "--" + d.dictLabel, raw: d };
          };
          //层级
          nv.forEach((e) => {
            if (e.raw.parent && e.raw.parent.dictCode) {
              return true;
            }
            items.push(e);
            //
            let d = e.raw;
            if (d.children && d.children.length) {
              d.children.forEach((c) => {
                items.push(conv(c));
              });
            }
            //
            nv.forEach((c) => {
              if (c.raw.parent && c.raw.parent.dictCode === d.dictCode) {
                items.push(conv(c.raw));
              }
            });
          });
        }
        //
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, items);
      },
    },

    // "ctrlData.dict.uis_exam_modality":{
    //   deep: true,
    //   handler(nv, ov) {
    //     if(!!nv) this.getList();
    //   }
    // },

    /**
     * 就诊类别字典取值后执行
     */
    "dict.type.uis_inp_type": {
      deep: true,
      handler(nv, ov) {
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      },
    },

    /**
     * 检查项目字典取值后执行
     */
    "dict.type.uis_exam_item": {
      deep: true,
      handler(nv, ov) {
        if (nv && Array.isArray(nv) && !this.isRegist()) {
          nv = nv.filter(
            (item) => !(item.raw && item.raw.extend && item.raw.extend.extendI1 === 1)
          );
        }
        console.log("nv", nv);
        this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
      },
    },

    examModality: {
      deep: true,
      handler(nv, ov) {
        if (!!nv) this.getList();
      },
    },
  },

  activated() {
    this.delayGetList();
    //this.mainForm = this.read()
  },

  deactivated() {
    clearTimeout(this.timer_getList);
  },

  beforeDestroy() {
    clearTimeout(this.timer_getList);
  },

  computed: {
    ...mapGetters(["examModality", "loginUsrExamItem"]),
    combo_resultStatus() {
      const dict = this.dict.type.uis_exam_result_status,
        rst = this.restrict;
      if (!rst || !rst.resultStatusValues || !rst.resultStatusValues.length) {
        return dict;
      }
      return dict.filter(
        (d) => -1 !== rst.resultStatusValues.findIndex((v) => v === d.value)
      );
    },
    patientSheetType() {
      return PATIENTSHEETTYPE;
    },
  },

  created() {
    let vm = this;
    this.getUser();

    // Load saved filters if available
    this.loadFilters();
  },
};
</script>
