<template>
  <div class="image-bubble" v-show="opened" :style="{left: dialogLeft}">
  <!--<el-dialog title="检查影像" :visible.sync="opened" :width="dialogWidth" close-on-click-modal>-->
    <div class="image-bubble-inner">
      <div class="cornerstone-element"></div>
    </div>
  <!--</el-dialog>-->
  </div>
</template>

<style scoped>
.image-bubble{
  position: absolute;
  left: 25%;
  bottom: 130px;
  width: 350px;
  height: 197px;
  padding: 10px;
  background-color: #F4F9FC;
  border-radius: 8px;
  overflow: hidden;
}
.image-bubble-inner{
  width: 330px;
  height: 185px;
}
.image-bubble-inner .cornerstone-element {
  width: 100%;
  height: 100%;
}
</style>

<script>
//cornerstone
//import "@/assets/scripts/cornerstone/cornerstone-setup";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {loadAndDisplay} from "@/assets/scripts/pacs/image/util";

import {imageDime} from "@/assets/scripts/gis/report/imagemonitor/ImageMonitor";

const model = {
  extends: BaseDialogModel,

  data() {
    return {
      image: null,
      dialogWidth: "800px",
      dialogLeft: "25%"
    }
  },

  methods: {
    view(img, evt) {
      this.open();

      this.image = img;
      this.$nextTick(this.display);
      this.setLayout();

      //this.dialogLeft = evt.offsetLeft;
    },

    display() {
      const img = this.image;

      const ele = document.querySelector(".image-bubble").querySelector(".cornerstone-element");

      try { cornerstone.getEnabledElement(ele); } catch (err) { cornerstone.enable(ele); }

      let imageId = img.uri;
      //console.log(imageId);
      try {
        loadAndDisplay(ele, imageId);
      } catch (err) { console.error(err); }
    },

    setLayout() {
      const re = document.body;
      const dime = imageDime(re.clientWidth * 0.9, re.clientHeight - 60);
      this.dialogWidth = (dime.width + 18) + "px";
    }
  }
};
export default model;
</script>
