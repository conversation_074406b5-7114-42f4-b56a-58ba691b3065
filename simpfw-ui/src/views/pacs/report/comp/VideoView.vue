<template>
  <el-dialog ref="videoViewDialog" title="检查影像" :visible.sync="opened" width="800px" class="video-view-dialog">
    <div class="image-view-pane">
      <div class="cornerstone-element" oncontextmenu="return false">
        <video type="video/mp4" :src="videoSrc"
        autoplay muted controls @loadeddata="setLayout"></video>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.image-view-pane{
  width: 100%;
  height: 100%;
}
.image-view-pane .cornerstone-element {
  width: 100%;
  height: 100%;
}
.video-view-dialog >>> .el-dialog{
  margin-top: 8px !important;
}

</style>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
//
const DIALOGBODYVPAD = 2 * 16, DIALOGBODYHPAD = 56 + 2 * 4;

const model = {
  extends: BaseDialogModel,

  data() {
    return {
      videoSrc: null
    }
  },

  methods: {
    view(img) {
      this.open();

      this.videoSrc = img.uri;
    },

    setLayout(evt) {
      const vod = evt.srcElement || evt.target, re = document.body;
      let width = re.clientWidth * 0.9, height = re.clientHeight * 0.9;
      //
      width = Math.min(width, vod.clientWidth + DIALOGBODYVPAD);
      height = Math.min(height, vod.clientHeight + DIALOGBODYHPAD);
      //
      let vodWidth, vodHeight;
      vod.removeAttribute("width");
      vod.removeAttribute("height");
      if(width < vod.clientWidth + DIALOGBODYVPAD) {
        vod.width = width - DIALOGBODYVPAD;
      }
      if(height < vod.clientHeight + DIALOGBODYHPAD) {
        vod.height = height - DIALOGBODYHPAD;
        width = vod.clientWidth + DIALOGBODYVPAD;
      }
      //
      const dia = this.$refs.videoViewDialog.$el.querySelector(".el-dialog");
      dia.style.width = width + "px";
    }
  }
};
export default model;
</script>
