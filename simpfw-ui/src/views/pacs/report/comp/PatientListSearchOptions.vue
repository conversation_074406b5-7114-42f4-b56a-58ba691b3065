<template>
<el-dialog title="设置默认条件" :visible="opened" width="400px" append-to-body @close="close">
  
  <el-form :model="mainForm" ref="mainForm" label-width="80px">
    <el-form-item label="工作状态">
      <el-select v-model="mainForm.resultStatusValues" clearable multiple collapse-tags>
        <el-option
          v-for="dict in dict.type.uis_exam_result_status"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>                
    </el-form-item>
    <!-- <el-form-item label="检查医生">
      <el-select v-model="mainForm.examDoctorUserNames" clearable multiple collapse-tags
       filterable>
        <el-option
          v-for="dict in combo.users"
          :key="dict.userName"
          :label="dict.nickName"
          :value="dict.userName"
        />
      </el-select>                
    </el-form-item> -->
    <el-form-item label="报告医生">
      <el-select v-model="mainForm.reportDoctorUserNames" clearable multiple collapse-tags
       filterable>
        <el-option
          v-for="dict in combo.users"
          :key="dict.userName"
          :label="dict.nickName"
          :value="dict.userName"
        />
      </el-select>                
    </el-form-item>
    <el-form-item label="登记天数">
      <el-input
        v-model="mainForm.datesCreated"
        clearable
        style="width: 120px"/>
    </el-form-item>
    <el-form-item label="检查天数">
      <el-input
        v-model="mainForm.datesExamed"
        clearable
        style="width: 120px"/>
    </el-form-item>
  </el-form>
    
  <div slot="footer" class="dialog-footer">
    <div class="foot-tools">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveOpts">保存</el-button>
    </div>
  </div>

</el-dialog>
</template>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {SearchOptions} from "@/assets/scripts/pacs/report/mixins";

import { listUser } from "@/api/system/user";

export default {
  extends: BaseDialogModel,

  mixins: [SearchOptions],

  dicts: ["uis_exam_result_status"],
  
  props: {
    cacheKey: {type: String}
  },

  data() {
    return {
      //mainForm: {},//SearchOptionsDef,

      combo: {
        users: []
      }
    };
  },

  created() {
    this.mainForm = this.readOpts();

    this.getUsers();
  },
  
  methods: {
    //读取医生信息
    getUsers(query) {
      listUser({posts: [{postCode: "CSYS"}]}).then(res => {
        this.combo.users = res.rows;
      });
    },
    //取缓存
    readOpts() {
      return this.read();
    },
    //存
    saveOpts() {
      this.save();

      this.triggerBind("change");

      this.close();
    },
  }
};
</script>