<template>
  <el-dialog ref="imageViewDialog" title="检查影像" :visible.sync="opened" :width="dialogOffset.width+'px'" close-on-click-modal>
    <div class="image-view-pane">
      <div class="cornerstone-element" :style="imageStyle" oncontextmenu="return false"></div>
    </div>
  </el-dialog>
</template>

<style scoped>
.image-view-pane{
  width: 100%;
  height: 100%;
}
.image-view-pane .cornerstone-element {
  width: 100%;
  height: 100%;
}
</style>

<script>
//cornerstone
//import "@/assets/scripts/cornerstone/cornerstone-setup";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import {loadAndDisplay} from "@/assets/scripts/pacs/image/util";
//
const DIALOGBODYPAD = 2 * 16;

const model = {
  extends: BaseDialogModel,

  data() {
    return {
      image: null,

      dialogOffset: {
        width: 800,
        height: 600,
        left: "25%"
      },
      imageStyle: null
    }
  },

  methods: {
    view(img, evt) {
      this.open();

      this.image = img;
      this.$nextTick(this.display);
      this.setLayout();

      //this.dialogOffset.left = evt.offsetLeft;
    },

    getViewElement() {
      return this.$refs.imageViewDialog.$el.querySelector(".image-view-pane").querySelector(".cornerstone-element");
    },

    display() {
      const img = this.image;

      const ele = this.getViewElement();

      try { cornerstone.getEnabledElement(ele); } catch (err) { 
        cornerstone.enable(ele);
        //左键移动
        let PanTool = cornerstoneTools.PanTool;
        cornerstoneTools.addToolForElement(ele, PanTool);
        cornerstoneTools.setToolActiveForElement(ele, "Pan", {mouseButtonMask: 1});

        //滚轮缩放
        const ZoomMouseWheelTool = cornerstoneTools.ZoomMouseWheelTool;
        //
        cornerstoneTools.addToolForElement(ele, ZoomMouseWheelTool, {
          //
          configuration: {
            invert: false,
            preventZoomOutsideImage: false,
            minScale: .1,
            maxScale: 20.0,
          }
        });
        //
        cornerstoneTools.setToolActiveForElement(ele, 'ZoomMouseWheel', { mouseButtonMask: 1 })
      }
      let imageId = img.uri;
      try {
        const p = loadAndDisplay(ele, imageId);
        p.then(img => {
          const dialogOffset = this.dialogOffset, availWidth = dialogOffset.width - DIALOGBODYPAD, availHeight = dialogOffset.height;
          const imageWidth = img.width, imageHeight = img.height;
          //console.log("dialog=%o, imageWidth=%d, imageHeight=%d", dialogOffset, imageWidth, imageHeight);
          let viewportWidth = Math.min(availWidth, imageWidth)
            , viewportHeight = viewportWidth * imageHeight / imageWidth;
            if(viewportHeight > availHeight) {
              viewportHeight = availHeight;
              viewportWidth = viewportHeight * imageWidth / imageHeight;
            }
            if(availWidth != (viewportWidth + DIALOGBODYPAD)) {
              dialogOffset.width = viewportWidth + DIALOGBODYPAD;
            }
            this.imageStyle = {width: viewportWidth + "px", height: viewportHeight + "px"};
            this.$nextTick(() => cornerstone.resize(ele, true));
        });
      } catch (err) { console.error(err); }
    },

    setLayout() {
      const re = document.body, maxWidth = re.clientWidth * 0.9, maxHeight = (re.clientHeight - 60) * 0.9;
      /*//图像尺寸
      const dime = imageDime(maxWidth * 0.9, maxHeight);
      this.imageStyle = {width: dime.width + "px", height: dime.height + "px"};
      //图像宽度+对话框主体内补白
      this.dialogWidth = (dime.width + DIALOGBODYPAD) + "px";*/
      //this.dialogWidth = maxWidth + "px";
      this.dialogOffset.width = maxWidth;
      this.dialogOffset.height = maxHeight;
    }
  }
};
export default model;
</script>
