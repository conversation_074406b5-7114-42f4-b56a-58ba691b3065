<template xmlns="http://www.w3.org/1999/html">
  <div class="ocr-confirm-container">
    <el-container class="inner-container" v-loading="loading" element-loading-fullscreen>
      <!-- 添加进度消息显示 -->
      <div v-if="loading && processingMessage" class="processing-message">
        {{ processingMessage }}
      </div>
      <el-main height="30%">
        <div>
          <el-table ref="mapTable"
                    :data="getFileMatchData()"
                    :row-class-name="rowClassName"
                    row-key="id"
                    highlight-current-row
                    @row-dblclick="handleRowDblClick"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"
                             :reserve-selection="true"
                             :row-key="row => row.id"
                             highlight-current-row="true"
                             :selectable="checkSelectable"
            ></el-table-column>

            <el-table-column label="文件信息" align="center">
              <el-table-column prop="fileUrl" label="源文件名" align="center" min-width="150" show-overflow-tooltip/>
              <el-table-column prop="replacedInitFileName" label="初始文件名(被替换)" align="center" min-width="150"
                               show-overflow-tooltip/>

              <el-table-column prop="fileUrl" label="匹配类型" align="center" width="80">
                <template slot-scope="scope">
                  <template
                    v-if="(scope.row.fileType === FileType.UPLOAD||scope.row.fileType === FileType.HISTORYNOMATCH)&&null==scope.row.examInfo">
                    <span style="color: #be4949;">{{ '未匹配' }}</span>
                  </template>
                  <template
                    v-if="(scope.row.fileType === FileType.UPLOAD||scope.row.fileType === FileType.HISTORYNOMATCH)&&null!=scope.row.examInfo">
                    <span style="color: #E6A23C;">{{ '待确认' }}</span>
                  </template>
                  <template v-if="scope.row.fileType === FileType.HISTORY">
                    <span style="color: #7ebe49;">{{ '已匹配' }}</span>
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="uploadTime" label="上传时间" align="center" width="175"/>

              <el-table-column label="文件状态" align="center" width="100">
                <template slot-scope="scope">
                  <!--                  <template v-if="scope.row.status === 1">
                                      <span style="color: #be4949;">{{ '确认后移除' }}</span>
                                    </template>-->
                  <template v-if="isOverwriteFile(scope.row)">
                      <span
                        style="color: #be4949;"
                      >
                        {{ getOverwriteStatusText(scope.row) }}
                      </span>
                  </template>
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="检查信息" align="center">
              <el-table-column prop="examInfo.patientInfo.name" label="患者姓名" width="80">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo && scope.row.examInfo.patientInfo">
                    {{ scope.row.examInfo.patientInfo.name }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.patientInfo.gender.dictLabel" label="性别" width="50">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo && scope.row.examInfo.patientInfo">
                    {{ scope.row.examInfo.patientInfo.gender.dictLabel }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.examAge" label="年龄" width="80" :formatter="colFmt_exam_age"/>

              <el-table-column prop="examInfo.examItem.dictLabel" label="检查项目" width="100">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo">
                    {{ scope.row.examInfo.examItem.dictLabel }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.patientInfo.registNo" label="登记号" min-width="120">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo && scope.row.examInfo.patientInfo">
                    {{ scope.row.examInfo.patientInfo.registNo }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.inpNo" label="住院号" width="120">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo">
                    {{ scope.row.examInfo.inpNo }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.createTime" label="登记时间" min-width="160">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo">
                    {{ scope.row.examInfo.createTime }}
                  </template>
                </template>
              </el-table-column>

              <el-table-column prop="examInfo.examSerialNo" label="检查流水号" min-width="100">
                <template slot-scope="scope">
                  <template v-if="scope.row.examInfo">
                    {{ scope.row.examInfo.examSerialNo }}
                  </template>
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column label="匹配患者" fixed="right" align="center" width="500">
              <el-table-column label="匹配结果" align="center" fixed="right" width="400">
                <template slot-scope="scope"
                          v-if="scope.row.fileType === FileType.UPLOAD||scope.row.fileType === FileType.HISTORYNOMATCH">

                  <span v-if="!scope.row.ocrContents || scope.row.ocrContents.length === 0" :style="{color: '#be4949'}">
                    识别区域中没有内容，请检查OCR配置中截取的识别区域是否过小
                  </span>
                  <span v-else-if="!scope.row.fileLabel" :style="{color: '#be4949'}">
                    没有在OCR返回结果中找到对应的报告类型关键字，是否为新的报告类型？或检查OCR配置中截取的识别区域是否包含报告类型关键字
                  </span>
                  <span v-else-if="!scope.row.foundValMessage" :style="{color: '#be4949'}">
                    没有在OCR返回结果中识别到患者关键信息（姓名、登记号、住院号），请检查
                    <span :style="{color: '#be4949'}">
                      {{ scope.row.matchExamItemCode + '-' + scope.row.fileLabel }}
                    </span>
                    下的配置是否正确
                  </span>

                  <span v-if="scope.row.foundValMessage" :style="{color: 'blue'}">
                    {{
                      '在检查项目: ' + scope.row.matchExamItemCode + '(' + scope.row.matchExamItem + ')'
                      + ' 中匹配到' + scope.row.matchCount + '条检查，'
                      + '患者信息：' + scope.row.foundValMessage
                    }}
                    <br/>
                    <span v-if="scope.row.matchCount !== 1" :style="{color: '#be4949'}">
                       {{ '请在【登记信息】页面查看患者：' + scope.row.foundValMessage }}
                       <br/>
                      <span v-if="scope.row.matchCount === 0" :style="{color: '#be4949'}">
                      1. 是否已登记
                      2. 是否已被审核
                      3. 该患者的检查项目是否正确
                      </span>
                      <span v-if="scope.row.matchCount > 1" :style="{color: '#be4949'}">
                      1. 是否被登记多次
                      </span>
                    </span>
                  </span>
                  <!-- 当有提示信息时显示图标和tooltip -->
                  <el-tooltip
                    placement="top-start"
                    :content="getTooltipContent(scope.row)"
                    popper-class="match-result-tooltip">
                    <div class="detail-result-box">
                      详细结果
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" align="center" class-name="small-padding" width="100">
                <template slot-scope="scope"
                          v-if="scope.row.fileType === FileType.UPLOAD||scope.row.fileType === FileType.HISTORYNOMATCH">
                  <el-button
                    size="small"
                    type="primary"
                    plain
                    @click="changeMatch(scope.row)"
                    class="operation-button">
                    手动匹配
                  </el-button>
                </template>
              </el-table-column>
            </el-table-column>
            <!-- <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50"
                :formatter="colFmt_dictData" /> -->

          </el-table>
          <div style="text-align:center">
            <el-pagination hide-on-single-page background layout="prev, pager, next,total" :total="total"
                           :page-size="pagesize" :current-page="pageNum"
                           @current-change="current_change" ></el-pagination>
          </div>
        </div>
        <div class="foot-tools">
          <div class="file-stats">
            <span style="margin-right: 20px">本次导入总文件数: {{ currentTotalUpFiles }}</span>
            <span style="margin-right: 20px; color: #67C23A">成功上传文件数: {{ currentSuccessUpFiles }}</span>
            <span style="color: #E6A23C">跳过文件数: {{ currentSkippedUpFiles }}</span>
          </div>
          <div class="button-section">
            <el-button type="danger" @click="clear()" v-if="isIFMSheetType()">删除</el-button>
            <el-button type="primary" @click="processUnmatchFileInfos" v-if="isIFMSheetType()" v-loading="loading">
              刷新列表
            </el-button>
            <el-button type="primary" @click="loadReports(inputTypes.folder)" v-if="!isIFMSheetType()">上传文件夹
            </el-button>
            <el-button type="primary" @click="loadReports(inputTypes.files)" v-if="!isIFMSheetType()">批量上传文件
            </el-button>
            <el-button type="primary" @click="confirm()">确认匹配</el-button>
          </div>
          <!-- 添加一个空的div来保持flex布局的平衡 -->
          <div class="placeholder"></div>
        </div>
      </el-main>


      <el-footer>
        <div style="margin-bottom: 20px; margin-left: 30px">
          <el-checkbox v-model="overwriteFiles" @change="handleOverwriteChange">覆盖同名文件</el-checkbox>
        </div>
        <el-select v-model="examItemCodes" @change="searchFormChange" clearable multiple collapse-tags
                   placeholder="请选择本机检查项目">
          <el-option v-for="dict in ctrlData.dict.uis_exam_item" :key="dict.value" :label="dict.label"
                     :value="dict.value"/>
        </el-select>
        <el-select v-model="createTimeValue" style="width: 90px" @change="searchFormChange">
          <el-option
            v-for="dict in createTime_props"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-date-picker
          style="width: 200px;"
          v-if="createTimeValue==-1?true:false"
          @change="changeDatePicker"
          v-model="startDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
        <div style="width: 100%" class="report-previewer-wrap" ref="pdfView" v-loading="extOperExportAsDoc"
             @contextmenu.prevent="() => $event.preventDefault()">
          <div style="width: 100%;height: 700px;overflow: auto;">
            <div v-for="pn in numPdfPages" :data-page="'report-previewer-page-' + pn"
                 class="report-previewer-page a4page">
              <canvas/>
              <!-- <img v-if="mime.isJPG && !!imagesDoc" :src="'data:image/jpeg;base64,'+imagesDoc[pn - 1]" /> -->
            </div>
          </div>
        </div>
      </el-footer>
      <!-- 添加告警提示弹窗 -->
      <!--      <el-dialog
              title="重复文件名提醒"
              :visible.sync="repeatFileDialogVisible"
              width="30%"
              center
              custom-class="repeat-file-dialog"
            >
              <div class="warning-content">
                <i class="el-icon-warning" style="color: #E6A23C; font-size: 20px; margin-right: 10px;"></i>
                <span>文件 "{{ currentRepeatFilename }}" 已存在，请重新选择检查项目相同的一批文件</span>
              </div>
              <span slot="footer" class="dialog-footer">
              <el-button @click="handleRepeatFileConfirm">确认</el-button>
            </span>
            </el-dialog>-->

      <LinksignPopup :qr="qr"/>
      <ReportFileUploader ref="ReportFileUploader" @getReportFiles="getReportFiles"/>
    </el-container>
    <ReportConfirmAl ref="reportConfirmAl" @selectExam="selectExam"/>
    <!-- 扫码 -->

  </div>
</template>

<!-- <style scoped src="@/assets/styles/pacs/report/ReportPreviewer.css"></style> -->
<style scoped>
.foot-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.file-stats {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  display: flex;
  justify-content: flex-start;
}

.button-section {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.placeholder {
  flex: 1;
}

div.div.report-previewer-wrap.a4page {
  padding: 0;
}

div.div.report-previewer-page, div.report-previewer-page canvas, div.report-previewer-page img {

  width: 100%;
  height: 100%;
}

div.div.report-previewer-page canvas {
  margin: 0 auto;
}

.inner-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.el-main {
  flex: none;
  min-height: 0;
  overflow: auto;
}

.el-footer {
  flex: none;
}
</style>

<style>
/* 覆盖 el-button 的默认样式 */
.overwrite-button {
  background-color: #fff;
  border-color: #be4949 !important;
  color: #be4949;
  padding: 0 !important; /* 移除按钮的内边距 */
}

/* 悬停样式 */
.overwrite-button:hover {
  background-color: #be4949 !important;
  border-color: #be4949 !important;
  color: #fff !important;
}

/* 确保文字颜色跟随按钮状态 */
.overwrite-button:hover span {
  color: #fff !important;
}

/* 确保文字颜色保持一致 */
.overwrite-button span {
  color: #be4949;
}

/* 点击时的样式 */
.overwrite-button:active {
  background-color: #be4949 !important;
  border-color: #be4949 !important;
  color: #fff !important;
}

/* 确保点击时文字颜色 */
.overwrite-button:active span {
  color: #fff !important;
}

/* focus 时的样式 */
.overwrite-button:focus {
  background-color: #fff !important;
  border-color: #be4949 !important;
  color: #be4949 !important;
}

/* 确保focus时文字颜色 */
.overwrite-button:focus span {
  color: #be4949 !important;
}

/* 注意：这里不要加 scoped，因为需要修改 el-tooltip 的样式 */
/* 详细结果文本框样式 */
.detail-result-box {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 8px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  font-size: 18px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

/* 鼠标悬浮效果 */
.detail-result-box:hover {
  background-color: #f5f7fa;
  border-color: #409EFF;
  color: #409EFF;
}

.match-result-tooltip {
  max-width: 300px;
  line-height: 1.5;
  white-space: pre-line;
  font-size: 14px;
}

/* 操作按钮的通用样式 */
.operation-button {
  font-size: 14px !important; /* 增加字体大小 */
  padding: 4px 8px !important; /* 增加按钮内边距 */
}

/* 如果需要让按钮文字加粗 */
.operation-button span {
  font-weight: 500;
}

/* 鼠标悬停效果 */
.operation-button:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 添加容器样式 */
.ocr-confirm-container {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.inner-container {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;
}

/* 添加进度消息的样式 */
.processing-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 20px;
  border-radius: 4px;
  z-index: 2001; /* 确保显示在loading遮罩层上面 */
  font-size: 40px;
  text-align: center;
  min-width: 200px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>

<script>
export {default} from "@/assets/scripts/pacs/report/comp/OcrConfirm.js";
</script>
