<template>
    <el-dialog ref="reportConfirmAl" title="选择患者信息" :visible.sync="opened" width="1500px" close-on-click-modal>
    <div class="regist-wrap flex-container hei100">
      <div v-loading="registFormLoading">
          <div>登记信息</div>
          <el-container style="height: 100%,width:100%">
    
            <el-main class="hei100">
              <div class="form-element-group">
                      <el-select v-model="searchForm.registWay" style="width: 140px">
                        <el-option
                          v-for="dict in dict.type.uis_regist_way"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>              
    
                      <el-input ref="registCodeInput" v-model="searchForm.registCode" placeholder="请按登记方法输入" class="form-el-w160"
                      @focus="handleFocusRegistCode"
                      @keyup.enter.native="loadRemote" 
                      @input="registCodeInput"/>
    
                      <span class="buttons-pane-gap">
                        <el-button type="primary" @click="loadRemote" :loading="loadRemoteProcessing">查询</el-button>
                        <!-- <el-button type="primary" disabled>读卡</el-button> -->
                        <el-button type="warning" @click="handleNew" v-hasPermi="['exam-info:edit']">新建</el-button>
                      </span>
                    </div>

              <el-form ref="registForm" :model="registForm" :rules="registFormRules" label-width="100px" style="height: 100%" class="tight-form" :disabled="!editEnabled">
          <el-container class="inner-container regist-container" style="height: 100%">
              <el-form ref="searchForm" label-width="70px">
                <!-- <el-row class="searchFormPane">
                  <el-col :span="24">
                    
                  </el-col>
                </el-row> -->
                <el-row>
                  <el-col >
                  <el-table :data="gridData" row-key="ordId"
    ref="multipleTable"
    stripe highlight-current-row
    height="600"
    style="width:700px"
    
    @row-dblclick="selectRow"
    @selection-change="handleSelectionChange"
    @select="selectCheckbox"
    >
    <el-table-column type="selection" width="55"/>
    <el-table-column prop="patientInfo.name" label="姓名" width="80" show-overflow-tooltip />
    <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50" />
    <el-table-column prop="patientInfo.age" label="年龄" width="60" />
    <el-table-column prop="ordName" label="医嘱"  width="180" />
    <el-table-column prop="reqTime" label="医嘱时间" width="180" />
    <el-table-column prop="examCost" label="费用" width="80" />
    <el-table-column prop="ordBillStatus" label="缴费状态" width="80" />
    <el-table-column prop="reqDoctor.nickName" label="申请医生" width="100" />
    <el-table-column prop="reqDept.deptName" label="申请科室" width="120" />
    <el-table-column prop="examDept.deptName" label="诊断科室" width="120" />
    <el-table-column prop="examParts[0].partsName" label="医嘱部位" width="120" />
    <el-table-column prop="ordId" label="医嘱id" width="120" />
                                                                     
    <!-- <el-table-column label="操作" fixed="right" align="center" width="60" class-name="button-col">
      <template slot-scope="scope">
        <el-button title="选择" icon="el-icon-plus" @click="selectRow(scope.row)"></el-button>
      </template>
    </el-table-column> -->
  </el-table>
</el-col>
                </el-row>
                <div style="margin-top: 15px;">
                <div style="display: flex">
                      <el-form-item label="检查类型" prop="examModality.dictValue">
                        <el-select v-model="registForm.examModality.dictValue" clearable >
                          <el-option
                            v-for="dict in ctrlData.dict.uis_exam_modality"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>              
                      </el-form-item>
                
    
                    
                      <el-form-item label="就诊类别" prop="inpType.dictValue">
                        <el-select v-model="registForm.inpType.dictValue">
                          <el-option
                            v-for="dict in ctrlData.dict.uis_inp_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>              
                      </el-form-item>
               
    
                    
                      <el-form-item label="检查项目" prop="examItem.dictValue">
                        <el-select v-model="registForm.examItem.dictValue" >
                          <el-option
                            v-for="dict in ctrlData.dict.uis_exam_item"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-form-item>
           
                    </div>
                    <div style="text-align: center">
                  
                    <el-button type="primary" @click="submitForm" :loading="submitFormProcessing"
                     v-hasPermi="['exam-info:edit']"
                     v-if="!undoDeleteEnabled && !undoCancelEnabled">登记并确认</el-button>
                    </div>
                  </div>
              </el-form>

    
              
          </el-container>
        </el-form>
            </el-main>
          </el-container>
        
        <!-- 选择医生 -->
        <UserPicker ref="userPicker" @pick="pickUser" />
        <!-- 更改机房
        <ExamEquipRoom ref="examEquipRoom" @change="refreshModified" /> -->
        <!-- 从his获取多个检查 -->
        <OrderPicker ref="ordPicker" @pick="pickOrd" />
        <!-- 从his获取多个检查 -->
        <OrderRefund ref="orderRefund" @refund="refundOrd" />
        <!-- 检查部位选择弹窗 -->
        <ExamPartsPicker ref="examPartsPicker"
          :checkedKeys="registForm.examParts_ids" @onChecked="handleCheckExamParts" />
        <DeptPicker ref="deptPicker"
          :checkedKey="registForm.reqDept.deptId" @onChecked="handleCheckDept" />
    
        <!-- 上传检查影像 -->
       <ReportUploader ref="reportUploader" />
    
       <!-- 报告预览 -->
       <ReportViewer ref="reportViewer" :viewLoadReport="false"/>
    
      </div>
      <div class="flex-item-fill hei100 scroll-auto">
      <div class="tabs-wrap hei100" style="border-left: 1px solid #DDD">
          <el-tabs tab-position="top" class="tab-ver tab-ver-cp tab-ver-flex hei100">
            <el-tab-pane class="hei100" >
              <span slot="label">患者列表</span>
              <div class="tab-body-p4 hei100" style="height: 700px">
                <PatientSheet ref="patientSheet" :refresh="8"
                  :filter="{status:0,resultStatusValues:['0','1',2]}"
                  :actions="patientListActions"
                  :limitUpload="true"
                  @dispatchAction="handlePatientListAction"
                  @editRow="handleEdit"
                  @dblClickRow="handleEdit" />
              </div>
              <div class="tab-body-p4 hei100" style="text-align: center">
                <!-- <el-button type="primary" >确认选择</el-button> -->
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
    </div>
    </el-dialog>
    </template>
    
    <style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>
    
    <script>
    import model from "@/assets/scripts/pacs/report/comp/ReportConfirmAl.js";
    export default model;
    </script>
    