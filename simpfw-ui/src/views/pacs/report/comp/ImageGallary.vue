<template>
<div class="gallary-wrap">
  <div class="buttons-pane buttons-pane-gap">
    <span>
      <el-date-picker type="date" v-model="date" style="width: 140px" placeholder="检查日期"></el-date-picker>    
      <el-button type="primary" @click="find" :loading="loading">查找</el-button>
      <el-button type="primary" @click="selectAll">{{selectedAll?'取消':''}}全选</el-button>
      <el-button type="primary" @click="deal">确定</el-button>
    </span>
    <span>
      <el-button type="danger" @click="delImages">删除</el-button>
    </span>
  </div>
  <div class="gallary-pane">
    <div v-for="(item,idx) in images" :key="item.sopInstanceUid" class="gallary-item">
      <div class="cornerstone-element-container">
        <div v-if="typeofVideo(item)" class="cornerstone-element cornerstone-element-video" @click="viewVideo(idx)"><i class="el-icon-video-camera"></i></div>
        <div v-else class="cornerstone-element" @click="zoomImage(idx)"></div>
        <div class="cornerstone-element-no">{{1 + idx}}</div>
        <div class="gallary-item-checkbox">
          <el-checkbox @change="state=>selectImage(state, item)" v-model="item.selected" />
        </div>
      </div>
    </div>
    <div class="gallary-empty" v-show="!images || images.length==0">当日没有采集图像。</div>
  </div>
  <!-- 查看图像 -->
  <ImageView ref="imageView" />
  <!-- 播放动态影像 -->
  <VideoView ref="videoView" />
</div>
</template>

<style scoped src="@/assets/styles/pacs/cornerstone-image.css"></style>
<style scoped>
  .gallary-item{
    float: left;
    margin-right: 8px;
    margin-bottom: 8px;
}
  .gallary-item::after{
    clear: both;
  }
 .cornerstone-element-container{
  margin-left: 0;
 }
 .gallary-empty{
  padding-top: 64px;
  text-align: center;
  color: #999;
 }
 .gallary-item-checkbox{
  position: absolute;
  top: 2px;
  right: 2px;
 }
</style>
  
<script>
import { mapGetters } from 'vuex';
//cornerstone
import "@/assets/scripts/cornerstone/cornerstone-setup";
//一些工具函数
import {currDate, parseTime} from "@/utils/common";
//cornerstone
import {loadAndDisplay} from "@/assets/scripts/pacs/image/util";
//图象接口
import * as api from "@/assets/scripts/pacs/image/api";
//放大影像
import ImageView from "@/views/pacs/report/comp/ImageView";
//
import VideoView from "@/views/pacs/report/comp/VideoView";
//定时刷新
let inerval_find = 8 * 1000, timer_find;

const model = {

  components: {ImageView, VideoView},

  data() {
    return {
      loading: false,
      examInfo: null,
      date: currDate(),
      images: [],
      //自动刷新后恢复勾选的图片
      lastSelectedImages: null,

      selectedAll: false,

      isDeactivated: false
    }
  },

  methods: {
    //当前书写的报告
    show(examInfo) {
      this.examInfo = examInfo;
    },
    //
    findAsInitd() {
      if(!this.loading && (!this.images || !this.images.length)) {
        this.find();
      }
    },
    //查找图像
    find() {
      this.stopTimer();    

      const room = this.currentEquipRoom, roomCode = !!room? room.roomCode : null;
      let date = this.date? parseTime(this.date, "{y}{m}{d}") : null;
      this.loading = true;
      api.gallary(roomCode, date).then(res =>  {
        this.loading = false;
        //
        let lastSelectedImages = this.lastSelectedImages;

        let images = res.data || [];
        images.forEach(img => {
          img.selected = !!lastSelectedImages && -1 !== lastSelectedImages.findIndex(e => e.sopInstanceUid === img.sopInstanceUid);//false;
          img.uri = api.imageDetached(img);
        });
        //
        this.lastSelectedImages = null;
        
        this.images = res.data;
        this.$nextTick(this.loadImages);

        this.timerFind();
      }).catch(() => this.loading = false);
    },
    //定时刷新
    timerFind() {
      this.stopTimer();
      //
      if(this.isDeactivated) { return; }

      timer_find = setTimeout(this.refind, inerval_find);
    },
    //
    refind() {
      //当前勾选的项目，在自动刷新后恢复勾选状态
      if(!!this.images) {
        this.lastSelectedImages = this.selectedImages;
      }

      this.find();
    },
    //放大查看图像
    zoomImage(idx) {
      this.$refs.imageView.view(this.images[idx]);
    },
    //
    viewVideo(idx) {
      this.$refs.videoView.view(this.images[idx]);
    },
    //标识图像选择状态
    selectImage(state, img) {
      img.selected = state;
    },
    //读取图像
    loadImages() {
      document.querySelectorAll(".gallary-pane .cornerstone-element").forEach((ele, i) => {
        const img = this.images[i];
        if(!this.typeofVideo(img)) {
          cornerstone.enable(ele);
          loadAndDisplay(ele, img.uri);
        }
      })
    },
    //全选/取消全选
    selectAll() {
      if(!this.images || !this.images.length) { return; }
      let selectedAll = this.selectedAll = !this.selectedAll;
      this.images.forEach((img, i) => {img.selected = selectedAll;} );
    },
    //完成选择
    deal() {
      let examInfo = this.examInfo;
      if(!examInfo || !examInfo.id) {
        this.$modal.alert("请选择一个检查开始书写。")
        return;
      }
      let selectedImages = this.selectedImages;
      if(!selectedImages || !selectedImages.length) {
        this.$modal.alert("请勾选图像。")
        return;
      }
      //
      let roomCode = this.currentEquipRoom.roomCode;
      api.saveImagesDetached(roomCode, examInfo.id, selectedImages).then(res => {
        this.find();
        let study = res.data, imagesSet;

        let numAffected = 0;
        if(!!study && !!study.seriesSet && !!study.seriesSet.length && !!(imagesSet = study.seriesSet[0].imagesSet) && !!imagesSet.length) {
          const examInfo = this.examInfo;
          imagesSet.forEach(img => {
            if(img.id) {
              numAffected ++;
              this.triggerBind("select", examInfo, img);
            }
          })
        }
        this.$modal.msg(`已选择${numAffected}张图像。`);
      });
    },
    //取消自动刷新
    stopTimer() {
      clearTimeout(timer_find);    
    },
    //删除所选
    delImages() {
      let selectedImages = this.selectedImages;
      if(!selectedImages || !selectedImages.length) {
        this.$modal.alert("请勾选图像。")
        return;
      }

      this.$modal.confirm(`是否确定删除所选${selectedImages.length}张图像？`).then(() => {
        let items = selectedImages.map(e => e.fileUrl);
        api.delImageDetached(items).then(this.find);
      });
    },
    //
    typeofVideo(img) {
      return !!img && !!img.fileUrl && img.fileUrl.toLowerCase().indexOf(".mp4") != -1;
    }
  },

  computed: {
    ...mapGetters(['currentEquipRoom']),

    selectedImages() {
      return !!this.images? this.images.filter(img => img.selected) : null;
    }
  },

  created() {
  //  this.find();
  },

  beforeDestroy() {
    //console.log("ImageGallary beforeDestroy");
    this.stopTimer();    
  },

  deactivated() {
    //console.log("ImageGallary deactivated");
    this.isDeactivated = true;
    this.stopTimer();
  },

  activated() {
    //console.log("ImageGallary deactivated");
    this.isDeactivated = false;
    this.timerFind();
  }
};
export default model;
</script>
  