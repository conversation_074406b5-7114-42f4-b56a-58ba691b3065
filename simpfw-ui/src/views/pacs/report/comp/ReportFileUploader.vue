<template>
  <el-dialog title="导入影像" :visible.sync="opened" width="400px" destroy-on-close>
    <el-form ref="mainForm" label-width="100px" class="tight-form">
      <div><el-alert type="warning" effect="dark" :closable="false" title="请选择影像文件，当前支持jpg和pdf格式文件"></el-alert></div>
      <el-row>
        <el-col :span="24">
          <div class="riu-file-field">
            <input v-if="input.isFILES" id="imageFile" type="file" accept=".jpg, .pdf" multiple>
            <input v-if="input.isFOLDER" id="imageFile" type="file"  webkitdirectory>
          </div>
        </el-col>
      </el-row>

<!--      <el-row style="margin-top: 15px;">
        <el-col :span="24">
          <div class="ocr-match-option">
            <div class="option-row">
              <div class="left-options">
                <el-checkbox v-model="useOcrMatch">使用OCR(光学识别)匹配</el-checkbox>
              </div>
            </div>
            <div v-if="$route.params.defaultExamInfo" class="patient-info">
              检测到默认可匹配的患者信息：<span class="patient-name">{{ $route.params.defaultExamInfo.patientInfo.name }}</span>
              要使用其他患者信息，请勾选"使用OCR(光学识别)匹配"。
            </div>
          </div>
        </el-col>
      </el-row>-->
    </el-form>

    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="submitForm" :loading="processing">导入</el-button>
            <el-button @click="close" :disabled="processing">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>

  </el-dialog>
</template>

<style scoped>
.riu-file-field{
  margin: 8px 0px;
}
.ocr-match-option {
  padding: 0 20px;
}
.option-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left-options {
  display: flex;
  gap: 20px;
}
.patient-info {
  margin-top: 10px;
  color: #1e88e5;
  font-size: 13px;
}
.patient-name {
  color: #f56c6c;
  font-weight: bold;
}
</style>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import {INPUTTYPES} from "@/assets/scripts/pacs/Const";

const model = {
  extends: BaseDialogModel,

  props: {
    inputType: {type: String, default: INPUTTYPES.files},
  },

  data() {
    return {
      processing: false
    }
  },

  methods: {
    prepare(inputType) {
      this.inputType = inputType;

      this.open();
    },
    

    submitForm() {
      const files = document.querySelector("#imageFile").files;
      if(!files || files.length === 0) {
        this.$modal.alert("请选择jpg或pdf文件。");
        return;
      }

      const moment = require('moment-timezone');
      const currentTime = moment.tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

      this.triggerBind("getReportFiles", {
        files: files,
        uploadTime: currentTime
      });

      this.close();
    }
  },

  computed: {
    input() {
      const inputType = this.inputType;
      return {
        isFILES: INPUTTYPES.files === inputType,
        isFOLDER: INPUTTYPES.folder === inputType
      }
    },
  }
};
export default model;
</script>
