<template>
  <div class="app-container">
    <el-row>

      <!--数据-->
      <el-col :span="24" :xs="24">

        <el-row :gutter="10" class="mb8">
          <el-col :span="24">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>

        </el-row>

        <el-table v-loading="loading" :data="grid.data" row-key="id"
         stripe highlight-current-row>
          <el-table-column prop="templateType.dictLabel" label="模板类型" width="100" :formatter="colFmt_dictData" />
          <el-table-column prop="examItem.dictLabel" label="检查项目" width="100" :formatter="colFmt_dictData" />
          <el-table-column prop="template" label="模板名称" min-width="200">
            <template slot-scope="scope">{{scope.row.template && scope.row.template.templateName}}</template>
          </el-table-column>
          <el-table-column prop="imageQuantity" label="图片数" width="80" />
          <el-table-column prop="preferredFlag" label="是否默认" width="80">
            <template slot-scope="scope"><el-checkbox v-model="scope.row.preferredFlag" /></template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
          <el-table-column label="操作" width="120" align="center" class-name="button-col">
            <template slot-scope="scope">
              <el-button
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
              <el-button
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="grid.pager.total>0"
          :total="grid.pager.total"
          :page.sync="grid.pager.pageNum"
          :limit.sync="grid.pager.pageSize"
          @pagination="getList"
        />
      </el-col>
      </el-col>
    </el-row>

    
    <el-dialog :title="editForm.title" :visible.sync="editForm.visible" width="600px" append-to-body>
      <el-form ref="editForm" :model="editForm" label-width="80px"><!-- :rules="rules" -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查项目">
              <el-select v-model="editForm.examItem.dictCode" clearable>
                <el-option
                  v-for="dict in dict.type.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.raw.dictCode"
                />
              </el-select>                
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板类型">
              <el-select v-model="editForm.templateType.dictCode" clearable>
                <el-option
                  v-for="dict in dict.type.uis_template_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.raw.dictCode"
                />
              </el-select>                
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="图片数量">
              <el-input v-model="editForm.imageQuantity" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="模板名称">
              <el-select v-model="editForm.template.id" clearable>
                <el-option
                  v-for="item in editForm.combo_template"
                  :key="item.id"
                  :label="item.templateName"
                  :value="item.id"
                />
              </el-select>                
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="默认模板">
              <el-checkbox v-model="editForm.preferredFlag" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import model from "@/assets/scripts/pacs/comcfg/templatebinding";
export default model;
</script>
