<template>
  <el-tree
    ref="tree" node-key="uid"
    :data="tree.data"
    :props="tree.props"
    :expand-on-click-node="false"
    :default-expand-all="defaultExpandAll"
    :default-expanded-keys="expendKeys"
    @node-click="handleNodeClick"
  />
</template>

<script>
import BaseTreeModel from "@/assets/scripts/pacs/BaseTreeModel";

import * as api from "@/assets/scripts/pacs/comcfg/examparts/api";

let model = {
  extends: BaseTreeModel,

  props: {
    wrapAll: {type: Boolean, default: true},
    expendKeys: {type: Array, default: () => ['C0']},
    defaultExpandAll: {type: Boolean, default: false},
    filter: {type: Object}
  },

  /*data() {
    return deepmerge(BaseTreeModel.data(), {});
  },*/
  methods: {

    buildTree() {
      let params = this.filter || {};
      api.treeselect(params).then(res => {
        let data = res.data;
        //
        data.forEach(e => {
          e.uid = "C" + e.id;
          //
          let chd = e.children;
          if(chd) {
            chd.forEach(c => c.uid = ("P" + c.id));
          }
        });
        if(this.wrapAll) {
          data = [{uid: "C0", label: "全部", children: data}];
        }
        this.tree.data = data;
        //
        this.$nextTick(() => {

        });
      });
    }
  }
};

export default model;

</script>
