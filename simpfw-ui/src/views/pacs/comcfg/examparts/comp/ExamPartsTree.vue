<template>
  <el-tree
    ref="tree"
    node-key="id"
    :data="tree.data"
    :props="tree.props"
    :show-checkbox="showCheckbox"
    check-strictly
    :expand-on-click-node="false"
    :default-expanded-keys="expandedKeys"
    :default-checked-keys="checkedKeys"
    @check-change="handleNodeCheck"
  />
</template>

<script>
import BaseTreeModel from "@/assets/scripts/pacs/BaseTreeModel";

import * as api from "@/assets/scripts/pacs/comcfg/examparts/api";

const model = {
  extends: BaseTreeModel,

  props: {
    wrapAll: {type: Boolean, default: true}
    , expandedKeys: {type: Array}
    , checkedKeys: {type: Array}
    , showCheckbox: {type: <PERSON>olean, default: true}
  },

  /*data() {
    return {
      tree: {
        // 树选项
        data: [],
        props: props
      }
    };
  },
  created() {

    this.buildTree();
  },*/
  methods: {
    //读取书节点
    buildTree() {
      api.treeselect().then(res => {
        let data = res.data;
        //
        data.forEach(e => {
          e.uid = "C" + e.id;
          //
          let chd = e.children;
          if(chd) {
            chd.forEach(c => c.uid = ("P" + c.id));
          }
        });
        //
        if(this.wrapAll) {
          data = [{label: "全部", children: data}];
        }
        this.tree.data = data;
      });
    },
    //节点勾选触发
    handleNodeCheck(node, state, substate) {
      try {
        //console.log(arguments);
        let handleCheck = this.$listeners["onChecked"];
        if(handleCheck) {
          handleCheck(node, state, substate);
        }
      } catch (err) { console.error(err); }
    },
    //设置勾选
    setCheckedKeys(nodes, leafOnly) {
      this.$refs["tree"].setCheckedKeys(nodes, leafOnly);
    },
    //设置节点勾选状态: 勾选/不勾选
    setChecked(nodes, state) {
      const tree = this.$refs["tree"];
      nodes.forEach(e => tree.setChecked(e, state));
    },
    //获取选中节点
    getCheckedNodes() {
      return this.$refs["tree"].getCheckedNodes();
    }
  },

  watch: {
    //“重置”时
    checkedKeys (nv, ol) {
      if(!nv || !nv.length) {
        let nodes = this.getCheckedNodes();
        if(nodes && nodes.length) {
          this.setChecked(nodes, false);
        }
      }
    }
  }
};

export default model;
</script>
