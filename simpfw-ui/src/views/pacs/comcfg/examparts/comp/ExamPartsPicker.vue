
<template>
  <el-dialog   append-to-body title="检查部位" :visible.sync="opened" width="618px">
    <!-- <div>
      <el-input v-model="partsName" clearable style="width: 240px" />
      <el-button type="primary" @click="findExamDeparts">查找</el-button>
    </div> -->
    
    <div class="exam-parts-items-pane">
      <el-tabs tab-position="top" class="tab-ver" v-model="currentPartsCatTab">
        <el-tab-pane v-for="cat in examParts" :key="cat.id" :name="partsCatTabTpl + cat.id">
          <span slot="label">{{cat.label}}</span>
          <div class="dialog-body-items-pane">
            <div class="dialog-body-item" v-for="nod in cat.children" :key="nod.id">
              <el-checkbox v-model="nod.checked" :title="nod.label"
                @change="state=>checkPart(state, nod)">{{nod.label}}</el-checkbox>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>  
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="submit">确定</el-button>
            <el-button @click="close">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  
  </el-dialog>
</template>
  
<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import * as api from "@/assets/scripts/pacs/comcfg/examparts/api";

const model = {
  extends: BaseDialogModel,

  props: {
    //无用？examItem: {type: Object},
    checkedKeys: {type: Array},
    filter: {type: Object}
  },

  data() {
    return {
      //可用部位
      examParts: [],
      //显示的部位
      affectedExamParts: [],
      //过滤部位
      partsName: null,
      //tab名前缀
      partsCatTabTpl: "partsCatTab_",
      //当前Tab
      currentPartsCatTab: null
    }
  },

  methods: {
    //读取部位数据，显示窗口
    findData(args) {
      let params = Object.assign({}, this.filter, args);
      api.treeselect(params).then(res => {
        let data = res.data;
        if(!!data && data.length > 0) {
          //就诊类别独立一组，靠前显示
          let inpType = args.inpType;
          if(inpType && inpType.dictValue) {
            let inpParts = [], pat = new RegExp("(^|.+,)" + inpType.dictValue + "(,.+|$)");
            data.forEach(c => {
              let parts = c.children;
              if(parts && parts.length > 0) {
                inpParts = inpParts.concat(parts.filter(p => p.data.inpTypesCode && pat.test(p.data.inpTypesCode)));
              }
            });
            if(inpParts.length > 0) {
              data.unshift({id: 0, label: (inpType.dictLabel), children: inpParts});
            }
          }
          //默认tab
          this.currentPartsCatTab = this.partsCatTabTpl + data[0].id;
        }
        this.precheckParts(data);
        this.affectedExamParts = this.examParts = data;

        this.open();
      });
    },
    //初始勾选
    precheckParts(data) {
      if(!data || !data.length) { return false; }
      //切换到勾选的分类
      let catFocus = false;
      //
      let checkedKeys = this.checkedKeys;
      //console.log(data, checkedKeys);
      data.forEach(cat => {
        let chd = cat.children;
        if(chd) {
          chd.forEach(nod => {
           nod.checked = !!checkedKeys.includes(nod.id);
           //
           if(!catFocus && nod.checked) {
            catFocus = true;
            this.currentPartsCatTab = this.partsCatTabTpl + cat.id;
           }
          });
        }
      });

      return true;
    },
    //点击勾选触发
    checkPart(state, node) {
      node.checked = state;
    },
    //完成勾选
    submit() {
      let checked = [];
      this.examParts.forEach(cat => {
        let chd = cat.children;
        if(!!chd) {
          chd.forEach(nod => {
            if(nod.checked && -1 === checked.findIndex(n => n.id === nod.id)) {checked.push(nod.data);};
          });
        }
      })
      
      this.triggerBind("onChecked", checked);

      this.close();
    },
    //过滤
    findExamDeparts() {
      if(!this.partsName) {
        this.affectedExamParts = this.examParts;
      } else {
        this.affectedExamParts = this.examParts.filter(e => {

        });
      }
    }
  }
};
export default model;
</script>
 
<style scoped>
.exam-parts-items-pane{
  min-height: 300px;
}
.exam-parts-items-pane >>> .el-tabs__item{
  padding: 0 10px;
}
.dialog-body-items-pane{
  padding-top: 4px;
}
.dialog-body-items-pane >>> .el-checkbox{
  display: inline-block;
  width: 146px;
  /*margin: 7px auto;*/
  padding: 7px 2px;
  margin: 0;
  overflow: hidden;
  border-radius: 4px;
}  
.dialog-body-items-pane >>> .el-checkbox:hover{
  background-color: #e8f4ff;
}
.dialog-body-item{
  display: inline-block;
}
/*.dialog-body-item:nth-child(2n+0){
  background-color: #EFEFEF;
}*/
</style>  