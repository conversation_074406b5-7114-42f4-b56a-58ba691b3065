<template>
<div style="height: 100%">
  <el-container style="height: 100%">
    <el-aside class="btrans-tree-wrap">
        <ExamPartsTree ref="examPartsTree" @onChangeArea="handleNodeClick" />
    </el-aside>
    <el-main>
      <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="部位名称">
          <el-input v-model="queryForm.partsName"
            placeholder="部位名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="grid.data" row-key="id"
       stripe highlight-current-row>
        <el-table-column prop="partsCode" label="部位编码" width="120" />
        <el-table-column prop="partsName" label="部位名称" width="200" />
        <el-table-column prop="orderNum" label="显示排序" width="200" />
        <!--<el-table-column prop="parent.partsCode" label="上级部位代码" width="120" />
        <el-table-column prop="parent.partsName" label="上级部位名称" width="200" />-->
        <el-table-column prop="modalityName" label="检查类型" width="80" />
        <el-table-column prop="examItem.dictLabel" label="检查项目" width="200" />
        <el-table-column prop="equipRoom.roomName" label="绑定机房" width="120" />
        <el-table-column prop="partsExamFactor" label="检查系数" width="80" />
        <el-table-column prop="partsReportFactor" label="书写系数" width="80" />
        <el-table-column prop="partsType.dictLabel" label="部位分类" width="100" />
        <el-table-column prop="partsType.extend.extendI1" label="分类检查系数" width="100" />
        <el-table-column prop="partsType.extend.extendI2" label="分类书写系数" width="100" />
        <el-table-column prop="status" label="状态" width="80" :formatter="colFmt_Status" />
        <el-table-column label="操作" align="center" width="120" class-name="button-col" fixed="right">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            ></el-button>
            <el-button
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            ></el-button>
            <!--<el-button
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
            ></el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="grid.pager.total>0"
        :total="grid.pager.total"
        :page.sync="grid.pager.pageNum"
        :limit.sync="grid.pager.pageSize"
        @pagination="getList"
      />
    </el-main>
  </el-container>
  
  <el-dialog :title="editForm.title" :visible.sync="editForm.visible" @close="cancelEdit"
  class="popupdialog" width="600px" append-to-body>
    <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="100px">
      <el-row style="display: none;">
        <el-col :span="12">
          <el-form-item label="父类编码">
            <el-input v-model="editForm.parent.partsCode" readonly />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="父类名称">
            <el-input v-model="editForm.parent.partsName" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="部位代码" prop="partsCode">
            <el-input v-model="editForm.partsCode" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="部位名称" prop="partsName">
            <el-input v-model="editForm.partsName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="英文名称">
            <el-input v-model="editForm.partsEnglish" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="拼音">
            <el-input v-model="editForm.partsPinyin" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="检查类型" prop="modalityCode">
            <el-select v-model="editForm.modalityCode" clearable>
              <el-option
                v-for="dict in effect_dict_uis_exam_modality"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>              
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部位分类">
            <el-select v-model="editForm.partsType.dictCode" clearable @change="onSelectPartsType">
              <el-option
                v-for="dict in dict.type.uis_exam_parts_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.raw.dictCode"
              />
            </el-select>              
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="HIS对应代码">
            <el-input v-model="editForm.partsCodeHis" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="检查费用">
            <el-input v-model="editForm.examCosts" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="绑定机房">
            <el-select v-model="editForm.equipRoom.roomCode" clearable>
              <el-option
                v-for="dict in editFormOpts.combo_equipRoom"
                :key="dict.roomCode"
                :label="dict.roomName"
                :value="dict.roomCode"
              />
            </el-select>              
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查项目">
            <el-select v-model="editForm.examItem.dictCode" clearable>
              <el-option
                v-for="dict in dict.type.uis_exam_item"
                :key="dict.value"
                :label="dict.label"
                :value="dict.raw.dictCode"
              />
            </el-select>              
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="检查系数">
            <el-input v-model="editForm.partsExamFactor" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="书写系数">
            <el-input v-model="editForm.partsReportFactor" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="分类检查系数">
            <el-input v-model="editForm.partsType.extend.extendI1" readonly />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="分类书写系数">
            <el-input v-model="editForm.partsType.extend.extendI2" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="显示排序">
            <el-input v-model="editForm.orderNum" />     
          </el-form-item>
        </el-col>     
        <el-col :span="12">
          <el-form-item label="就诊类别">
            <el-select v-model="editForm.inpTypesCodes" clearable multiple collapse-tags>
              <el-option
                v-for="dict in dict.type.uis_inp_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.raw.dictValue"
              />
            </el-select>              
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="注意事项">
            <el-input v-model="editForm.noteInfo" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitEditForm">确 定</el-button>
      <el-button @click="cancelEdit">取 消</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import model from "@/assets/scripts/pacs/comcfg/examparts";
export default model;
</script>
