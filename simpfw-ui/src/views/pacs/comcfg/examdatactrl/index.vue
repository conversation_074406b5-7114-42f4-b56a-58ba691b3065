<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item>
        <span class="buttons-pane-gap">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">刷新</el-button>
        </span>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="grid.data">
      <el-table-column label="权限对象类型" prop="objType" width="160" :formatter="colFmt_objType" />
      <el-table-column label="权限对象名称" prop="objName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog title="编辑数据权限" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="formRules" label-width="140px">
        <el-form-item label="权限对象类型" prop="objType">
          <el-select v-model="form.objType" placeholder="请选择" @change="handleChangeObjType">
            <el-option v-for="item in combo.objTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限对象" prop="objId">
          <el-input v-model="form.objName" placeholder="请选择权限对象" class="input-field-narr" readonly>
            <el-button slot="append" size="mini" icon="el-icon-menu" @click="toPickObj"></el-button>
          </el-input>
        </el-form-item>
        <div>
          <div>权限列表</div>
          <el-row :gutter="10">
            <el-col :span="5">
              <el-select v-model="itemForm.itemType" placeholder="项目类型" @change="handleChangeItemType">
                <el-option v-for="item in combo.itemTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="14">
              <!--<el-input v-model="itemForm.itemName" placeholder="请选择项目" class="input-field-narr" readonly>
                <el-button slot="append" size="mini" icon="el-icon-menu" @click="toPickItem"></el-button>
              </el-input>-->
              <el-select v-model="itemForm.itemId" placeholder="请选择项目"
               style="width: 100%"
               filterable no-match-text="-" :clearable="false"
               :multiple="itemForm.multiple" collapse-tags
               @change="handleChangeItem">
                <el-option v-for="item in combo.itemData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="3"><el-button type="primary" @click="addItem">增加</el-button></el-col>
          </el-row>
          <el-table v-loading="loading" :data="form.detail" width="850px">
            <el-table-column label="项目类型" prop="itemType" width="100px" :formatter="colFmt_itemType" />
            <el-table-column label="项目名称" prop="itemName" width="150px">
              <template slot="header">
                <el-tooltip placement="top" raw-content>
                  <div slot="content">
                    数据权限目前只对下列项目生效：<br/>
                    - 检查机房<br/>
                    - 数据字典/检查类型<br/>
                    - 数据字典/就诊类别<br/>
                    - 数据字典/检查项目<br/>
                  </div>
                  <span>项目名称 <i class="el-icon-question"></i></span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="项目子项" width="520px">
              <template slot="header">
                <el-tooltip placement="top" raw-content>
                  <div slot="content">
                    项目子项目前只对下列项目生效：<br/>
                    - 数据字典/检查项目<br/>
                  </div>
                  <span>项目子项 <i class="el-icon-question"></i></span>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <div style="margin-bottom: 10px">
                  <el-tag
                    v-for="(item, index) in scope.row.subDetail"
                    :key="index"
                    closable
                    size="small"
                    style="margin-right: 5px; margin-bottom: 5px"
                    @close="handleRemoveSubDetail(scope.row, index)"
                  >
                    {{ item.itemName }}
                  </el-tag>
                </div>
                <el-row :gutter="10">
                  <el-col :span="7">
                    <el-select v-model="scope.row.itemForm.itemType" placeholder="子项类型" @change="(val) => handleChangeSubItemType(val, scope.row)">
                      <el-option v-for="item in scope.row.combo.itemTypes"
                                 :key="item.value"
                                 :label="item.label"
                                 :value="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="13">
                    <el-select v-model="scope.row.itemForm.itemId" placeholder="请选择子项"
                               style="width: 100%"
                               filterable
                               no-match-text="-"
                               multiple
                               collapse-tags
                               @change="(val) => handleChangeSubItem(val, scope.row)">
                      <el-option v-for="item in scope.row.combo.itemData"
                                 :key="item.value"
                                 :label="item.label"
                                 :value="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="4"><el-button type="primary" @click="addSubItem(scope.row)">增加</el-button></el-col>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="60px">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteDetail(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>

    <DeptPicker ref="deptPicker"
      :checkedKey="0" @onChecked="pickObj" />
    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickObj" />

  </div>
</template>

<script>
//import model from "@/assets/scripts/pacs/comcfg/examdatactrl/index";
export {default} from "@/assets/scripts/pacs/comcfg/examdatactrl/index";;
</script>
