<template>
  <div class="home-container">
    <div class="home-header">
      <div class="left">
        <div class="home-title">全局进度</div>
        <div class="speed">
          <div class="speed-pie" :speed="speed">
            <div id="chart"></div>
            <div class="sp-info">
              <p>总人数（人）</p>
              <p class="number">{{statTotal}}</p>
            </div>
          </div>
          <!-- <ul class="info" :class="{'info-3c': examItemsData.length > 8}">
            <li class="info-block" v-for="(item,i) in examItemsData" :key="item.id">
              <div class="info-name">{{item.label}}</div>
              <div class="info-number"><span>{{fmtNum(!!item.stat? item.stat.portion : 0)}}</span>%</div>
              <div class="cri" :style="{backgroundColor: colors[i]}"></div>
            </li>
          </ul> -->
        </div>
      </div>
      <div class="right">
        <span class="right-name">欢迎使用{{appTitle}}！</span>
        <p class="right-ename">Welcome to the Medical Technician Information System!</p>
        <div class="right-content">
          <div class="text" v-if="false">
            属性可以使得文本域的高度能够根据文本内容自动进行调整，并且
            属性可以使得文本域的高度能够根据文本内容自动进行调整，并且属性可以使得文本域的高度能够根据文本内容自动进行调整，并且属性可以使得文本域的高度能够根据文本内容自动进行调整，并且
          </div>
          <div class="not-data text" v-else>
            <img src="../assets/images/home/<USER>" alt="">
            <!-- <img src="../assets/image/home/<USER>" alt=""> -->
          </div>
        </div>
      </div>
    </div>
    <ul class="home-content">
      <li v-for="item in examItemsData" :key="item.dictCode">
        <div class="hc-h">
          <div class="home-title">{{item.label}}</div>
          <div class="left">
            <el-progress type="circle" :format="() => ''" :stroke-width="3" :width="18" :percentage="!!item.stat? item.stat.ratio : 0"></el-progress>
            <span class="num">{{fmtNum(!!item.stat? item.stat.ratio : 0)}}%</span>
          </div>
        </div>
        <div class="line">
          <span class="line-label">总人数</span>
          <span class="line-value">{{!!item.stat? item.stat.total : 0}}</span>人
        </div>
        <div class="line">
          <span class="line-label">预约人数</span>
          <span class="line-value">{{!!item.stat? item.stat.numAppoint : 0}}</span>人
        </div>
        <div class="line">
          <span class="line-label">未检查人数</span>
          <span class="line-value">{{!!item.stat? item.stat.numQueue : 0}}</span>人
        </div>
        <div class="line">
          <span class="line-label">过号人数</span>
          <span class="line-value">{{!!item.stat? item.stat.numPast : 0}}</span>人
        </div>
      </li>

    </ul>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  import { mapGetters } from 'vuex'
  import {stat as estat} from "@/assets/scripts/pacs/equiproom/examresultstatusstat/api";

  import {COLORS} from "@/assets/scripts/pacs/colors";

  import request from "@/utils/request";

  import { DataCtrlDict } from "@/assets/scripts/pacs/comcfg/examdatactrl/index"
  import ExamDataScope from "@/assets/scripts/pacs/comcfg/examdatactrl/mixins";

  const ctx = "/exammanagement/callingBoard", examItemCtx = "/exammanagement/examItem/find";

  export default {

    mixins: [ExamDataScope],
    dicts: ['uis_exam_item'],

    /* name: "Home", */
    data: () => ({
      speed: 80,

      colors: COLORS,

      examItems: [],
      examItemsLoaded: false,

      examStatData: [],
      examStatDataLoaded: false
    }),
    methods: {
      //全局进度图表
      loadEcharts() {
        if(!this.examItemsLoaded || !this.examStatDataLoaded) {
          return;
        }

        const examItemsData = this.examItemsData;
        const seriesData = examItemsData.map(es => {
          return {value: (es.stat && es.stat.total || 0), name: es.dictLabel};
        });
        
        const chartDom = document.querySelector('#chart')
        const chart = echarts.init(chartDom);
        const option = {
          series: [
            {
              name: '全局进度',
              type: 'pie',
              radius: ['44%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  //fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              color: this.colors,
              data: seriesData
            }
          ]
        }
        chart.setOption(option)
      },
      //检查项目
      // findExamItems() {
      //   request.get(examItemCtx).then(res => {
      //     this.examItems = res.data;

      //     this.examItemsLoaded = true;
      //     this.loadEcharts();
      //   });
      // },
      //检查进度
      // examStat() {
      //   estat("examItem").then(res => {
      //     this.examStatData = res.data;

      //     this.examStatDataLoaded = true;
      //     this.loadEcharts();
      //   });
      // },

      fmtNum(v) {
        if(null !== v && !isNaN(v)) {
          return v.toFixed(1);
        }

        return v;
      }
    },

    watch: {
      /**
       * 检查项目字典取值后执行
       */
      "dict.type.uis_exam_item": {
        deep: true,
        handler(nv, ov) {
          this.applyDataCtrl(DataCtrlDict.ItemType.dict, nv);
        }
      },

      "ctrlData.dict.uis_exam_item": {
        deep: true,
        handler(nv, ov) {
          this.examItems = nv;

          this.examItemsLoaded = true;
          this.loadEcharts();
          //检查进度
          estat("examItem").then(res => {
              this.examStatData = [];
              this.examItems.forEach(ei => {
                  let es = res.data.find(s => s.dime === ei.value);
                  if(!!es) {
                      this.examStatData.push(es);
                  }
                });
              this.examStatDataLoaded = true;
              this.loadEcharts();
            });
        }
      }
    },

    mounted() {
      // this.findExamItems();

      // this.examStat();
    },

    computed: {
        ...mapGetters([
      'appTitle'
    ]),
      examItemsData()   {
        let examItems = this.examItems, examStatData = this.examStatData;
        //console.log(examItems, examStatData);
        examItems.forEach(ei => {
          let es = examStatData.find(s => s.dime === ei.value);
          if(!!es) {
            ei.stat = es;
            //已完成比率
            let ratio = 0;
            if(!!es.total && !!es.numReport) {
              ratio = 100.0 * es.numReport / es.total;
            }
            es.ratio = ratio;
            //总人数占比
            const statTotal = this.statTotal;
            if(!!statTotal && !!es.total) {
              es.portion = 100.0 * es.total / statTotal;
            }
          }
        });
        return examItems;
      },
      //总人数
      statTotal() {
        let total = 0;
        this.examStatData.forEach(es => total += (es.total || 0));
        return total;
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .bg-content {
    width: 100%;
    height: 100%;
  
    img {
      width: 100%;
      object-fit: cover;
    }
  }
  
  .home-container {
    height: 100%;
    width: 100%;
    // padding: 20px;
    background-color: #eaf3f9;;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-image: url("../assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-position: right top;
    background-origin: content-box;
    background-size: 644px 634px;
  
    .home-title {
      font-weight: 600;
      font-size: 20px;
      line-height: 28px;
      color: #3d555f;
      position: relative;
      padding-left: 8px;
      margin-bottom: 35px;
  
      &::after {
        content: "";
        position: absolute;
        left: 0;
        top: calc(50% - 10px);
        width: 3px;
        height: 20px;
        background: #018FD7;
        border-radius: 3px;
      }
    }
  
    .home-header {
      height: 326px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      background-image: url("../assets/images/home/<USER>");
      background-repeat: no-repeat;
      background-position: right 69px;
      background-origin: content-box;
      background-size: 224px 278px;
  
      .left {
        margin-left: 32px;
        margin-top: 36px;
  
        .speed {
          display: flex;
          align-items: center;
          transform: translateY(-38px) translateX(-38px);
  
          &-pie {
            width: 254px;
            height: 254px;
            position: relative;
  
            #chart {
              width: 100%;
              height: 100%;
            }
          }
  
          .sp-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #525d64;
            position: absolute;
            z-index: 9;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
  
  
            :first-child {
              font-size: 12px;
              line-height: 17px;
              color: #525D64;
            }
  
            .number {
              font-weight: 600;
              font-size: 40px;
              line-height: 56px;
              margin: 0 auto;
            }
          }
  
          .info {
            display: flex;
            flex-wrap: wrap;
            width: 216px;
            justify-content: space-between;
            padding-top: 20px;
  
            li {
              flex-shrink: 0;
              width: 70px;
              margin-bottom: 16px;
              color: #525d64;
              position: relative;
  
              &:nth-child(2n -1) {
                margin-right: 48px;
              }
  
              .cri {
                position: absolute;
                top: 4px;
                left: -13px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #2ABEF1;
              }
  
              &:first-child {
                margin-right: 50px;
              }
  
              .info-name {
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                white-space: nowrap;
              }
  
              .info-number {
                font-size: 14px;
                line-height: 20px;
                white-space: nowrap;
  
                span {
                  font-weight: 600;
                  font-size: 20px;
                  line-height: 28px;
                  margin-right: 5px;
                }
              }
            }
          }

          .info-3c{
            width: 336px;
            li:nth-child(2n-1){
              margin-right: 0;
            }
            li:nth-child(3n+1),li:nth-child(3n + 2) {
              margin-right: 48px;
            }
          }
        }
      }
  
      .right {
        margin-right: 165px;
        margin-top: 24px;
  
        &-name {
          font-weight: 600;
          font-size: 40px;
          line-height: 50px;
          color: #236995;
          white-space: nowrap;
        }
  
        &-ename {
          line-height: 20px;
          color: #226385;
        }
  
        &-content {
          margin-top: 16px;
          display: flex;
          // justify-content: flex-end;
  
          .text {
            width: 262px;
            height: 180px;
            box-sizing: border-box;
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #9ca4aa;
            margin-left: 50px;
  
            &.not-data {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
  
          .type {
            display: flex;
            flex-direction: column;
            justify-content: center;
  
            li {
              width: 56px;
              height: 30px;
              margin: 10px 0;
              background: #42c5f1;
              opacity: 0.5;
              text-align: center;
              line-height: 30px;
              color: #fff;
              border-radius: 0 5px 5px 0;
              cursor: pointer;
  
              &.active {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  
    .home-content {
      background: #ffffff;
      min-height: calc(100% - 342px - 20px);
      box-shadow: 0px 2px 12px 2px rgba(139, 192, 223, 0.25);
      border-radius: 12px;
      padding: 20px 20px 0 0;
      margin: 0 12px;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-y: auto;
      max-height: calc(100vh - 400px);
  
  
      li {
        flex-shrink: 0;
        width: 274px;
        height: 166px;
        background: #f3f8fb;
        border-radius: 10px;
        margin-left: 20px;
        margin-bottom: 20px;
        padding: 12px 0px 16px 12px;
        box-sizing: border-box;
  
        .home-title {
          margin-bottom: 0;
        }
  
        .hc-h {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 11px;
  
          .left {
            width: 72px;
            height: 24px;
            background: #DDEFF8;
            display: flex;
            align-items: center;
            border-radius: 12px 0 0 12px;
            padding-left: 2px;
            box-sizing: border-box;
  
            .num {
              margin-left: 6px;
              font-size: 12px;
              line-height: 17px;
  
              color: #4E5969;
  
            }
          }
        }
  
        .line {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #525d64;
          margin: 0 0 8px 9px;
  
          &-label {
            width: 98px;
            display: inline-block;
          }
  
          &-value {
            font-weight: 600;
            margin-right: 5px;
          }
        }
      }
    }
  
    // 150%缩放
    @media screen and (max-height: 646px) {
      .home-header {
        height: 290px;
  
        .right-content {
          margin-top: 10px;
        }
      }
  
      .home-content {
        padding-top: 5px;
  
        li {
          margin-left: 10px;
          margin-bottom: 10px;
          height: auto;
          padding: 5px 0 5px 12px;
  
          .hc-h {
            margin-bottom: 5px;
          }
  
          .line {
            margin-bottom: 5px;
          }
        }
      }
    }
  }
  </style>