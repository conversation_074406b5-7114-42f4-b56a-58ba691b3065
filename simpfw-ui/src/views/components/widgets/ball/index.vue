<template>
    <div class="chat-ball" @mousedown="onMouseDown" :class="{ 'draggable-hover': !isNotMore }" @dblclick="dblClick"
        @contextmenu="handleContextMenu">
        <Chat_v1></Chat_v1>
    </div>
</template>

<script>
import Chat_v1 from './chat_v1.vue';
import { useDrag } from './dragWindow';

export default {
    name: 'ChatBall',
    components: {
        Chat_v1
    },
    data() {
        return {
            isNotMore: true,
            biasX: 0,
            biasY: 0,
            screenX: 0,
            lastScreenX: 0,
            lastScreenY: 0,
            screenY: 0,
            isMove: false
        }
    },
    created() {
        // 绑定鼠标移动事件
        document.addEventListener('mousemove', this.handleMouseMove);
    },
    beforeDestroy() {
        // 解绑鼠标移动事件
        document.removeEventListener('mousemove', this.handleMouseMove);
    },
    methods: {
        // 使用组合式API中的拖拽方法
        ...useDrag(),

        handleMouseMove(e) {
            let ctx = window._utools_ctx
            if (!ctx || !this.isMove) {
                return
            }
            let x = e.screenX - this.lastScreenX
            let y = e.screenY - this.lastScreenY
            this.lastScreenX = e.screenX
            this.lastScreenY = e.screenY
            console.log('x', x, 'y', y)
            getMainCtx().windows.move(x, y, "ball")
        },

        /**
         * 双击事件
         */
        dblClick() {
            console.log('dblClick')
        },

        handleContextMenu() {
            console.log('handleContextMenu')
        }
    }
}
</script>

<style scoped>
.chat-ball {
    /* width: 80px;
    height: 30px; */
    overflow: hidden;
}

.main-container {
    height: 24px;
    width: 74px;
    /* background-color: var(--sub-color); */
    /* border-radius: 20px; */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 3px solid #333;
    font-weight: bold;
    position: relative;
    overflow: hidden;
    /* opacity: 0.6; */
}

.draggable-hover {
    cursor: pointer;
    /* 鼠标样式，表示正在拖动 */
}

.op-next {
    width: 80px;
    width: 80px;
    background-color: #67c23a;
    text-align: center;
}
</style>