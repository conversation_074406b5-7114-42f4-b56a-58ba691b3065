<template>
    <!-- <div class="float-drag-button" @click="openQueueView" @contextmenu="openContextmenu">
      <span class="queue-span">排队叫号</span>
    </div> -->
    <div class="float-alert-box" @contextmenu="openContextmenu">
      <div v-if="unLogin" @click="login">
        <div>MT无纸化</div>
        <div>系统未登录</div>
        <div>点击登录</div>
      </div>
      <div v-else>
        <div class="alert-item" @click="redirectTo('unCheck')">
          <span class="alert-text">未检查</span>
          <span class="alert-number">{{ data.unCheck }}</span>
        </div>
        <div class="alert-item" @click="redirectTo('reportError')">
          <span class="alert-text">报告出错</span>
          <span class="alert-number">{{ data.reportError }}</span>
        </div>
        <div class="alert-item" @click="redirectTo('unSubmit')">
          <span class="alert-text">未提交</span>
          <span class="alert-number">{{ data.unSubmit }}</span>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import _ from "lodash";
  
  export default {
    name: 'FloatAlertBox',
    data() {
      return {
        data: {
          unCheck: 0,
          reportError: 0,
          unSubmit: 0,
        },
        todoListRefreshInterval: 10,
        unLogin: false,
        refreshTimer: null
      }
    },
    created() {
      this.init();
      this.freshTodoList();
    },
    beforeDestroy() {
      // 清除定时器
      if(this.refreshTimer) {
        clearInterval(this.refreshTimer);
      }
    },
    methods: {
      // 打开排队叫号页面
     
      
      openContextmenu(e) {
        // 菜单模板
        const menuTemplate = [
          { label: "隐藏", id: "ball-hide" },
          { label: "退出", id: "ball-quite" },
          { label: "刷新主界面", id: "main-refresh" },
        ];
        const mainCtx = window._utools_ctx;
        if (mainCtx && mainCtx.system) {
          mainCtx.system.showSystemMenu(menuTemplate, "ball");
        }
      },
      
      parseTodoList(todos) {
        if (todos) {
          this.data.unCheck = todos["unCheck"] && todos["unCheck"].num || 0;
          this.data.reportError = todos["reportError"] && todos["reportError"].num || 0;
          this.data.unSubmit = todos["unSubmit"] && todos["unSubmit"].num || 0;
        }
      },
      
      freshTodoList() {
        const utoolsCtx = window._utools_ctx;
        if (utoolsCtx && utoolsCtx.mtApi) {
          utoolsCtx.mtApi.getTodoListData().then((res) => {
            this.unLogin = false;
            if (res && res.code === 200) {
              this.parseTodoList(res.data);
            }
            if (res && res.code === 401) {
              this.unLogin = true;
            }
          }).catch(() => {
            console.log("服务器异常");
          });
        }
      },
      
      init() {
        const utoolsCtx = window._utools_ctx;
        if (utoolsCtx && utoolsCtx.config) {
          utoolsCtx.config.getSettings().then((res) => {
            this.todoListRefreshInterval = _.get(res, "todolist.refreshInterval") || 10;
            this.refreshTimer = setInterval(() => {
              this.freshTodoList();
            }, this.todoListRefreshInterval * 1000);
          });
        }
      },
      
      redirectTo(type) {
        let routerName = "MTReportWriting";
        if ("reportError" === type) {
          routerName = "MTReportError";
        }
        const utoolsCtx = window._utools_ctx;
        if (utoolsCtx && utoolsCtx.windows) {
          utoolsCtx.windows.showIWindow("main");
          utoolsCtx.windows.sendMessageToWindow("main", {
            type: "main-go-to-router",
            to: routerName,
          });
        }
      },
      
      login() {
        const utoolsCtx = window._utools_ctx;
        if (utoolsCtx && utoolsCtx.windows) {
          utoolsCtx.windows.showIWindow("main");
        }
      }
    }
  }
  </script>
  
  <style scoped>
  /* 样式保持不变 */
  .float-drag-button {
    overflow: hidden;
    padding: 5px;
    width: 25px;
    opacity: 1;
    background-color: #26d40e;
    border-radius: 8px 0px 0px 8px;
    box-shadow: 0px 2px 15px 0px rgba(9, 41, 77, 0.15);
    cursor: pointer;
    font-size: 14px;
    color: #ffffff;
  }
  
  .float-alert-box {
    z-index: 20000;
    width: 80px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    padding: 10px 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .float-alert-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  }
  
  .alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .alert-item:last-child {
    border-bottom: none;
  }
  
  .alert-text {
    font-size: 12px;
    color: #333;
    font-weight: 500;
  }
  
  .alert-number {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    border-radius: 12px;
    background-color: #ff5252;
    color: white;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(255, 82, 82, 0.3);
    transition: all 0.2s ease;
  }
  
  .alert-number:hover {
    transform: scale(1.1);
  }
  </style>