export const useDrag = () => {
    let animationId;
    let mouseX;
    let mouseY;
    let clientWidth = 0;
    let clientHeight = 0;
    let draggable = true;
  
    const onMouseDown = (e) => {
      // 右击不移动
      if (e.button === 2) return;
      draggable = true;
      mouseX = e.clientX;
      mouseY = e.clientY;
      if (Math.abs(document.body.clientWidth - clientWidth) > 5) {
        clientWidth = document.body.clientWidth;
      }
      if (Math.abs(document.body.clientHeight - clientHeight) > 5) {
        clientHeight = document.body.clientHeight;
      }
      document.addEventListener("mouseup", onMouseUp);
      animationId = requestAnimationFrame(moveWindow);
    };
  
    const onMouseUp = () => {
      draggable = false;
      document.removeEventListener("mouseup", onMouseUp);
      cancelAnimationFrame(animationId);
    };
  
    const moveWindow = () => {
      if (
        window &&
        window._utools_ctx &&
        window._utools_ctx.windows &&
        typeof window._utools_ctx.windows.windowMoving === 'function'
      ) {
        window._utools_ctx.windows.windowMoving(
          mouseX,
          mouseY,
          clientWidth,
          clientHeight,
        );
      }
      if (draggable) animationId = requestAnimationFrame(moveWindow);
    };
  
    return {
      onMouseDown,
    };
  };
  
  // export default useDrag;