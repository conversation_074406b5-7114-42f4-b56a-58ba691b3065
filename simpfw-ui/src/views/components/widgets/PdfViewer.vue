<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="title"
    width="80%"
    @close="close"
    custom-class="full-height-dialog"
  >
    <iframe
      :src="pdfUrl"
      frameborder="0"
      width="100%"
    ></iframe>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      pdfUrl: '',
      title: '',
      // iframeHeight: '700px' // 默认高度
    };
  },
  methods: {
    open(url, title = 'PDF帮助文档') {
      console.log("打开文档")
      this.pdfUrl = url;
      this.title = title;
      this.dialogVisible = true;
      // this.$nextTick(() => {
      //   this.updateIframeHeight();
      // });
    },
    close() {
      this.dialogVisible = false;
    },
    // updateIframeHeight() {
    //   // 设置iframe高度为窗口高度的90%
    //   this.iframeHeight = `${window.innerHeight * 0.7}px`;
    // }
  }
};
</script>

<style scoped>
/* 可根据实际需求调整样式 */
/* 使用/deep/选择器穿透scoped样式 */
/deep/ .full-height-dialog {
  display: flex;
  flex-direction: column;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  height: 100vh; /* 占屏幕高度的80% */
  max-height: 100vh;
}

/* 调整对话框内容区域高度 */
/deep/ .full-height-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  padding: 0; /* 如果需要可以调整内边距 */
}

/* 调整iframe样式 */
/deep/ .full-height-dialog iframe {
  height: 100%;
}
</style>
