<template>
  <div style="height: 100%">
    <el-container class="inner-container inner-container-split" style="height: 100%; width: 100%">
      <el-row style="height: 100%; width: 100%">
        <el-col :span="3">
          <el-tabs class="tabs-container-1" tab-position="left" @tab-click="handleClick1">
          <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :id="tab.acname" :reportType="tab.name">
        </el-tab-pane>
      </el-tabs>

        </el-col>
        <el-col style="height: 100%" :span="21">

        <StaffReport :para="reportType" v-if="activeName=='staff'"  style="height: 100%; width: 100%"/>
        <StaffPartReport :para="reportType" v-if="activeName=='staff_part'"  style="height: 100%; width: 100%"/>
        <EquipReport v-if="activeName=='equip'"  style="height: 100%; width: 100%"/>
        <PartReport v-if="activeName=='part'"  style="height: 100%; width: 100%"/>
        <ItemReport v-if="activeName=='item'"  style="height: 100%; width: 100%"/>
        <PropReport v-if="activeName=='prop'"  style="height: 100%; width: 100%"/>
        <EquipPropReport v-if="activeName=='equip_prop'"  style="height: 100%; width: 100%"/>

        </el-col>
      </el-row>





    </el-container>

  </div>
  </template>

  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>

  <script>

  import model from "@/assets/scripts/uis/exammanagement/workreport/Index";
  export default model;
  </script>
