<template>
  <div style="height: 100%">
    <el-container class="inner-container inner-container-split" style="height: 100%">
  
      <!-- <el-tabs class="tabs-container-1" tab-position="left" @tab-click="handleClick1">
        <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :id="tab.name">
        </el-tab-pane>
      </el-tabs>   -->

      <el-main>
        <div class="data-container flex-container-column">
          <div class="search-form-v">
            <el-form :model="searchFormSimp" :inline="true" class="exam-search-form exam-search-form-simp"><!-- class="tight-form"-->
              <!-- <svg-icon icon-class="list-border" class-name="search-form-toggler"  @click="toggleSearchForm"/> -->
              <!-- <el-select v-model="searchFormSimp.textFieldName" clearable>
                <el-option v-for="item in combo.searchTextFields"
                  :key="item.name"
                  :label="item.label"
                  :value="item.name"
                />
              </el-select>
              <el-input v-model="searchFormSimp.textField" placeholder="请输入" />
                           -->
              <el-select v-model="searchFormSimp.dateFieldName" clearable>
                <el-option v-for="item in combo.searchDateFields"
                  :key="item.name"
                  :label="item.label"
                  :value="item.name"
                />
              </el-select>
              <el-date-picker
                v-model="searchFormSimp.dateFieldGe"
                type="date"
                placeholder="开始时间"
                class="el-date-editor--noprefix"
                :clearable="true">
              </el-date-picker>
              -
              <el-date-picker
                v-model="searchFormSimp.dateFieldLt"
                type="date"
                placeholder="结束时间"
                class="el-date-editor--noprefix"
                :clearable="true">
              </el-date-picker>
              <el-select v-model="searchFormSimp.examModalityPara" placeholder="设备类型"
               multiple collapse-tags class="select-multi-sing">
                <el-option v-for="dict in dict.type.uis_exam_modality"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
              <el-select v-model="searchFormSimp.examDevicesCode" placeholder="设备型号"
               multiple collapse-tags class="select-multi-sing">
                <el-option v-for="item in combo.devices"
                  :key="item.deviceCode"
                  :label="item.device"
                  :value="item.deviceCode"
                />
              </el-select>
              <el-select v-model="searchFormSimp.inpTypeValues" placeholder="就诊类型"
               multiple collapse-tags class="select-multi-sing">
                <el-option
                  v-for="dict in dict.type.uis_inp_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
                  <!-- <el-select v-model="searchFormSimp.equipRoomsCode" multiple collapse-tags placeholder="检查房间" clearable class="select-multi-sing">
                    <el-option
                      v-for="dict in combo.equipRooms"
                      :key="dict.roomCode"
                      :label="dict.roomName"
                      :value="dict.roomCode"
                    />
                  </el-select>        -->
                  

              <el-select v-model="searchFormSimp.resultStatusValues" placeholder="检查状态"
               multiple collapse-tags class="select-multi-sing">
                <el-option
                  v-for="dict in dict.type.uis_exam_result_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
                <el-option label="已删除" value="-2" />
              </el-select>


              <el-select v-model="searchFormSimp.doctorType" placeholder="医生类型" clearable>
                <el-option v-for="item in combo.searchReportType"
                  :key="item.name"
                  :label="item.label"
                  :value="item.val"
                />
              </el-select>
              <el-input v-model="searchFormSimp.doctorName" placeholder="医生名字" />
              <br>
              <div style="display: auto">
                <el-row style="height: 100%; width: 100%">
                     <el-col :span="3">
                <div style="display: flex; position: left: inherit;">
                    <el-checkbox v-model="searchFormSimp.examResultProp.dictValue" true-label="1" false-label=""/>
                    只统计阳性
                  </div>
                  </el-col>
                  <el-col style="height: 100%" :span="21">

                  <div style="display: flex; position: right: inherit;    justify-content: flex-end;">
                    <el-button type="primary" icon="el-icon-search" @click="search('simp')">查询</el-button>
                    <download-excel
                        types="xls"
                        :data="dataArr"
                        :fields="fields"
                        :name="exportName"
                        :worksheet="exportSheet"
                        :fetch='fetchData'

                        style="width: min-content"
                        
                    >
                            <el-button type="primary">导出</el-button>
                    </download-excel>
                  </div>
                  </el-col>
                  </el-row>
              </div>
            </el-form>
          </div>
  
          <div class="flex-item-fill" style="overflow: auto;">
  
            <el-table v-loading="loading" :data="grid.data" row-key="id" height="100%"
            stripe highlight-current-row>
              <el-table-column prop="examParts.partsName" label="检查部位" width="180" :formatter="colFmt_object" show-overflow-tooltip  /> 

              <!-- <el-table-column prop="doctorName" label="医生姓名" width="100" >
                <template slot-scope="scope">
                  <div>{{scope.row.doctorName == undefined ? '-' : scope.row.doctorName }}</div>
                </template>
              </el-table-column> -->
              <el-table-column prop="workTimes" label="工作量（人次）" width="180" />
              <el-table-column prop="" label="工作量百分比" width="140" >
                <template slot-scope="{row}">
                  {{(row.workTimes/countInfo.workTimesTotal*100).toFixed(2)+"%"}}
                </template>
              </el-table-column>
              <el-table-column prop="workCost" label="收费总额" width="80" />
              <el-table-column prop="" label="收费百分比" width="140" >
                <template slot-scope="{row}">
                  {{(row.workCost/countInfo.workCostTotal*100).toFixed(2)+"%"}}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="examParts.partsName" label="检查部位" width="180" :formatter="colFmt_object" show-overflow-tooltip  />  -->

              <el-table-column  label="备注" width="100" />


            </el-table>
          </div>
  
          <pagination
            v-show="grid.total>0"
            :total="grid.total"
            :page.sync="searchForm.pageNum"
            :limit.sync="searchForm.pageSize"
            @pagination="getList"
          />
        </div>
      </el-main>
    </el-container>
  
    <Contextmenu ref="tableContextmenu" :items="tableAction" @select="handleTableAction" />
  
    <ExamViewer ref="examViewer" />
    <!-- <ExamEquipRoom ref="examEquipRoom" @change="search" /> -->
    <OperationAuth ref="operAuth" @success="handleDelete" />
  </div>
  </template>
  
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
  
  <script>
  //import '@/assets/styles/uis/common.css';
  //import '@/assets/styles/uis/exammanagement/patient/Index.css';
  
  import model from "@/assets/scripts/gis/exammanagement/workreport/part";
  export default model;
  </script>
  