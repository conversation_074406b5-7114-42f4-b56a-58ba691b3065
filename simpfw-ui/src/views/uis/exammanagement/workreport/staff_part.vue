<template>
  <div style="height: 100%">
    <el-container class="inner-container inner-container-split" style="height: 100%">

      <el-main>
        <div class="data-container flex-container-column">
          <div class="search-form-v">
            <el-form :model="searchFormSimp" :inline="true" class="exam-search-form exam-search-form-simp">

              <el-select v-model="searchFormSimp.dateFieldName" clearable>
                <el-option v-for="item in combo.searchDateFields"
                  :key="item.name"
                  :label="item.label"
                  :value="item.name"
                />
              </el-select>
              <el-date-picker
                v-model="searchFormSimp.dateFieldGe"
                type="month"
                value-format="yyyy-MM"
                placeholder="统计月份"
                class="el-date-editor--noprefix"
                :clearable="true">
              </el-date-picker>
              
              <el-select v-model="searchFormSimp.doctorType" placeholder="医生类型" clearable>
                <el-option v-for="item in combo.searchReportType"
                  :key="item.name"
                  :label="item.label"
                  :value="item.val"
                />
              </el-select>
              <!-- <el-input v-model="searchFormSimp.doctorName" placeholder="医生代码" /> -->
              <div style="display: auto">
                <el-row style="height: 100%; width: 100%">
                     <el-col :span="0">
                  <!-- <div style="display: flex; position: left: inherit;">
                    <el-checkbox v-model="searchFormSimp.examResultProp.dictValue" true-label="1" false-label=""/>
                    只统计阳性
                  </div> -->
                  </el-col>
                  <el-col style="height: 100%" :span="24">

                  <div style="display: flex; position: right;    justify-content: flex-end;">
                    <el-button type="primary" icon="el-icon-search" @click="search('simp')">查询</el-button>
                    <download-excel
                        types="xls"
                        :data="dataArr"
                        :fields="fields"
                        :name="exportName"
                        :worksheet="exportSheet"
                        :fetch='fetchData'

                        style="width: min-content"
                        
                    >
                            <el-button type="primary">导出</el-button>
                    </download-excel>
                  </div>
                  </el-col>
                  
                </el-row>
              </div>
            </el-form>
          </div>
  
          <div class="flex-item-fill" style="overflow: auto;">
  
            <el-table v-loading="loading" :data="grid.data" row-key="id" height="100%"
            stripe highlight-current-row>
              <el-table-column prop="day" label="日期" />
              <el-table-column prop="name" label="医生"  >
                <template slot-scope="scope">
                  <div>{{scope.row.name == undefined ? '-' : scope.row.name }}</div>
                </template>
              </el-table-column>

              <el-table-column v-for="(item, key, index) in grid.data[0].parts" :key="index">
                <template slot="header">{{item.part}}</template>
                <template slot-scope="scope">{{grid.data[scope.$index].parts[key].num}}</template>
              </el-table-column>
              <el-table-column prop="other" label="其他" />


            </el-table>
          </div>
          

        </div>
      </el-main>
    </el-container>
  
  </div>
  </template>
  
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"></style>
  
  <script>
  
  import model from "@/assets/scripts/gis/exammanagement/workreport/staff_part";
  export default model;
  </script>
  