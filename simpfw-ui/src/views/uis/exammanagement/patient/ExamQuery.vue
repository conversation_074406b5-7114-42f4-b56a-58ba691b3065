<template>

<el-container style="height: 100%">
  <el-aside width="280px" class="pcq-form-pane">
    <el-form label-position="left" label-width="80px" size="medium" style="height:100%">
      <el-card class="nested-card card-pane hnb-pane hnb-pane-column">
        <div slot="header" class="clearfix">
          <span>过滤器</span>
          <div class="nested-card-tools">
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </div>
        </div>
    
        <div class="box-body hnb-pane-fill" style="overflow: auto;">
          <div>
            <el-form-item label="检查日期" class="el-form-item-date-label">
            </el-form-item>          
            <el-form-item label-width="0" class="el-form-item-alcenter el-form-item-date-input">
              <el-date-picker
                v-model="searchForm.performedOnStart"
                type="date"
                placeholder="开始日期"
                class="el-date-editor--noprefix"
                style="width:45%"
                @change="onChanged_performedOnQuick()">
              </el-date-picker>
              -
              <el-date-picker
                v-model="searchForm.performedOnEnd"
                type="date"
                placeholder="结束日期"
                class="el-date-editor--noprefix"
                style="width: 45%"
                @change="onChanged_performedOnQuick()">
              </el-date-picker>
            </el-form-item>          
            <el-form-item label-width="0" class="form-item-thin el-form-item-srow">
              <el-radio-group v-model="searchForm.performedOnQuick" @change="onChanged_performedOnQuick">
                <el-radio v-for="item in searchForm.combo_performedOnQuick" :key="item" :label="item"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="姓名">
              <el-tooltip content="请输入*或空格进行模糊查询, 如: 张*三, *某四" placement="top-start" :open-delay="1000">
                <el-input v-model="searchForm.patient.nameLabel" placeholder="请输入*进行模糊查询" clearable></el-input>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="住院号">
              <el-input v-model="searchForm.encounter.encounterMRN" clearable></el-input>
            </el-form-item>
            <el-form-item label="登记号">
              <!-- <el-input v-model="searchForm.patient.patientNumbers"></el-input> -->
              <el-input v-model="searchForm.encounter.externalId" clearable :readonly="searchForm.pinPatient"></el-input>
            </el-form-item>
             <el-form-item label="就诊类型">
              <el-select v-model="searchForm.radOrder.encounterType" placeholder="全部" style="width:100%" filterable clearable>
                <el-option v-for="item in searchForm.combo_encounterTypes" v-bind:key="item.value"
                 :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申请科室">
              <el-select v-model="searchForm.radOrder.enteredAt" placeholder="全部" style="width:100%" allow-create filterable clearable>
                <el-option v-for="item in searchForm.combo_orgParts" v-bind:key="item.value"
                 :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label-width="0" class="el-form-item-srow el-form-item-modalities">
              <div>检查类型
                <el-checkbox label="true" v-model="searchForm.radOrder.imagingModalityCheckall" 
                  @change="onChanged_imagingModalityCheckall">全部</el-checkbox>
              </div>
              <div>
                <el-checkbox-group v-model="searchForm.radOrder.imagingModalities">
                  <span v-for="item in searchForm.combo_imagingModalities"
                   :key="item.value"><!--  v-if="'全部' != item.label" class="el-checkbox-wrap" -->
                    <el-checkbox :label="item.value" @change="onChanged_imagingModalities">{{item.label}}
                      <i :class="searchForm.imagingModalitiesExpand == item.value? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                       @click="expandSubImagingModalities($event, item.value)" 
                       v-if="!!item.children && item.children.length > 0"></i>
                    </el-checkbox>
                    <div class="pcq-form-chk-l1" v-if="searchForm.imagingModalitiesExpand == item.value">
                      <el-checkbox v-for="item1 in item.children" :key="item1.value" :label="item1.value"
                       @change="onChanged_imagingModalities">{{item1.label}}</el-checkbox>
                    </div>
                  </span>
                </el-checkbox-group>
              </div>
            </el-form-item>
            
            <div v-if="searchForm.allItems">
              <el-form-item label="检查号">
                <el-input v-model="searchForm.pacsPatientNumber" clearable></el-input>
              </el-form-item>
             <el-form-item label="性别">
                <el-radio-group v-model="searchForm.patient.genderLabel">
                  <el-radio v-for="item in searchForm.combo_gender" :key="item.value" :label="item.label"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="就诊号">
                <el-input v-model="searchForm.encounterNumber" clearable></el-input>
              </el-form-item>
              <el-form-item label="报告医生">
                <!-- <el-select v-model="searchForm.enteredByLabel" placeholder="全部" clearable style="width:100%">
                  <el-option v-for="item in searchForm.combo_doctors" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-input v-model="searchForm.enteredByLabel" clearable></el-input>
              </el-form-item>
              <el-form-item label="检查所见">
                <el-input v-model="searchForm.resultText" clearable placeholder="多关键字以逗号或空格间隔"></el-input>
              </el-form-item>
              <el-form-item label="报告结论">
                <el-input v-model="searchForm.resultInterpretation" clearable placeholder="多关键字以逗号或空格间隔"></el-input>
              </el-form-item>
              <el-form-item label="检查状态">
                <el-select v-model="searchForm.resultStatus" placeholder="全部" style="width:100%" filterable clearable>
                  <el-option v-for="item in searchForm.combo_resultStatus" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="检查部位">
                <!-- <el-select v-model="searchForm.radOrder.bodyPart" placeholder="全部" style="width:100%">
                  <el-option v-for="item in searchForm.combo_bodyPart" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-input v-model="searchForm.radOrder.bodyPartLabel" clearable></el-input>
              </el-form-item>
              <el-form-item label="申请医生">
                <!-- <el-select v-model="searchForm.radOrder.orderedBy" placeholder="全部" style="width:100%">
                  <el-option v-for="item in searchForm.combo_doctors" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-input v-model="searchForm.radOrder.orderedByLabel" clearable></el-input>
              </el-form-item>
              <el-form-item label="病区">
                <!-- <el-select v-model="searchForm.encounter.assignedWard" placeholder="全部" style="width:100%">
                  <el-option v-for="item in searchForm.combo_wards" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <el-input v-model="searchForm.encounter.assignedWard" clearable></el-input>

              </el-form-item>
              <el-form-item label="设备型号">
                <el-select v-model="searchForm.device" placeholder="全部" style="width:100%" filterable clearable>
                  <el-option v-for="item in searchForm.combo_imagingDevices" v-bind:key="item.value"
                   :label="item.label" :value="item.value"></el-option>
                </el-select>
                <!-- <el-input v-model="searchForm.deviceLabel"></el-input> -->
              </el-form-item>
            </div>
            <el-form-item label-width="0" class="el-form-item-alcenter el-form-item-srow">
              <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
              <el-tooltip :content="searchFormMoreLabel + '查询条件'" placement="top" :enterable="false">
                <el-button icon="el-icon-more" class="el-button--primary-second" @click="showAllItems">{{searchFormMoreLabel}}</el-button>
              </el-tooltip>
            </el-form-item>
            <div class="clear"></div>
          </div>
          <div class="hide">
            <el-input v-model="searchForm.orderFillerId" ></el-input>
            <el-input v-model="searchForm.routeToOneOff" ></el-input>
          </div>
        </div>
      </el-card>
    </el-form>
  </el-aside>

  <el-main class="pcq-body-pane" style="background-color:#EFEFEF">
    <div class="hnb-pane hnb-pane-column" style="height: 100%;">
      <!--  -->
      <el-card class="nested-card card-pane hnb-pane hnb-pane-column" style="max-height: 1024px;">
        <div slot="header" class="clearfix">
          <span>患者检查</span>
          <div class="nested-card-tools">
            <el-tooltip :content="'切换为' + altLayoutLabel" placement="left" :enterable="false">
              <el-button type="primary" icon="el-icon-menu" size="medium" @click="setLayout">{{ altLayoutLabel }}</el-button>
            </el-tooltip>
          </div>
        </div>

        <div class="box-body hnb-pane-fill" style="overflow: auto;">

          <querySheet ref="lastCheckResult" :params="searchForm.queryParams"
           @selectRow="selectQuerySheetItem"
           @hideCheckInfo="hideCheckInfo"
            />
        </div>
      </el-card>
      
      <el-card class="nested-card card-pane hnb-pane hnb-pane-column" v-if="4 == layout.type">
        <div slot="header" class="clearfix">
          <span>历史检查</span>
        </div>

        <div class="box-body">

          <querySheet ref="historyCheckResult" mode="PATIENT" :study="querySheetItem"
           @selectRow="selectQuerySheetItem"
           @hideCheckInfo="hideCheckInfo"
            />
        </div>
      </el-card>

    </div>
  </el-main>

  <!-- 检查详情 -->
  <PatientCheckQueryDetail :checkInfo="checkInfo" />
</el-container>

</template>

<script>
import '@/assets/styles/pacs/common.css';
import '@/assets/styles/pacs/exammanagement/patient/Index.css';

import model from "@/assets/scripts/gis/exammanagement/patient/Index";
export default model;
</script>
