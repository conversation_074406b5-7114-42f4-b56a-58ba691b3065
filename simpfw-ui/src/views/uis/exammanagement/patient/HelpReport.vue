<template>
<div style="margin: 4em auto 0;width: 800px;height: 100%">
  <el-form label-width="100px">
    <div class="form-row">
      <el-alert type="error" :closable="false"
       title="检查信息补登/报告补发"
       description="输入&quot;检查记录ID&quot;或者&quot;检查号&quot;或者&quot;登记日期&quot;。"
       ></el-alert>
    </div>
    <el-form-item label="操作">
      <el-select v-model="mainForm.opt" placeholder="选择"
       multiple collapse-tags>
        <el-option
          v-for="item in combo.opt"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="检查记录ID">
      <el-input v-model="mainForm.id" clearable style="width: 600px" />
    </el-form-item>
    <el-form-item label="检查号">
      <el-input v-model="mainForm.examNo" clearable style="width: 600px" />
    </el-form-item>
    <el-form-item label="登记日期">
      <el-input v-model="mainForm.createTime" clearable style="width: 600px" />
    </el-form-item>
    <div class="form-row align-center">
      <el-button type="danger" @click="doHelp()" :loading="processing">执行</el-button>
    </div>
  </el-form>
</div>
</template>

<script>
import request from '@/utils/request'

export default {
  data() {
    return {
      combo: {
        opt: [{value: 'ro', label: '补登记'}, {value: 'sr', label: '补发报告'}, {value: 'ssr', label: '生成报告'}]
      },
      mainForm: {
        opt: []
        , id: null
        , examNo: null
        , createTime: null
      },

      processing: false
    };
  },

  methods: {
    doHelp() {
      let args = [], fm = this.mainForm;
      args.push('opt=' + fm.opt.join(','));
      if(!!fm.id) {
        args.push('id=' + fm.id);
      }
      if(!!fm.examNo) {
        args.push('examNo=' + fm.examNo);
      }
      if(!!fm.createTime) {
        args.push('createTime=' + fm.createTime);
      }
      let url = '/exammanagement/examInfo/report/helpReport?' + args.join('&');
      this.processing = true;
      request.get(url, {timeout: 60 * 1000}).then(res => {
        this.processing = false;
        this.$modal.msg(res.msg);
      }).catch(() => this.processing = false);
    }
  }
}
</script>
