<template>
  <el-dialog title="选择检查" :visible.sync="opened" class="popupdialog" width="800px">
    <div style="height: 400px">
      <PatientSheet ref="patientSheet"
          :filter="examFilter"
          :restrict="examRestrict"
          @selectRow="change"
          @dblClickRow="select" />
    </div>
  
    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="select">确定</el-button>
            <el-button @click="close">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  
  </el-dialog>
  </template>
  
  <script>
  import {cloneDeep, mergeWith} from "lodash";
  
  import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
  //
  import PatientSheet from "@/views/gis/exammanagement/patient/comp/PatientSheet";
  import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus";

  const model = {
//    name: "ExamEquipRoom",
    
    extends: BaseDialogModel,

    components: {PatientSheet},
  
    props: {
    },
  
    data() {
      return {
        examFilter: {status: 0, datesCreated: 99},
        examRestrict: {resultStatusValues: [StatusDict.regist, StatusDict.exam, StatusDict.report]},
        origData: null,
        examInfo: null
      }
    },
  
    methods: {
      show(origData) {
        this.origData = origData;

        this.open();
      },
      change(exam) {
        this.examInfo = exam;
      },
      //
      select(mix) {
        if(!!mix && !!mix.id) {
          this.examInfo = mix;  
        }
        const exam = this.examInfo;
        if(!exam || !exam.id) {
          this.$modal.alert("请选择检查。");
          return ;
        }

        this.triggerBind("pick", this.origData, exam);

        this.close();
      }
    },
    //
    created() {
      //this.findEquipRoom();
    }
  };
  export default model;
  </script>
  