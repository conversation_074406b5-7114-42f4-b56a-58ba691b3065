<template>
<div v-show="visible">

  <span v-if="showLabel" class="examHistoryHeader" >历史检查</span>
  <el-table ref="dataGrid" :data="gridData" row-key="id"
    stripe highlight-current-row height="100%"
    @current-change="handleRowSelect"
    @row-dblclick="handleRowDblClick">

    <el-table-column prop="patientInfo.name" label="患者姓名" width="80" :formatter="colFmt_object" />
    <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50"/>
    <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" />

    <el-table-column prop="examNo" label="检查号" min-width="120" show-overflow-tooltip />
    <el-table-column  label="查看报告" min-width="80" >
        <template slot-scope="scope">
            
            <el-tooltip  effect="light" placement="top" >
                
                    <div style="width: 400px;" slot="content">
                        <h2 style="font-weight: bold;">检查所见:</h2>
                        <span style="font-size: medium; white-space: pre-wrap;"> {{ scope.row.examDesc }}</span>
                        <h2 style="font-weight: bold;">检查诊断:</h2>
                        <span style="font-size: medium; white-space: pre-wrap;"> {{ scope.row.examDiagnosis }}</span>
                        <el-button type="primary" size="small"  style="float: right;" @click="applyReport(scope.row)">拷贝</el-button>
                    </div>
                    <el-button  circle type="primary" icon="el-icon-search" size="mini"></el-button>
            </el-tooltip>
          </template>
    </el-table-column>
    <el-table-column prop="examTime" label="检查时间" min-width="160" show-overflow-tooltip />
    <el-table-column prop="examItem.dictLabel" label="检查项目" min-width="120" :formatter="colFmt_object" show-overflow-tooltip />
    <el-table-column prop="examParts.partsName" label="检查部位" min-width="100" :formatter="colFmt_object" show-overflow-tooltip />
   
     <el-table-column prop="reqDept.deptName" label="申请科室" min-width="140" 
         :formatter="colFmt_object"
          show-overflow-tooltip />
     <el-table-column prop="reqDoctor.nickName" label="申请医生" min-width="100" />
     <el-table-column prop="reportDoctor.nickName" label="报告医生" min-width="100" />
     <el-table-column prop="patientInfo.registNo" label="登记号" min-width="140"
     :formatter="colFmt_object" />
    <!-- <el-table-column label="操作"  fixed="right"  align="center" width="50" class-name="button-col" v-if="viewButtonVisible">
      <template slot-scope="scope">
        <el-button title="查看报告"
          icon="el-icon-document"
          @click="viewReport(scope.row)"
        ></el-button>
      </template>
    </el-table-column> -->

  </el-table>

</div>

</template>

<style scoped>
    .examHistoryHeader {
        position: fixed;
        margin-top: -30px;
    }
</style>
<script>
//检查信息接口
import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";

import BaseGridModel from "@/assets/scripts/pacs/BaseGridModel";
import {StatusDict} from "@/assets/scripts/pacs/exammanagement/examinfo/ResultStatus"

const model = {
  props: {
    showLabel: {type: Boolean, default: true}
  },

  data() {
    return {
      visible: true,//false,
      examInfo: null,
      gridData: []
    }
  },

  methods: {
    show() {
      //this.visible = !this.visible;
      this.getList();
    },
    //查询
    getList(exam) {
      //console.log(exam);
      //主要检查
      if(exam) {
        if(this.examInfo && this.examInfo.id === exam.id) {
          return;
        }
        this.examInfo = exam;
      }
      //可见时才查询
      let patientInfo = this.examInfo && this.examInfo.patientInfo || null;
      if(!this.visible || !patientInfo || !patientInfo.registNo) {
        return;
      }
      //患者已报告的检查
      const params = {patientInfo: {
        registNo: patientInfo.registNo
      }
      , resultStatusValues: [
          StatusDict.report,
          StatusDict.audit,
          StatusDict.reaudit,
          StatusDict.print,
          StatusDict.archive
      ]
      , pageSize: 9999};
      eiapi.find(params).then(res => {
        this.gridData = res.rows;
      });
    },
    //格式对象
    colFmt_object(row, col, val, idx) {
      return BaseGridModel.methods.colFmt_object(row, col, val, idx);
    },
    //年龄列
    colFmt_age(row) {
      const pat = row.patientInfo;
      return pat && pat.age? (pat.age + (pat.ageUnit? pat.ageUnit.dictLabel : '')) : (pat? pat.age : '');
    },
    //选择/高亮行
    handleRowSelect(currRow, prevRow) {
      this.triggerBind("select", currRow);
    },
    //选择/高亮行
    handleRowDblClick(row, column, event) {
      this.triggerBind("row-dblclick", row);
    },
    applyReport(row){
        this.triggerBind('applyReport',row);
    },
    //查看报告
    viewReport(row) {
      //console.log(row);
      //读取检查
      eiapi.get(row.id).then(res => {
        //读取影像
        this.triggerBind("view", res.data);
      });
    }
  },

  computed: {
    viewButtonVisible() {
      const lis = this.$listeners;
      return !!lis && ("view" in lis);
    }
  }
};
export default model;
</script>
