
<template>
<el-dialog title="拆分报告" :visible.sync="opened" width="800px">
  <el-form ref="mainForm" :model="mainForm" label-width="100px" class="tight-form">

    <el-row style="padding: 4px 8px">
      <el-col :span="4">姓名：{{mainForm.patientInfo.name}}</el-col>
      <el-col :span="4">性别：{{mainForm.patientInfo.gender.dictLabel}}</el-col>
      <el-col :span="4">年龄：{{mainForm.patientInfo.age}}
      {{mainForm.patientInfo.age&&mainForm.patientInfo.ageUnit?mainForm.patientInfo.ageUnit.dictLabel : null}}</el-col>
      <el-col :span="12">&nbsp;</el-col>
    </el-row>
    <div style="padding: 4px 8px">
      <el-button type="primary" @click="handleCombine">合并</el-button>
    </div>

    <el-table :data="gridData" :height="400" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="检查部位" prop="examParts.partsName" min-width="240" align="left" :formatter="colFmt_object" />
      <el-table-column label="检查机房" prop="equipRoom.roomName" min-width="160">
        <template slot-scope="scope">
          <el-select v-model="scope.row.equipRoom.roomCode" size="small" placeholder="请选择">
            <el-option
              v-for="item in combo_equipRoom"
              :key="item.roomCode"
              :label="item.roomName"
              :value="item.roomCode">
            </el-option>
          </el-select>          
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80" class-name="button-col">
        <template slot-scope="scope" v-if="scope.row.examParts.length > 1">
          <el-button type="text"
            @click="handleSplit(scope.row)">拆分</el-button>
        </template>
      </el-table-column>
    </el-table>

  </el-form>

  <div slot="footer" class="dialog-footer">
    <div class="foot-tools">
      <el-row>
        <el-col :span="24">
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="close">关闭</el-button>
        </el-col>
      </el-row>
    </div>
  </div>

</el-dialog>
</template>

<script>
import {cloneDeep} from "lodash";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import BaseGridModel from '@/assets/scripts/pacs/BaseGridModel';
import * as api from "@/assets/scripts/gis/exammanagement/examinfo/api";
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";

const mixins_grid = {
  methods: {
    colFmt_object: BaseGridModel.methods.colFmt_object
  }
};

function emptyForm() {
  return {
    id: null,
    patientInfo: {
      id: null,
      gender: {},
      age: null,
      ageUnit: {}
    }
  };
}

const model = {
  extends: BaseDialogModel,

  mixins: [mixins_grid],

  data() {
    return {
      mainForm: emptyForm(),
      gridData: [],
      currentSelection: null,
      combo_equipRoom: []
    }
  },

  methods: {
    fill(exam) {
      if(!exam || !exam.id) {
        this.$modal.alert("请选择检查。");
        return;
      }
      //
      if(2 === exam.status) {
        this.$modal.alert("该检查已删除。");
        return;
      }
      //是否含多部位
      if(!exam.examParts || exam.examParts.length < 2) {
        this.$modal.alert("该检查不含多个检查部位，不支持拆分。");
        return;
      }
      //
      const resultStatus = exam.resultStatus;
      if(resultStatus && resultStatus.dictValue && !/^[01]$/.test(resultStatus.dictValue)) {
        this.$modal.alert("该检查无法上报，原因：检查状态为" + resultStatus.dictLabel + "。");
        return;
      }
      //避免修改源信息
      exam = cloneDeep(exam);
      //检查信息
      this.mainForm = exam;
      //表格信息
      this.gridData = [exam];
      //
      this.open();
    },
    //提交拆分
    submitForm() {
      let gridData = this.gridData;
      if(gridData.length < 2) {
        this.$modal.alert("没有拆分的数据。");
        return;
      }
      //确认
      this.$modal.confirm("是否确定拆分？").then(() => {
        return api.split(gridData);
      }).then(res => {
        this.$modal.msgSuccess("拆分完成。");
        this.triggerBind("success");
        this.close();
      });
    },
    //勾选触发
    handleSelectionChange(rows) {
      this.currentSelection = rows;
    },
    //拆分
    handleSplit(origRow) {
      let gridData = this.gridData, examParts = origRow.examParts;
      if(examParts.length < 2) {
        return;
      }
      
      for(let i = 1; i < examParts.length; i ++) {
        let row = cloneDeep(origRow);
        row.id = null;
        row.examParts = [examParts[i]];
        gridData.push(row);
      }
      origRow.examParts = [examParts[0]];
      console.log(gridData);
    },
    //合并
    handleCombine() {
      if(!this.currentSelection || this.currentSelection.length < 2) {
        this.$modal.alert("请勾选至少2个检查。");
        return;
      }
      let rows = this.currentSelection, gridData = this.gridData;;
      let tarRow = rows[0];
      for(let i = 1; i < rows.length; i ++) {
        let row = rows[i], examParts = row.examParts;
        if(!tarRow.id && row.id) { tarRow.id = row.id; }
        tarRow.examParts = tarRow.examParts.concat(examParts);
        //
        let rowi = gridData.findIndex(r => r.examParts[0].id === examParts[0].id)
        if(-1 != rowi) {
          gridData.splice(rowi, 1);
        }
      }
      console.log(gridData);
    },

    findEquipRoom() {
      findRoom({}).then(res => {
        this.combo_equipRoom = res && res.rows || [];
      });
    }
  },

  created() {
    this.findEquipRoom();
  }
};
export default model;
</script>

