<template>
<el-dialog title="选择医嘱" :visible.sync="opened" width="1200px">
<div>
  <el-table :data="gridData" row-key="ordId"
    ref="multipleTable"
    stripe highlight-current-row
    height="400"
    
    @row-dblclick="selectRow"
    @selection-change="handleSelectionChange"
    @select="selectCheckbox"
    >
    <el-table-column type="selection" width="55"/>
    <el-table-column prop="patientInfo.name" label="姓名" width="80" show-overflow-tooltip />
    <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50" />
    <el-table-column prop="patientInfo.age" label="年龄" width="60" />
    <el-table-column prop="ordName" label="医嘱"  width="380" />
    <el-table-column prop="reqTime" label="医嘱时间" width="180" />
    <el-table-column prop="examCost" label="费用" width="80" />
    <el-table-column prop="ordBillStatus" label="缴费状态" width="80" />
    <el-table-column prop="reqDoctor.nickName" label="申请医生" width="100" />
    <el-table-column prop="reqDept.deptName" label="申请科室" width="120" />
    <el-table-column prop="examDept.deptName" label="接收科室" width="120" />
                                                                     
    <!-- <el-table-column label="操作" fixed="right" align="center" width="60" class-name="button-col">
      <template slot-scope="scope">
        <el-button title="选择" icon="el-icon-plus" @click="selectRow(scope.row)"></el-button>
      </template>
    </el-table-column> -->
  </el-table>
  <div style="margin-top: 20px">
    <el-button type="primary" @click="submit">确定</el-button>
    <el-button type="warning" @click="cancel">取消</el-button>
  </div>
</div>
</el-dialog>
</template>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel"
export default {
  extends: BaseDialogModel,

  data() {
    return {
      gridData: [],
      multipleSelection:[],
    }
  },

  methods: {
    show(gridData) {
      // 使用sort方法进行排序
      gridData.sort((a, b) => {
          let timeA = a.reqTime;
          let timeB = b.reqTime;
          return new Date(timeB) - new Date(timeA);
      });
      
      this.gridData = gridData;
      if(gridData && gridData.length) {
        this.open();
      }
    },


    async selectCheckbox (selection, row){

        if(row.ordBillStatus=='未收费'){
            // if(!row.tick){
            //     await this.$confirm('该条数据未收费,请确认是否添加登记?', '提示', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'warning'
            //         }).then(() => {
            //             //nothing
            //             row.tick=true
            //         }).catch(() => {
            //         this.$message({
            //             type: 'info',
            //             message: '已取消删除'
                        
            //         });   
            //         row.tick=false
            //         });


            //         if(!row.tick){
            //             this.$refs.multipleTable.toggleRowSelection(row);
            //         }
            // }else{
            //     row.tick=!row.tick
            // }
            
        }else{

            //nothing
        }
    },
    selectRow(row) {
      //this.triggerBind("pick", row);
      //this.gridData = [];
      //this.close();
      this.toggleSelection(row)
    },
    handleSelectionChange(val) {
        this.multipleSelection = val;
      },
    toggleSelection(row) {
        if (row) {      
            if(row.ordBillStatus=='未收费'){
                if(!row.tick){
                    this.$confirm('该条数据未收费,请确认是否添加登记?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                        }).then(() => {
                            row.tick = true
                            this.$refs.multipleTable.toggleRowSelection(row);
                        }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消删除'
                        });   
                            row.tick = false
                        });
                }else {
                    row.tick = false
                    this.$refs.multipleTable.toggleRowSelection(row);
                }
                
            
            }else {
                row.tick = !row.tick
                this.$refs.multipleTable.toggleRowSelection(row);
            }
            
          
        } else {

         // this.$refs.multipleTable.clearSelection();
        }
    },
    submit(){

        var tempRow={
            admNo:[],
            admSeriesNum:[],
            ordId:[],
            ordName:[],
            examCost:null,
            tick:false,
            examParts:[]
        }
        
       this.multipleSelection.forEach(row=>{
           console.log(row)
            tempRow.admNo.push(row.admNo)
            tempRow.admSeriesNum.push(row.admSeriesNum)
            tempRow.ordId.push(row.ordId)
            tempRow.ordName.push(row.ordName)
            tempRow.examParts.push(row.examParts[0])
            tempRow.examCost +=  row.examCost
            //tempRow.tick ||= row.tick
            //缴费状态暂不关注
            
        })
        
        //console.log(tempRow)

        var newRow = this.multipleSelection[0]
        newRow.admNo = [...new Set(tempRow.admNo)].join("@")
        newRow.admSeriesNum =  [...new Set(tempRow.admSeriesNum)].join("@")
        newRow.ordId =  tempRow.ordId.join("@")
        newRow.ordName =  tempRow.ordName.join(",")
        newRow.examCost = tempRow.examCost
        newRow.examParts = tempRow.examParts
        console.log(newRow)
        this.triggerBind("pick", newRow);
        this.gridData = [];
        this.close();
    },
    cancel(){
      this.gridData = [];
      this.close();
    },
  }
};
</script>
