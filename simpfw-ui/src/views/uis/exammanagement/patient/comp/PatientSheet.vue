<template>
  <el-container ref="patientListWrap" class="nested-container" style="height: 100%">
    <el-aside
      class="inner-container-aside"
      :class="{ 'inner-container-aside-collapse': !searchFormVisible }"
      width="200px"
    >
      <el-form
        label-position="left"
        label-width="5em"
        style="height: 100%"
        class="tight-form exam-search-form"
        @keyup.enter.native="search"
      >
        <el-card
          class="nested-card hei100 card-pane"
          :body-style="{
            overflow: 'hidden',
            display: 'flex',
            'flex-direction': 'column',
          }"
        >
          <div slot="header">
            <span class="nested-card-header-title">过滤器</span>
            <div class="nested-card-tools">
              <el-checkbox v-model="rememberFilters" @change="handleRememberFiltersChange"
                >记住选择</el-checkbox
              >
            </div>
          </div>

          <div class="flex-item-fill" style="overflow: auto">
            <el-scrollbar class="scrollpane scrollpane-h"
              ><!--  -->
              <div>
                <el-form-item
                  label="登记日期"
                  class="el-form-item-date-label"
                ></el-form-item>
                <el-form-item
                  label-width="0"
                  class="el-form-item-alcenter el-form-item-date-input"
                >
                  <el-date-picker
                    v-model="searchForm.createTimeGe"
                    type="date"
                    placeholder="开始日期"
                    class="el-date-editor--noprefix"
                    style="width: 45%"
                    :clearable="true"
                  >
                  </el-date-picker>
                  -
                  <el-date-picker
                    v-model="searchForm.createTimeLt"
                    type="date"
                    placeholder="结束日期"
                    class="el-date-editor--noprefix"
                    style="width: 45%"
                    :clearable="true"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  label-width="时间选择"
                  class="form-item-thin el-form-item-srow"
                >
                </el-form-item>
                <el-form-item label="姓名">
                  <el-tooltip
                    content="请输入*或空格进行模糊查询, 如: 张*三, *某四"
                    placement="top-start"
                    :open-delay="1000"
                  >
                    <el-input
                      v-model="searchForm.patientInfo.name"
                      placeholder="请输入*进行模糊查询"
                      clearable
                    />
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="住院号">
                  <el-input v-model="searchForm.inpNo" clearable />
                </el-form-item>
                <el-form-item label="登记号">
                  <el-input
                    v-model="searchForm.patientInfo.registNo"
                    clearable
                    :readonly="searchForm.pinPatient"
                  ></el-input>
                </el-form-item>
                <el-form-item label="就诊类型">
                  <el-select
                    v-model="searchForm.inpTypeValues"
                    placeholder="全部"
                    multiple
                    collapse-tags
                    class="select-multi-sing"
                  >
                    <el-option
                      v-for="dict in ctrlData.dict.uis_inp_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="申请科室">
                  <Treeselect v-model="searchForm.reqDept.deptId" :options="deptTreeData" :show-count="true"
                    placeholder="选择" />
                </el-form-item> -->

                <el-form-item
                  label="检查类别"
                  class="el-form-item-srow el-form-item-modalities"
                >
                  <el-select v-model="searchForm.examModality.dictValue" clearable>
                    <el-option
                      v-for="dict in ctrlData.dict.uis_exam_modality"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="检查项目"
                  class="el-form-item-srow el-form-item-modalities"
                >
                  <el-select v-model="searchForm.examItemCodes" clearable multiple>
                    <el-option
                      v-for="dict in ctrlData.dict.uis_exam_item"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="检查进度"
                  class="el-form-item-srow el-form-item-resultStatusValues"
                >
                  <el-select
                    v-model="searchForm.resultStatusValues"
                    clearable
                    multiple
                    collapse-tags
                    class="select-multi-sing"
                    placeholder="检查进度"
                  >
                    <el-option
                      v-for="dict in combo_resultStatus"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                    <el-option label="已删除" value="-2" />
                  </el-select>
                </el-form-item>

                <el-form-item label="检查号">
                  <el-input v-model="searchForm.examNo" clearable></el-input>
                </el-form-item>
                <el-form-item label="性别">
                  <el-select v-model="searchForm.patientInfo.gender.dictValue" clearable>
                    <el-option
                      v-for="dict in dict.type.uis_gender_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="就诊号">
                  <el-input v-model="searchForm.outpNo" clearable></el-input>
                </el-form-item>
                <el-form-item label="申请医生">
                  <el-input
                    v-model="searchForm.reqDoctor.nickName"
                    clearable
                    class="input-field-narr"
                    @clear="clearReqDoctor"
                  >
                    <el-button
                      slot="append"
                      size="mini"
                      icon="el-icon-user-solid"
                      @click="toPickUser({ multiple: false, target: 'reqDoctor' })"
                    ></el-button>
                  </el-input>
                </el-form-item>
                <el-form-item label="检查医生">
                  <el-input v-model="searchForm.examDoctor.nickName" clearable></el-input>
                </el-form-item>
                <el-form-item label="报告医生">
                  <el-input
                    v-model="searchForm.reportDoctor.nickName"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核医生">
                  <el-input
                    v-model="searchForm.auditDoctor.nickName"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="操作者">
                  <el-input v-model="searchForm.operDoctor.nickName" clearable></el-input>
                </el-form-item>
                <el-form-item label="检查所见">
                  <el-input
                    v-model="searchForm.examDesc"
                    clearable
                    placeholder="多关键字以逗号或空格间隔"
                  ></el-input>
                </el-form-item>
                <el-form-item label="检查诊断">
                  <el-input
                    v-model="searchForm.examDiagnosis"
                    clearable
                    placeholder="多关键字以逗号或空格间隔"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="阴阳性"
                  class="el-form-item-srow el-form-item-examResultProp"
                >
                  <el-select v-model="searchForm.examResultProp.dictValue" clearable>
                    <el-option
                      v-for="dict in dict.type.uis_exam_result_prop"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </div> </el-scrollbar
            ><!--  -->
          </div>
          <div style="padding: 4px 0; text-align: right">
            <el-button type="primary" icon="el-icon-search" @click="search"
              >查询</el-button
            >
          </div>
        </el-card>
      </el-form>
    </el-aside>

    <el-main>
      <div class="data-container flex-container-column">
        <div class="searchFormBar">
          <el-form class="exam-search-form exam-search-form-simp">
            <el-tooltip effect="dark" content="详细查询" placement="top">
              <svg-icon
                icon-class="list-border"
                class-name="search-form-toggler"
                @click="toggleSearchForm"
              />
            </el-tooltip>

            <el-select
              v-model="searchFormSimp.propName"
              style="width: 90px"
              @change="focusPropValue"
            >
              <el-option
                v-for="dict in searchFormSimp.combo_props"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-input
              ref="propValue"
              v-model="searchFormSimp.propValue"
              @keyup.enter.native="getList"
              @blur="checkValue"
              clearable
              style="width: 115px"
            />
            <el-select
              v-model="searchFormSimp.resultStatusValues"
              @change="searchFormChange"
              style="width: 100px"
              clearable
              multiple
              collapse-tags
              class="select-multi-sing"
              placeholder="检查进度"
            >
              <el-option
                v-for="dict in combo_resultStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
              <el-option label="已删除" value="-2" />
            </el-select>
            <el-select
              v-model="createTimeValue"
              style="width: 90px"
              @change="searchFormChange"
            >
              <el-option
                v-for="dict in createTime_props"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-date-picker
              style="width: 200px"
              v-if="createTimeValue == -1 ? true : false"
              @change="changeDatePicker"
              v-model="startDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>

            <el-select
              v-model="searchFormSimp.examItemCodes"
              @change="searchFormChange"
              style="width: 180px"
              clearable
              multiple
              collapse-tags
              placeholder="检查项目"
            >
              <el-option
                v-for="dict in ctrlData.dict.uis_exam_item"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>

            <!-- <el-select v-model="searchFormSimp.equipExamItemCode" v-show="isPendingReport()||isPendingAudit()" style="width: 105px;" collapse-tags class="select-multi-sing" placeholder="检查项目"
            @change="searchFormChange">
                <el-option
                  v-for="dict in ctrlData.dict.uis_exam_item"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select> -->
            <el-tooltip class="item" effect="dark" content="查询" placement="bottom">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="search('simp')"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                disabledUpload() ? '请仅选择一个检查项目后上传' : '点击批量上传文件'
              "
              placement="bottom"
            >
              <el-button
                v-hasPermi="['report:reportImage:upload']"
                v-show="isPendingReport()"
                :disabled="disabledUpload()"
                :loading="uploading"
                type="primary"
                icon="el-icon-upload2"
                @click="reportImageUploads"
              >
                批量上传影像
              </el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                disabledUpload() ? '请仅选择一个检查项目后上传' : '点击批量上传文件'
              "
              placement="bottom"
            >
              <el-button
                v-hasPermi="['report:reportPdf:upload']"
                v-show="isPendingReport()"
                :disabled="disabledUpload()"
                :loading="uploading"
                type="primary"
                icon="el-icon-upload2"
                @click="reportPdfUploads"
                >批量上传报告
              </el-button>
            </el-tooltip>
            <!-- <el-button v-hasPermi="['report:reportImage:upload','report:reportPdf:upload']" v-show="sheetType==2?true:false" :disabled="searchForm.equipExamItemCode=='NYST'" :loading="auditing" type="primary" @click="batchAudit">批量审核</el-button> -->
            <!-- <el-tooltip class="item" effect="dark" content="偏好配置" placement="bottom">
              <el-button type="primary" icon="el-icon-s-tools" @click="prepareSearchOpt"></el-button></el-tooltip> -->
          </el-form>
        </div>
        <div class="flex-item-fill scroll-auto" style="overflow: auto">
          <el-table
            ref="dataGrid"
            class="custom-table"
            v-loading="loading"
            :data="grid.data"
            row-key="id"
            stripe
            highlight-current-row
            height="100%"
            style="min-height: 200px"
            :cell-class-name="colStyle"
            @row-contextmenu="showAction"
            @current-change="handleCurrentChange"
            @row-dblclick="handleRowDblClick"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column
              type="selection"
              width="55"
              v-if="isPendingAudit()"
              :reserve-selection="true"
              :row-key="(row) => row.id"
              highlight-current-row="true"
              :selectable="selectable"
            ></el-table-column>
            <el-table-column
              prop="patientInfo.name"
              label="患者姓名"
              width="80"
              :formatter="colFmt_object"
            />
            <el-table-column
              prop="patientInfo.gender.dictLabel"
              label="性别"
              width="50"
              :formatter="colFmt_dictData"
            />
            <!-- <el-table-column prop="patientInfo.age" label="年龄" width="60" :formatter="colFmt_age" /> -->
            <el-table-column
              prop="examAge"
              label="年龄"
              width="60"
              :formatter="colFmt_exam_age"
            />
            <el-table-column prop="resultStatus.dictLabel" label="工作状态" width="90">
              <template slot-scope="scope" v-if="!!scope.row.resultStatus">
                <!-- <i class="el-icon-error state-icon-err"
                    v-if="2 === scope.row.status || '10' === scope.row.resultStatus.dictValue"></i>
                <i :class="'el-icon-success' + ' result-status-icon-' + scope.row.resultStatus.dictValue"
                    v-else></i> -->
                <span style="margin-left: 4px">{{
                  2 === scope.row.status
                    ? "已删除"
                    : !!scope.row.resultStatus
                    ? scope.row.resultStatus.dictLabel
                    : null
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileInfos.length"
              label="文件数"
              align="center"
              width="70"
            >
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.fileInfos &&
                    scope.row.fileInfos.length > 0 &&
                    reportable(scope.row)
                  "
                  class="clickable-link"
                  style="cursor: pointer; color: #409eff"
                  @click="goToOcrConfirm(scope.row)"
                >
                  {{ scope.row.fileInfos.length }}
                </span>
                <span v-else>{{
                  scope.row.fileInfos ? scope.row.fileInfos.length : 0
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="patientInfo.registNo"
              label="登记号"
              min-width="100"
              :formatter="colFmt_object"
            />
            <!-- <el-table-column prop="examParts.partsName" label="检查部位" width="100" :formatter="colFmt_object" show-overflow-tooltip  /> -->
            <!-- <el-table-column prop="examDiag" label="检查结论" width="120" show-overflow-tooltip /> -->
            <el-table-column prop="examItem.dictLabel" label="检查项目" width="130" />
            <el-table-column
              prop="imageNo"
              v-hasPermi="['report:reportImage:upload']"
              label="影像编号"
              width="140"
            />
            <el-table-column
              prop="reportUploadFilenameStr"
              :show-overflow-tooltip="true"
              v-hasPermi="['report:reportPdf:upload']"
              label="报告文件名"
              width="140"
            />
            <el-table-column
              prop="reportUploadFilenameStr"
              :show-overflow-tooltip="true"
              v-hasPermi="['report:reportImage:upload']"
              label="影像文件名"
              width="140"
            >
              <template slot-scope="scope">
                {{ getImageName(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="reportNo"
              v-hasPermi="['report:reportPdf:upload']"
              label="报告编号"
              width="140"
            />
            <!-- <el-table-column prop="reportUrlJpgName" v-hasPermi="['report:reportImage:upload']" label="影像文件名" width="140" /> -->
            <el-table-column prop="inpNo" label="住院号" width="120" />
            <el-table-column prop="examNo" label="检查号" width="120" />
            <el-table-column prop="imageNum" label="图像数量" width="80" />
            <el-table-column
              prop="examModality.dictLabel"
              label="检查类型"
              width="140"
              :formatter="colFmt_dictData"
            />
            <!-- <el-table-column prop="callInfo.callNo" label="排队号" width="70" :formatter="colFmt_object" /> -->
            <el-table-column prop="unexecuteOrdIds" label="医嘱状态" width="100">
              <template slot-scope="scope" v-if="Array.isArray(scope.row.ordIds) && scope.row.ordIds.length > 0">
                <span style="margin-left: 4px">{{
                    undefined === scope.row.unexecuteOrdIds || scope.row.unexecuteOrdIds.length === 0
                      ? "已执行"
                      : "未执行"
                  }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="statusOfSendReport" label="发送状态" width="100">
              <template slot-scope="scope" v-if="!!scope.row.statusOfSendReport">
                <span style="margin-left: 4px">{{
                  1 == scope.row.statusOfSendReport || 3 == scope.row.statusOfSendReport
                    ? "成功"
                    : "失败"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="creator.nickName" label="登记人员" min-width="160" />
            <el-table-column prop="createTime" label="登记时间" min-width="160" />
            <el-table-column prop="examTime" label="检查时间" min-width="160" />
            <el-table-column prop="examSerialNo" label="检查流水号" width="140" />
            <el-table-column
              prop="equipRoom.roomName"
              label="检查房间"
              width="100"
              :formatter="colFmt_equipRoom"
            />

            <el-table-column
              label="操作"
              v-if="isPendingReport() || isPendingAudit()"
              fixed="right"
              align="center"
              class-name="small-padding"
              width="120"
            >
            <template slot="header" slot-scope="scope">
                <span>操作</span>
                <!-- <el-button type="info" size="mini" @click="showHelp">
                  <i class="el-icon-question"></i>
                </el-button> -->
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-question"
                  title="帮助文档"
                  style="margin-right: 5px"
                  @click="help()"
                ></el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('uploadPdf', scope.row) && isPendingReport()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('uploadPdf', scope.row)"
                  >上传报告
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('reloadPdf', scope.row) && isPendingAudit()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('reloadPdf', scope.row)"
                  >已上传(重传)
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('uploadJpg', scope.row) && isPendingReport()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('uploadJpg', scope.row)"
                  >上传影像
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  v-show="
                    getReportDataSourceEdit('reloadJpg', scope.row) && isPendingAudit()
                  "
                  icon="el-icon-plus"
                  @click="reportOperate('reloadJpg', scope.row)"
                  >已上传(重传)
                </el-button>
                <!-- <el-button size="mini" type="text" icon="el-icon-plus" @click="reportOperate('upload', scope.row)">上传报告</el-button> -->
                <!-- <el-button size="mini" type="text" :disabled="getReportDataSourceEdit('view', scope.row)" v-show="!limitUpload" icon="el-icon-view"
                  @click="reportOperate('view', scope.row)">查看报告</el-button> -->
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              v-else-if="isRegist() || isNormal()"
              fixed="right"
              align="center"
              class-name="small-padding"
              width="120"
            >
              <template slot="header" slot-scope="scope">
                <span>操作</span>
                <!-- <el-button type="info" size="mini" @click="showHelp">
                  <i class="el-icon-question"></i>
                </el-button> -->
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-question"
                  title="帮助文档"
                  style="margin-right: 5px"
                  @click="help()"
                ></el-button>
              </template>
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-setting"
                  v-show="Array.isArray(scope.row.ordIds) && scope.row.ordIds.length > 0
                    && Array.isArray(scope.row.unexecuteOrdIds) && scope.row.unexecuteOrdIds.length > 0"
                  @click="ordExecute(scope.row)"
                  >执行
                </el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column label="操作" align="center" width="80" class-name="button-col" fixed="right">
              <template slot-scope="scope">
                <el-button title="编辑"
                  icon="el-icon-edit"
                  @click="handleEdit(scope.row)"
                ></el-button>
                <el-button v-if="2 === scope.row.status"
                  title="撤销删除"
                  icon="el-icon-refresh-left"
                  @click="undoDelete(scope.row)"
                  v-hasPermi="['exam-info:delete']"
                  ></el-button>
                <el-button v-else
                  title="删除"
                  icon="el-icon-delete"
                  @click="verifyForDelete(scope.row)"
                  v-hasPermi="['exam-info:delete']"
                  ></el-button>
              </template>
            </el-table-column> -->
          </el-table>
          <!-- 右键菜单 -->
          <Contextmenu ref="tableContextmenu" :items="actions" @select="handleAction" />
          <!-- 操作密码验证 -->
          <OperationAuth ref="operAuth" @success="handleDelete" />
          <!-- 查询偏好设置 -->
          <PatientListSearchOptions
            ref="searchOpt"
            cacheKey="exam::patientSheetSearchOptions"
            @change="getList"
          />

          <!-- 上传检查影像 -->
          <ReportUploader ref="reportUploader" />
          <ReportUploaderBatch
            ref="reportUploaderBatch"
            @reportUploadStateUpdate="reportUploadStateUpdate"
          />

          <!-- 报告预览 -->
          <!-- <ReportViewer ref="reportViewer" :viewLoadReport="false"/> -->

          <!-- 扫码 -->
          <LinksignPopup :qr="qr" />

          <!-- 选择医生 -->
          <UserPicker ref="userPicker" @pick="pickUser" />
        </div>

        <pagination
          small
          layout="total, prev, pager, next"
          :total="grid.pager.total"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          @pagination="getList"
        />
      </div>
    </el-main>
  </el-container>
</template>
<style scoped src="@/assets/styles/pacs/exammanagement/patient/Index.css"></style>
<style
  scoped
  src="@/assets/styles/pacs/exammanagement/patient/exam-info-icon.css"
></style>
<style scoped>
.searchFormBar {
  margin-bottom: 4px;
}

.searchFormBar >>> .el-button {
  margin-left: 4px;
  padding: 10px;
}
</style>

<style>
/* 更具体的选择器 */
.el-table.custom-table td,
.el-table.custom-table th {
  border-right: 1px solid #f5f5fa !important;
  /* border-color: red !important; */
}

/* 设置表格行的padding为5px */
.el-table.custom-table td {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}
</style>

<script>
import "@/assets/styles/pacs/exammanagement/patient/Index.css";

import model from "@/assets/scripts/uis/exammanagement/patient/comp/PatientSheet";

export default model;
</script>
