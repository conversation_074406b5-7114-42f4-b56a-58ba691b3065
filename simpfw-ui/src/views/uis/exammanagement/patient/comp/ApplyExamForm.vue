<template>
  <div>
    <h3 class="apply-exam-form-title">电子申请单</h3>
    <el-form label-width="100px" class="tight-form exam-view-form apply-exam-form-sheet">
  
      <el-row>
        <el-col :span="8">
          <el-form-item label="患者姓名:">{{!!examInfo.patientInfo? examInfo.patientInfo.name : null}}</el-form-item>
        </el-col>
  
        <el-col :span="8">
          <el-form-item label="患者性别:">{{!!examInfo.patientInfo && !!examInfo.patientInfo.gender? examInfo.patientInfo.gender.dictLabel : null}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="出生日期:">{{!!examInfo.patientInfo && !!examInfo.patientInfo.birthday? examInfo.patientInfo.birthday.split(" ")[0] : null}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="影像号:"></el-form-item>
        </el-col>
  
        <el-col :span="8">
          <el-form-item label="登记号:">{{!!examInfo.patientInfo? examInfo.patientInfo.registNo : null}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话:">{{!!examInfo.patientInfo? examInfo.patientInfo.phone : null}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="患者来源:">{{!!examInfo.inpType? examInfo.inpType.dictLabel : null}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="住院号:">{{!!examInfo.patientInfo? examInfo.patientInfo.inpNo : null}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="床号:">{{examInfo.bedNo}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="联系地址:">{{!!examInfo.patientInfo? examInfo.patientInfo.address : null}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="诊疗卡号:">{{!!examInfo.patientInfo? examInfo.patientInfo.medicalRecordNo : null}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请单号:">{{examInfo.ordId}}</el-form-item>
        </el-col>
        <el-col :span="8">
          &nbsp;
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="申请科室:">{{!!examInfo.reqDept? examInfo.reqDept.deptName: '-'}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请医生:">{{!!examInfo.reqDoctor? examInfo.reqDoctor.nickName : '-'}}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请时间:">{{examInfo.reqTime}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="检查项目:">
            <template v-if="!!examInfo.ordName">
              <div>【申请检查项目】</div>
              <div>{{examInfo.ordName}}</div>
            </template>
            <div>【登记检查项目】</div>
            <div>{{!!examInfo.examParts? examInfo.examParts.map(e => e.partsName).join(",") : '-'}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="临床诊断:">{{examInfo.clinicDiagnosis}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="病史:">{{examInfo.clinicDisease}}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="体征:">-</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="其它信息:">-</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注:">{{examInfo.noteInfo}}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  
  </div>
  </template>
  
  <style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>
  
  <script>
  import {mergeWith as mergeWithDeep} from "lodash";
  
  import {mergeWithNotNull} from "@/utils/common";
  
  import * as eiapi from "@/assets/scripts/gis/exammanagement/examinfo/api";
  
  import {emptyRegist} from "@/assets/scripts/gis/exammanagement/patient/Regist";
  
  const model = {
  
    data() {
      return {
        examInfo: emptyRegist()
      }
    },
  
    methods: {
      view(exam) {
        let tpl = emptyRegist();
        eiapi.get(exam.id).then(res => {
          mergeWithDeep(tpl, res.data, null, mergeWithNotNull);
          this.examInfo = tpl;
        })
      }
    }
  };
  export default model;
  </script>
  
  <style scoped>
  .exam-view-form >>> .el-form-item__content{
    font-weight: bold;
  }
  .apply-exam-form-title{
    margin: 16px auto;
    padding: 0;
    text-align: center;
    font-size: 2em;
  }
  .apply-exam-form-sheet >>> .el-form-item__content
  , .apply-exam-form-sheet >>> .el-row
  , .apply-exam-form-sheet >>> .el-col{
    border: 0px solid #DDD;
  }
  .apply-exam-form-sheet >>> .el-form-item{
    margin-bottom: 0;
  }
  .apply-exam-form-sheet >>> .el-form-item__content{
    border-left: 1px solid #DDD;
  }
  .apply-exam-form-sheet >>> .el-row{
    border-width: 0 0 1px 0;
  }
  .apply-exam-form-sheet >>> .el-row:first-child{
    border-width: 1px 0;
  }
  .apply-exam-form-sheet >>> .el-col{
    border-width: 0 1px 0 0;
  }
  .apply-exam-form-sheet >>> .el-col:first-child{
    border-width: 0 1px;
  }
  </style>