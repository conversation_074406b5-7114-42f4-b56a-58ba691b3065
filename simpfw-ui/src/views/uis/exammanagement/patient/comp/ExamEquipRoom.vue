<template>
<el-dialog title="更改机房" :visible.sync="opened" class="popupdialog" width="400px">
  <el-form label-width="100px" class="tight-form">

    <el-row>
      <el-col :span="24">
        <el-form-item label="患者姓名">{{examInfo.patientInfo.name}}</el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="检查机房">
          <el-select v-model="toEquipRoom.roomCode" style="min-width: 160px">
            <el-option
              v-for="dict in combo_equipRoom"
              :key="dict.roomCode"
              :label="dict.roomName"
              :value="dict.roomCode"
            />
          </el-select>              
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="">
          <el-checkbox v-model="scope" :label="1" :disabled="!scopable">
            应用到所有<strong>{{fromRoomLabel}}</strong>患者
          </el-checkbox>            
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <div slot="footer" class="dialog-footer">
    <div class="foot-tools">
      <el-row>
        <el-col :span="24">
          <el-button type="primary" @click="submitChange">更改</el-button>
          <el-button @click="close">关闭</el-button>
        </el-col>
      </el-row>
    </div>
  </div>

</el-dialog>
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>

<script>
import {cloneDeep, mergeWith} from "lodash";

import {undefinedOrNull, mergeWithNotNull} from "@/utils/common";

import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
import {emptyRegist} from "@/assets/scripts/gis/exammanagement/patient/Regist";
//机房信息
import {find as findRoom} from "@/assets/scripts/pacs/equiproom/api";
//排队信息
import {get as getCallInfo, changeEquipRoom} from "@/assets/scripts/gis/exammanagement/queue/api";

const model = {
  name: "ExamEquipRoom",
  
  extends: BaseDialogModel,

  props: {
    //是否允许应用同一个机房
    scopable: {type: Boolean, default: true}
  },

  data() {
    return {
      examInfo: emptyRegist()
      , scope: false        //更改应用范围
      , toEquipRoom: {}     //更改到的机房
      , combo_equipRoom: [] //机房下拉
    }
  },

  methods: {
    change(exam) {
      let tpl = emptyRegist();
      this.toEquipRoom = {};

      if(!exam || !exam.id) {
        this.$modal.alert("请选择检查。");
        return;
      }
      const resultStatus = exam.resultStatus, resultStatusCode = resultStatus? resultStatus.dictValue : null;
      //登记完成和已检查的可进行呼叫
      if(!undefinedOrNull(resultStatus) && !/^[01]$/.test(resultStatusCode)) {
        this.$modal.alert("该检查无法更改机房，原因："  + resultStatus.dictLabel);
        return;
      }
      //机房有相应检查项目
      let roomParam = {};
      if(!!exam.examItem) {
        roomParam = {examItems:[exam.examItem]};
      }
      this.findEquipRoom(roomParam);
      //读取检查信息
      //getExam(exam.id).then(rexam => {
      //  exam = rexam.data;
        if(!exam.callInfo || !exam.callInfo.id) {
          this.$modal.alert("没有该检查排队信息。");
          return;
        }
        mergeWith(tpl, exam, null, mergeWithNotNull);
        this.examInfo = tpl;
        //读取排队/叫号信息
        getCallInfo(exam.callInfo.id).then(rque => {
          const que = rque.data;
          tpl.callInfo = que;

          this.toEquipRoom = cloneDeep(que.callRoom);
          this.scope = false;
          this.open();
        });
      //});
    },
    //执行变更
    submitChange() {
      const toEquipRoomCode = this.toEquipRoom.roomCode;
      if(!toEquipRoomCode) {
        this.$modal.alert("请选择机房。");
        return;
      }
      let {id: fromId, callRoom: equipRoom} = this.examInfo.callInfo, fromEquipRoomCode = null;
      if(this.scope && equipRoom.roomCode) {
        fromEquipRoomCode = equipRoom.roomCode;
      }
      //
      if(fromEquipRoomCode === toEquipRoomCode) {
        this.$modal.msgSuccess("无更改。");
        return;
      }
      //
      changeEquipRoom(fromId, fromEquipRoomCode, toEquipRoomCode).then(res => {
        if(200 === res.code) {
          this.$modal.msgSuccess("更改完成。");
          this.triggerBind("change");
          this.close();
          return;
        }
        this.$modal.alert(res.msg);
      });
    },
    //读取机房列表
    findEquipRoom(filter) {
      this.combo_equipRoom = [];
      findRoom(filter || {}).then(res => {
        this.combo_equipRoom = res && res.rows || [];
      });
    }
  },
  //
  created() {
    //this.findEquipRoom();
  },

  computed: {
    fromRoomLabel() {
      const que = this.examInfo.callInfo;
      return !!que && !!que.callRoom? que.callRoom.roomName : null;
    }
  }
};
export default model;
</script>
