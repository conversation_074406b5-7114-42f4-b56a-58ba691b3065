<template>
  <el-dialog title="取消检查/医嘱" :visible.sync="opened" width="1200px">
  <div>
    <div><strong>取消检查部位</strong></div>
    <div class="lst-pane">
      <ol>
        <li v-for="item in examParts" :key="item.id">{{ item.partsName }}</li>
      </ol>
    </div>
    <div><strong>选择退费医嘱</strong></div>
    <el-table :data="gridData" row-key="ordId"
      ref="multipleTable"
      stripe highlight-current-row
      height="400"
      
      @row-dblclick="selectRow"
      @selection-change="handleSelectionChange"
      @select="selectCheckbox"
      >
      <el-table-column type="selection" width="55"/>
      <el-table-column prop="patientInfo.name" label="姓名" width="80" show-overflow-tooltip />
      <el-table-column prop="patientInfo.gender.dictLabel" label="性别" width="50" />
      <el-table-column prop="patientInfo.age" label="年龄" width="60" />
      <el-table-column prop="ordName" label="医嘱"  width="380" />
      <el-table-column prop="reqTime" label="医嘱时间" width="180" />
      <el-table-column prop="examCost" label="费用" width="80" />
      <el-table-column prop="ordBillStatus" label="缴费状态" width="80" />
      <el-table-column prop="reqDoctor.nickName" label="申请医生" width="100" />
      <el-table-column prop="reqDept.deptName" label="申请科室" width="120" />
      <el-table-column prop="examDept.deptName" label="诊断科室" width="120" />
    </el-table>
    <div style="margin-top: 20px">
      <el-button type="primary" @click="submit">确定</el-button>
      <el-button type="warning" @click="cancel">取消</el-button>
    </div>
  </div>
  </el-dialog>
  </template>
  
  <style scoped>
  .lst-pane{
    margin: 4px auto;
    text-align: left;
  }
  .lst-pane ol{
    padding-left: 0;
  }
  .lst-pane ol li{
    list-style: inside decimal;
  }
  </style>

  <script>
  import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel"
  export default {
    extends: BaseDialogModel,
  
    data() {
      return {
        gridData: [],
        examParts: [],
        multipleSelection:[],
      }
    },
  
    methods: {
      show(gridData, examParts) {
        this.gridData = gridData;
        this.examParts = examParts;
        this.multipleSelection = [];

        if(gridData && gridData.length && examParts && examParts.length) {
          this.open();
          //
          this.$nextTick(() => {
            this.gridData.forEach(o => {
              if(-1 !== examParts.findIndex(e => e.partsName === o.ordName)) {
                this.toggleSelection(o);
              }
            });
          });
        }
      },
  
      selectCheckbox (selection, row){
        //this.$refs.multipleTable.toggleRowSelection(row);
      },
      selectRow(row) {
        //this.triggerBind("pick", row);
        //this.gridData = [];
        //this.close();
        this.toggleSelection(row)
      },
      handleSelectionChange(val) {
          this.multipleSelection = val;
        },
      toggleSelection(row) {
        this.$refs.multipleTable.toggleRowSelection(row);
      },
      submit(){
        this.triggerBind("refund", this.multipleSelection);
        
        this.cancel();
      },

      cancel(){
        this.gridData = [];
        this.examParts = [];
        this.multipleSelection = [];
        this.close();
      },
    }
  };
  </script>
  