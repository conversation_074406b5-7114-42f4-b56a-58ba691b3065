<template>
<div class="regist-wrap flex-container hei100">
  <div class="regist-form-pane hei100 scroll-auto" style="min-width: 658px" v-loading="registFormLoading">
    <el-form ref="registForm" :model="registForm" :rules="registFormRules" label-width="100px" style="height: 100%" class="tight-form" :disabled="!editEnabled">
      <el-container class="inner-container regist-container" style="height: 100%">
        <el-header height="unset">
          <el-form ref="searchForm" label-width="100px">
            <el-row class="searchFormPane">
              <el-col :span="24">
                <div class="form-element-group">
                  <el-select v-model="searchForm.registWay" style="width: 140px">
                    <el-option
                      v-for="dict in dict.type.uis_regist_way"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>

                  <el-input ref="registCodeInput" v-model="searchForm.registCode" placeholder="请按登记方法输入" class="form-el-w160"
                  :disabled="registCodeInputDisabled"
                  @focus="handleFocusRegistCode"
                  @keyup.enter.native="loadRemote"
                  @input="registCodeInput"/>

                  <span class="buttons-pane-gap">
                    <el-button :disabled="registCodeInputDisabled" type="primary" @click="loadRemote" :loading="loadRemoteProcessing">查询</el-button>
                    <!-- <el-button type="primary" disabled>读卡</el-button> -->
                    <el-button type="warning" @click="handleNew" v-hasPermi="['exam-info:edit']">新建</el-button>
                  </span>
                </div>

                  <!-- 预约签到：
                  <el-input v-model="searchForm.examNo" placeholder="流水号" class="form-el-w160" />
                  <el-button type="primary" disabled>签到</el-button> -->

                  <!-- 检查单：
                  <el-input v-model="searchForm.mediaPlayer" placeholder="流水/病历/住院号" class="form-el-w160" />
                  <el-button type="primary" disabled>打印</el-button> -->
              </el-col>
            </el-row>
          </el-form>
        </el-header>

        <el-main>
          <el-scrollbar class="scrollpane scrollpane-h">
            <el-card class="nested-card card-pane patient-info-card">
              <div slot="header">
                <span class="nested-card-header-title">基本信息</span>
                <!-- <el-button style="float: right; padding: 3px 0" type="text">+</el-button> -->
              </div>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="患者姓名" prop="patientInfo.name">
                    <el-input v-model="registForm.patientInfo.name" @input="delayConvToPinyin" />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="患者性别" prop="patientInfo.gender.dictValue">
                    <el-select v-model="registForm.patientInfo.gender.dictValue" clearable  class="w100">
                      <el-option
                        v-for="dict in dict.type.uis_gender_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="患者年龄" prop="examAgeN">
                    <el-input v-model="registForm.examAgeN" style="width: 60%"
                     :disabled="ageDisabled"
                     @change="handleChangeAge" />
                    <el-select v-model="registForm.examAgeUnit.dictValue"
                    :disabled="ageDisabled"
                    @change="handleChangeAgeUnit"
                    style="width: 39%" placeholder="">
                      <el-option
                        v-for="dict in dict.type.uis_age_unit"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>

                <el-col :span="16">
                  <el-form-item label="登记号" prop="patientInfo.registNo">
                    <div class="input-group" style="display: flex; align-items: center; width: 213px;">
                      <el-input v-if="inspectionOrgCode" v-model="inspectionOrgCode" :disabled="true" :style="{ width: `${inspectionOrgCode.length * 9 + 10}px`, marginRight: '5px' }"/>
                      <el-input ref="registNoInput" v-model="getOriginRegistNo" @blur="findPatient" style="flex: 1;"/>
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="出生日期">
                    <el-date-picker v-model="registForm.patientInfo.birthday"
                    type="date" editable clearable prefix-icon="nvl"
                    :disabled="true"
                    class="simp-date-picker"
                    @change="handleChangeBirthday" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>

                <el-col :span="8">
                  <el-form-item label="病历号" class="form-textfield">
                    <el-input v-model="registForm.patientInfo.medicalRecordNo" readonly />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="民族">
                    <el-select v-model="registForm.patientInfo.nation.dictValue" clearable class="w100">
                      <el-option
                        v-for="dict in dict.type.comm_nation"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="婚否">
                    <el-select v-model="registForm.patientInfo.marriedStatus.dictValue" clearable class="w100">
                      <el-option
                        v-for="dict in dict.type.comm_married_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
              </el-col>

              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="检查号" class="form-textfield">
                    <el-input v-model="registForm.examNo" :disabled="true"/>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="级别">
                    <el-select v-model="registForm.patientInfo.vipFlag" clearable class="w100">
                      <el-option
                        v-for="dict in dict.type.uis_patient_sens_grade"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="职业">
                    <el-select v-model="registForm.patientInfo.occupation.dictValue" clearable class="w100">
                      <el-option
                        v-for="dict in dict.type.comm_occupation"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="姓名拼音">
                    <el-select v-model="registForm.patientInfo.namePingyin" clearable class="w100" disabled>
                      <el-option
                        v-for="item in registFormOpts.combo_pinyin"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                    <!--<el-input v-model="registForm.patientInfo.namePingyin" />-->
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="身份证号">
                    <el-input v-model="registForm.patientInfo.cardNo" :maxlength="18" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="联系电话">
                    <el-input v-model="registForm.patientInfo.phone" />
                  </el-form-item>
                </el-col>

                <el-col :span="16">
                  <el-form-item label="联系地址">
                    <el-input v-model="registForm.patientInfo.address" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="工作单位" prop="assignedUnit">
                    <el-input v-model="registForm.patientInfo.assignedUnit" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="nested-card card-pane exam-info-card" style="overflow: visible;">
              <div slot="header">
                <span class="nested-card-header-title">检查信息</span>
              </div>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="检查类型" prop="examModality.dictValue">
                    <el-select v-model="registForm.examModality.dictValue" clearable class="w100">
                      <el-option
                        v-for="dict in ctrlData.dict.uis_exam_modality"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="就诊类别" prop="inpType.dictValue">
                    <el-select v-model="registForm.inpType.dictValue" class="w100">
                      <el-option
                        v-for="dict in ctrlData.dict.uis_inp_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="检查项目" prop="examItem.dictValue">
                    <el-select v-model="registForm.examItem.dictValue" class="w100">
                      <el-option
                        v-for="dict in ctrlData.dict.uis_exam_item"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="16">
                  <el-form-item label="检查部位" prop="examParts_names">
                    <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                      :content="registForm.examParts_names" :disabled="!registForm.examParts_names">
                      <el-input v-model="registForm.examParts_names" @focus="pickExamParts" readonly />
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="医嘱名称" prop="ordName" >
                    <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                        :content="registForm.ordName" >
                        <el-input v-model="registForm.ordName" :disabled="true" />
                      </el-tooltip>
                  </el-form-item>
<!--                  <el-form-item label="报告编号前缀" v-hasPermi="['report:reportPdf:upload']" >
                    <el-input v-model="registForm.reportNoPrefix" />
                  </el-form-item>-->
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="送检单位" prop="inspectionOrg.dictValue">
                    <el-select v-model="inspectionOrg.dictValue" class="w100" clearable
                               @change="handleInspectionOrgChange"
                    >
                      <el-option
                        v-for="dict in dict.type.common_organization_code"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="影像编号" v-hasPermi="['report:reportImage:upload']" >
                    <el-input v-model="registForm.imageNo" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="报告编号" v-hasPermi="['report:reportPdf:upload']" >
                    <el-input v-model="registForm.reportNo" clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="医嘱执行医师">
                    <el-input v-model="registForm.ordExecDoctorName" clearable class="el-input-group-thin" :disabled="!!registForm.ordId && !registForm.unexecuteOrdId">
                      <el-button el-button slot="append"  size="mini" icon="el-icon-user-solid"
                                 title="选择医嘱执行医师"
                                 @click="toPickUser('ordExecDoctor',  [{}])"></el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- <el-col :span="8">
                  <el-form-item label="住院病区">
                    <el-select v-model="registForm.inpWard.dictValue" placeholder="">
                      <el-option
                        v-for="dict in dict.type.uis_inp_ward"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="8">
                  <el-form-item label="住院号(门诊号)" class="form-item-thin">
                    <el-input v-model="registForm.inpNo" />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="床号">
                    <el-input v-model="registForm.bedNo" />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <!-- <el-form-item label="病人性质">
                    <el-select v-model="registForm.patientInfo.chargeType.dictValue" clearable class="w100">
                      <el-option
                        v-for="dict in dict.type.comm_charge_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item> -->
                  <el-form-item label="收费类型">
                      <el-select v-model="registForm.examCostType.dictValue" clearable
                          class="w100">
                          <el-option v-for="dict in dict.type.uis_exam_cost_type"
                              :key="dict.value" :label="dict.label" :value="dict.value" />
                      </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="检查医师">
                      <el-input v-model="registForm.examDoctorsName" clearable class="el-input-group-thin">
                        <el-button el-button slot="append"  size="mini" icon="el-icon-user-solid"
                         title="选择检查医师"
                         @click="toPickUser('examDoctors',  [{}])"></el-button>
                    </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="申请科室">
                      <!-- <Treeselect v-model="registForm.reqDept.deptId" :options="deptTreeData" :show-count="true" placeholder="选择" /> -->
                      <el-input v-model="registForm.reqDept.deptName" class="el-input-group-thin">
                        <el-button el-button slot="append"  size="mini" icon="el-icon-s-ticket"
                         title="选择申请科室"
                         @click="pickDept"></el-button>
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="申请医生">
                      <el-input v-model="registForm.reqDoctor.nickName" clearables class="el-input-group-thin">
                        <el-button el-button slot="append" size="mini" icon="el-icon-user-solid"
                         title="选择申请医师"
                         @click="toPickUser('reqDoctor')"></el-button>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>

                  <el-col :span="8">
                    <el-form-item label="检查房间">
                      <el-select v-model="registForm.callInfo.callRoom.roomCode" clearable class="w100">
                        <el-option
                          v-for="dict in ctrlData.room"
                          :key="dict.roomCode"
                          :label="dict.roomName"
                          :value="dict.roomCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="住院次数">
                      <el-input v-model="registForm.inpTimes" />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <!-- <el-form-item label="预约时间">
                      <el-date-picker v-model="registForm.appointTime" type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      editable clearable prefix-icon="nvl" class="simp-date-picker" />
                    </el-form-item> -->
                    <el-form-item label="病人性质">
                      <el-select v-model="registForm.condParting.dictValue" clearable class="w100">
                        <el-option
                          v-for="dict in dict.type.uis_cond_parting"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="16">
                    <el-form-item label="临床诊断">
                      <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                      :content="registForm.clinicDiagnosis" :disabled="!registForm.clinicDiagnosis">
                        <el-input v-model="registForm.clinicDiagnosis" />
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="预约时间">
                      <el-date-picker v-model="registForm.appointTime" type="datetime"
                      format="yyyy-MM-dd HH:mm:ss"
                      editable clearable prefix-icon="nvl" class="simp-date-picker" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="16">
                    <el-form-item label="过敏史">
                      <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                      :content="registForm.allergyHistory" :disabled="!registForm.allergyHistory">
                        <el-input v-model="registForm.allergyHistory" />
                      </el-tooltip>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="检查费用"><el-input v-model="registForm.examCost" /></el-form-item>
                  </el-col>

                </el-row>

                <el-row>
                  <el-col :span="12">
                    <el-form-item label="患者病史">
                      <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                      :content="registForm.clinicDisease" :disabled="!registForm.clinicDisease">
                        <el-input type="textarea" :rows="2" v-model="registForm.clinicDisease"/>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="注意事项">
                      <el-tooltip class="item" effect="light" placement="top-start" :open-delay="600"
                      :content="registForm.noteInfo" :disabled="!registForm.noteInfo">
                        <el-input type="textarea" :rows="2" v-model="registForm.noteInfo" />
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                </el-row>

            </el-card>

          </el-scrollbar>
        </el-main>

        <el-footer height="unset">
          <div class="foot-tools">
            <el-row>
              <el-col :span="24">
                <!-- <el-checkbox v-model="registForm.greenChannelFlagValue">绿色通道</el-checkbox>
                <el-checkbox v-model="registForm.examAtPmValue">延迟检查</el-checkbox> -->
                <!-- <el-checkbox v-model="registForm.examPrerequireValue">诊前准备</el-checkbox> -->
                <!-- <el-checkbox v-model="registForm.reservedNoUsedValue">启用预留排队号</el-checkbox> -->
                <!-- <el-checkbox v-model="registForm.examAtPmValue">下午检查</el-checkbox> -->

                <el-button type="primary" @click="submitForm" :loading="submitFormProcessing"
                 v-hasPermi="['exam-info:edit']"
                 v-if="!undoDeleteEnabled && !undoCancelEnabled">保存</el-button>

                <!-- <el-button type="danger" @click="handleCancel" v-if="!undoDeleteEnabled && cancelEnabled">取消</el-button>
                <el-button type="danger" @click="undoCancel" v-if="undoCancelEnabled">撤销取消</el-button> -->

                <!-- <el-button type="danger" @click="handleDelete" v-if="deleteEnabled">删除</el-button>
                <el-button type="danger" @click="undoDelete" v-if="undoDeleteEnabled">撤销删除</el-button> -->
                <el-button type="danger" @click="handleCancel" v-if="!!registForm.id">取消修改</el-button>
              </el-col>
            </el-row>
          </div>
        </el-footer>
      </el-container>
    </el-form>
    <!-- 选择医生 -->
    <UserPicker ref="userPicker" @pick="pickUser" />
    <!-- 更改机房
    <ExamEquipRoom ref="examEquipRoom" @change="refreshModified" /> -->
    <!-- 从his获取多个检查 -->
    <OrderPicker ref="ordPicker" @pick="pickOrd"  @pickAr="pickOrdAr" />
    <!-- 从his获取多个检查 -->
    <OrderRefund ref="orderRefund" @refund="refundOrd" />
    <!-- 检查部位选择弹窗 -->
    <ExamPartsPicker ref="examPartsPicker"
      :checkedKeys="registForm.examParts_ids" @onChecked="handleCheckExamParts" />
    <DeptPicker ref="deptPicker"
      :checkedKey="registForm.reqDept.deptId" @onChecked="handleCheckDept" />

    <!-- 上传检查影像 -->
   <ReportUploader ref="reportUploader" />

   <!-- 报告预览 -->
   <ReportViewer ref="reportViewer" :viewLoadReport="false"/>

  </div>

  <div class="flex-item-fill hei100 scroll-auto">
    <div class="tabs-wrap hei100" style="border-left: 1px solid #DDD">
      <el-tabs tab-position="top" class="tab-ver tab-ver-cp tab-ver-flex hei100">
        <!-- <el-tab-pane :name="tab.names.examParts">
          <span slot="label">检查部位</span>

          <el-card class="nested-card card-pane">
            <div slot="header" class="clearfix">
              <span>检查部位</span>
            </div>
            <el-form label-width="100px">
              <el-row>
                <el-col :span="24">
                  <div class="examPartsTreePane">
                    <ExamPartsTree ref="examPartsTree" :wrap-all="false"
                    :checkedKeys="registForm.examParts_ids" @onChecked="handleCheckExamParts" />
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </el-card>

          <el-card class="nested-card card-pane">
            <div slot="header" class="clearfix">
              <span>已选部位（双击移除）</span>
            </div>
            <div>
              <div class="examPartsPane">
                <ul>
                  <li v-for="item in registForm.examParts" :key="item.id"
                  @dblclick="uncheckExamParts(item)">{{item.partsName}}</li>
                </ul>
              </div>
            </div>
          </el-card>
        </el-tab-pane> -->

        <el-tab-pane class="hei100">
          <span slot="label">患者列表</span>
          <div class="tab-body-p4 hei100">
            <PatientSheet ref="patientSheet" :refresh="8"
              :filter="{status:0}"
              :actions="patientListActions"
              :limitUpload="true"
              :sheetType="patientSheetType.regist"
              @dispatchAction="handlePatientListAction"
              @editRow="handleEdit"
              @dblClickRow="handleEdit" />
          </div>
        </el-tab-pane>

        <!-- <el-tab-pane>
          <span slot="label">排队列表</span>
          <div class="tab-body-p4">
            <Queue ref="regQueue" :refresh="8" :filter="{examInfo: {examAtPm: 0}}" />
          </div>
        </el-tab-pane>

        <el-tab-pane>
          <span slot="label">机房状态</span>
          <div class="tab-body-p4">
            <EquipRoomStatus :editable="false" />
          </div>
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</div>
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>

<script>
import model from "@/assets/scripts/uis/exammanagement/patient/Regist";
export default model;
</script>
