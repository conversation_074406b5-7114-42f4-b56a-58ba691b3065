<template>
  <Examnn ref="examnn" :examAtPm="1" @change="updateExamPreriod"
   button-icon-state="el-icon-sort" button-tip-state="检查" />
</template>

<script>
import Examnn from "@/views/gis/exammanagement/patient/comp/Exammn";

import * as api from "@/assets/scripts/gis/exammanagement/examinfo/api";

export default {
  name: "examAtPm",
  components: {Examnn},

  methods: {
    updateExamPreriod(row) {
      this.$modal.confirm('是否确定？').then(() => {
        const nrow = {id: row.id, examAtPm: null};
        return api.updateExamPeriod(nrow);
      }).then(res => {
        if(200 === res.code) {
          this.$refs.examnn.getList();
          this.$modal.msgSuccess("完成操作");
        }
      }).catch(err => console.error(err));
    }
  }
}; 
</script>