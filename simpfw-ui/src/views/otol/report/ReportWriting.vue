<template>
  <el-container class="inner-container" style="height: 100%">
    <el-main>
      <ReportWriter ref="reportWriter" @dispatchAction="handleAction" 
      @refreshExamInfo="refreshExamInfo"
      @switchTab="switchTab"
      @focusFormField="focusFormField" />
    </el-main>

    <el-aside width="45%">
      <div class="flex-container-column hei100">
        <div class="tabs-wrap flex-item-fill report-tabs-wrap">
          <el-tabs v-model="currentTab" class="tab-ver tab-ver-cp tab-ver-flex hei100" @tab-click="handleTabClick">
            <!-- 本机列表 -->
            <!-- <el-tab-pane label="本机列表" :name="tabsName.PatientList">
              <div class="tab-body-p4 hei100">
                <EquipRoomPatientSheet ref="patientList" :refresh="8" @dispatchAction="handleAction" />
              </div>
            </el-tab-pane>           -->
            <!--  -->
            <!-- <el-tab-pane label="排队列表" :name="tabsName.Queue">
              <div class="tab-body-p4 hei100">
                <Queue ref="queue" :refresh="8" :roomRestricted="true"
                  :filter="queueFilter" 
                  @afterCall="switchTab(tabsName.PatientList)"
                  @dblClickRow="writeReport" />
              </div>
            </el-tab-pane> -->
            <!-- :filter="{examModality: {dictValue: 'UIS'}}" -->
            <!--  -->
            <el-tab-pane :label="'待报告列表('+PatientUploadNum + ')'" :name="tabsName.PatientUploadSheet">
              <div class="tab-body-p4 hei100">
                <PatientUploadSheet
                  ref="patientSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.pendingReport"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                  @updatePatientNum="updatePatientNum"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane :label="'待审核列表('+PatientAuditNum + ')'" :name="tabsName.PatientAuditSheet">
              <div class="tab-body-p4 hei100">
                <PatientAuditSheet
                  ref="patientAuditSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.pendingAudit"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                  @updatePatientNum="updatePatientNum"
                />
              </div>
            </el-tab-pane>

            <el-tab-pane label="患者列表" :name="tabsName.PatientSheet">
              <div class="tab-body-p4 hei100">
                <PatientSheet
                  ref="patientSheet"
                  :refresh="8"
                  :filter="{ status: 0 }"
                  :actions="patientSheetActions"
                  :limitUpload="false"
                  :sheetType="patientSheetType.normal"
                  @dispatchAction="handleAction"
                  @dblClickRow="writeReport"
                />
              </div>
            </el-tab-pane>


            <!--  -->
            <!-- <el-tab-pane style="height: 95%;" label="书写模板" :name="tabsName.Template">
              <div class="tab-body-p4 " >
                <TemplateTree  ref="templateTree" @dblclick="applyTemplate" :priflag="true" :targets="portableReportProps" @change="updateReport"/>
              </div>
            </el-tab-pane> -->

            <!-- <el-tab-pane label="常用符号及短语">
                <el-container style="height: 100%;">
                    <el-aside style="width: 40% ;border-right : 1px solid #cfcfcf;">
                        <div style="margin-top: 8px;">
                            <span>常用符号</span>
                            <SymbolSelect :targets="portableReportProps" @change="updateReport" />
                        </div>
                    </el-aside>
                    <el-main style="margin-left: 10px;">
                        <div style="margin-top: 8px;">
                            <span>常用短语</span>
                            <PhraseSelect :targets="portableReportProps" @change="updateReport" />
                        </div>
                    </el-main>
                </el-container>
            </el-tab-pane> -->

            <!-- 常用符号
            <el-tab-pane label="常用符号">
              <div class="tab-body-p4">
                <SymbolSelect :targets="portableReportProps" @change="updateReport" />
              </div>
            </el-tab-pane> -->

            <!-- 
            <el-tab-pane label="个人模板">

              <el-card header="个人模板" class="nested-card hei100 card-pane">
                <div>
                  <TemplateTree :params="{personalFlag: 1}" @click="showTemplate" @dblclick="applyTemplate" />
                </div>
              </el-card>
            </el-tab-pane>
            -->

            <!-- 
            <el-tab-pane label="书写词库">
              <div class="tab-body-p4">
                <WritePhraseSelect ref="writePhraseSelect" 
                :targets="portableReportProps" 
                :examItem="currExamItem" :formFieldCode="currFormField"
                @change="updateReport" />
              </div>
            </el-tab-pane> -->

            <!-- 
            <el-tab-pane label="关键短语">
              <div class="tab-body-p4">
                <PhraseSelect :targets="portableReportProps" @change="updateReport" />
              </div>
            </el-tab-pane> -->
            <!-- 
            <el-tab-pane label="常用符号及短语">
              <div class="tab-body-p4">
                <PhraseSelect :targets="portableReportProps" @change="updateReport" />
              </div>
            </el-tab-pane> -->

            <el-tab-pane label="影像采集" :name="tabsName.ImageMonitor">
              <div class="tab-body-p4 hei100">
                <ImageMonitor ref="imageMonitor" @capture="handleCaptureImage" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="影像提取" :name="tabsName.ImageGallary">
              <div class="tab-body-p4">
                <ImageGallary ref="imageGallary" @select="handleCaptureImage" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="ocr识别信息" :name="tabsName.OcrExamResult">
              <div class="tab-body-p4">
                <OcrExamResult ref="ocrExamResult" />
              </div>
            </el-tab-pane>

          </el-tabs>
        </div>
        <!-- 历史检查 -->
        <ExamHistory ref="examHistory" @row-dblclick="writeReport" @applyReport="applyReport" @view="previewReport" style="height: 220px" />

        <!-- <div class="rw-buttons-pane">
          <!- - <el-row>
            <el-col :span="6"><el-button type="primary" size="mini" @click="saveReport">保存</el-button></el-col>
            <el-col :span="6"><el-button type="primary" size="mini" @click="signReport">医生签字</el-button></el-col>
            <el-col :span="6"><el-button type="primary" size="mini" @click="printReport">打印</el-button></el-col>
            <el-col :span="6"><el-button type="primary" size="mini" @click="previewReport">预览</el-button></el-col>
          </el-row> - ->
          <el-row>
            <el-col :span="6"><el-button type="primary" @click="nextExam">下一个</el-button></el-col>
            <el-col :span="6"><el-button type="primary" @click="callExam"
              :disabled="currentTab!==tabsName.PatientList">复呼</el-button></el-col>
            <!- - <el-col :span="6"><el-button type="primary" @click="getHistList">历史检查</el-button></el-col> - ->
            <el-col :span="6"><el-button type="primary" @click="handleQueuePast"
              :disabled="currentTab!==tabsName.PatientList">迟到</el-button></el-col>
            <el-col :span="6">
              <el-dropdown trigger="click" @command="handleExtendCommand" style="width: 100%">
                <el-button type="primary">更多操作</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="report::withdrawAudit">召回报告</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
          </el-row>
        </div> -->
      </div>
      <!-- 模板预览-->
      <TemplateOverview ref="tplOverview" @applyTemplate="applyTemplate" />
      <!-- 报告预览 -->
      <ReportViewer ref="reportViewer" mime-type='pdf' @makeTplDoc="makeTplDoc" />
      <!-- 报告模板 -->
      <ReportTemplate ref="reportTemplate" />
      <!-- 更改房间-->
      <ExamEquipRoom ref="examEquipRoom" :scopable="false" @change="refreshExamInfo" />
      <!-- 呼叫-->
      <ExamCalling ref="examCalling" @refreshExamInfo="refreshExamInfo" />
      <!-- 检查单 -->
      <ExamViewer ref="examViewer" />
      <!-- 危急值 -->
      <CriticalValues ref="criticalValues" />
      <!-- 召回报告 -->
      <ReportAuditWithdraw ref="reportAuditWithdraw" @refreshExamInfo="afterWithdrawReport" />
      <!-- 当前机房 -->
      <!-- <CurrentEquipRoom /> -->

      <!-- 报告预览 -->
      <ReportViewerLoad ref="reportViewerLoad" :viewLoadReport="false"/>

      <!-- 诊室排队叫号界面 add@20230307 -->
      <!-- <QueueNew @callAndWriteReport="writeReport"/> -->
    </el-aside>
  </el-container>
</template>

<style scoped src="@/assets/styles/pacs/report/ReportWriting.css"></style>

<script>
export {default} from "@/assets/scripts/otol/report/ReportWriting";
</script>
