<template>
<div class="report-writer-wrap flex-container-column">
  <div class="report-writer-form flex-item-fill">
  <el-form ref="reportForm" label-width="90px" class="tight-form hei100 flex-container-column">
    <ReportWriterDetail :report="reportForm" v-show="!!reportForm && !!reportForm.id" />
    <!---->
    <el-row>
      <el-col :span="6">
        <el-form-item label="姓名">
          {{reportForm.patientInfo.name}}
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="年龄">
          <span style="font-size: 0.8em;">{{ formattedExamAge }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="记录人员">
          <el-input v-model="reportForm.recordersName" size="mini" :disabled="withdrawable" class="input-field-narr">
              <el-button slot="append" size="mini" icon="el-icon-user-solid"
              @click="toPickUser({multiple: true, target: 'recorders', posts: [{postCode:'CSYS'}]})"
              :disabled="withdrawable"></el-button>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="检查号">
          {{reportForm.examNo}}
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="6">
        <el-form-item label="卡号">
          {{reportForm.outpNo}}
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="性别">
          {{reportForm.patientInfo.gender.dictLabel}}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="检查医师">
          <el-input v-model="reportForm.examDoctorsName" size="mini" :disabled="withdrawable" class="input-field-narr">
            <el-button slot="append" size="mini" icon="el-icon-user-solid"
             @click="toPickUser({multiple: true, target: 'examDoctors', posts: [{postCode:'CSYS'}]})"
             :disabled="withdrawable"></el-button>
            <!-- <i slot="suffix" class="el-input__icon el-icon-search"></i> -->
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="登记号">
          {{reportForm.patientInfo.registNo}}
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="6">
        <el-form-item label="住院号">
          {{reportForm.inpNo}}
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="床号">
          {{reportForm.bedNo}}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="操作者" prop="operDoctor.nickName">
          <el-input v-model="reportForm.operDoctor.nickName" size="mini" :disabled="withdrawable" class="input-field-narr">
            <el-button slot="append" size="mini" icon="el-icon-user-solid"
             @click="toPickUser({multiple: false, target: 'operDoctor', posts: [{postCode:'YS'}], roles: [{roleKey:'jdtys'}]})"
             :disabled="withdrawable"></el-button>
          </el-input>
        </el-form-item>

        <!-- <el-form-item label="会诊医师">
          <el-input v-model="reportForm.consultantsName" size="mini" :disabled="withdrawable" class="input-field-narr">
            <el-button slot="append" size="mini" icon="el-icon-user-solid"
             @click="toPickUser({multiple: true, target: 'consultants', posts: [{postCode:'CSYS'}]})"
             :disabled="withdrawable"></el-button>
          </el-input>
        </el-form-item> -->
      </el-col>
      <el-col :span="8">
        <el-form-item label="申请科室">
          {{reportForm.reqDept? reportForm.reqDept.deptName : ''}}
        </el-form-item>
      </el-col>

      <!-- <el-col :span="6">
        <el-form-item label="书写医生">
          {{reportForm.reportDoctor.nickName}}
        </el-form-item>
      </el-col> -->

    </el-row>

    <!-- <el-row>


      <el-col :span="6">
        <el-form-item label="检查医师">
          {{reportForm.examDoctor? reportForm.examDoctor.nickName : null}}
        </el-form-item>
      </el-col>

    </el-row> -->

    <el-row>

      <el-col :span="16">
        <el-form-item label="检查部位">
          {{reportForm.examParts_names}}
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="检查日期">
          {{!!reportForm.examTime? reportForm.examTime.split(" ")[0] : null}}
        </el-form-item>
      </el-col>
    </el-row>

    <!-- <el-row>
      <el-col :span="6">
        <el-form-item label="检查设备">
          {{reportForm.examModality.dictLabel}}
        </el-form-item>
      </el-col>


      <el-col :span="6">
        <el-form-item label="阴阳性">
          <el-select v-model="reportForm.examResultProp.dictValue" clearable v-if="dict.type.uis_exam_result_prop.length>2">
            <el-option
              v-for="dict in dict.type.uis_exam_result_prop"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-radio-group v-model="reportForm.examResultProp.dictValue" v-if="dict.type.uis_exam_result_prop.length<=2">
            <el-radio v-for="dict in dict.type.uis_exam_result_prop" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row> -->

    <!-- <el-row class="flex-item-fill-2">
      <el-col :span="24" class="hei100">
        <el-form-item label="检查所见" class="hei100 fhta">
          <el-link :underline="false" v-if="!withdrawable" class="clean-link" @click="cleanExam">清空检查</el-link>
          <el-input name="examDesc" v-model="reportForm.examDesc"
           type="textarea" :rows="8" :disabled="withdrawable" @focus="switchTab('Template')" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row class="flex-item-fill">
      <el-col :span="24" class="hei100">
        <el-form-item label="检查诊断" class="hei100 fhta">
          <el-input name="examDiagnosis" v-model="reportForm.examDiagnosis"
           type="textarea" :rows="6" :disabled="withdrawable" @focus="switchTab('Template')" />
        </el-form-item>
      </el-col>
    </el-row> -->

    <div class="report-writer-wrap hei100">
        <iframe frameborder="0" :key="ikey" id="reportTable" class="nested-frame-full" :src="reportDSrc"></iframe>
    </div>

    <!-- <el-row class="flex-item-fill">
      <el-col :span="24" class="hei100">
        <el-form-item label="建议" class="hei100 fhta">
          <el-input name="operationSuggestion" v-model="reportForm.operationSuggestion"
           type="textarea" :rows="2" :disabled="withdrawable" />
        </el-form-item>
      </el-col>
    </el-row> -->

    <!-- <el-row>
      <el-col :span="24">
        <el-form-item label="危急值">
            <el-button type="danger" @click="reportCriticalValues" :disabled="!reportable || withdrawable">上报</el-button>

            <span style="margin: 0 8px 0 16px"></span>
            <el-radio-group v-model="reportForm.examResultProp.dictValue" :disabled="withdrawable">
              <el-radio v-for="dict in dict.type.uis_exam_result_prop" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
            </el-radio-group>

        </el-form-item>
      </el-col>
    </el-row> -->

    <!-- <el-row>
      <el-col :span="24">
        <el-form-item label="术后医嘱">
            <el-input name="operationSuggestion" v-model="reportForm.operationSuggestion"
             type="textarea" :rows="2"/>
        </el-form-item>
      </el-col>
    </el-row> -->

    <el-row>
      <el-col :span="6">
        <el-form-item label="报告医师">
          <el-input v-model="reportForm.reportDoctor.nickName" size="mini" :disabled="withdrawable" class="input-field-narr">
            <el-button slot="append" size="mini" icon="el-icon-user-solid"
             @click="toPickUser({multiple: false, target: 'reportDoctor', posts: [{postCode:'CSYS'}]})"
             :disabled="withdrawable"></el-button>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="审核医师">
          <!-- {{reportForm.auditDoctor? reportForm.auditDoctor.nickName : null}} -->
          <img v-if="!!reportForm.signImage" style="height: 32px; vertical-align: middle;" :src="'data:image/png;base64,'+reportForm.signImage" />
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <div class="buttons-pane-gap buttons-pane-na">
          <!-- <el-button type="primary" @click="editable = true" v-if="!!reportForm.id && !editable">编辑报告</el-button>
          <template v-if="editable"> -->
            <el-button type="primary" @click="clearReport"  v-loading="saveLoading" v-if="reportable && !transferActive"
           v-hasPermi="['exam-report:write']">清空数据</el-button>

           <!-- <el-button type="primary" @click="resultStatusRollback" v-loading="saveLoading"
             v-if="rollbackable && !transferActive"
             v-hasPermi="['exam-report:write']">回退</el-button> -->

          <el-button type="primary" v-loading="saveLoading" @click="save({firmed: true})"
           v-if="reportable && !transferActive"
           v-hasPermi="['exam-report:write']">{{editButtonLabel}}</el-button>
          <!-- :disabled="currentExamItemCode!='NYST'" -->
          <el-button type="primary" @click="handleAudit" v-loading="saveLoading"
           v-if="reportable && !transferActive" v-hasPermi="['exam-report:audit']">审核报告</el-button>
          <!-- <el-button type="primary" @click="handleReaudit"
           v-if="reauditable">复核报告</el-button> -->

          <el-button type="danger" @click="handleWithdrawAuditReport"
           v-if="withdrawable" v-hasPermi="['exam-report:audit']">召回</el-button>

          <!-- <el-button type="danger" @click="handleCancel" v-if="reportable">取消</el-button> -->
          <!-- </template> -->
          <el-button type="primary" @click="handlePreview" v-loading="saveLoading"
           :disabled="!reportForm.id">预览报告</el-button>
          <el-button type="primary" @click="handlePreview('report::print')" v-loading="saveLoading"
           :disabled="!printable"
           v-hasPermi="['exam-report:print']">打印报告</el-button><!--  v-if="printable" -->
        </div>
      </el-col>
    </el-row>
  </el-form>
  </div>

  <div v-if="!splitScreenUsed" class="report-writer-image">

<div class="exam-image-stat">
  <strong>检查影像</strong>（共有影像<span>{{null!=imagesSet? imagesSet.length:0}}</span>幅，其中<span>{{numImagesSelected}}</span>幅影像被选中）

  <el-button type="primary" icon="el-icon-paperclip" size="mini" 
  @click="handleResbackImage" 
  v-if="reportable"
  v-hasPermi="['exam-report:write']">导入</el-button>

  <el-button-group style="margin-left: 4px" v-hasPermi="['exam-report:write']">
    <template v-if="!transferActive">
    <el-button type="primary" icon="el-icon-document-add" size="mini" 
    @click="setTransferAction('batch')" 
    v-if="reportable">批量转移</el-button>
    <el-button type="primary" icon="el-icon-document-checked" size="mini" @click="setTransferAction('all')" v-if="reportable">全部转移</el-button>
    </template><template v-else>
    <el-button type="primary" icon="el-icon-sort" size="mini" @click="batchTransfer" v-if="reportable">确定转移</el-button>
    <el-button type="warning" icon="el-icon-d-arrow-left" size="mini" @click="setTransferAction(null)" v-if="reportable">取消转移</el-button>
    </template>
  </el-button-group>
</div>

<div class="cornerstone-elements-wrap flex-container">
  <div class="cornerstone-elements-scroller cornerstone-elements-scroller-forward" @click="scrollImage(-1)">&lt;</div>
  <div ref="imageScrollView" class="cornerstone-elements-pane flex-item-fill"
   :class="{'cornerstone-elements-pane-emtpy': !imagesSet || imagesSet.length<=0}"
   @mousewheel="scrollImageByWheel">
    <template v-if="null!=imagesSet && imagesSet.length>0" v-for="(item,idx) in imagesSet">
      <div v-if="validateExamImage(item)" :key="item.SOPInstanceUID"
       :data-index="idx"
       class="cornerstone-element-container"
       @contextmenu.prevent="showImageContextmenu"><!-- -->
        <!-- 动态影像 -->
        <template v-if="typeofVideo(item)">
          <div class="cornerstone-element cornerstone-element-video"
           @dblclick="viewVideo($event)"
           title="双击播放视频"><i class="el-icon-video-camera"></i></div>
        </template>
        <!-- 静态影像 -->
        <template v-else>
          <div class="cornerstone-element"
           @mouseover="zoomImage($event)"
           @mouseleave="zoomImage()" 
           @dblclick="viewImage($event)" 
           title="双击看大图"></div>
        </template>
        <!-- 勾选图像序号 -->
        <div class="cornerstone-element-no" v-show="!!item.selectNum && !transferActive">{{item.selectNum}}</div>
        <!-- 删除按钮 -->
        <div v-if="reportable && !transferActive && typeofJpg(item)" class="cornerstone-element-cls" @click="deleteExamImage(item)"><i class="el-icon-delete"/></div>
        <!-- 勾选报告影像 -->
        <div v-if="!transferActive && !isExamImageDeleted(item)" class="exami-mage-checkbox">
          <el-checkbox @change="state=>selectExamImage(state, item)" v-model="item.selected"
           :disabled="!reportable || (!typeofJpg(item) && !typeofDcm(item))" />
        </div>
        <!-- 勾选转移影像 -->
        <div v-if="transferActive && !isExamImageDeleted(item)" class="exami-mage-checkbox">
          <el-checkbox @change="state=>selectTransferImage(state, item)" v-model="item.transferSelected" :disabled="!reportable" />
        </div>
        <!-- 删除按钮   -->
        <div v-if="reportable && !transferActive && !isExamImageDeleted(item)" class="cornerstone-element-cls" @click="deleteExamImage(item)"><i class="el-icon-delete"/></div>
      
      </div>
    </template>
  </div>
  <div class="cornerstone-elements-scroller cornerstone-elements-scroller-backward" @click="scrollImage(1)">&gt;</div>
  <div class="uploadImage">
<el-upload
:disabled="true"
  accept="image/*"
  action=""
  ref="uploadBtn"
  multiple
  :limit="1"
  class="upload"
  style="width: 100%;"
>
  <i class="el-icon-plus"
  ref="uploadImage"
  @paste="handlePaste" 
  contenteditable="" 
  @keydown="handleFocus" v-on:click="pasteClick"><br>将图片粘贴<br>至此处</i>
  
</el-upload>

</div>
</div>
</div>
  <!-- <ReportPreviewer ref="reportPreviewer" :report="reportForm" /> -->
  <!-- 扫码 -->
  <LinksignPopup :qr="qr" />
  <!-- 查看图像 -->
  <ImageBubble ref="imageBubble" />
  <!-- 播放动态影像 -->
  <VideoView ref="videoView" />
  <!-- 选择医生 -->
  <UserPicker ref="userPicker" @pick="pickUser" />
  <!-- 采集图像右键菜单 -->
  <Contextmenu ref="imageContextmenu"
   :items="imageContextmenuItems"
   @select="handleImageContextMenu" />
   <!-- 选择检查信息 -->
   <ExamPicker ref="examPicker" @pick="handleTransferImage"/>
   <!-- 上传检查影像 -->
   <ReportImageUploader ref="reportImageUploader" @success="afterResbackImage"/>

  <!-- <el-dialog  title="提示"  top='30%'  :visible.sync="d_examResultProp"  width="30%"  >
    <el-radio-group v-model="reportForm.examResultProp.dictValue" :disabled="withdrawable">
        <el-radio v-for="dict in dict.type.uis_exam_result_prop" :key="dict.value" :label="dict.value">{{dict.label}}</el-radio>
    </el-radio-group>
    <span slot="footer" class="dialog-footer">
        <el-button @click="d_examResultProp = false">取 消</el-button>
        <el-button type="primary" @click="save(his_opt);d_examResultProp = false">确 定</el-button>
    </span>
  </el-dialog> -->

  <!--  -->
  <el-dialog title="图像浏览" width="800px" :visible.sync="dialogVisible" :modal="false"   :append-to-body="false"  v-dialog-drag>
    <el-row >
        <el-col :span="21">
          <imageView v-if="dialogVisible" :changeUrl="imageURL.uri">
              <img :src="imageURL.uri" >
          </imageView>
        </el-col>
        <el-col :span="3" v-if="currentItemIdx!=null && !!imagesSet && imagesSet.length > 0">

          <el-button style="margin: 1%;" type="success" v-if="reportable&&!imagesSet[currentItemIdx].selected" @click="selectExamImage(1,imagesSet[currentItemIdx]);imagesSet[currentItemIdx].selected=!imagesSet[currentItemIdx].selected">选择</el-button>
          <el-button style="margin: 1%;" type="warning" v-if="reportable&&imagesSet[currentItemIdx].selected" @click="selectExamImage(0,imagesSet[currentItemIdx]);imagesSet[currentItemIdx].selected=!imagesSet[currentItemIdx].selected">取消</el-button>
          <el-button style="margin: 1%;" :disabled="currentItemIdx===0" type="primary" @click="nextView(-1)">上一张</el-button>
          <el-button style="margin: 1%;" :disabled="currentItemIdx===(imagesSet.length-1)" type="primary" @click="nextView(1)">下一张</el-button>
          <el-button class='view-buttom-delete' type="danger" v-if='reportable' @click="deleteExamImage(imagesSet[currentItemIdx])">删除</el-button>
        </el-col>
    </el-row>
  </el-dialog>
</div>
</template>

<style scoped src="@/assets/styles/pacs/exammanagement/patient/Regist.css"></style>
<style scoped src="@/assets/styles/pacs/report/ReportWriter.css"></style>
<style scoped src="@/assets/styles/pacs/cornerstone-image.css"></style>
<style lang="scss" scoped>

.nested-frame-full{
  width: 100%;
  height: 100%;
  border-width: 0;
}

.el-icon-plus{
  width: 116px;
    height: 116px;
    display:block;
    text-align: center;
    color: blue;
    padding: 40px 0px;
}

.uploadImage{
    width: 120px;
    height: 120px;
    user-select: none;
    border: 1px dashed #d9d9d9;
}
</style>

<script>

export {default} from "@/assets/scripts/otol/report/comp/ReportWriter";

</script>
