<template>
  <div class="app-container">
    <el-row style="height: 100%;">
      <el-col :span="6" :xs="24" style="height: 100%;background-color: #f8fcff;">
        <div>
          <div class="text-center user-avatar-wrap">
            <userAvatar :user="user" />
            <div class="user-avatar-title">
              <span>个人信息</span>
            </div>
          </div>
          <ul class="list-group list-group-striped">
            <li class="list-group-item">
              <svg-icon icon-class="user" />
              <span class="list-group-item-label">用户名称</span>
              <span class="list-group-item-content">{{ user.userName }}</span>
            </li>
            <li class="list-group-item">
              <svg-icon icon-class="phone" />
              <span class="list-group-item-label">手机号码</span>
              <span class="list-group-item-content">{{ user.phonenumber }}</span>
            </li>
            <li class="list-group-item">
              <svg-icon icon-class="email" />
              <span class="list-group-item-label">用户邮箱</span>
              <span class="list-group-item-content">{{ user.email }}</span>
            </li>
            <li class="list-group-item">
              <svg-icon icon-class="tree" />
              <span class="list-group-item-label">所属部门</span>
              <span class="list-group-item-content" v-if="user.dept">{{ user.dept.deptName }} / {{ postGroup }}</span>
            </li>
            <li class="list-group-item">
              <svg-icon icon-class="peoples" />
              <span class="list-group-item-label">所属角色</span>
              <span class="list-group-item-content">{{ roleGroup }}</span>
            </li>
            <li class="list-group-item">
              <svg-icon icon-class="date" />
              <span class="list-group-item-label">创建日期</span>
              <span class="list-group-item-content">{{ user.createTime }}</span>
            </li>
          </ul>
        </div>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card class="nested-card profile-form-card">
          <div slot="header" class="clearfix">
            <span class="nested-card-header-title">基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "userinfo"
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
      });
    }
  }
};
</script>
