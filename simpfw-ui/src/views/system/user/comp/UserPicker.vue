<template>
<el-dialog title="选择用户" :visible="opened" width="800px" custom-class="popupdialog" append-to-body @close="close">

    <!-- 最近选择的用户 -->
    <div class="recent-users" v-if="recentUsers.length > 0">
      <span class="recent-label">最近选择: </span>
      <el-tag
        v-for="user in recentUsers"
        :key="user.userName"
        size="small"
        @click="pick(user)"
        class="recent-user-tag">
        {{user.userName}} | {{user.nickName}}
      </el-tag>
    </div>

    <el-row :gutter="4" class="hei100" style="height: 84vh; max-height: 640px">
      <!--部门数据-->
      <el-col :span="8" class="hei100 flex-container-column">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 4px"
          />
        </div>
        <div class="flex-item-fill scroll-auto">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="16" class="hei100 flex-container-column">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="工号" prop="userName">
            <el-input
              v-model="queryParams.userName"
              placeholder="请输入工号"
              clearable
              style="width: 100px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入姓名"
              clearable
              style="width: 100px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <span class="buttons-pane-gap">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button type="primary" icon="el-icon-check" size="mini" @click="afirmPick" v-if="this.multiple">完成选择</el-button>
            </span>
          </el-form-item>
        </el-form>

        <div class="flex-item-fill scroll-auto">
          <el-table ref="userSheet" v-loading="loading" :data="userList" @selection-change="handleSelectUser">
            <el-table-column type="selection" width="55" :key="Math.random()" v-if="multiple" />
            <el-table-column label="工号" width="120" align="left" key="userName" prop="userName" />
            <el-table-column label="姓名" min-width="120" align="left" key="nickName" prop="nickName" />
            <el-table-column label="科室" width="120" align="left" key="deptName" prop="dept.deptName" />
            <el-table-column
              label="操作"
              align="center"
              width="80"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="pick(scope.row)"
                >选择<i class="el-icon-check el-icon--right"></i></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

</el-dialog>
</template>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel.js";

import { listUser } from "@/api/system/user";

import { treeselect } from "@/api/system/dept";

export default {
  name: "UserPicker",
  extends: BaseDialogModel,
  dicts: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      //限制用户岗位
      limitDeptId:null,
      //
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        handleQuery: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined,
      },

      // 最近选择的用户
      recentUsers: [],

      pickTarget: null,
      multiple: false,
      selectedUsers: null,
      selectedUsersPage: null
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getTreeselect();
    this.loadRecentUsers();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.actSelectedUsers();

      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
          //
          this.$nextTick(this.reaSelectUsers);
        }
      );
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.deptOptions = response.data;
      });
    },    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      if(undefined!=this.limitDeptId&&data.id!=this.limitDeptId) return;
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //显示窗口
    showPicker(opts = {}) {
      //
      const {multiple, target, posts, selectedUsers, roles,deptId} = opts;
      //
      this.multiple = !!multiple;
      this.selectedUsers = selectedUsers;
      this.selectedUsersPage = null;
      //
      this.pickTarget = target;
      //岗位
      this.limitDeptId = deptId;
      this.queryParams.deptId = this.limitDeptId;
      if(posts) {
        posts.forEach((p,i) => {
          this.queryParams["posts[" + i + "].postCode"] = p.postCode;
        });
      }
      //角色
      if(roles) {
        roles.forEach((r,i) => {
          this.queryParams["roles[" + i + "].roleKey"] = r.roleKey;
        });
      }
      //
      this.getList();

      this.open();
    },
    //选择/勾选触发
    pick(row) {
      if(this.multiple) {
        this.$refs.userSheet.toggleRowSelection(row);
        return;
      };

      this.updateRecentUsers(row);
      this.afirmPick(row);
    },
    //完成选择
    afirmPick(dat) {
      if(this.multiple) {
        dat = this.actSelectedUsers();
      }
      this.updateRecentUsers(dat);
      this.$listeners["pick"](this.pickTarget, dat);
      this.close();
    },
    //勾选触发
    handleSelectUser(rows) {
      this.selectedUsersPage = rows;
    },
    //初始选中的用户
    reaSelectUsers() {
      let selectedUsers = this.selectedUsers;
      if(!selectedUsers || !selectedUsers.length) {
        return;
      }

      const sheet = this.$refs.userSheet;
      this.userList.forEach(e => {
        for(let i = selectedUsers.length - 1; i >= 0; i --) {
          let r = selectedUsers[i];
          if(r.userName === e.userName) {
            sheet.toggleRowSelection(e, true);
            selectedUsers.splice(i, 1);
          }
        }
      });
    },
    //选择的
    actSelectedUsers() {
      //当前页勾选结果
      let selectedUsersPage = this.selectedUsersPage;
      if(!selectedUsersPage || !selectedUsersPage.length) {
        return this.selectedUsers;
      }
      let selectedUsers = this.selectedUsers || [];
      //selectedUsersPage.forEach(e => {
      //  if(!selectedUsersPage.find(r => r.userName === e.userName)) {
      //    selectedUsers.push(e);
      //  }
      //});
      this.selectedUsers = selectedUsers.concat(selectedUsersPage);
      return this.selectedUsers;
    },
    // 更新最近选择的用户
    updateRecentUsers(user) {
      if (!user) return;

      const users = Array.isArray(user) ? user : [user];

      users.forEach(u => {
        // 从现有列表中移除相同用户
        this.recentUsers = this.recentUsers.filter(ru => ru.userName !== u.userName);

        // 添加到列表开头
        this.recentUsers.unshift(u);
      });

      // 只保留最近5个
      this.recentUsers = this.recentUsers.slice(0, 5);

      // 保存到本地存储
      localStorage.setItem('userPickerRecentUsers', JSON.stringify(this.recentUsers));
    },

    // 加载最近选择的用户
    loadRecentUsers() {
      try {
        const saved = localStorage.getItem('userPickerRecentUsers');
        if (saved) {
          this.recentUsers = JSON.parse(saved).slice(0, 5);
        }
      } catch (e) {
        console.error('Failed to load recent users', e);
      }
    }
  }
};
</script>

<style scoped>
.recent-users {
  margin-bottom: 10px;
  padding: 5px;
}
.recent-label {
  font-size: 13px;
  margin-right: 8px;
}
.recent-user-tag {
  margin-right: 5px;
  cursor: pointer;
}
</style>
