<template>
  <el-tree
    ref="tree" node-key="uid"
    :data="tree.data"
    :props="tree.props"
    :expand-on-click-node="false"
    :default-expand-all="defaultExpandAll"
    :default-expanded-keys="expendKeys"
    @node-click="handleNodeClick"
  />
</template>

<script>
import BaseTreeModel from "@/assets/scripts/pacs/BaseTreeModel";

import { listData } from "@/api/system/dict/data";

let model = {
  extends: BaseTreeModel,

  props: {
    wrapAll: {type: Boolean, default: true},
    expendKeys: {type: Array, default: () => []},
    dictType: {type: String},
    defaultExpandAll: {type: Boolean, default: false}
  },

  /*data() {
    return deepmerge(BaseTreeModel.data(), {});
  },*/
  methods: {

    buildTree() {
      const p = {pageSize: 9999, dictType: this.dictType};
      listData(p).then(r => {
        let data = r.rows;

        this.convToNode(data);

        if(this.wrapAll) {
          data = [{uid: "C0", label: "全部", children: data}];
        }
        this.tree.data = data;
      });
    },

    convToNode(items) {
      if(!items || !items.length) { return; }

      items.forEach(e => {
        e.value = e.dictCode;
        e.label = e.dictLabel;
        
        this.convToNode(e.children);
      });
    }
  }
};

export default model;

</script>
