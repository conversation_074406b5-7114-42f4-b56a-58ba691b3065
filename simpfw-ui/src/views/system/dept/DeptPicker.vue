<template>
  <el-dialog title="选择科室" :visible.sync="opened" width="618px">
    <!-- 最近选择的科室 -->
    <div class="recent-depts" v-if="recentDepts.length > 0">
      <span class="recent-title">最近选择:</span>
      <el-tag
        v-for="dept in recentDepts"
        :key="dept.deptId"
        class="recent-tag"
        @click="quickSelect(dept)">
        {{ dept.deptName }}
      </el-tag>
    </div>

    <div>
      <el-input v-model="deptName" clearable style="width: 240px" />
      <el-button type="primary" @click="findDept">查找</el-button>
    </div>

    <div style="min-height: 300px;">
      <el-tabs tab-position="top" class="tab-ver">
        <el-tab-pane v-for="cat in deptTypeData" :key="cat.dictValue">
          <span slot="label">{{cat.dictLabel}}</span>
          <div class="dialog-body-items-pane">
            <el-radio-group v-model="checkedDept">
              <el-radio v-for="nod in affectedDeptData" :key="nod.deptId" :label="nod.deptId"
               v-if="0 !== nod.parentId && (cat.dictValue === nod.type || '9' === cat.dictValue && !nod.type)"
               :title="nod.deptName">{{nod.deptName}}</el-radio>
             </el-radio-group>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="submit">确定</el-button>
            <el-button @click="close">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>

  </el-dialog>
</template>

<script>
import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";

import { listData as listDictData } from "@/api/system/dict/data";
import { listDept } from "@/api/system/dept";

const model = {
  extends: BaseDialogModel,

  props: {
    checkedKey: {type: Number}
  },

  data() {
    return {
      checkedDept: null,
      deptTypeData: [],
      deptData: [],
      affectedDeptData: [],
      deptName: null,
      recentDepts: [] // 存储最近选择的科室
    }
  },

  methods: {
    //读取数据
    findData() {
      //科室类别
      if(!this.deptTypeData.length) {
        listDictData({dictType: "uis_dept_cate"}).then(res => {
          this.deptTypeData = res.rows;
        });
      }
      //
      if(!this.deptData.length) {
        listDept().then(res => {
          this.affectedDeptData = this.deptData = res.data.filter(d => !d.extend || 0 !== d.extend.forRegist);
        });
      }

      // 加载最近选择的科室
      this.loadRecentDepts();

      this.open();
    },

    // 加载最近选择的科室
    loadRecentDepts() {
      try {
        const savedDepts = localStorage.getItem('recentSelectedDepts');
        if (savedDepts) {
          this.recentDepts = JSON.parse(savedDepts).slice(0, 5);
        }
      } catch (e) {
        console.error('Failed to load recent depts', e);
        this.recentDepts = [];
      }
    },

    // 保存最近选择的科室
    saveRecentDept(dept) {
      if (!dept) return;

      try {
        let recentDepts = [];
        const savedDepts = localStorage.getItem('recentSelectedDepts');

        if (savedDepts) {
          recentDepts = JSON.parse(savedDepts);
        }

        // 如果已存在，先移除
        const index = recentDepts.findIndex(d => d.deptId === dept.deptId);
        if (index > -1) {
          recentDepts.splice(index, 1);
        }

        // 添加到开头
        recentDepts.unshift(dept);

        // 只保留最近的5条
        if (recentDepts.length > 5) {
          recentDepts = recentDepts.slice(0, 5);
        }

        localStorage.setItem('recentSelectedDepts', JSON.stringify(recentDepts));
        this.recentDepts = recentDepts;
      } catch (e) {
        console.error('Failed to save recent dept', e);
      }
    },

    // 快速选择科室
    quickSelect(dept) {
      this.saveRecentDept(dept);
      this.triggerBind("onChecked", dept);
      this.close();
    },

    //完成勾选
    submit() {
      const dept = this.deptData.find(d => d.deptId === this.checkedDept);
      if (dept) {
        this.saveRecentDept(dept);
      }
      this.triggerBind("onChecked", dept);
      this.close();
    },
    //
    findDept() {
      const deptName = this.deptName;
      if(!deptName) {
        this.affectedDeptData = this.deptData;
        return;
      }

      this.affectedDeptData = this.deptData.filter(d => -1 != d.deptName.indexOf(deptName))
    }
  },

  /*computed: {
    affectedDeptData() {
      const deptName = this.deptName;
      if(!deptName) {
        return this.deptData;
      }

      return deptData0.filter(d => -1 != d.deptName.indexOf(deptName))
    }
  },*/

  watch: {
    checkedKey() {
      this.checkedDept = this.checkedKey;
    }
  }
};
export default model;
</script>

<style scoped>
.dialog-body-items-pane >>> .el-radio{
  display: inline-block;
  width: 146px;
  margin: 7px auto;
  overflow: hidden;
}

.recent-depts {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.recent-title {
  font-weight: bold;
  margin-right: 10px;
}

.recent-tag {
  margin-right: 8px;
  margin-bottom: 5px;
  cursor: pointer;
}
</style>
