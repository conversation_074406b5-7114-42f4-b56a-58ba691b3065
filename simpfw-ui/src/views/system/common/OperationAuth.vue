<template>
  <el-dialog title="操作验证" :visible.sync="opened" width="400px">
    <el-form label-width="80px" class="tight-form" @submit.native.prevent="verify">
      <h3 class="form-fieldset-legend">该操作要求进行密码验证。</h3>
      <el-row>
        <el-col :span="24">
          <el-form-item label="当前用户">
            {{nickName}}             
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="密码">
            <el-input v-model="password" type="password" placeholder="请输入密码" ></el-input>              
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  
    <div slot="footer" class="dialog-footer">
      <div class="foot-tools">
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="verify">验证</el-button>
            <el-button @click="close">关闭</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  
  </el-dialog>
  </template>
  
  <style scoped>
  .form-fieldset-legend{
    margin-left: 16px;
    font-size: 1.1em;
  }  
  </style>
  
  <script>
  import { mapGetters } from 'vuex';
  
  import BaseDialogModel from "@/assets/scripts/pacs/BaseDialogModel";
  
  import { login } from '@/api/login'

  import config from "@/assets/scripts/global/config";

  import DesUtil from "@/utils/DesUtil";
  
  const model = {
  
    extends: BaseDialogModel,
  
    data() {
      return {
        refData: null,
        password: null
      }
    },
  
    methods: {
      //显示选择窗口
      prepare(refData) {
        this.refData = refData;
        this.password = null;

        this.open();
      },
      //
      verify() {
        const name = DesUtil.encode(this.name, config.securityKey);
        const password = DesUtil.encode(this.password, config.securityKey);
        login(name, password, null, null).then(res => {
          this.triggerBind("success", this.refData);
          this.close();
        }).catch(err => {
          if("cancel" !== err) {
          //  this.$modal.alert(err);
          }
        });
      }
    },
  
    computed: {
      //登录的房间
      ...mapGetters(['name', 'nickName']),
    }
  };
  export default model;
  </script>
  