import store from '@/store'

export const PERMI_AUDIT = "exam-report:audit",
  PERMI_SECOND_AUDIT = "exam-report:second_audit",
  PERMI_THIRD_AUDIT = "exam-report:third_audit",
  PERMI_WRITE = "exam-report:write";
export default {
  methods: {
    hasPermi(checkPermi) {
      if(!checkPermi) { throw new Error(`请设置操作权限标签值`) }

      const all_permission = "*:*:*";
      const permissions = store.getters && store.getters.permissions

      return -1 !== permissions.findIndex(permission => {
        return all_permission === permission || checkPermi === permission;
      });
    }
  }
}
