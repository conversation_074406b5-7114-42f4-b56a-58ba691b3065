import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/base.scss' //  css
import '@/assets/font/css/fontFamily.css' //  css
import App from './App'
import store from './store'
import router from './router'
import starter from './assets/starter/index'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import PdfViewerPlugin from './plugins/PdfViewerPlugin';
import { download } from '@/utils/request'

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree, triggerBind } from "@/utils/common";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 通用定义
import { VarModality } from '@/assets/scripts/global/mixins';

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.triggerBind = triggerBind

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})
// 通用定义
Vue.mixin(VarModality);
Vue.use(PdfViewerPlugin)

//点击遮罩不关闭对话框
Element.Dialog.props.closeOnClickModal.default = false;
//对话默认框样式
Element.Dialog.props.customClass.default = 'popupdialog';
//对话默认框样式
Element.Dialog.props.top.default = '6vh';
//表格斑马线
Element.Table.props.stripe.default = true;
//
Element.Aside.props.width.default = "200px";

Vue.config.productionTip = false

starter.startApp()

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
