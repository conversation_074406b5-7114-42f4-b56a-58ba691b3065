{"name": "rheis", "version": "1.2.0", "description": "RUANHANGTECH EIS", "author": "ruanhangtech.com", "license": "MIT", "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve ", "dev:local": "vue-cli-service serve --mode local", "build:prod": "cross-env NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://ruanhangtech.com/uis/base/ui.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "^3.21.1", "cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^4.22.1", "cornerstone-wado-image-loader": "^4.1.3", "cornerstone-web-image-loader": "^2.1.1", "cross-env": "^7.0.3", "dicom-parser": "^1.8.13", "echarts": "^5.3.3", "element-ui": "2.15.6", "file-saver": "2.0.5", "fuse.js": "6.4.3", "hammerjs": "^2.0.8", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "js-md5": "0.8.3", "jsencrypt": "3.2.1", "jspdf": "^2.5.1", "moment": "2.30.1", "moment-timezone": "0.5.46", "nprogress": "0.2.0", "pdfjs-dist": "^2.6.347", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "tui-image-editor": "^3.15.3", "uuid": "3.3.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-json-editor": "^1.4.3", "vue-json-excel": "^0.3.0", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}