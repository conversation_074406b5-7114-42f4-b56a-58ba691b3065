package yyy.xxx.simpfw.module.rad.service.impl;

import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.exception.job.TaskException;
import yyy.xxx.simpfw.module.rad.entity.AllotedWork;
import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;
import yyy.xxx.simpfw.module.rad.entity.WorkShift;
import yyy.xxx.simpfw.module.rad.mapper.AllotedWorkMapper;
import yyy.xxx.simpfw.module.rad.mapper.ScheduleGroupMapper;
import yyy.xxx.simpfw.module.rad.mapper.ScheduleUserMapper;
import yyy.xxx.simpfw.module.rad.mapper.WorkShiftMapper;
import yyy.xxx.simpfw.module.rad.service.SheduleService;
import yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo;
import yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo;
import yyy.xxx.simpfw.quartz.domain.SysJob;
import yyy.xxx.simpfw.quartz.mapper.SysJobMapper;
import yyy.xxx.simpfw.quartz.service.ISysJobService;
import org.quartz.SchedulerException;

import java.util.Date;
import java.util.List;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class ScheduleServiceImpl implements SheduleService {

    @Autowired
    private ScheduleUserMapper scheduleUserMapper;

    @Autowired
    private WorkShiftMapper workShiftMapper;

    @Autowired
    private ScheduleGroupMapper scheduleGroupMapper;

    @Autowired
    private AllotedWorkMapper allotedWorkMapper;

    @Autowired
    private ISysJobService jobService;

    @Autowired
    private SysJobMapper jobMapper;

    public ScheduleUserVo selectUserOne(ScheduleUser param) {
        return scheduleUserMapper.selectOne(param);
    }

    public List<ScheduleUserVo> selectUserList(ScheduleUser param) {
        return scheduleUserMapper.selectList(param);
    }

    public int insertScheduleUser(ScheduleUser user){
        return scheduleUserMapper.insert(user);
    }

    @Transactional
    public boolean updateScheduleUser(ScheduleUser user){
        return scheduleUserMapper.update(user);
    }

    @Override
    @Transactional
    public int deleteScheduleUser(String[] userCodes) {
        return scheduleUserMapper.delete(userCodes);
    }


    public WorkShift selectWorkShiftOne(WorkShift param) {
        return workShiftMapper.selectOne(param);
    }

    public List<WorkShift> selectWorkShiftList(WorkShift param) {
        return workShiftMapper.selectList(param);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public void insertJob(WorkShift workShift,String username)  throws SchedulerException, TaskException{
        SysJob sysJob = new SysJob();
        sysJob.setCreateBy(username);
        sysJob.setJobName("workShiftSchedule_" + workShift.getId().toString());
        sysJob.setJobGroup("workShiftSchedule");
        String cronEx = workShift.getBeginTime().getSeconds()+" ";
        cronEx+=workShift.getBeginTime().getMinutes()+" ";
        cronEx+=workShift.getBeginTime().getHours()+" ";
        cronEx+="* * ?";
        sysJob.setInvokeTarget("reportsAutoAssignService.testS(\"1\")");
        sysJob.setCronExpression(cronEx);
        sysJob.setMisfirePolicy("1");
        sysJob.setConcurrent("1");
        sysJob.setStatus("0");
        sysJob.setCreateTime(new Date());
        jobService.insertJob(sysJob);

        sysJob.setStatus("0");
        jobService.changeStatus(sysJob);

    }

    @Override
    @Transactional
    public int insertWorkShift(WorkShift param,String username)  throws SchedulerException, TaskException{
        int rs = workShiftMapper.insert(param);
        insertJob(param,username);
        return rs;
    }

    @Override
    @Transactional
    public boolean updateWorkShift(WorkShift param,String username)  throws SchedulerException, TaskException{
        boolean res = workShiftMapper.update(param);
        SysJob sysJob = new SysJob();
        sysJob.setJobName("workShiftSchedule_" + param.getId().toString());
        List<SysJob> list = jobMapper.selectJobList(sysJob);
        sysJob = list.get(0);
        if(null==sysJob){
            insertJob(param,username);
        }else{
            String cronEx = param.getBeginTime().getSeconds()+" ";
            cronEx+=param.getBeginTime().getMinutes()+" ";
            cronEx+=param.getBeginTime().getHours()+" ";
            cronEx+="* * ?";
            sysJob.setInvokeTarget("reportsAutoAssignService.testS(\"1\")");
            sysJob.setCronExpression(cronEx);

            jobService.updateJob(sysJob);

        }
        return res;
    }

    @Override
    @Transactional
    public int deleteWorkShift(Long id) throws SchedulerException{
        WorkShift workShift = new WorkShift();
        workShift.setId(id);
        SysJob sysJob = new SysJob();
        workShift = selectWorkShiftOne(workShift);
        sysJob.setJobName("workShiftSchedule_" + workShift.getId().toString());
        List<SysJob> list = jobMapper.selectJobList(sysJob);
        sysJob = list.get(0);
        if(null!=sysJob){
            jobService.deleteJob(sysJob);
        }

        return workShiftMapper.delete(id);
    }

    public ScheduleGroup selectScheduleGroupOne(ScheduleGroup param) {
        return scheduleGroupMapper.selectOne(param);
    }

    public List<ScheduleGroup> selectScheduleGroupList(ScheduleGroup param) {
        return scheduleGroupMapper.selectList(param);
    }

    public AllotedWorkVo selectAllotedWorkOne(AllotedWork param) {
        return allotedWorkMapper.selectOne(param);
    }

    public List<AllotedWorkVo> selectAllotedWorkList(AllotedWork param) {
        return allotedWorkMapper.selectList(param);
    }

    @Override
    public int insertAllotedWork(AllotedWork param) {
        return allotedWorkMapper.insert(param);
    }

    @Override
    public boolean updateAllotedWork(AllotedWork param) {
        return allotedWorkMapper.update(param) > 0;
    }

    @Override
    public int updateAllotedWorkStatusStop(String[] ids){
        return allotedWorkMapper.updateStatusStop(ids);
    }

    @Override
    public int deleteAllotedWork(String[] ids) {
        return allotedWorkMapper.delete(ids);
    }

    @Override
    public int insertScheduleGroup(ScheduleGroup group) {
        return scheduleGroupMapper.insert(group);
    }

    @Override
    public boolean updateScheduleGroup(ScheduleGroup group) {
        return scheduleGroupMapper.update(group);
    }

    @Override
    public int deleteScheduleGroup(String[] ids) {
        return scheduleGroupMapper.delete(ids);
    }
}
