package yyy.xxx.simpfw.module.rad.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.module.pacs.entity.DicomStudy;
import yyy.xxx.simpfw.module.pacs.service.DicomStudyService;
import yyy.xxx.simpfw.module.rad.service.WorkStationService;
import yyy.xxx.simpfw.module.rad.vo.StudyInfoVo;

import java.util.List;

@RestController
@RequestMapping("/exammanagement/workStation")
public class WorkStationController extends BaseController {

    @Autowired private DicomStudyService dicomStudyService;

    @Autowired private WorkStationService service;

    @RequestMapping("/list")
    public TableDataInfo list(StudyInfoVo param) {
        startPage();
        List<DicomStudy> list = service.selectListWorkStation(param);
        return getDataTable(list);
    }


    @GetMapping("/workStationGet")
    public AjaxResult workStationGet(DicomStudy param) {
        DicomStudy study = dicomStudyService.selectOne(param);
//        if(null != study && null != study.getSeriesSet() && !study.getSeriesSet().isEmpty()) {
//            List<DicomImage> images = study.getSeriesSet().get(0).getImagesSet();
//            if(null != images) {
//                images.forEach(e -> securityMask(e));
//            }
//        }
        return AjaxResult.success(study);
    }

}
