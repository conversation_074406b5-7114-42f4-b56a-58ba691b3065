package yyy.xxx.simpfw.module.rad.entity;


import yyy.xxx.simpfw.module.pacs.entity.BaseEntity;

public class ScheduleUser  extends BaseEntity {

  private String userCode;
  private String userName;
  private String noteInfo;
  private String groupCode;

  private Boolean groupIsEmpty;

  private Boolean deleteGroupCode;

  public Boolean getGroupIsEmpty() {
    return groupIsEmpty;
  }

  public void setGroupIsEmpty(Boolean groupIsEmpty) {
    this.groupIsEmpty = groupIsEmpty;
  }

  public Boolean getDeleteGroupCode() {
    return deleteGroupCode;
  }

  public void setDeleteGroupCode(Boolean deleteGroupCode) {
    this.deleteGroupCode = deleteGroupCode;
  }

  public String getUserCode() {
    return userCode;
  }

  public void setUserCode(String userCode) {
    this.userCode = userCode;
  }


  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }


  public String getNoteInfo() {
    return noteInfo;
  }

  public void setNoteInfo(String noteInfo) {
    this.noteInfo = noteInfo;
  }

  public String getGroupCode() {
    return groupCode;
  }

  public void setGroupCode(String groupCode) {
    this.groupCode = groupCode;
  }

}
