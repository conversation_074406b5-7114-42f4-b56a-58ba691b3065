package yyy.xxx.simpfw.module.rad.mapper;



import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;

import java.util.List;

public interface ScheduleGroupMapper {
    ScheduleGroup selectOne(ScheduleGroup param);
    List<ScheduleGroup> selectList(ScheduleGroup param);
    int insert(ScheduleGroup group);
    boolean update(ScheduleGroup group);
    int delete(String[] ids);
}
