package yyy.xxx.simpfw.module.rad.vo;

import yyy.xxx.simpfw.module.rad.entity.AllotedWork;
import yyy.xxx.simpfw.module.rad.entity.WorkShift;

import java.util.List;

public class AllotedWorkVo extends AllotedWork {

    private ScheduleUserVo userVo;

    private WorkShift workShift;

    private List<WorkShift> workShiftList;

    public List<WorkShift> getWorkShiftList() {
        return workShiftList;
    }

    public void setWorkShiftList(List<WorkShift> workShiftList) {
        this.workShiftList = workShiftList;
    }

    public ScheduleUserVo getUserVo() {
        return userVo;
    }

    public void setUserVo(ScheduleUserVo userVo) {
        this.userVo = userVo;
    }

    public WorkShift getWorkShift() {
        return workShift;
    }

    public void setWorkShift(WorkShift workShift) {
        this.workShift = workShift;
    }
}
