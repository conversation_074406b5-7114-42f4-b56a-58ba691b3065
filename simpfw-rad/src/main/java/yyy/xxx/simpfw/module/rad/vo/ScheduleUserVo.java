package yyy.xxx.simpfw.module.rad.vo;

import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;

import java.util.List;

public class ScheduleUserVo extends ScheduleUser {

    private ScheduleGroup group;

    private List<AllotedWorkVo> allotedWorkList;

    public ScheduleGroup getGroup() {
        return group;
    }

    public void setGroup(ScheduleGroup group) {
        this.group = group;
    }


    public List<AllotedWorkVo> getAllotedWorkList() {
        return allotedWorkList;
    }

    public void setAllotedWorkList(List<AllotedWorkVo> allotedWorkList) {
        this.allotedWorkList = allotedWorkList;
    }

}
