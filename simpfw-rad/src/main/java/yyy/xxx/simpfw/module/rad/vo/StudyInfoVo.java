package yyy.xxx.simpfw.module.rad.vo;

import yyy.xxx.simpfw.module.pacs.entity.DicomStudy;
import yyy.xxx.simpfw.module.pacs.entity.Patient;

import java.util.Date;
import java.util.List;

public class StudyInfoVo extends DicomStudy {
    private Patient patientInfo;

    public Patient getPatientInfo() {
        return patientInfo;
    }

    public void setPatientInfo(Patient patientInfo) {
        this.patientInfo = patientInfo;
    }

    private Date CreatedOn;

    public Date getCreatedOn() {
        return CreatedOn;
    }

    public void setCreatedOn(Date createdOn) {
        CreatedOn = createdOn;
    }

    /**
     * 检查起始日期
     */
    private Date examTimeGe, examTimeLt;
    public Date getExamTimeGe() {
        return examTimeGe;
    }
    public void setExamTimeGe(Date examTimeGe) {
        this.examTimeGe = examTimeGe;
    }
    public Date getExamTimeLt() {
        return examTimeLt;
    }
    public void setExamTimeLt(Date examTimeLt) {
        this.examTimeLt = examTimeLt;
    }

    private boolean noExamNo;

    public boolean isNoExamNo() {
        return noExamNo;
    }

    public void setNoExamNo(boolean noExamNo) {
        this.noExamNo = noExamNo;
    }

    private List<String> examModalityPara;

    public List<String> getExamModalityPara() {
        return examModalityPara;
    }

    public void setExamModalityPara(List<String> examModalityPara) {
        this.examModalityPara = examModalityPara;
    }
}
