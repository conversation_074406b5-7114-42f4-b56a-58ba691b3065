package yyy.xxx.simpfw.module.rad.entity;


import yyy.xxx.simpfw.module.pacs.entity.BaseEntity;

public class AllotedWork extends BaseEntity {

  private java.sql.Date workDate;

  private String userCode;
  private String shiftCode;

  private String workType;
  private String diagnosisType;
  private String modalityType;

  private String devicesCode;

  private Integer reportAllocateCount;

  private Integer auditAllocateCount;

  private Integer status;

  @Override
  public Integer getStatus() {
    return status;
  }

  @Override
  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getReportAllocateCount() {
    return reportAllocateCount;
  }

  public void setReportAllocateCount(Integer reportAllocateCount) {
    this.reportAllocateCount = reportAllocateCount;
  }

  public Integer getAuditAllocateCount() {
    return auditAllocateCount;
  }

  public void setAuditAllocateCount(Integer auditAllocateCount) {
    this.auditAllocateCount = auditAllocateCount;
  }

  public String getDevicesCode() {
    return devicesCode;
  }

  public void setDevicesCode(String devicesCode) {
    this.devicesCode = devicesCode;
  }

  public java.sql.Date getWorkDate() {
    return workDate;
  }

  public void setWorkDate(java.sql.Date workDate) {
    this.workDate = workDate;
  }

  public String getWorkType() {
    return workType;
  }

  public void setWorkType(String workType) {
    this.workType = workType;
  }


  public String getDiagnosisType() {
    return diagnosisType;
  }

  public void setDiagnosisType(String diagnosisType) {
    this.diagnosisType = diagnosisType;
  }


  public String getModalityType() {
    return modalityType;
  }

  public void setModalityType(String modalityType) {
    this.modalityType = modalityType;
  }

  public String getUserCode() {
    return userCode;
  }

  public void setUserCode(String userCode) {
    this.userCode = userCode;
  }

  public String getShiftCode() {
    return shiftCode;
  }

  public void setShiftCode(String shiftCode) {
    this.shiftCode = shiftCode;
  }

}
