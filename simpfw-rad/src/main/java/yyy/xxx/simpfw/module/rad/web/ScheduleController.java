package yyy.xxx.simpfw.module.rad.web;

import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.common.exception.job.TaskException;
import yyy.xxx.simpfw.module.rad.entity.AllotedWork;
import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;
import yyy.xxx.simpfw.module.rad.entity.WorkShift;
import yyy.xxx.simpfw.module.rad.service.SheduleService;
import yyy.xxx.simpfw.module.rad.service.impl.ReportsAutoAssignService;
import yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo;
import yyy.xxx.simpfw.module.rad.vo.RadExamInfoVo;
import yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo;
import yyy.xxx.simpfw.quartz.domain.SysJob;
import yyy.xxx.simpfw.quartz.mapper.SysJobMapper;
import yyy.xxx.simpfw.quartz.service.ISysJobService;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/schedule")
public class ScheduleController extends BaseController {

    @Autowired
    private SheduleService service;

    @Autowired
    private ReportsAutoAssignService reportsAutoAssignService;


    /**
     * 读取
     * @param param
     * @return
     */
    @GetMapping("/scheduleUser/get")
    public AjaxResult getScheduleDoctor(ScheduleUser param) {
        ScheduleUserVo doctorVo = service.selectUserOne(param);
        return AjaxResult.success(doctorVo);
    }


    @PostMapping("/scheduleUser/list")
    public TableDataInfo list(ScheduleUser param) {
        startPage();
        List<ScheduleUserVo> list = service.selectUserList(param);
        return getDataTable(list);
    }

    @PostMapping("/scheduleUser/insert")
    public AjaxResult insertScheduleUser(@RequestBody List<ScheduleUser> userList) {
        for(int i=0;i<userList.size();i++){
            if(userList.get(i).isNew()){
                ///w 1 用户存在，但信息改变，得先删除才能添加 2、页面提示用户已经存在
                ScheduleUser user = service.selectUserOne(userList.get(i));
                if(null==user){
                    service.insertScheduleUser(userList.get(i));
                }
            }
            else{
                service.updateScheduleUser(userList.get(i));
            }
        }
        return AjaxResult.success();
        //return AjaxResult.success(service.insertScheduleUser(param));
    }

    @DeleteMapping("/scheduleUser/del/{userCodes}")
    public AjaxResult deleteScheduleUser(@PathVariable String[] userCodes) {

        return toAjax(service.deleteScheduleUser(userCodes));
    }

    @PostMapping("/scheduleGroup/list")
    public TableDataInfo listScheduleGroup(ScheduleGroup group) {
        startPage();
        List<ScheduleGroup> list = service.selectScheduleGroupList(group);
        return getDataTable(list);
    }


    @PostMapping("/scheduleGroup/insert")
    public AjaxResult insertScheduleGroup(@RequestBody ScheduleGroup group) {
        if(group.isNew()){
            service.insertScheduleGroup(group);
        }else{
            service.updateScheduleGroup(group);
        }
        return AjaxResult.success();
        //return AjaxResult.success(service.insertScheduleUser(param));
    }

    @DeleteMapping("/scheduleGroup/del/{ids}")
    public AjaxResult deleteScheduleGroup(@PathVariable String[] ids) {

        return toAjax(service.deleteScheduleGroup(ids));
    }

    /**
     * 读取
     * @param param
     * @return
     */
    @GetMapping("/workShift/get")
    public AjaxResult getWorkShift(WorkShift param) {
        WorkShift workShift = service.selectWorkShiftOne(param);
        return AjaxResult.success(workShift);
    }


    @PostMapping("/workShift/list")
    public TableDataInfo listWorkShift(WorkShift param) {
        startPage();
        List<WorkShift> list = service.selectWorkShiftList(param);
        return getDataTable(list);
    }

    @PostMapping("/workShift/insert")
    public AjaxResult insertworkShift(@RequestBody WorkShift workShift) throws SchedulerException, TaskException {
        //reportsAutoAssignService.test();
        //reportsAutoAssignService.scheduleStart();

        if(null== service.selectWorkShiftOne(workShift)){
            service.insertWorkShift(workShift,getUsername());
        }else{
            service.updateWorkShift(workShift,getUsername());
        }

        return AjaxResult.success();
        //return AjaxResult.success(service.insertScheduleUser(param));
    }

    @DeleteMapping("/workShift/del/{id}")
    public AjaxResult delete(@PathVariable Long id) throws SchedulerException{
        return toAjax(service.deleteWorkShift(id));
    }

    /**
     * 读取
     * @param param
     * @return
     */
    @GetMapping("/allotedWork/get")
    public AjaxResult getAllotedWork(AllotedWork param) {
        AllotedWorkVo allotedWorkVo = service.selectAllotedWorkOne(param);
        return AjaxResult.success(allotedWorkVo);
    }


    @PostMapping("/allotedWork/list")
    public TableDataInfo listAllotedWork(AllotedWork param) {
        startPage();
        List<AllotedWorkVo> list = service.selectAllotedWorkList(param);
        return getDataTable(list);
    }

    @PostMapping("/allotedWork/insert")
    public AjaxResult insertAllotedWork(@RequestBody List<AllotedWork> list) {
        for(int i=0;i<list.size();i++){
            if(list.get(i).isNew()){

                //如果该用户该日期已经存在该班次，则不添加
                AllotedWork allotedWorkI = new AllotedWork();
                allotedWorkI.setUserCode(list.get(i).getUserCode());
                allotedWorkI.setWorkDate(list.get(i).getWorkDate());
                allotedWorkI.setShiftCode(list.get(i).getShiftCode());
                AllotedWorkVo allotedWorkRes = service.selectAllotedWorkOne(allotedWorkI);
                if(null==allotedWorkRes){
                    service.insertAllotedWork(list.get(i));
                }
            }else{
                service.updateAllotedWork(list.get(i));
            }
        }
        return AjaxResult.success();
        //return AjaxResult.success(service.insertScheduleUser(param));
    }

    @DeleteMapping("/allotedWork/del/{ids}")
    public AjaxResult delete(@PathVariable String[] ids) {
        return toAjax(service.deleteAllotedWork(ids));
    }

    @PostMapping("/allotedWork/updateStatusStop/{ids}")
    public AjaxResult updateStatusStop(@PathVariable String[] ids) {
        return toAjax(service.updateAllotedWorkStatusStop(ids));
    }

    @PostMapping("/autoAllocate")
    public AjaxResult autoAllocate(RadExamInfoVo param) {
        //startPage();
        reportsAutoAssignService.autoAllocate(param);
        //List<AllotedWorkVo> list = service.selectAllotedWorkList(param);
        return AjaxResult.success();
    }
}
