package yyy.xxx.simpfw.module.rad.web;

import yyy.xxx.simpfw.common.core.controller.BaseController;


import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import yyy.xxx.simpfw.common.annotation.Log;
import yyy.xxx.simpfw.common.core.domain.entity.*;
import yyy.xxx.simpfw.common.enums.BusinessType;
import yyy.xxx.simpfw.common.utils.*;
import yyy.xxx.simpfw.framework.aspectj.DataScopeAspect;
import yyy.xxx.simpfw.framework.web.service.PermissionService;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.constants.Constants;
import yyy.xxx.simpfw.module.pacs.dict.ExamEnum;
import yyy.xxx.simpfw.module.pacs.dict.RegistWay;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.rad.service.impl.ReportsAutoAssignService;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamPartsVo;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.module.rad.vo.RadExamInfoVo;
import yyy.xxx.simpfw.system.service.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/rad/exammanagement/examInfo")
public class RadExamInfoController extends BaseController {

    @Autowired
    private ExamInfoService service;

    @Autowired
    private ExamReportService reportService;

    @Autowired
    private PatientService patientService;

    @Autowired
    private OrdInterService interfaceService;

    @Autowired
    private CallInfoService callService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ExamPartsService examPartsService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ExamAttachmentService attachmentService;

    @Autowired
    private DicomImageIndexService imageIndexService;

    @Autowired
    private PermissionService ss;

    @Autowired
    private BridgeService bridgeService;

    @Autowired
    private ExamDataCtrlService dataCtrlService;

    @Autowired
    private QueueInterService queueInterService;

    @Autowired
    private ReportsAutoAssignService reportsAutoAssignService;

    /**
     * 查询
     *
     * @param param
     * @return
     */
    @PostMapping("/list")
    public TableDataInfo list(ExamInfoVo param) {
        //在startPage之前执行，否则导致分页失效， MyBatis的分页从ThreadLocal取？
        snipDataScope(param);

        startPage();

        List<ExamInfoVo> list = service.selectList(param);
        return getDataTable(list);
    }

    /**
     * 指定检查
     *
     * @param param
     * @return
     */
    @GetMapping(value = "/get")
    public AjaxResult get(ExamInfo param) {
        snipDataScope(param);

        ExamInfo entity = service.selectOne(param);
        //排队叫号信息
        //if(null == entity.getCallInfo()) {
        entity.setCallInfo(callService.selectByExam(entity.getId()));
        //}
        //签名图片
        if (null != entity.getResultStatus() && (
                ResultStatus.AUDIT.is(entity.getResultStatus().getDictValue())
                        || ResultStatus.REAUDIT.is(entity.getResultStatus().getDictValue())
        )) {
            try {
                reportService.readSignImage(entity);
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
            }
        }

        return AjaxResult.success(entity);
    }

    /**
     * 新增/修改
     */
    @PostMapping(value = "/save")
    @PreAuthorize("@ss.hasAnyPermi('exam-info:edit')")
    public AjaxResult save(@RequestBody ExamInfo entity) {
        try {
            //
            boolean isNew = entity.isNew();
            //状态
            if (!isNew) {
                ExamInfo exists = service.selectById(entity.getId());
                if (null == exists) {
                    return AjaxResult.error("数据不存在。");
                }
                //状态为登记、检查和报告时可修改
                String resultStatusCode;
                if (null != exists.getResultStatus()
                        && StringUtils.isNotBlank(resultStatusCode = exists.getResultStatus().getDictValue())
                        && !ResultStatus.REGIST.is(resultStatusCode)
                        && !ResultStatus.EXAM.is(resultStatusCode)
                        && !ResultStatus.REPORT.is(resultStatusCode)) {
                    return AjaxResult.error("无法更新检查信息，原因：当前检查状态为%s", entity.getResultStatus().getDictLabel());
                }
                //医嘱退费
                String existsOrdId = exists.getOrdId(), ordId = entity.getOrdId();
                if (logger.isDebugEnabled()) {
                    if (StringUtils.isNotBlank(existsOrdId)) {
                        logger.debug("医嘱 {} {} => {}", entity.getExamNo(), existsOrdId, ordId);
                    }
                }
                if (StringUtils.isNotBlank(existsOrdId) && !existsOrdId.equals(ordId)) {
                    String[] existsOrdsId = existsOrdId.split(Const.at), ordsId = StringUtils.isNotBlank(ordId) ? ordId.split(Const.at) : null;
                    //找出退费的医嘱
                    for (String eoi : existsOrdsId) {
                        if (null == ordsId || !ArrayUtils.contains(ordsId, eoi)) {
                            interfaceService.cancelFeeApp(exists, getLoginUser().getUser(), eoi);
                        }
                    }
                }
            } else {
                entity.setCreator(getLoginUser().getUser());
            }
            //生成检查号
            if (StringUtils.isBlank(entity.getExamNo())) {
                entity.setExamNo(service.makeExamNo(entity.getExamModality().getDictValue()));
            }
            //病历号
            if (StringUtils.isBlank(entity.getPatientInfo().getMedicalRecordNo())) {
                entity.getPatientInfo().setMedicalRecordNo(patientService.makeMedicalRecordNo());
            }
            //
            service.theDoctors(entity);
            //申请科室编码和名称对不上，认为是手输，不保存编码
            SysDept reqDept = entity.getReqDept();
            String reqDeptCode = null;
            if (null != reqDept) {
                if (StringUtils.isNotBlank(reqDept.getDeptCode())) {
                    String reqDeptName = reqDept.getDeptName();
                    if (StringUtils.isNotBlank(reqDeptName)) {
                        SysDept dept = deptService.selectDeptByCode(reqDept.getDeptCode());
                        if (null != dept && reqDeptName.equals(dept.getDeptName())) {
                            reqDeptCode = reqDept.getDeptCode();
                        }
                    }
                }
                reqDept.setDeptCode(reqDeptCode);
            }
            //保存登记信息
            service.saveOrUpdate(entity);
            //回写接口服务医嘱状态
            /*if(logger.isInfoEnabled() && null != entity.getCreator() && "TJauto".equalsIgnoreCase(entity.getCreator().getUserName())) {
                logger.debug("isNew={}, registWay={}", isNew, entity.getRegistWay());
            }*/
            //就诊类别不是体检的向his登记
            if (isNew && RegistWay.INTER.is(entity.getRegistWay())
                    && (null == entity.getInpType() || !ExamEnum.InpTypeBodyExam.equals(entity.getInpType().getDictValue()))) {
                interfaceService.regOrd(entity);
            }
            //排队叫号接口
            try {
                // queueInterService.enqueue(entity.getCallInfo());
                // 20240325  调用超声的排队叫号服务
                ///待定queueInterService.enqueueV2(entity.getCallInfo(), Constants.PARAM_UIS_QUEUE_SERVICE_BASEURL);
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
            }

            return AjaxResult.success(entity);
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 复制检查
     *
     * @param param 源检查信息
     */
    @Log(title = "复制检查", businessType = BusinessType.INSERT)
    @RequestMapping("/copy")
    public AjaxResult copy(@RequestBody ExamInfo param) {
        if (null == param || null == param.getId()) {
            return AjaxResult.error("没有要复制的检查。");
        }
        //读取源检查信息
        ExamInfo entity = service.selectById(param.getId());
        if (null == entity) {
            return AjaxResult.error("找不到要复制的检查。");
        }
        SysDictData resultStatus = entity.getResultStatus();
        String resultStatusCode = null != resultStatus ? resultStatus.getDictValue() : null;
        if (StringUtils.isNotBlank(resultStatusCode)
                && ResultStatus.REGIST.is(resultStatusCode)
                && ResultStatus.EXAM.is(resultStatusCode)) {
            return AjaxResult.error("只允许复制工作状态为登记完成和已检查的检查信息。");
        }
        //
        entity.setId(null);
        entity.setOriginId("CP" + param.getId());//标记源检查

        //return save(entity);
        return toAjax(service.saveOrUpdate(entity));
    }

    @Log(title = "拆分检查", businessType = BusinessType.INSERT)
    @RequestMapping("/split")
    public AjaxResult split(@RequestBody List<ExamInfo> items) {
        if (null == items || items.isEmpty()) {
            return AjaxResult.error("没有要拆分你的检查。");
        }
        //读取源检查信息

        Optional<ExamInfo> opt = items.stream().filter(r -> null != r.getId()).findFirst();
        ExamInfo param = null != opt && opt.isPresent() ? opt.get() : null;
        if (null == param) {
            return AjaxResult.error("要拆分的检查不存在。");
        }
        ExamInfo origEntity = service.selectById(param.getId());
        if (null == origEntity) {
            return AjaxResult.error("找不到要拆分的检查。");
        }
        SysDictData resultStatus = origEntity.getResultStatus();
        String resultStatusCode = null != resultStatus ? resultStatus.getDictValue() : null;
        if (StringUtils.isNotBlank(resultStatusCode)
                && ResultStatus.REGIST.is(resultStatusCode)
                && ResultStatus.EXAM.is(resultStatusCode)) {
            return AjaxResult.error("只允许拆分工作状态为登记完成和已检查的检查信息。");
        }
        //
        for (ExamInfo item : items) {
            if (null != item.getId()) {
                continue;
            }
            origEntity.setId(null);
            origEntity.setOriginId("SP" + param.getId());//标记源检查
            origEntity.setExamParts(item.getExamParts());
            origEntity.setEquipRoom(item.getEquipRoom());
            service.saveOrUpdate(origEntity);
        }
        //
        origEntity.setId(param.getId());
        origEntity.setExamParts(param.getExamParts());
        origEntity.setEquipRoom(param.getEquipRoom());
        service.saveOrUpdate(origEntity);

        return AjaxResult.success();
    }

    /**
     * 修改
     */
    @PutMapping(value = "/update")
    @PreAuthorize("@ss.hasAnyPermi('exam-info:edit')")
    public AjaxResult update(@RequestBody ExamInfo entity) {
        return save(entity);//toAjax(service.saveOrUpdate(entity));
    }
    /**
     *

     @PutMapping(value = "/saveReport")
     public AjaxResult saveReport(@RequestBody ExamInfo entity) {
     return toAjax(service.saveReport(entity));
     }*/

    /**
     * 更新检查状态
     *
     * @param entity 获取检查id和更新的检查状态（resultStatus）
     * @return
     */
    @Log(title = "患者检查", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/updateResultStatus")
    public AjaxResult updateResultStatus(@RequestBody RadExamInfoVo entity) {
//        SysDictData resultStatus;
//        String resultStatusCode;
//        if(null == entity.getId()
//                || null == (resultStatus = entity.getResultStatus())
//                || !NumberUtils.isDigits(resultStatusCode = resultStatus.getDictValue())) {
//            return AjaxResult.error("请提供检查id和检查状态。");
//        }
//        //读取检查信息，1验证检查是否存在，2-对比当前状态和更新的状态
//        ExamInfo entity0 = service.selectOne(entity);
//        if(null == entity0) {
//            return AjaxResult.error("该检查不存在。");
//        }
//        //状态按状态升序走
//        //登记为0，已检查为1，已报告为2，已审核为3，复审状态为4，已打印为5，已完成为6，已取消为10
        try {
//            SysDictData resultStatus0 = entity0.getResultStatus();
//            String resultStatusCode0;
//            //状态代码按检查执行顺序一致：0-登记完成，1-开始检查，2-报告书写完成。。。，只能往后更新
//            //10-取消检查，可以恢复取消的检查
//            if(null != resultStatus0 && NumberUtils.isDigits(resultStatusCode0 = resultStatus0.getDictValue())) {
//                int resultStatusCancel = 10, resultStatusRegist = 0
//                        , resultStatusValue = Integer.valueOf(resultStatusCode)
//                        , resultStatusValue0 = Integer.valueOf(resultStatusCode0);
//
//                if(resultStatusRegist == resultStatusValue && resultStatusCancel != resultStatusValue0
//                        || resultStatusRegist != resultStatusValue && resultStatusValue0 > resultStatusValue) {
//                    String dictLabel = DictUtils.getDictLabel(Const.DICT_TYPE_RESULT_STATUS, resultStatusCode0);
//                    return AjaxResult.error(String.format("该检查状态为%s，无法更新为请求的状态。", dictLabel));
//                }
//            }
//            //排队信息
//            CallInfoVo callInfo = callService.selectByExam(entity.getId());
//            if(null != callInfo && (null != callInfo.getFirstCallTime() || null != callInfo.getLastCallTime())) {
//                Date now = DateUtils.getNowDate();
//                if(null == callInfo.getFirstEndCallTime()) {
//                    callInfo.setFirstEndCallTime(now);
//                } else {
//                    callInfo.setLastEndCallTime(now);
//                }
//                callService.insertOrUpdate(callInfo);
//            }
            //
            reportsAutoAssignService.allocateReport(entity, null, false);
            return toAjax(service.updateResultStatus(entity));
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 删除
     */
    @Log(title = "患者检查", businessType = BusinessType.DELETE)
    //@PreAuthorize("@ss.hasPermi('exam-info:delete')")
    @PreAuthorize("@ss.hasAnyPermi('exam-info:delete,exam-info:delete-regist')")
    @PutMapping("/del/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        ExamInfo entity = service.selectById(id);
        if (null == entity) {
            return AjaxResult.error("检查不存在。");
        }

        SysDictData resultStatus = entity.getResultStatus();
        String resultStatusCode = null != resultStatus ? resultStatus.getDictValue() : null;
        SysUser usr = super.getLoginUser().getUser();
        if (ResultStatus.REGIST.is(resultStatusCode)
                || (ss.hasPermi("exam-info:delete") && (ResultStatus.EXAM.is(resultStatusCode)
                || ResultStatus.REPORT.is(resultStatusCode)))) {
            int res = service.delete(id);
            if (1 == res) {
                interfaceService.cancelFeeApp(entity, usr, null);
            }
            return toAjax(res);
        }
        return AjaxResult.error(String.format("无法删除该检查，原因：检查进度为%s", (null != resultStatus ? resultStatus.getDictLabel() : "未知")));
    }

    /**
     * 删除
     */
    @Log(title = "患者检查", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasAnyPermi('exam-info:delete,exam-info:delete-regist')")
    @PutMapping("/undoDel/{id}")
    public AjaxResult undoDelete(@PathVariable Long id) {
        ExamInfo entity = service.selectById(id);
        if (null == entity) {
            return AjaxResult.error("检查不存在。");
        }

        int res = service.undoDelete(id);
        if (1 == res) {
            interfaceService.regOrd(entity);
        }
        return toAjax(res);
    }

    /**
     * 调用HIS接口获取登记信息
     */
    @Log(title = "患者检查", businessType = BusinessType.INVOKEREGISTAPI)
    @GetMapping("/loadRemote/{registWay}/{registCode}")
    public AjaxResult loadRemote(@PathVariable String registWay, @PathVariable String registCode
            , String ordStatus, String ordId) {
        try {
            List<ExamInfo> items = interfaceService.getOrd(registWay, registCode, ordStatus, ordId);
            if (null == items) {
                logger.info("无医嘱信息。");
                return AjaxResult.success();
            }
            //
            AjaxResult res = AjaxResult.success(items);
            //
            try {
                Set<String> modified = new HashSet<>();
                for (ExamInfo exam : items) {
                    localizeExamInfo(exam, modified);
                }
                res.put("modified", modified);
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
            }
            //
            return res;
        } catch (Exception err) {
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 从接口创建不存在的科室、用户字典和部位
     *
     * @param item
     * @param modified
     * @throws Exception
     */
    private void localizeExamInfo(Object item, Set<String> modified) throws Exception {
        if (null == item) {
            return;
        }

        Class<?> clz = (item instanceof ExamInfo) ? ExamInfo.class : (item instanceof Patient ? Patient.class : null);
        Field[] fields = clz.getDeclaredFields();
        //根科室
        SysDept topDept = null;
        //“医生”岗位
        SysPost docPos = null;

        for (Field field : fields) {
            field.setAccessible(true);
            //属性值
            Object fldVal = field.get(item);
            if (null == fldVal) {
                continue;
            }
            //科室，如不存在，新增
            if (fldVal instanceof SysDept) {
                SysDept dept = (SysDept) fldVal;
                //
                if (StringUtils.isBlank(dept.getDeptCode())) {
                    continue;
                }
                //科室编码是否存在
                SysDept dept0 = deptService.selectDeptByCode(dept.getDeptCode());
                if (null != dept0) {
                    dept.setDeptId(dept0.getDeptId());
                    continue;
                }
                //获取上级科室
                if (null == topDept) {
                    topDept = new SysDept();
                    topDept.setParentId(0L);
                    List<SysDept> rows = deptService.selectDeptList(topDept);
                    if (null == rows || rows.isEmpty()) {
                        continue;
                    }
                    topDept = rows.get(0);
                }
                dept.setParentId(topDept.getDeptId());
                dept.setOrderNum("9999");
                //
                try {
                    deptService.insertDept(dept);
                    //
                    modified.add("dept");
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                }
            } else if (fldVal instanceof SysUser) {
                //用户，如不存在，新增
                SysUser usr = (SysUser) fldVal;
                //
                String usrNam = usr.getUserName();
                if (StringUtils.isBlank(usrNam)) {// && StringUtils.isBlank(usr.getNickName())
                    continue;
                }
                /*if(StringUtils.isBlank(usrNam)) {
                    try {
                        String[] py = PinyinUtil.toPinyin(usr.getNickName(), true, null);
                        usrNam = py[0];
                        usrNam += yyy.xxx.simpfw.common.utils.StringUtils.randomNumbers(2);
                        usr.setUserName(usrNam);
                    } catch (Exception err) { logger.error(err.getMessage(), err); }
                }*/
                SysUser usr0 = userService.selectUserByUserName(usrNam);
                if (null != usr0) {
                    usr.setUserId(usr0.getUserId());
                    continue;
                }
                //医生岗位
                if (null == docPos) {
                    docPos = postService.selectPostByCode("YS");
                    if (null == docPos) {
                        continue;
                    }
                }
                usr.setPostIds(new Long[]{docPos.getPostId()});
                //初始密码
                usr.setPassword(SecurityUtils.encryptPassword(configService.selectConfigByKey("sys.user.initPassword")));
                try {
                    userService.insertUser(usr);
                    //
                    modified.add("user");
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                }
            } else if (fldVal instanceof SysDictData) {
                //字典，如不存在，新增
                SysDictData dictData = (SysDictData) fldVal;

                String dictType = dictData.getDictType(), dictValue = dictData.getDictValue(), dictLabel = dictData.getDictLabel();
                //
                if (StringUtils.isBlank(dictType)
                        || StringUtils.isBlank(dictValue) && StringUtils.isBlank(dictLabel)) {
                    continue;
                }
                //存在字典值
                if (StringUtils.isNotBlank(dictValue)) {
                    String dict0 = dictDataService.selectDictLabel(dictType, dictValue);
                    if (StringUtils.isNotBlank(dict0)) {
                        if (StringUtils.isBlank(dictLabel)) {
                            dictData.setDictLabel(dict0);
                        }
                        continue;
                    }
                }
                //存在字典名
                if (StringUtils.isNotBlank(dictLabel)) {
                    String dict0 = DictUtils.getDictValue(dictType, dictLabel);
                    if (StringUtils.isNotBlank(dict0)) {
                        //if(StringUtils.isBlank(dictValue)) {
                        dictData.setDictValue(dict0);
                        //}
                        continue;
                    }
                }
                //
                if (StringUtils.isBlank(dictValue)) {
                    //yyy.xxx.simpfw.common.utils.StringUtils.randomChars(4)
                    //拼音首字母做字典值
                    String[] py = PinyinUtil.toPinyin(dictLabel, 1, null);
                    dictValue = py[0];
                    //避免字典纸重叠
                    if (StringUtils.isNotBlank(DictUtils.getDictLabel(dictType, dictValue))) {
                        dictValue += yyy.xxx.simpfw.common.utils.StringUtils.randomNumbers(2);
                    }
                    dictData.setDictValue(dictValue);
                }
                if (StringUtils.isBlank(dictLabel)) {
                    dictData.setDictLabel(dictData.getDictValue());
                }

                dictData.setDictSort(9999L);
                try {
                    dictDataService.insertDictData(dictData);
                    //
                    modified.add("dict::" + dictType);
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                }
            } else if ("examParts".equals(field.getName())) {
                //部位，不存在则新建
                List<ExamParts> parts = (List<ExamParts>) fldVal;
                if (parts.isEmpty()) {
                    continue;
                }
                for (ExamParts part : parts) {
                    ExamParts examParts = part;
                    String partsCode = examParts.getPartsCode(), partsName = examParts.getPartsName();
                    //是否存在部位编码
                    if (StringUtils.isNotBlank(partsCode)) {
                        ExamParts examParts0 = new ExamParts();
                        examParts0.setPartsCode(partsCode);
                        examParts0 = examPartsService.selectOne(examParts0);
                        if (null != examParts0) {
                            examParts.setId(examParts0.getId());
                            examParts.setPartsName(examParts0.getPartsName());
                            continue;
                        }
                    }
                    //是否存在部位名称
                    if (StringUtils.isNotBlank(partsName)) {
                        ExamParts examParts0 = new ExamParts();
                        examParts0.setPartsName(partsName);
                        List<ExamPartsVo> rows = examPartsService.selectList(examParts0);
                        if (null != rows && !rows.isEmpty()) {
                            examParts0 = rows.get(0);
                            examParts.setId(examParts0.getId());
                            examParts.setPartsCode(examParts0.getPartsCode());
                            continue;
                        }
                    }
                    //
                    if (StringUtils.isBlank(partsCode)) {
                        String[] pys = PinyinUtil.toPinyin(partsName, new PinyinUtil.Options().setCount(1).setWordOnly(true));
                        final String py = pys.length > 0 ? pys[0] : yyy.xxx.simpfw.common.utils.StringUtils.randomChars(16);
                        partsCode = String.format("%s.%s", py, yyy.xxx.simpfw.common.utils.StringUtils.randomChars(2));
                        examParts.setPartsCode(partsCode);
                    }
                    examPartsService.insert(examParts);
                }
                //
                try {
                    final String mod = "examParts";
                    if (!modified.contains(mod)) {
                        modified.add(mod);
                    }
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                }
            } else if (fldVal instanceof Patient) {
                localizeExamInfo(fldVal, modified);
            }
        }
    }

    /**
     * 更新诊前准备状态
     *
     * @param entity
     * @return
     */
    @Log(title = "患者检查", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateExamPrerequire")
    public AjaxResult updateExamPrerequire(@RequestBody ExamInfo entity) {
        try {
            service.updateExamPrerequire(entity);
            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 下午检查/延迟检查
     *
     * @param entity
     * @return
     */
    @Log(title = "患者检查", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateExamPeriod")
    public AjaxResult updateExamPeriod(@RequestBody ExamInfo entity) {
        try {
            service.updateExamPeriod(entity);
            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 科室及个人数据权限，适用检查信息查询
     *
     * @param examInfo
     */
    private void snipDataScope(ExamInfo examInfo) {
        //
        SysUser usr = getLoginUser().getUser();
        //用户所属科室
        /*SysDept usrDept = usr.getDept();
        if(null == usrDept || null == usrDept.getDeptId()) {
            SysUser usr0 = userService.selectUserById(usr.getUserId());
            if(null != usr0.getDept()) {
                usr.setDept(usrDept = usr0.getDept());
                usr.setDeptId(usrDept.getDeptId());
            }
        } else if(StringUtils.isBlank(usrDept.getDeptCode())) {
            usrDept = deptService.selectDeptById(usr.getDeptId());
        }*/
        //usrDept = null != usrDept && StringUtils.isNotBlank(usrDept.getDeptCode())? usrDept : deptService.selectDeptById(usr.getDeptId());
        //以所属科室??过滤体检中心只能查就诊类型为“体检”的数据
        /*if(!usr.isAdmin() && null != usrDept && "C9901".equals(usrDept.getDeptCode())) {
            if(logger.isDebugEnabled()) { logger.debug("体检中心用户>体检数据: {}", usr.getUserName()); }
            SysDictData inpType = new SysDictData();
            inpType.setDictValue("H");
            examInfo.setInpType(inpType);
        }*/
        //获取角色拥有的数据权限
        Set<SysDept> depts = bridgeService.selectDeptToDataScope(usr);
        //所有数据权限
        if (null == depts) {
            return;
        }
        //
        StringBuilder sqlDataScope = new StringBuilder();
        /*
         * 申请科室、检查科室、登记人科室
         * req_dept_code, exam_dept_code, regist_user_code
         */
        if (!depts.isEmpty()) {
            int i;
            //申请科室
            sqlDataScope.append(" OR h.req_dept_code in (");
            i = 0;
            for (SysDept dept : depts) {
                if (0 < i) {
                    sqlDataScope.append(Const.comma);
                }
                sqlDataScope.append(String.format("'%s'", dept.getDeptCode()));
                ++i;
            }
            sqlDataScope.append(")");
            //检查科室
            sqlDataScope.append(" OR h.exam_dept_code in (");
            i = 0;
            for (SysDept dept : depts) {
                if (0 < i) {
                    sqlDataScope.append(Const.comma);
                }
                sqlDataScope.append(dept.getDeptId());
                ++i;
            }
            sqlDataScope.append(")");
            //登记人所属科室
            sqlDataScope.append(" OR h.regist_user_code in (select user_name from v_sys_user where dept_id in (");
            i = 0;
            for (SysDept dept : depts) {
                if (0 < i) {
                    sqlDataScope.append(Const.comma);
                }
                sqlDataScope.append(dept.getDeptId());
                ++i;
            }
            sqlDataScope.append("))");
        }
        //个人创建的
        if (usr.getRoles().stream().anyMatch(r -> DataScopeAspect.DATA_SCOPE_SELF.equals(r.getDataScope()))) {
            sqlDataScope.append(String.format(" OR h.regist_user_code='%s' ", usr.getUserName()));
        }

        if (sqlDataScope.length() > 0) {
            //删除“ OR ”
            sqlDataScope.delete(0, 4).insert(0, " AND (").append(")");
            //其它权限设置
            Map<String, List<ExamDataCtrlDetail>> dataCtrl = dataCtrlService.findDataScope(usr);
            if (null != dataCtrl && !dataCtrl.isEmpty()) {
                List<ExamDataCtrlDetail> dataCtrlDetails;
                //限定房间
                dataCtrlDetails = dataCtrl.get(ExamDataCtrlDetail.ItemTypeRoom);
                if (null != dataCtrlDetails && !dataCtrlDetails.isEmpty()) {
                    sqlDataScope.append(" and (ci.call_room_code is null or ci.call_room_code in (");
                    int i = 0;
                    for (ExamDataCtrlDetail e : dataCtrlDetails) {
                        if (i > 0) {
                            sqlDataScope.append(Const.comma);
                        }
                        sqlDataScope.append(String.format("'%s'", e.getItemId()));
                        ++i;
                    }
                    sqlDataScope.append("))");
                }
                //限定数据字典
                dataCtrlDetails = dataCtrl.get(ExamDataCtrlDetail.ItemTypeDict);
                if (null != dataCtrlDetails && !dataCtrlDetails.isEmpty()) {
                    List<ExamDataCtrlDetail> dataCtrlDetails0;
                    //限定检查类型
                    final String dictTypeFlagMod = "uis_exam_modality:";
                    dataCtrlDetails0 = dataCtrlDetails.stream().filter(e -> e.getItemId().startsWith(dictTypeFlagMod))
                            .collect(Collectors.toList());
                    if (null != dataCtrlDetails0 && !dataCtrlDetails0.isEmpty()) {
                        sqlDataScope.append(" and (h.exam_modality_code is null or h.exam_modality_code in (");
                        int i = 0;
                        for (ExamDataCtrlDetail e : dataCtrlDetails0) {
                            if (i > 0) {
                                sqlDataScope.append(Const.comma);
                            }
                            sqlDataScope.append(String.format("'%s'", e.getItemId().substring(dictTypeFlagMod.length())));
                            ++i;
                        }
                        sqlDataScope.append("))");
                    }
                    //限定就诊类别
                    String dictTypeFlagInp = "uis_inp_type:";
                    dataCtrlDetails0 = dataCtrlDetails.stream().filter(e -> e.getItemId().startsWith(dictTypeFlagInp))
                            .collect(Collectors.toList());
                    if (null != dataCtrlDetails0 && !dataCtrlDetails0.isEmpty()) {
                        sqlDataScope.append(" and (h.inp_type_code is null or h.inp_type_code in (");
                        int i = 0;
                        for (ExamDataCtrlDetail e : dataCtrlDetails0) {
                            if (i > 0) {
                                sqlDataScope.append(Const.comma);
                            }
                            sqlDataScope.append(String.format("'%s'", e.getItemId().substring(dictTypeFlagInp.length())));
                            ++i;
                        }
                        sqlDataScope.append("))");
                    }
                    //限定检查项目
                    String dictTypeFlagItem = "uis_exam_item:";
                    dataCtrlDetails0 = dataCtrlDetails.stream().filter(e -> e.getItemId().startsWith(dictTypeFlagItem))
                            .collect(Collectors.toList());
                    if (null != dataCtrlDetails0 && !dataCtrlDetails0.isEmpty()) {
                        sqlDataScope.append(" and (h.exam_item_code is null or h.exam_item_code in (");
                        int i = 0;
                        for (ExamDataCtrlDetail e : dataCtrlDetails0) {
                            if (i > 0) {
                                sqlDataScope.append(Const.comma);
                            }
                            sqlDataScope.append(String.format("'%s'", e.getItemId().substring(dictTypeFlagItem.length())));
                            ++i;
                        }
                        sqlDataScope.append("))");
                    }
                }

            }
        } else {
            sqlDataScope.append(" AND 1=0");
        }
        examInfo.getParams().put(DataScopeAspect.DATA_SCOPE, sqlDataScope);
    }
}

