package yyy.xxx.simpfw.module.rad.mapper;

import yyy.xxx.simpfw.module.pacs.mapper.BaseMapper;
import yyy.xxx.simpfw.module.rad.entity.AllotedWork;
import yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo;

import java.util.List;

public interface AllotedWorkMapper extends BaseMapper<AllotedWork, AllotedWorkVo> {

    List<AllotedWorkVo> selectMinWorkCount(AllotedWorkVo allotedWorkS);
    int delete(String[] ids);
    int updateStatusStop(String[] ids);
}
