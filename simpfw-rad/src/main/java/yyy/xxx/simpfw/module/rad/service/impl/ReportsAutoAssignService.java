package yyy.xxx.simpfw.module.rad.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.spring.SpringUtils;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.rad.vo.RadExamInfoVo;
import yyy.xxx.simpfw.module.rad.mapper.AllotedWorkMapper;
import yyy.xxx.simpfw.module.pacs.mapper.ExamPartsMapper;
import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.WorkShift;
import yyy.xxx.simpfw.module.rad.mapper.RadExamInfoMapper;
import yyy.xxx.simpfw.module.rad.mapper.WorkShiftMapper;
import yyy.xxx.simpfw.module.pacs.service.ExamInfoService;
import yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Service("reportsAutoAssignService")
@DataSource(value = DataSourceType.SLAVE)
public class ReportsAutoAssignService {

    private static final Logger log = LoggerFactory.getLogger(ReportsAutoAssignService.class);

    @Autowired
    private WorkShiftMapper workShiftMapper;

    @Autowired
    private RadExamInfoMapper examInfoMapper;

    @Autowired
    private AllotedWorkMapper allotedWorkMapper;

    @Autowired
    private ExamPartsMapper examPartsMapper;

    @Autowired
    private ExamInfoService infoService;

    @Autowired private RedisCache redis;

//   private Lock lock = new  ReentrantLock();

    private ScheduledExecutorService executor = Executors.newScheduledThreadPool(20);

//    //定时器线程锁，只能有一个定时器正在执行
//    private Lock scheduleLock = new  ReentrantLock();
//
//    //定时器线任务程锁
//    private Lock scheduleTaskLock = new  ReentrantLock();

    private List<ScheduledFuture<?>> scheduledFutureList = new ArrayList<>();

    //线程池锁
    private final String redisLockExecutor = "redisLockExecutor";

    //报告分配锁
    private  final String redisLockReportAllocate = "redisLockReportAllocate";

    //报告更新锁
    private  final String redisLockReportUpdate = "redisLockReportUpdate";

    public void test(){
//        ScheduledExecutorService executor = Executors.newScheduledThreadPool(2);
//        System.out.println("开始时间：" + new Date());
//        ScheduledFuture<?> scheduledFuture1 = executor.schedule(() -> {
//            testTask();
//            try {
//                Thread.sleep(20000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            System.out.println("1执行结束时间：" + new Date());
//        }, 5000, TimeUnit.MILLISECONDS);
//        ScheduledFuture<?> scheduledFuture2 = executor.schedule(() -> {
//            testTask();
//        }, 20000, TimeUnit.MILLISECONDS);
//        try {
//            Thread.sleep(10000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
////           scheduledFuture.isDone();
//        scheduledFuture1.cancel(false);
//        scheduledFuture2.cancel(false);
    }

    public void testTask(){
        System.out.println("执行时间：" + new Date());
    }

    /**
     * 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库ID和任务组名，否则会导致脏数据）
     */
    @PostConstruct
    public void init()
    {
        //scheduleStart();
        /*redisLockExecutor = redissonClient.getLock("redisLockExecutor");
        redisLockReportAllocate = redissonClient.getLock("redisLockReportAllocate");
        redisLockReportUpdate = redissonClient.getLock("redisLockReportUpdate");*/

    }

    public List<WorkShift> getNowWorkShift(){
        List<WorkShift> listRe = new ArrayList<>();
        WorkShift workShifts = new WorkShift();
        workShifts.setOrderBy("begin_time");
        List<WorkShift> listWorkShift = workShiftMapper.selectList(workShifts);
        for(int i=0;i<listWorkShift.size();i++) {
            WorkShift workShift = listWorkShift.get(i);
            // 获得当前时间
            LocalDateTime now = LocalDateTime.now();
            //上班时间
            java.sql.Time beginTime = workShift.getBeginTime();
            // 获取本周四 18:00:00.000
            LocalDateTime startRunTime =
                    now.withHour(beginTime.getHours()).withMinute(beginTime.getMinutes()).withSecond(beginTime.getSeconds()).withNano(0);

            //下班时间
            java.sql.Time endTime = workShift.getEndTime();
            // 获取本周四 18:00:00.000
            LocalDateTime endRunTime =
                    now.withHour(endTime.getHours()).withMinute(endTime.getMinutes()).withSecond(endTime.getSeconds()).withNano(0);
            // 如果当前时间已经超过 本周四 18:00:00.000， 那么找下周四 18:00:00.000
            if (now.compareTo(startRunTime) < 0 || now.compareTo(endRunTime) > 0) {
                continue;
            }
            listRe.add(workShift);
        }
        return listRe;
    }

    //解决事务失效
    private ReportsAutoAssignService getService(){
        return SpringUtils.getBean(this.getClass());
    }

    //分配报告
    @Transactional
    public void allocate(ExamInfo examInfoVo,List<WorkShift> workShiftList,String resultStatusCode){

        //检查状态
        String workType = resultStatusCode;

        //获取分组
        ExamParts part = examInfoVo.getExamParts().get(0);
        part = examPartsMapper.selectOne(part);
        String examPartsTypeCode = part.getPartsTypeCode();

        //班次筛选
        AllotedWorkVo allotedWorkS = new AllotedWorkVo();
        //allotedWorkS.setShiftCode(workShift.getShiftCode());
        allotedWorkS.setWorkShiftList(workShiftList);
        allotedWorkS.setWorkType(workType);
        long time = System.currentTimeMillis();
        allotedWorkS.setWorkDate(new java.sql.Date(time));
        allotedWorkS.setDiagnosisType(examInfoVo.getInpType().getDictValue());
        allotedWorkS.setModalityType(examInfoVo.getExamModality().getDictValue());


//        String[] deviceCodeAr = examInfoVo.getDeviceCodeValues();
//        //examInfoVo.getExamDevice()
//        allotedWorkS.setDevicesCode(null==deviceCodeAr||deviceCodeAr.length<1?null:deviceCodeAr[0]);
        //设备型号
        DicomInfo examDevice = examInfoVo.getExamDevice();
        if(null!=examDevice){
            allotedWorkS.setDevicesCode(examDevice.getDeviceCode());
        }

        //设定组别
        ScheduleUserVo scheduleUserVoS = new ScheduleUserVo();
        ScheduleGroup scheduleGroupS = new ScheduleGroup();
        scheduleUserVoS.setGroup(scheduleGroupS);
        scheduleGroupS.setGroupCode(examPartsTypeCode);
        allotedWorkS.setUserVo(scheduleUserVoS);

        //获取满足条件，且记录数最小的排班
        if(ResultStatus.EXAM.is(resultStatusCode)){
            allotedWorkS.setOrderBy("report_allocate_count");
        }else if(ResultStatus.REPORT.is(resultStatusCode)){
            allotedWorkS.setOrderBy("audit_allocate_count");
        }

        List<AllotedWorkVo> allotedWorkVo = allotedWorkMapper.selectMinWorkCount(allotedWorkS);
        if(0==allotedWorkVo.size()) {
            if(log.isInfoEnabled()) { log.info("id:" + examInfoVo.getId()+"就诊方式："+examInfoVo.getInpType().getDictValue()+
                    "设备类型："+examInfoVo.getExamModality().getDictValue()+"工作内容:" + workType+"没有符号排班，无法分配");}
            return;
        }
        AllotedWorkVo allotedWork = allotedWorkVo.get(0);

        //分配报告
        RadExamInfoVo entity = new RadExamInfoVo();
        entity.setId(examInfoVo.getId());


        //更新分配记录
        allotedWorkS = new AllotedWorkVo();
        allotedWorkS.setId(allotedWork.getId());
        if(ResultStatus.EXAM.is(resultStatusCode)){
            entity.setReportDoctorCodeAllocate(allotedWork.getUserCode());
            examInfoMapper.reportAssignment(entity);

            allotedWorkS.setReportAllocateCount(allotedWork.getReportAllocateCount()+1);
        }else if(ResultStatus.REPORT.is(resultStatusCode)){
            entity.setAuditDoctorCodeAllocate(allotedWork.getUserCode());
            examInfoMapper.reportAssignment(entity);

            allotedWorkS.setAuditAllocateCount(allotedWork.getAuditAllocateCount()+1);
        }

        allotedWorkMapper.update(allotedWorkS);
        int test = 0;
        //throw new RuntimeException("报错啦...");
    }
    //分配报告前条件检查
    public void allocateReport(RadExamInfoVo examInfo, String resultStatusCode, boolean reAllocate){
        if(null==resultStatusCode){
            ExamInfo info = infoService.selectOne(examInfo);
            if(null == info) {
                return ;
            }
            //状态检查，已登记、已检查和书写完成可保存报告
            SysDictData resultStatus = info.getResultStatus();
            //已检查或已报告才分配
            if(null == resultStatus || !(ResultStatus.EXAM.is(resultStatusCode = resultStatus.getDictValue())
                    || ResultStatus.REPORT.is(resultStatusCode)
            )){
                return;
            }
        }

        if(ResultStatus.EXAM.is(resultStatusCode)){
            if(!reAllocate&&examInfo.getReportDoctorCodeAllocate()!=null) return;
        }else if(ResultStatus.REPORT.is(resultStatusCode)){
            if(!reAllocate&&examInfo.getAuditDoctorCodeAllocate()!=null) return;
        }

        List<WorkShift> listRe = getNowWorkShift();
        if(0==listRe.size()) return; //当前没有上班的本次，不分配
        try {
            //redisLockReportUpdate.lock();
            redis.lock(redisLockReportUpdate, redisLockReportUpdate);
            getService().allocate(examInfo,listRe,resultStatusCode);
            //lock.unlock();
        }catch (Exception err){
            log.error(err.getMessage(), err);
        }finally {
            //redisLockReportUpdate.unlock();
            redis.unlock(redisLockReportUpdate);
        }
    }
    //获取所有需分配检查，逐一分配
    public void examAllocate(String resultStatus,String doctorCodeAllocate){
        //分配
        RadExamInfoVo examInfoVos = new RadExamInfoVo();
        //examInfo.setExamTimeGe(new Date());
        examInfoVos.setExamTimeLt(new Date());
        List<String> resultStatusList = new ArrayList<String>();
        //检查状态为已检查
        resultStatusList.add(resultStatus);
        examInfoVos.setResultStatusValues(resultStatusList);

        examInfoVos.setStatus(0);
        //按照检查、审核时间排序
        if(ResultStatus.EXAM.is(resultStatus)){
            examInfoVos.setReportDoctorCodeAllocate(doctorCodeAllocate);
            examInfoVos.setOrderBy("exam_time");
        }else if(ResultStatus.REPORT.is(resultStatus)){
            examInfoVos.setAuditDoctorCodeAllocate(doctorCodeAllocate);
            examInfoVos.setOrderBy("report_date");
        }
        //redisLockReportAllocate.lock();
        redis.lock(redisLockReportAllocate, redisLockReportAllocate);
        try {
            if (log.isInfoEnabled()) {log.info("分配任务启动：" + new Date());}
            List<RadExamInfoVo> examList = examInfoMapper.selectList(examInfoVos);
            if (log.isInfoEnabled()) {log.info("本次有" + examList.size() + "份报告需要分配" + resultStatus); }
            for (int i = 0; i < examList.size(); i++) {
                allocateReport(examList.get(i), resultStatus, null != doctorCodeAllocate);
            }
            //自动释放再分配
            if(ResultStatus.EXAM.is(resultStatus)&&null!=doctorCodeAllocate){
                int remReleaseNum = examList.size();
                //int releaseNum = releaseTotalNum/3;
                AllotedWorkVo allotedWorkS = new AllotedWorkVo();
                allotedWorkS.setUserCode(doctorCodeAllocate);
                List<AllotedWorkVo> allotedWorkVo = allotedWorkMapper.selectMinWorkCount(allotedWorkS);
                int alloNum = allotedWorkVo.size();
                if(alloNum>0){
                    int releaseBaseNum = remReleaseNum/alloNum+remReleaseNum%alloNum;
                    for(int i=0;i<alloNum;i++) {
                        int releaseNum = allotedWorkVo.get(i).getReportAllocateCount() > releaseBaseNum ? releaseBaseNum : allotedWorkVo.get(i).getReportAllocateCount();
                        allotedWorkS = new AllotedWorkVo();
                        allotedWorkS.setId(allotedWorkVo.get(i).getId());
                        allotedWorkS.setReportAllocateCount(allotedWorkVo.get(i).getReportAllocateCount() - releaseNum);
                        remReleaseNum-=releaseNum;
                        if (0==remReleaseNum){
                            break;
                        }
                    }

                    if(remReleaseNum>0){
                        for(int i=0;i<alloNum;i++) {
                            int releaseNum = allotedWorkVo.get(i).getReportAllocateCount() > remReleaseNum ? remReleaseNum : allotedWorkVo.get(i).getReportAllocateCount();
                            allotedWorkS = new AllotedWorkVo();
                            allotedWorkS.setId(allotedWorkVo.get(i).getId());
                            allotedWorkS.setReportAllocateCount(allotedWorkVo.get(i).getReportAllocateCount() - releaseNum);
                            remReleaseNum-=releaseNum;
                            if(0==remReleaseNum){
                                break;
                            }
                        }
                    }
                }

            }else if(ResultStatus.REPORT.is(resultStatus)&&null!=doctorCodeAllocate){
                int remReleaseNum = examList.size();
                //int releaseNum = releaseTotalNum/3;
                AllotedWorkVo allotedWorkS = new AllotedWorkVo();
                allotedWorkS.setUserCode(doctorCodeAllocate);
                List<AllotedWorkVo> allotedWorkVo = allotedWorkMapper.selectMinWorkCount(allotedWorkS);
                int alloNum = allotedWorkVo.size();
                if(alloNum>0){
                    int releaseBaseNum = remReleaseNum/alloNum+remReleaseNum%alloNum;
                    for(int i=0;i<alloNum;i++) {
                        int releaseNum = allotedWorkVo.get(i).getAuditAllocateCount() > releaseBaseNum ? releaseBaseNum : allotedWorkVo.get(i).getReportAllocateCount();
                        allotedWorkS = new AllotedWorkVo();
                        allotedWorkS.setId(allotedWorkVo.get(i).getId());
                        allotedWorkS.setAuditAllocateCount(allotedWorkVo.get(i).getAuditAllocateCount() - releaseNum);
                        remReleaseNum-=releaseNum;
                        if (0==remReleaseNum){
                            break;
                        }
                    }

                    if(remReleaseNum>0){
                        for(int i=0;i<alloNum;i++) {
                            int releaseNum = allotedWorkVo.get(i).getAuditAllocateCount() > remReleaseNum ? remReleaseNum : allotedWorkVo.get(i).getReportAllocateCount();
                            allotedWorkS = new AllotedWorkVo();
                            allotedWorkS.setId(allotedWorkVo.get(i).getId());
                            allotedWorkS.setAuditAllocateCount(allotedWorkVo.get(i).getAuditAllocateCount() - releaseNum);
                            remReleaseNum-=releaseNum;
                            if(0==remReleaseNum){
                                break;
                            }
                        }
                    }
                }
            }
        }catch(Exception err){
            log.error(err.getMessage(), err);
        }finally {
            if(log.isInfoEnabled()) { log.info("分配任务结束：" + new Date());}
            //redisLockReportAllocate.unlock();
            redis.unlock(redisLockReportAllocate);
        }
    }

    public void testS(String aa){
        log.debug("testBegin"+aa);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.debug("testEnd");
    }
    //重新分配报告
    public void autoAllocate(RadExamInfoVo param){
        if(null!=param.getReportDoctorCodeAllocate()){
            //分配已检查
            examAllocate(ResultStatus.EXAM.getValue(),param.getReportDoctorCodeAllocate());
        }

        if(null!=param.getAuditDoctorCodeAllocate()){
            //分配已报告
            examAllocate(ResultStatus.REPORT.getValue(),param.getAuditDoctorCodeAllocate());
        }
    }
    //分配任务定时线程
    public void task(){
        //分配已检查
        examAllocate(ResultStatus.EXAM.getValue(),null);

        //分配已报告
        examAllocate(ResultStatus.REPORT.getValue(),null);
    }

    //开启定时器
    public void scheduleStart(){
        WorkShift workShifts = new WorkShift();
        workShifts.setOrderBy("begin_time");
        List<WorkShift> listWorkShift = workShiftMapper.selectList(workShifts);

        try {
            //redisLockExecutor.lock();
            redis.lock(redisLockExecutor, redisLockExecutor);

            //如果有定时任务还没启动，取消
            for(int i=0;i<scheduledFutureList.size();i++){
                if(scheduledFutureList.get(i).cancel(false)){
                    if(log.isInfoEnabled()) { log.info("班次定时器-" + i + "取消");}
                }
            }

            scheduledFutureList.clear();
            //每个班次启动一个定时器
            for(int i=0;i<listWorkShift.size();i++){
                WorkShift workShift = listWorkShift.get(i);
                // 获得当前时间
                LocalDateTime now = LocalDateTime.now();

                java.sql.Time beginTime = workShift.getBeginTime();
                // 获取班次时间
                LocalDateTime startRunTime =
                        now.withHour(beginTime.getHours()).withMinute(beginTime.getMinutes()).withSecond(beginTime.getSeconds()).withNano(0);
                // 如果当前时间已经超过班次开始时间，跳过
                if(now.compareTo(startRunTime) >= 0) {
                    continue;
                }
                // 计算时间差，即延时执行时间
                long initialDelay = Duration.between(now, startRunTime).toMillis();

                if(log.isInfoEnabled()) { log.info(workShift.getShiftName()+"班次定时器开始：" + new Date());}
                //开启
                ScheduledFuture<?> scheduledFuture = executor.schedule(() -> {
                    task();
                }, initialDelay, TimeUnit.MILLISECONDS);

                scheduledFutureList.add(scheduledFuture);
            }

            //马上执行一次分配
            ScheduledFuture<?> scheduledFuture = executor.schedule(() -> {
                task();
            }, 1, TimeUnit.MILLISECONDS);

            scheduledFutureList.add(scheduledFuture);
        }catch (Exception err){
            log.error(err.getMessage(), err);
        }finally {
            //redisLockExecutor.unlock();
            redis.unlock(redisLockExecutor);
        }
    }
}
