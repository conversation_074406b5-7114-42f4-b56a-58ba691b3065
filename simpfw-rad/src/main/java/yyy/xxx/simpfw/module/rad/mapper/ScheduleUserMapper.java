package yyy.xxx.simpfw.module.rad.mapper;



import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;
import yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo;

import java.util.List;

public interface ScheduleUserMapper {
    ScheduleUserVo selectOne(ScheduleUser user);
    List<ScheduleUserVo> selectList(ScheduleUser user);
    int insert(ScheduleUser user);
    boolean update(ScheduleUser user);
    int delete(String[] userCodes);
}
