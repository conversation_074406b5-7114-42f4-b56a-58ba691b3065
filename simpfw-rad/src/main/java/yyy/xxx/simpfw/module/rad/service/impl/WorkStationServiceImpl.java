package yyy.xxx.simpfw.module.rad.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.module.pacs.entity.DicomStudy;
import yyy.xxx.simpfw.module.rad.mapper.WorkStationMapper;
import yyy.xxx.simpfw.module.rad.service.WorkStationService;
import yyy.xxx.simpfw.module.rad.vo.StudyInfoVo;

import java.util.List;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class WorkStationServiceImpl implements WorkStationService {

    @Autowired private WorkStationMapper mapper;

    @Override
    public List<DicomStudy> selectListWorkStation(StudyInfoVo param) {
        return mapper.selectListWorkStation(param);
    }
}
