package yyy.xxx.simpfw.module.rad.entity;


import yyy.xxx.simpfw.module.pacs.entity.BaseEntity;

public class WorkShift  extends BaseEntity {

  private String shiftCode;
  private String shiftName;
  private java.sql.Time beginTime;
  private java.sql.Time endTime;
  private String noteInfo;

  public String getShiftCode() {
    return shiftCode;
  }

  public void setShiftCode(String shiftCode) {
    this.shiftCode = shiftCode;
  }


  public String getShiftName() {
    return shiftName;
  }

  public void setShiftName(String shiftName) {
    this.shiftName = shiftName;
  }


  public java.sql.Time getBeginTime() {
    return beginTime;
  }

  public void setBeginTime(java.sql.Time beginTime) {
    this.beginTime = beginTime;
  }


  public java.sql.Time getEndTime() {
    return endTime;
  }

  public void setEndTime(java.sql.Time endTime) {
    this.endTime = endTime;
  }


  public String getNoteInfo() {
    return noteInfo;
  }

  public void setNoteInfo(String noteInfo) {
    this.noteInfo = noteInfo;
  }

}
