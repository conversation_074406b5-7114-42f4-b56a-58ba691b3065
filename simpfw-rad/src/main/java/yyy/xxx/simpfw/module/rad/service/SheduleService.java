package yyy.xxx.simpfw.module.rad.service;

import org.quartz.SchedulerException;
import yyy.xxx.simpfw.common.exception.job.TaskException;
import yyy.xxx.simpfw.module.rad.entity.AllotedWork;
import yyy.xxx.simpfw.module.rad.entity.ScheduleGroup;
import yyy.xxx.simpfw.module.rad.entity.ScheduleUser;
import yyy.xxx.simpfw.module.rad.entity.WorkShift;
import yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo;
import yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo;

import java.util.List;

public interface SheduleService {
     //用户
     ScheduleUserVo selectUserOne(ScheduleUser param);

     List<ScheduleUserVo> selectUserList(ScheduleUser param);

     int insertScheduleUser(ScheduleUser user);

     boolean updateScheduleUser(ScheduleUser user);

     int deleteScheduleUser(String[] userCodes);

     //分组
     ScheduleGroup selectScheduleGroupOne(ScheduleGroup param) ;

     List<ScheduleGroup> selectScheduleGroupList(ScheduleGroup param) ;

     int insertScheduleGroup(ScheduleGroup user);

     boolean updateScheduleGroup(ScheduleGroup user);

     int deleteScheduleGroup(String[] ids);

     //班次
     WorkShift selectWorkShiftOne(WorkShift param);

     List<WorkShift> selectWorkShiftList(WorkShift param);

     int insertWorkShift(WorkShift param,String username) throws SchedulerException, TaskException;

     boolean updateWorkShift(WorkShift param,String username) throws SchedulerException, TaskException;

     int deleteWorkShift(Long id) throws SchedulerException;

     //排班
     AllotedWorkVo selectAllotedWorkOne(AllotedWork param);

     List<AllotedWorkVo> selectAllotedWorkList(AllotedWork param);

     int insertAllotedWork(AllotedWork param);

     boolean updateAllotedWork(AllotedWork param);

     int deleteAllotedWork(String[] ids);

     int updateAllotedWorkStatusStop(String[] ids);
}
