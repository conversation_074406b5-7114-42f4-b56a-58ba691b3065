<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.WorkStationMapper">

    <resultMap id="resultListMap" type="yyy.xxx.simpfw.module.rad.vo.StudyInfoVo" autoMapping="true">
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
                     column="PatientID"
                     select="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.selectOne"/>

        <collection property="seriesSet" ofType="DicomSeries"
                    column="{studyInstanceUid=studyInstanceUid{" select="yyy.xxx.simpfw.module.pacs.mapper.DicomSeriesMapper.selectList" />
    </resultMap>

    <sql id="selectListBase">
        select `r`.`id`
        ,`r`.`studyInstanceUid`,`r`.`exam_uid` `examUid`,`r`.`studyDate`,`r`.`AccessionNumber`, `r`.`PatientID`, `r`.`CreatedOn`, `r`.`StudyDate`
        from `d_dicom_study` `r`
    </sql>
    <sql id="selectWhereKey">
        where 1=1
        <if test="null!=examUid and ''!=examUid"> and `r`.`exam_uid`=#{examUid} </if>
        <if test="null!=examInfo and null!=examInfo.examUid and ''!=examInfo.examUid"> and `r`.`exam_uid`=#{examInfo.examUid}</if>
        <if test="null!=id"> and `r`.`id`=#{id}</if>
        <!-- 未匹配 -->
        <if test="null!=noExamNo and noExamNo==true"> and (`r`.`exam_uid` is null or `r`.`exam_uid` = '')</if>


        <!-- 设备类型 -->
        <if test="null!=examModalityPara and examModalityPara.size>0">
            <foreach collection="examModalityPara" item="item" open=" and r.Modalities in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <!-- 患者信息 -->
        <if test="null!=patientInfo">
            <if test="null!=patientInfo.id and ''!=patientInfo.id"> and `r`.`PatientID` = #{patientInfo.id}</if>
            <if test="null!=patientInfo.name and ''!=patientInfo.name"> and `r`.`PatientName` like replace(#{patientInfo.name},'*','%')</if>
        </if>

        <!-- 接收时间 -->
        <if test="null!=createTimeGe"> and (`r`.`CreatedOn`&gt;=#{createTimeGe}) </if>
        <if test="null!=createTimeLt"> and (`r`.`CreatedOn`&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>

        <!-- 检查时间 -->
        <if test="null!=examTimeGe"> and `r`.`StudyDate`&gt;=#{examTimeGe}</if>
        <if test="null!=examTimeLt"> and `r`.`StudyDate`&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)</if>

    </sql>

    <select id="selectListWorkStation" resultMap="resultListMap">
        <include refid="selectListBase"/>
        <include refid="selectWhereKey"/>

        order by <if test="null!=orderBy and ''!=orderBy">${orderBy},</if>r.id desc
    </select>


</mapper>