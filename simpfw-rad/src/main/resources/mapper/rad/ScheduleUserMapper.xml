<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.ScheduleUserMapper">

    <resultMap id="resultMap" type="yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo" autoMapping="true">
        <association property="group" javaType="yyy.xxx.simpfw.module.rad.entity.ScheduleGroup" autoMapping="true" columnPrefix="gr__" />
        <collection property="allotedWorkList" ofType="yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo" column="userCode=userCode" select="yyy.xxx.simpfw.module.rad.mapper.AllotedWorkMapper.selectListOnly"/>
    </resultMap>

    <sql id="selectBase">
        select `r`.`id`
             ,`r`.`user_code` userCode,`r`.`user_name` `userName`,`r`.`note_info` noteInfo,`r`.`group_code` gr__groupCode,
             e.group_name gr__groupName
        from `d_schedule_user` `r`
        left join d_schedule_group e on e.group_code=r.group_code
    </sql>

    <sql id="selectWhere">
        where  1=1
            <choose>
                <when test="null!=id and 0!=id"> and `r`.`id`=#{id}</when>
                <when test="null!=userCode and ''!=userCode">and  `r`.`user_code`=#{userCode}</when>
                <when test="null!=userName and ''!=userName">and `r`.`user_name`=#{userName} </when>
                <when test="null!=groupCode and ''!=groupCode">and `r`.`group_code`=#{groupCode} </when>
            </choose>

            <if test="false==groupIsEmpty">and `r`.`group_code` is not null </if>
            <if test="true==groupIsEmpty">and `r`.`group_code` is null </if>

    </sql>

    <select id="selectOne" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
        <if test="(null==id or 0==id) and (null==userCode or ''==userCode) and (null==userName or ''==userName)"> and 1=0</if>
        LIMIT 1
    </select>

    <select id="selectList" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
    </select>

    <insert id="insert"  useGeneratedKeys="true" keyProperty="id" >
        insert into `d_schedule_user` (`user_code`,`user_name`,`note_info`,group_code)
        values(#{userCode},#{userName}
        ,<choose><when test="null!=noteInfo">#{noteInfo}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=groupCode">#{groupCode}</when><otherwise>null</otherwise></choose>
        )
    </insert>

    <insert id="update">
        update `d_schedule_user` set
        `id`=#{id}
        <if test="null!=userCode">,`user_code`=#{userCode}</if>
        <if test="null!=userName">,`user_name`=#{userName}</if>
        <if test="null!=noteInfo">,`note_info`=#{noteInfo}</if>
        <if test="null!=groupCode">,`group_code`=#{groupCode}</if>
        <if test="true==deleteGroupCode">,`group_code`=null</if>

        where `id`=#{id}
    </insert>

    <delete id="delete">
        delete from `d_schedule_user` where `user_code` in
        <foreach collection="array" item="userCode" open="(" separator="," close=")">
            #{userCode}
        </foreach>
    </delete>
</mapper>