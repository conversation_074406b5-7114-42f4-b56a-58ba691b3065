<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.RadExamInfoMapper">

    <sql id="selectBase">
        select h.id,h.status,h.create_time createTime,h.update_time updateTime,h.note_info noteInfo
        ,h.exam_uid examUid,h.exam_no examNo,h.patient_id,h.exam_modality_code, h.inp_type_code, h.exam_item_code, h.equip_room_code, h.exam_parts_id
        ,h.req_dept_code, h.req_doctor_code, h.exam_cost examCost, h.green_channel_flag greenChannelFlag
        ,h.appoint_time appointTime, h.clinic_diagnosis clinicDiagnosis, h.allergy_history allergyHistory, h.clinic_disease clinicDisease, h.exam_prerequire examPrerequire
        ,h.reserved_no_used reservedNoUsed, h.exam_at_pm examAtPm, h.adm_no admNo, h.operation_info operationInfo
        ,h.adm_series_num admSeriesNum,h.inp_no inpNo,h.bed_no bedNo,h.apply_path applyPath,h.exam_diagnosis examDiagnosis
        ,h.ord_id ordId,h.ord_name ordName,h.arcim_code arcimCode,h.ord_bill_status ordBillStatus
        ,h.ord_priority_code ordPriorityCode,h.ord_priority ordPriority,h.exam_purpose examPurpose,h.is_emergency emergency
        ,h.ord_status ordStatus,h.req_time reqTime,h.exam_time examTime,operation_suggestion operationSuggestion,h.exam_conclusion examDesc
        ,h.audit_time auditTime,h.reaudit_time reauditTime,h.sign_time signTime,h.origin_id originId,h.inp_times inpTimes
        ,exam_doctor_code examDoctorsCode,exam_doctor_name examDoctorsName
        ,consultants_code consultantsCode,consultants_name consultantsName
        ,h.recorders_code recordersCode,h.recorders_name recordersName,h.report_date reportTime,h.outp_no outpNo
        ,h.regist_time registTime,h.regist_user_code regu__userName,regist_user_name regu__nickName
        ,h.report_url_pdf reportUrlPdf,h.report_url_jpg reportUrlJpg,h.report_url_username reportUrlUsername,h.report_url_password reportUrlPassword
        ,h.report_doctor_code_allocate reportDoctorCodeAllocate
        ,h.audit_doctor_code_allocate auditDoctorCodeAllocate
        ,p.name p__name,p.name_pingyin p__namePingyin,p.age p__age,p.reg_no p__registNo,p.birthday p__birthday
        ,pg.dict_label P__gd__dictLabel,p.gender_code P__gd__dictValue
        ,pau.dict_label P__ageu__dictLabel,p.age_unit_code P__ageu__dictValue

        ,ci.id ci__id,ci.call_no ci__callNo,cer.room_code ci__rm__roomCode,cer.room_name ci__rm__roomName

        <!-- ,dcs.id dcs__id,dcs.studyInstanceUid dcs__studyInstanceUid,dcs.dicomStudyInstanceUid dcs__dicomStudyInstanceUid -->

        ,rt.dict_code rt__dictCode,rt.dict_value rt__dictValue,rt.dict_label rt__dictLabel
        ,rts.dict_code rts__dictCode,rts.dict_value rts__dictValue,rts.dict_label rts__dictLabel
        ,ei.dict_code ei__dictCode,ei.dict_value ei__dictValue,ei.dict_label ei__dictLabel
        ,er.room_code er__roomCode,er.room_name er__roomName
        ,wd.dict_code wd__dictCode,wd.dict_value wd__dictValue,wd.dict_label wd__dictLabel
        ,rm.dict_code rm__dictCode,rm.dict_value rm__dictValue,rm.dict_label rm__dictLabel
        ,cos.dict_code cos__dictCode,cos.dict_value cos__dictValue,cos.dict_label cos__dictLabel
        ,res2.dict_code res2__dictCode,res2.dict_value res2__dictValue,res2.dict_label res2__dictLabel
        ,erp.dict_code erp__dictCode,erp.dict_value erp__dictValue,erp.dict_label erp__dictLabel
        ,cp.dict_code cp__dictCode,cp.dict_value cp__dictValue,cp.dict_label cp__dictLabel

        ,h.exam_doctor_code eu__userName,h.exam_doctor_name eu__nickName
        <!-- ,eu.user_id eu__userId,eu.user_name eu__userName,eu.nick_name eu__nickName -->
        <!--存什么读什么,ru.user_id ru__userId,ru.user_name ru__userName,ifnull(h.req_doctor_name,ru.nick_name) ru__nickName
        ,aud.user_id aud__userId,aud.user_name aud__userName,ifnull(h.audit_doctor_name,aud.nick_name) aud__nickName
        ,aud2.user_id aud2__userId,aud2.user_name aud2__userName,ifnull(h.reaudit_doctor_name,aud2.nick_name) aud2__nickName
        ,sig.user_id sig__userId,sig.user_name sig__userName,ifnull(h.sign_doctor_name,sig.nick_name) sig__nickName
        ,rpu.user_id rpu__userId,rpu.user_name rpu__userName,ifnull(h.report_doctor_name,rpu.nick_name) rpu__nickName -->
        ,h.req_doctor_code ru__userName,h.req_doctor_name ru__nickName
        ,h.audit_doctor_code aud__userName,h.audit_doctor_name aud__nickName
        ,h.reaudit_doctor_code aud2__userName,h.reaudit_doctor_name aud2__nickName
        ,h.sign_doctor_code sig__userName,h.sign_doctor_name sig__nickName
        ,h.report_doctor_code rpu__userName,h.report_doctor_name rpu__nickName

        <!-- ,rd.dept_id rd__deptId,rd.dept_name rd__deptName -->
        ,h.req_dept_code rd__deptCode,h.req_dept_name rd__deptName
        ,ex.dept_id ex__deptId,ex.dept_name ex__deptName

        ,exdev.id exdev__id,exdev.modality_code exdev__modalityCode,exdev.device_code exdev__deviceCode

        <!-- 检查单 -->
        from `d_exam_info` `h`
        <!-- 患者信息 -->
        join d_patient p on p.patient_id=h.patient_id
        <!-- 排队信息 -->
        left join d_call_info ci on ci.exam_info_id=h.id
        left join d_equip_room cer on cer.room_code=ci.call_room_code
        <!-- dicom
        left join d_dicom_study dcs on dcs.exam_info_id=h.id -->
        <!-- -->
        left join v_sys_dict_data rt on rt.dict_type='uis_exam_modality' and rt.dict_value=h.exam_modality_code
        left join v_sys_dict_data rts on rts.dict_type='uis_inp_type' and rts.dict_value=h.inp_type_code
        left join v_sys_dict_data ei on ei.dict_type='uis_exam_item' and ei.dict_value=h.exam_item_code
        left join d_equip_room er on er.room_code=h.equip_room_code
        left join v_sys_dict_data wd on wd.dict_type='uis_inp_ward' and wd.dict_value=h.inp_ward_code
        left join v_sys_dict_data rm on rm.dict_type='uis_inp_room' and rm.dict_value=h.inp_room_code
        left join v_sys_dict_data cos on cos.dict_type='uis_exam_cost_type' and cos.dict_value=h.exam_cost_type_code
        left join v_sys_dict_data res2 on res2.dict_type='uis_exam_result_status' and res2.dict_value=h.result_status_code
        left join v_sys_dict_data erp on erp.dict_type='uis_exam_result_prop' and erp.dict_value=h.exam_result_prop_code
        left join v_sys_dict_data pg on pg.dict_type='uis_gender_type' and pg.dict_value=p.gender_code
        left join v_sys_dict_data pau on pau.dict_type='uis_age_unit' and pau.dict_value=p.age_unit_code
        left join v_sys_dict_data cp on cp.dict_type='uis_cond_parting' and cp.dict_value=h.cond_parting_code

        <!-- left join v_sys_user eu on eu.user_name=h.exam_doctor_code -->
        <!--存什么读什么left join v_sys_user ru on ru.user_name=h.req_doctor_code
        left join v_sys_user aud on aud.user_name=h.audit_doctor_code
        left join v_sys_user aud2 on aud2.user_name=h.reaudit_doctor_code
        left join v_sys_user sig on sig.user_name=h.sign_doctor_code
        left join v_sys_user rpu on rpu.user_name=h.report_doctor_code -->

        <!-- left join v_sys_dept rd on rd.dept_id=h.req_dept_code -->
        left join v_sys_dept ex on ex.dept_id=h.exam_dept_code

        left join d_dicom_info exdev on exdev.modality_code=h.modality_code
    </sql>

    <select id="selectList" parameterType="yyy.xxx.simpfw.module.rad.vo.RadExamInfoVo" resultMap="yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper.resultMapOutline">
        <include refid="selectBase"/>
        <include refid="yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper.selectWhereKey"/>

        <if test="null!=examNo and ''!=examNo"> and `h`.`exam_no`=#{examNo}</if>
        <if test="null!=inpNo and ''!=inpNo"> and `h`.`inp_no`=#{inpNo}</if>
        <!-- 检查类型 -->
        <if test="null!=examModality and null!=examModality.dictValue and ''!=examModality.dictValue"> and h.exam_modality_code=#{examModality.dictValue}</if>
        <if test="null!=examModalitiesCodes">
            <foreach collection="examModalitiesCodes" item="var" open=" and h.exam_modality_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 患者信息 -->
        <if test="null!=patientInfo">
            <if test="null!=patientInfo.name and ''!=patientInfo.name"> and `p`.`name` like replace(#{patientInfo.name},'*','%')</if>
            <if test="null!=patientInfo.medicalRecordNo and ''!=patientInfo.medicalRecordNo"> and `p`.`medical_record_no`=#{patientInfo.medicalRecordNo}</if>
            <if test="null!=patientInfo.registNo and ''!=patientInfo.registNo"> and `p`.`reg_no`=#{patientInfo.registNo}</if>
            <if test="null!=patientInfo.namePingyin and ''!=patientInfo.namePingyin"> and `p`.`name_pingyin`=#{patientInfo.namePingyin}</if>
            <if test="null!=patientInfo.gender and null!=patientInfo.gender.dictValue and ''!=patientInfo.gender.dictValue"> and `p`.`gender_code`=#{patientInfo.gender.dictValue}</if>
            <if test="null!=patientInfo.birthday"> and `p`.`birthday`=#{patientInfo.birthday}</if>
        </if>
        <!-- 检查项目 -->
        <if test="null!=examItemCodes">
            <foreach collection="examItemCodes" item="var" open=" and h.exam_item_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 诊前准备 -->
        <if test="null!=examPrerequire"> and h.exam_prerequire=#{examPrerequire}</if>
        <!-- 下午检查 -->
        <if test="null!=examAtPm"> and h.exam_at_pm=#{examAtPm}</if>
        <!-- 排除诊前准备 -->
        <if test="null!=examPrerequireExclude and examPrerequireExclude"> and (h.exam_prerequire is null or h.exam_prerequire=1)</if>
        <!--  -->
        <if test="0==status"> and (h.status is null or h.status=0)</if>
        <!-- 检查医生 -->
        <if test="null!=examDoctor">
            <if test="null!=examDoctor.nickName and ''!=examDoctor.nickName"> and h.exam_doctor_name like replace(#{examDoctor.nickName},'*','%')</if>
            <if test="null!=examDoctor.userName and ''!=examDoctor.userName"> and h.exam_doctor_code=#{examDoctor.userName}</if>
        </if>
        <if test="null!=examDoctorUserNames">
            <foreach collection="examDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.exam_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 工作状态 -->
        <if test="null!=resultStatus and null!=resultStatus.dictValue and ''!=resultStatus.dictValue">
            and h.result_status_code=#{resultStatus.dictValue}
        </if>
        <if test="null != resultStatusValues or (null!=resultStatusAsStatus and ''!=resultStatusAsStatus)">
            and(
            <trim prefixOverrides="or">
                <if test="null != resultStatusValues">
                    <foreach collection="resultStatusValues" item="var" open=" or h.result_status_code in (" close=")" separator=",">#{var}</foreach>
                </if>
                <if test="(null!=resultStatusAsStatus and ''!=resultStatusAsStatus)"> or h.status=#{resultStatusAsStatus}</if>
            </trim>
            )
        </if>
        <!-- 登记时间 -->
        <if test="null!=createTimeGe"> and (h.create_time&gt;=#{createTimeGe} and h.appoint_time is null or h.appoint_time&gt;=#{createTimeGe})</if>
        <if test="null!=createTimeLt"> and (h.create_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY) and h.appoint_time is null or h.appoint_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>
        <!-- 预约时间 -->
        <if test="null!=appointTimeGe"> and h.appoint_time&gt;=#{appointTimeGe}</if>
        <if test="null!=appointTimeLt"> and h.appoint_time&lt;date_add(#{appointTimeLt},INTERVAL 1 DAY)</if>
        <!-- 当日检查的：当日登记+预约当日
        <if test="appointWithCreated"> and(h.create_time&gt;=current_date() and h.appoint_time is null
            or h.appoint_time&gt;=current_date() and h.appoint_time&lt;date_add(current_date(), interval 1 day))</if> -->
        <!-- 检查时间 -->
        <if test="null!=examTimeGe"> and h.exam_time&gt;=#{examTimeGe}</if>
        <if test="null!=examTimeLt"> and h.exam_time&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)</if>
        <!-- 审核时间 -->
        <if test="null!=auditTimeGe"> and h.audit_time&gt;=#{auditTimeGe}</if>
        <if test="null!=auditTimeLt"> and h.audit_time&lt;date_add(#{auditTimeLt},INTERVAL 1 DAY)</if>
        <!-- 检查机房 -->
        <if test="null!=callInfo and null!=callInfo.callRoom and null!=callInfo.callRoom.roomCode and ''!=callInfo.callRoom.roomCode">
            and ci.call_room_code=#{callInfo.callRoom.roomCode}
        </if>
        <if test="null!=equipRoomsCode and equipRoomsCode.size()>0">
            <foreach collection="equipRoomsCode" item="var" open=" and ci.call_room_code in (" close=")" separator=",">#{var}</foreach>
        </if>
        <!-- 设备型号 -->
        <if test="null!=examDevicesCode and ''!=examDevicesCode">
            and cer.room_code in (select equip_room_code from r_dicominfo_equip_room rde,d_dicom_info dcmi where rde.equip_code=dcmi.id and dcmi.device_code in(
            <foreach collection="examDevicesCode" item="var" separator=",">#{var}</foreach>
            ))
        </if>
        <!-- 住院号 -->
        <if test="null!=inpNo and ''!=inpNo"> and h.inp_no=#{inpNo}</if>
        <!-- 报告医生 -->
        <if test="null!=reportDoctor">
            <if test="null!=reportDoctor.nickName and ''!=reportDoctor.nickName"> and h.report_doctor_name like replace(#{reportDoctor.nickName},'*','%')</if>
            <if test="null!=reportDoctor.userName and ''!=reportDoctor.userName"> and h.report_doctor_code=#{reportDoctor.userName}</if>
        </if>
        <if test="null!=reportDoctorUserNames">
            <foreach collection="reportDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.report_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 审核医生 -->
        <if test="null!=auditDoctor">
            <if test="null!=auditDoctor.nickName and ''!=auditDoctor.nickName"> and h.audit_doctor_name like replace(#{auditDoctor.nickName},'*','%')</if>
            <if test="null!=auditDoctor.userName and ''!=auditDoctor.userName"> and h.audit_doctor_code=#{auditDoctor.userName}</if>
        </if>
        <!-- 检查所见 -->
        <if test='null != examDescSegm and examDescSegm.length > 0'>
            and (
            <foreach collection="examDescSegm" item="item" open="" separator=" and " close="">
                h.exam_conclusion like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 检查诊断 -->
        <if test='null != examDiagnosisSegm and examDiagnosisSegm.length > 0'>
            and (
            <foreach collection="examDiagnosisSegm" item="item" open="" separator=" and " close="">
                h.exam_diagnosis like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 就诊类型 -->
        <if test="null!=inpType and null!=inpType.dictValue and ''!=inpType.dictValue">
            and h.inp_type_code=#{inpType.dictValue}
        </if>
        <if test="null!=inpTypeValues and inpTypeValues.length>0">
            <foreach collection="inpTypeValues" item="item" open=" and h.inp_type_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 状态 -->
        <if test="null!=status"> and h.status=#{status}</if>

        <!-- 分配医生 -->
        <if test="null!=reportDoctorCodeAllocate and ''!=reportDoctorCodeAllocate"> and h.report_doctor_code_allocate=#{reportDoctorCodeAllocate}</if>
        <if test="null!=auditDoctorCodeAllocate and ''!=auditDoctorCodeAllocate"> and h.audit_doctor_code_allocate=#{auditDoctorCodeAllocate}</if>

        <!-- 就诊号 -->
        <if test="null!=outpNo and ''!=outpNo"> and h.outp_no=#{outpNo}</if>
        <!-- -->
        <if test="null!=statusOfSendReport"> and h.status_of_send_report=#{statusOfSendReport}</if>

        order by <if test="null!=orderBy and ''!=orderBy">${orderBy},</if>h.id desc
    </select>

    <update id="reportAssignment">
        update d_exam_info
        <choose><when test='null!=reportDoctorCodeAllocate'>
            set report_doctor_code_allocate=#{reportDoctorCodeAllocate}
            where id=#{id}
        </when><when test='null!=auditDoctorCodeAllocate'>
            set audit_doctor_code_allocate=#{auditDoctorCodeAllocate}
            where id=#{id}
        </when><otherwise>where 1=0</otherwise></choose>
    </update>
</mapper>