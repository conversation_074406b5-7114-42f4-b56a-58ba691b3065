<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.AllotedWorkMapper">

    <resultMap id="resultMap" type="yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo" autoMapping="true">
        <association property="workShift" javaType="yyy.xxx.simpfw.module.rad.entity.WorkShift" autoMapping="true" columnPrefix="ws__" />
<!--        <association property="userVo" javaType="yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo" column="userCode=userCode"  select="yyy.xxx.simpfw.module.rad.mapper.ScheduleUserMapper.selectOne"/>-->
    </resultMap>

    <resultMap id="resultMapAUser" type="yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo" autoMapping="true">
        <association property="workShift" javaType="yyy.xxx.simpfw.module.rad.entity.WorkShift" autoMapping="true" columnPrefix="ws__" />
        <association property="userVo" javaType="yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo" column="userCode=userCode"  select="yyy.xxx.simpfw.module.rad.mapper.ScheduleUserMapper.selectOne"/>
    </resultMap>

    <resultMap id="resultMapAUserWithPre" type="yyy.xxx.simpfw.module.rad.vo.AllotedWorkVo" autoMapping="true">
        <association property="workShift" javaType="yyy.xxx.simpfw.module.rad.entity.WorkShift" autoMapping="true" columnPrefix="ws__" />
        <association property="userVo" javaType="yyy.xxx.simpfw.module.rad.vo.ScheduleUserVo" autoMapping="true" columnPrefix="us__"/>
        <association property="userVo.group" javaType="yyy.xxx.simpfw.module.rad.entity.ScheduleGroup" autoMapping="true" columnPrefix="gr__"/>
    </resultMap>

    <sql id="selectBase">
        select a.`id` ,
               a.`work_date` workDate,
               a.`user_code` userCode,
               a.`shift_code` shiftCode,
               a.`work_type` workType,
               a.`diagnosis_type` diagnosisType,
               a.`modality_type` modalityType,
               a.`devices_code` devicesCode,
               a.`report_allocate_count` reportAllocateCount,
               a.`audit_allocate_count` auditAllocateCount,
               a.`status` status,
               w.shift_name ws__shiftName,
               w.begin_time ws__beginTime,
               w.end_time ws__endTime
        from `d_alloted_work` `a`
        left join d_work_shift w on a.shift_code = w.shift_code
    </sql>

    <sql id="selectWhere">
        where 1=1
        <choose>
            <when test="null!=id and 0!=id"> and a.id=#{id}</when>
            <when test="null!=userCode and ''!=userCode"> and a.user_code=#{userCode}</when>
<!--            <when test="null!=className and ''!=className"> c.class_name=#{className} </when>-->
<!--            <when test="null!=date and ''!=date"> c.date=#{date} </when>-->
        </choose>
        <if test="null==status"> and (a.status is null or a.status=0)</if>
        <if test="null!=status"> and  a.status=#{status}</if>
    </sql>

    <select id="selectOne" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
        <if test="null!=workDate"> and a.work_date=#{workDate}</if>
        <if test="null!=shiftCode and ''!=shiftCode"> and a.shift_code=#{shiftCode}</if>
        <if test="(null==id or 0==id) and (null==userCode or ''==userCode)"> and 1=0</if>
        LIMIT 1
    </select>

    <select id="selectMinWorkCount" resultMap="resultMapAUserWithPre">
        select a.`id` ,
        a.`work_date` workDate,
        a.`user_code` userCode,
        a.`shift_code` shiftCode,
        a.`work_type` workType,
        a.`diagnosis_type` diagnosisType,
        a.`modality_type` modalityType,
        a.`devices_code` devicesCode,
        a.`report_allocate_count` reportAllocateCount,
        a.`audit_allocate_count` auditAllocateCount,
        a.`status` status,
        w.shift_name ws__shiftName,
        w.begin_time ws__beginTime,
        w.end_time ws__endTime,
        u.user_code us__userCode,u.user_name us__userName,u.note_info us__noteInfo,u.group_code us__groupCode,
        g.group_code gr__groupCode,g.group_name gr__groupName
        from `d_alloted_work` `a`
        left join d_work_shift w on a.shift_code = w.shift_code
        left join d_schedule_user u on a.user_code = u.user_code
        left join d_schedule_group g on u.group_code = g.group_code

        <include refid="selectWhere"/>
        <if test="null!=workDate"> and a.work_date=#{workDate}</if>
        <if test="null!=shiftCode and ''!=shiftCode"> and a.shift_code=#{shiftCode}</if>
        <if test="null!=workType and ''!=workType"> and a.work_Type REGEXP CONCAT('(^', #{workType},'$|^', #{workType},',|,', #{workType},',|,', #{workType},'$)' )</if>
        <if test="null!=diagnosisType and ''!=diagnosisType"> and a.diagnosis_type REGEXP CONCAT('(^', #{diagnosisType},'$|^', #{diagnosisType},',|,', #{diagnosisType},',|,', #{diagnosisType},'$)' )</if>
        <if test="null!=modalityType and ''!=modalityType"> and a.modality_type REGEXP CONCAT('(^', #{modalityType},'$|^', #{modalityType},',|,', #{modalityType},',|,', #{modalityType},'$)' )</if>
        <if test="null!=devicesCode and ''!=devicesCode"> and a.devices_code REGEXP CONCAT('(^', #{devicesCode},'$|^', #{devicesCode},',|,', #{devicesCode},',|,', #{devicesCode},'$)' )</if>
        <if test="null!=userVo.group.groupCode"> and g.group_code=#{userVo.group.groupCode}</if>
        <if test="null==userVo.group.groupCode"> and g.group_code is null</if>
        <if test="null!=workShiftList">
            <foreach collection="workShiftList" item="workShift" open=" and a.shift_code in (" close=")" separator=",">#{workShift.shiftCode}</foreach>
        </if>

    </select>



    <select id="selectList" resultMap="resultMapAUser">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
        <if test="null!=shiftCode and ''!=shiftCode"> and a.shift_code=#{shiftCode}</if>
        <if test="null!=workType and ''!=workType"> and a.work_Type REGEXP CONCAT('(^', #{workType},'$|^', #{workType},',|,', #{workType},',|,', #{workType},'$)' )</if>
        <if test="null!=workDate"> and a.work_date=#{workDate}</if>
<!--        order by <if test="null!=orderBy and ''!=orderBy">a.${orderBy},</if>a.id desc-->
    </select>

    <select id="selectListOnly" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
<!--        order by <if test="null!=orderBy and ''!=orderBy">a.${orderBy},</if>a.id desc-->
    </select>



    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into `d_alloted_work` (`work_date`,`user_code`,`shift_code`,work_type,diagnosis_type,modality_type,devices_code,report_allocate_count,audit_allocate_count)
        values(#{workDate},#{userCode},#{shiftCode},#{workType},#{diagnosisType},#{modalityType},#{devicesCode},#{reportAllocateCount},#{auditAllocateCount})
    </insert>

    <update id="update">
        update `d_alloted_work` set
        `id`=#{id}
        <if test="null!=workDate">,`work_date`=#{workDate}</if>
        <if test="null!=userCode">,`user_code`=#{userCode}</if>
        <if test="null!=shiftCode">,`shift_code`=#{shiftCode}</if>
        <if test="null!=workType">,`work_type`=#{workType}</if>
        <if test="null!=diagnosisType">,`diagnosis_type`=#{diagnosisType}</if>
        <if test="null!=modalityType">,`modality_type`=#{modalityType}</if>
        <if test="null!=devicesCode">,`devices_code`=#{devicesCode}</if>
        <if test="null!=reportAllocateCount">,`report_allocate_count`=#{reportAllocateCount}</if>
        <if test="null!=auditAllocateCount">,`audit_allocate_count`=#{auditAllocateCount}</if>
        <if test="null!=status">,`status`=#{status}</if>
        where `id`=#{id}
    </update>

<!--    <delete id="delete">-->
<!--        delete from `d_alloted_work` where `id`=#{id}-->
<!--    </delete>-->

    <update id="updateStatusStop">
        update `d_alloted_work` set status=1 where `id` in
        <foreach collection="array" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>

    <delete id="delete">
        delete from `d_alloted_work` where `id` in
        <foreach collection="array" item="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </delete>
</mapper>