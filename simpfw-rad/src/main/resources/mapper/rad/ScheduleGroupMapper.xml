<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.ScheduleGroupMapper">

    <resultMap id="resultMap" type="yyy.xxx.simpfw.module.rad.entity.ScheduleGroup" autoMapping="true">
<!--        <association property="dClasses" javaType="yyy.xxx.simpfw.module.rad.vo.ClassVo" column="id" select="selectOne"/>-->
    </resultMap>

    <sql id="selectBase">
        select s.id id, s.group_code groupCode, s.group_name groupName
        from `d_schedule_group` `s`
    </sql>

    <sql id="selectWhere">
        where 1=1
            <choose>
                <when test="null!=id and 0!=id"> and s.id=#{id}</when>
                <when test="null!=groupCode and ''!=groupCode"> and s.group_code=#{groupCode}</when>
                <when test="null!=groupName and ''!=groupName"> and s.group_name=#{groupName}</when>
            </choose>
    </sql>

    <select id="selectOne" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
        <if test="(null==id or 0==id) and (null==groupCode or ''==groupCode) and (null==groupName or ''==groupName)"> and 1=0</if>
        LIMIT 1
    </select>

    <select id="selectList" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
    </select>

    <insert id="insert"  useGeneratedKeys="true" keyProperty="id" >
        insert into `d_schedule_group` (`group_code`,`group_name`)
        values(#{groupCode},#{groupName})
    </insert>

    <update id="update">
        update `d_schedule_group` set `group_code`=#{groupCode}
        <if test="null!=groupName">,`group_name`=#{groupName}</if>
        where `id`=#{id}
    </update>

    <delete id="delete">
        delete from `d_schedule_group` where `id` in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>