<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.rad.mapper.WorkShiftMapper">

    <resultMap id="resultMap" type="yyy.xxx.simpfw.module.rad.entity.WorkShift" autoMapping="true">
<!--        <association property="dClasses" javaType="yyy.xxx.simpfw.module.rad.vo.ClassVo" column="id" select="selectOne"/>-->
    </resultMap>

    <sql id="selectBase">
        select c.id id, c.shift_code shiftCode,c.shift_name shiftName,
        c.begin_time beginTime, c.end_time endTime, c.note_info noteInfo
        from d_work_shift c
    </sql>

    <sql id="selectWhere">
        where 1=1
        <choose>
            <when test="null!=id and 0!=id"> and c.id=#{id}</when>
<!--            <when test="null!=classCode and ''!=classCode"> c.class_code=#{classCode}</when>-->
<!--            <when test="null!=className and ''!=className"> c.class_name=#{className} </when>-->
<!--            <when test="null!=date and ''!=date"> c.date=#{date} </when>-->
        </choose>
    </sql>

    <select id="selectOne" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
        <if test="(null==id or 0==id) "> and 1=0</if>
        LIMIT 1
    </select>

    <select id="selectList" resultMap="resultMap">
        <include refid="selectBase"/>
        <include refid="selectWhere"/>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into `d_work_shift` (`shift_code`,`shift_name`,`begin_time`,end_time,note_info)
        values(#{shiftCode},#{shiftName},#{beginTime},#{endTime}
        ,<choose><when test="null!=noteInfo">#{noteInfo}</when><otherwise>null</otherwise></choose>
        )
    </insert>

    <update id="update">
        update `d_work_shift` set
        `shift_code`=#{shiftCode}
        <if test="null!=shiftName">,`shift_name`=#{shiftName}</if>
        <if test="null!=beginTime">,`begin_time`=#{beginTime}</if>
        <if test="null!=endTime">,`end_time`=#{endTime}</if>
        <if test="null!=noteInfo">,`note_info`=#{noteInfo}</if>
        where `shift_code`=#{shiftCode}
    </update>

    <delete id="delete">
        delete from `d_work_shift` where `id`=#{id}
    </delete>
</mapper>