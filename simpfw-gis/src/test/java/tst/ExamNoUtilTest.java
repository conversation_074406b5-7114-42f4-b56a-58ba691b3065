package tst;

import static yyy.xxx.simpfw.module.pacs.utils.ExamNoUtil.*;

public class ExamNoUtilTest {

	public static void main(String[] args) {
		String pattern = "{yyyy}{MM}{dd}003292", examNo;
		System.out.println((examNo = make(null, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println();
		
		pattern = "003292";
		//System.out.println((examNo = make(null, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println();
		
		pattern = "ES_003292";
		//System.out.println((examNo = make(null, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println((examNo = make(examNo, pattern)));
		System.out.println();
	}

}
