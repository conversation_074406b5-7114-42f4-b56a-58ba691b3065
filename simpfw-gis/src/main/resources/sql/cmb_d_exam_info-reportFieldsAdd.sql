ALTER TABLE `rhpacs`.`d_exam_info` ADD pathology_diagnosis varchar(1024) COMMENT '病理诊断'
ALTER TABLE `rhpacs`.`d_exam_info` ADD pathology_treatment varchar(1024) COMMENT '病理处理'
ALTER TABLE `rhpacs`.`d_exam_info` ADD anes_way_code varchar(16) COMMENT '麻醉方式代码'
ALTER TABLE `rhpacs`.`d_exam_info` ADD mirror_measure_code varchar(16) COMMENT '进镜措施代码'
ALTER TABLE `rhpacs`.`d_exam_info` ADD breather_way_code varchar(16) COMMENT '通气方式代码'
ALTER TABLE `rhpacs`.`d_exam_info` ADD retain_specimen_flag varchar(16) COMMENT '是否留取标本，0为否，1为是'
ALTER TABLE `rhpacs`.`d_exam_info` ADD operation_complication_flag varchar(16) COMMENT '是否是操作并发症，0为否，1为是'
ALTER TABLE `rhpacs`.`d_exam_info` ADD operation_complications_code varchar(128) COMMENT '操作并发症代码'
ALTER TABLE `rhpacs`.`d_exam_info` ADD oc_treatment_measure varchar(1024) COMMENT '处理措施'