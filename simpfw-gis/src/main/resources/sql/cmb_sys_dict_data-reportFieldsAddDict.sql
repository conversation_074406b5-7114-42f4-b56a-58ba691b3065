-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '麻醉方式', 'gis_anes_way', '0', 'admin', '2023-04-03 10:28:55', '', NULL, NULL);
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '进镜措施', 'gis_mirror_measure', '0', 'admin', '2023-04-03 10:28:55', '', NULL, NULL);
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '操作并发症', 'gis_operation_complication', '0', 'admin', '2023-04-03 10:28:55', '', NULL, NULL);
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '通气方式', 'gis_breather_way', '0', 'admin', '2023-04-03 10:28:55', '', NULL, NULL);

INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '基础麻醉', '0', 'gis_anes_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:16:09', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '镇静镇痛', '1', 'gis_anes_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:16:17', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '全身麻醉', '2', 'gis_anes_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:16:28', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '鼻咽腔', '0', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:04', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '口腔', '1', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:11', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '喉罩', '2', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:20', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '硬镜', '3', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:30', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '气管插管导管', '4', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:38', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '气管切开套管', '5', 'gis_mirror_measure', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:18:49', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '出血', '0', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:32:27', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '自主呼吸', '0', 'gis_breather_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:20:10', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '常频通气', '1', 'gis_breather_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:20:31', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '高频通气', '2', 'gis_breather_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:20:40', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '其它', '3', 'gis_breather_way', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:20:46', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 1, '低氧血症', '1', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-08 00:13:44', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 2, '喉-支气管痉挛', '2', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-08 00:14:39', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 3, '气道损伤', '3', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-08 00:15:02', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 4, '气胸', '4', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-08 00:15:20', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 5, '纵膈气肿', '5', 'gis_operation_complication', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-08 00:16:56', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
