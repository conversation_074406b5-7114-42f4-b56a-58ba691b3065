INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内镜检查类型', 'gis_order_type', '0', 'admin', '2023-03-31 16:17:59', '', NULL, NULL);
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '预约时间段', 'gis_booked_time', '0', 'admin', '2023-03-31 16:23:38', '', NULL, NULL);
INSERT INTO `cmb`.`sys_dict_type`(`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '预约状态', 'gis_booked_status', '0', 'admin', '2023-04-03 10:28:55', '', NULL, NULL);

INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '胃镜', 'wj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:45:26', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 1, '无痛胃镜', 'wtwj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:45:38', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 2, '肠镜', 'cj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:45:52', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 3, '无痛肠镜', 'wtcj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:46:03', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 4, '超声内镜', 'csnj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:46:15', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 5, '小肠镜', 'xcj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:46:26', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 6, '色素内镜', 'ssnj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:46:38', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 7, '十二指肠镜', 'srzcj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:46:54', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 8, '支气管镜', 'zqgj', 'gis_order_type', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-29 23:47:08', 'admin', '2023-03-29 23:47:13', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '8:00--9:00', 'time1', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:24:19', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 1, '9:00--10:00', 'time2', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:24:50', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 2, '10:00--11:00', 'time3', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:25:15', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 4, '11:00--12:00', 'time5', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:25:30', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 5, '14:00--15:00', 'time6', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:25:49', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 6, '15:00--16:00', 'time7', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:26:11', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 7, '16:00--17:00', 'time8', 'gis_booked_time', 0, NULL, 'default', 'N', '0', 'admin', '2023-03-31 16:26:37', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '未激活', '0', 'gis_booked_status', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:29:08', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 0, '激活', '1', 'gis_booked_status', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-03 10:29:15', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `cmb`.`sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `parent_dict_code`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `extend_s1`, `extend_s2`, `extend_s3`, `extend_s4`, `extend_i1`, `extend_i2`, `extend_f1`, `extend_f2`) VALUES ( 11, '已预约', '11', 'uis_exam_result_status', 0, NULL, 'default', 'N', '0', 'admin', '2023-04-06 15:12:21', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `cmb`.`sys_menu`( `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '预约排队', 0, 10, 'bookedinfo', 'gis/exammanagement/bookedinfo/Index', NULL, 1, 0, 'C', '0', '0', '', 'ellipse-circle', 'admin', '2023-03-30 15:26:24', 'admin', '2023-03-31 09:52:51', '');
