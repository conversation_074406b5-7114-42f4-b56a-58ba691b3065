<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper">
    
    <resultMap id="resultMapBase" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" autoMapping="true">
        <!-- <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo" autoMapping="true" columnPrefix="p__" /> -->
        <association property="callInfo" autoMapping="true" columnPrefix="ci__" resultMap="callInfoMap" />
        <!-- <association property="dicomStudy" javaType="yyy.xxx.simpfw.module.pacs.entity.DicomStudy" autoMapping="true" columnPrefix="dcs__" /> -->
        <association property="examModality" javaType="SysDictData" autoMapping="true" columnPrefix="rt__" />
        <association property="inpType" javaType="SysDictData" autoMapping="true" columnPrefix="rts__" />
        <association property="examItem" javaType="SysDictData" autoMapping="true" columnPrefix="ei__" />
        <association property="equipRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom" autoMapping="true" columnPrefix="er__" />
        <association property="creator" javaType="SysUser" autoMapping="true" columnPrefix="regu__" />
        <association property="examDoctor" javaType="SysUser" autoMapping="true" columnPrefix="eu__" />
        <association property="reqDoctor" javaType="SysUser" autoMapping="true" columnPrefix="ru__" />
        <association property="auditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="aud__" />
        <association property="reauditDoctor" javaType="SysUser" autoMapping="true" columnPrefix="aud2__" />
        <association property="signDoctor" javaType="SysUser" autoMapping="true" columnPrefix="sig__" />
        <association property="reportDoctor" javaType="SysUser" autoMapping="true" columnPrefix="rpu__" />
        <association property="anesDoctor" javaType="SysUser" autoMapping="true" columnPrefix="ad__" />
        <association property="operNurse" javaType="SysUser" autoMapping="true" columnPrefix="on__" />
        <association property="reqDept" javaType="SysDept" autoMapping="true" columnPrefix="rd__" />
        <association property="examDept" javaType="SysDept" autoMapping="true" columnPrefix="ex__" />
        <association property="inpWard" javaType="SysDictData" autoMapping="true" columnPrefix="wd__" />
        <association property="inpRoom" javaType="SysDictData" autoMapping="true" columnPrefix="rm__" />
        <association property="examCostType" javaType="SysDictData" autoMapping="true" columnPrefix="cos__" />
        <association property="resultStatus" javaType="SysDictData" autoMapping="true" columnPrefix="res2__" />
        <association property="examResultProp" javaType="SysDictData" autoMapping="true" columnPrefix="erp__" />
        <association property="operationGrade" javaType="SysDictData" autoMapping="true" columnPrefix="og__" />
        <association property="condParting" javaType="SysDictData" autoMapping="true" columnPrefix="cp__" />
        <association property="examDevice" javaType="yyy.xxx.simpfw.module.pacs.entity.DicomInfo" autoMapping="true" columnPrefix="exdev__" />
        <association property="anesWay" javaType="SysDictData" autoMapping="true" columnPrefix="aw__" />
        <association property="mirrorMeasure" javaType="SysDictData" autoMapping="true" columnPrefix="mm__" />
        <association property="breatherWay" javaType="SysDictData" autoMapping="true" columnPrefix="bw__" />

        <collection property="examParts" ofType="yyy.xxx.simpfw.module.pacs.vo.ExamParts"
                    column="exam_parts_id"
                    select="yyy.xxx.simpfw.module.pacs.mapper.ExamPartsMapper.selectExamParts" />
        <collection property="dicomStudies" ofType="yyy.xxx.simpfw.module.pacs.entity.DicomStudy"
                    column="{examUid=examUid}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.DicomStudyMapper.selectList" />
    </resultMap>
    <!-- 检查完整信息 -->
    <resultMap id="resultMapOutline" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" extends="resultMapBase" autoMapping="true">
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
         resultMap="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.resultMap" columnPrefix="p__" />

    </resultMap>
    <!-- 检查列表信息 -->
    <resultMap id="resultMapFull" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" extends="resultMapBase" autoMapping="true">
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
                     column="{patientId=patient_id}"
                     select="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.selectOne"/>
        <collection property="operationComplicationsCode" ofType="SysDictData"
                    column="{dict_type=operation_complication_dict_type,dict_value_in=operation_complications_dict_code}"
                    select="yyy.xxx.simpfw.module.pacs.mapper.VisionMapper.selectDictContains" />
    </resultMap>
    <!-- 呼叫信息 -->
    <resultMap id="callInfoMap" type="yyy.xxx.simpfw.module.pacs.entity.CallInfo" autoMapping="true">
        <association property="callRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom">
            <result property="roomCode" column="rm__roomCode"/>
            <result property="roomName" column="rm__roomName"/>
        </association>
    </resultMap>

    <sql id="selectBase">
        select h.id,h.status,h.create_time createTime,h.update_time updateTime,h.note_info noteInfo
        ,h.exam_uid examUid,h.exam_no examNo,h.patient_id,h.exam_modality_code, h.inp_type_code, h.exam_item_code, h.equip_room_code, h.exam_parts_id
        ,h.req_dept_code, h.req_doctor_code, h.exam_cost examCost, h.green_channel_flag greenChannelFlag
        ,h.appoint_time appointTime, h.clinic_diagnosis clinicDiagnosis, h.allergy_history allergyHistory, h.clinic_disease clinicDisease, h.exam_prerequire examPrerequire
        ,h.reserved_no_used reservedNoUsed, h.exam_at_pm examAtPm, h.adm_no admNo, h.operation_info operationInfo
        ,h.adm_series_num admSeriesNum,h.inp_no inpNo,h.bed_no bedNo,h.apply_path applyPath,h.exam_diagnosis examDiagnosis
        ,h.ord_id ordId,h.ord_name ordName,h.arcim_code arcimCode,h.ord_bill_status ordBillStatus
        ,h.ord_priority_code ordPriorityCode,h.ord_priority ordPriority,h.exam_purpose examPurpose,h.is_emergency emergency
        ,h.ord_status ordStatus,h.req_time reqTime,h.exam_time examTime,operation_suggestion operationSuggestion,h.exam_conclusion examDesc
        ,h.audit_time auditTime,h.reaudit_time reauditTime,h.sign_time signTime,h.origin_id originId,h.inp_times inpTimes
        ,exam_doctor_code examDoctorsCode,exam_doctor_name examDoctorsName
        ,consultants_code consultantsCode,consultants_name consultantsName
        ,h.recorders_code recordersCode,h.recorders_name recordersName,h.report_date reportTime,h.outp_no outpNo
        ,h.regist_time registTime,h.regist_user_code regu__userName,regist_user_name regu__nickName
        ,h.report_url_pdf reportUrlPdf,h.report_url_jpg reportUrlJpg,h.report_url_username reportUrlUsername,h.report_url_password reportUrlPassword

        ,p.name p__name,p.name_pingyin p__namePingyin,p.age p__age,p.reg_no p__registNo,p.birthday p__birthday
        ,pg.dict_label P__gd__dictLabel,p.gender_code P__gd__dictValue
        ,pau.dict_label P__ageu__dictLabel,p.age_unit_code P__ageu__dictValue

        ,ci.id ci__id,ci.call_no ci__callNo,cer.room_code ci__rm__roomCode,cer.room_name ci__rm__roomName

        <!-- ,dcs.id dcs__id,dcs.studyInstanceUid dcs__studyInstanceUid,dcs.dicomStudyInstanceUid dcs__dicomStudyInstanceUid -->

        ,rt.dict_code rt__dictCode,rt.dict_value rt__dictValue,rt.dict_label rt__dictLabel
        ,rts.dict_code rts__dictCode,rts.dict_value rts__dictValue,rts.dict_label rts__dictLabel
        ,ei.dict_code ei__dictCode,ei.dict_value ei__dictValue,ei.dict_label ei__dictLabel
        ,er.room_code er__roomCode,er.room_name er__roomName
        ,wd.dict_code wd__dictCode,wd.dict_value wd__dictValue,wd.dict_label wd__dictLabel
        ,rm.dict_code rm__dictCode,rm.dict_value rm__dictValue,rm.dict_label rm__dictLabel
        ,cos.dict_code cos__dictCode,cos.dict_value cos__dictValue,cos.dict_label cos__dictLabel
        ,res2.dict_code res2__dictCode,res2.dict_value res2__dictValue,res2.dict_label res2__dictLabel
        ,erp.dict_code erp__dictCode,erp.dict_value erp__dictValue,erp.dict_label erp__dictLabel
        ,cp.dict_code cp__dictCode,cp.dict_value cp__dictValue,cp.dict_label cp__dictLabel
        ,og.dict_code og__dictCode,og.dict_value og__dictValue,og.dict_label cp__dictLabel

        ,h.exam_doctor_code eu__userName,h.exam_doctor_name eu__nickName
        <!-- ,eu.user_id eu__userId,eu.user_name eu__userName,eu.nick_name eu__nickName -->
        <!--存什么读什么,ru.user_id ru__userId,ru.user_name ru__userName,ifnull(h.req_doctor_name,ru.nick_name) ru__nickName
        ,aud.user_id aud__userId,aud.user_name aud__userName,ifnull(h.audit_doctor_name,aud.nick_name) aud__nickName
        ,aud2.user_id aud2__userId,aud2.user_name aud2__userName,ifnull(h.reaudit_doctor_name,aud2.nick_name) aud2__nickName
        ,sig.user_id sig__userId,sig.user_name sig__userName,ifnull(h.sign_doctor_name,sig.nick_name) sig__nickName
        ,rpu.user_id rpu__userId,rpu.user_name rpu__userName,ifnull(h.report_doctor_name,rpu.nick_name) rpu__nickName -->
        ,h.req_doctor_code ru__userName,h.req_doctor_name ru__nickName
        ,h.audit_doctor_code aud__userName,h.audit_doctor_name aud__nickName
        ,h.reaudit_doctor_code aud2__userName,h.reaudit_doctor_name aud2__nickName
        ,h.sign_doctor_code sig__userName,h.sign_doctor_name sig__nickName
        ,h.report_doctor_code rpu__userName,h.report_doctor_name rpu__nickName
        ,h.anes_doctor_code ad__userName,h.anes_doctor_name ad__nickName
        ,h.oper_nurse_code on__userName,h.oper_nurse_name on__nickName

        <!-- ,rd.dept_id rd__deptId,rd.dept_name rd__deptName -->
        ,h.req_dept_code rd__deptCode,h.req_dept_name rd__deptName
        ,ex.dept_id ex__deptId,ex.dept_name ex__deptName

        ,exdev.id exdev__id,exdev.modality_code exdev__modalityCode,exdev.device_code exdev__deviceCode,exdev.modality exdev__modality

        ,h.pathology_diagnosis pathologyDiagnosis,h.pathology_treatment pathologyTreatment
        ,aw.dict_code aw__dictCode,aw.dict_value aw__dictValue,aw.dict_label aw__dictLabe
        ,mm.dict_code mm__dictCode,mm.dict_value mm__dictValue,mm.dict_label mm__dictLabel
        ,bw.dict_code bw__dictCode,bw.dict_value bw__dictValue,bw.dict_label bw__dictLabel
        ,h.retain_specimen_flag retainSpecimenFlag,h.operation_complication_flag operationComplicationFlag
        ,'gis_operation_complication' operation_complication_dict_type,h.operation_complications_code operation_complications_dict_code
        ,h.oc_treatment_measure ocTreatmentMeasure
        ,h.appoint_exam_date appointExamDate
        ,h.appoint_exam_time appointExamTime

        <!-- 检查单 -->
        from `d_exam_info` `h`
        <!-- 患者信息 -->
        join d_patient p on p.patient_id=h.patient_id
        <!-- 排队信息 -->
        left join d_call_info ci on ci.exam_info_id=h.id
        left join d_equip_room cer on cer.room_code=ci.call_room_code
        <!-- dicom
        left join d_dicom_study dcs on dcs.exam_info_id=h.id -->
        <!-- -->
        left join v_sys_dict_data rt on rt.dict_type='uis_exam_modality' and rt.dict_value=h.exam_modality_code
        left join v_sys_dict_data rts on rts.dict_type='uis_inp_type' and rts.dict_value=h.inp_type_code
        left join v_sys_dict_data ei on ei.dict_type='uis_exam_item' and ei.dict_value=h.exam_item_code
        left join d_equip_room er on er.room_code=h.equip_room_code
        left join v_sys_dict_data wd on wd.dict_type='uis_inp_ward' and wd.dict_value=h.inp_ward_code
        left join v_sys_dict_data rm on rm.dict_type='uis_inp_room' and rm.dict_value=h.inp_room_code
        left join v_sys_dict_data cos on cos.dict_type='uis_exam_cost_type' and cos.dict_value=h.exam_cost_type_code
        left join v_sys_dict_data res2 on res2.dict_type='uis_exam_result_status' and res2.dict_value=h.result_status_code
        left join v_sys_dict_data erp on erp.dict_type='uis_exam_result_prop' and erp.dict_value=h.exam_result_prop_code
        left join v_sys_dict_data pg on pg.dict_type='uis_gender_type' and pg.dict_value=p.gender_code
        left join v_sys_dict_data pau on pau.dict_type='uis_age_unit' and pau.dict_value=p.age_unit_code
        left join v_sys_dict_data cp on cp.dict_type='uis_cond_parting' and cp.dict_value=h.cond_parting_code
        left join v_sys_dict_data og on og.dict_type='gis_operation_grade' and og.dict_value=h.operation_grade_code
        left join v_sys_dict_data aw on aw.dict_type='gis_anes_way' and aw.dict_value=h.anes_way_code
        left join v_sys_dict_data mm on mm.dict_type='gis_mirror_measure' and mm.dict_value=h.mirror_measure_code
        left join v_sys_dict_data bw on bw.dict_type='gis_breather_way' and bw.dict_value=h.breather_way_code

        <!-- left join v_sys_user eu on eu.user_name=h.exam_doctor_code -->
        <!--存什么读什么left join v_sys_user ru on ru.user_name=h.req_doctor_code
        left join v_sys_user aud on aud.user_name=h.audit_doctor_code
        left join v_sys_user aud2 on aud2.user_name=h.reaudit_doctor_code
        left join v_sys_user sig on sig.user_name=h.sign_doctor_code
        left join v_sys_user rpu on rpu.user_name=h.report_doctor_code -->

        <!-- left join v_sys_dept rd on rd.dept_id=h.req_dept_code -->
        left join v_sys_dept ex on ex.dept_id=h.exam_dept_code

        left join d_dicom_info exdev on exdev.modality_code=h.modality_code
    </sql>

    <sql id="selectWhereKey">
        where 1=1
        <if test="null!=id"> and `h`.`id`=#{id}</if>
        <if test="null!=examUid and ''!=examUid"> and `h`.`exam_uid`=#{examUid}</if>
        <if test="null!=params and null!=params.dataScope">${params.dataScope}</if>
    </sql>

    <select id="selectOne" resultMap="resultMapFull">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>
        <if test="null==id and (null==examUid or ''==examUid)"> and 1=0</if>
    </select>
    
    <select id="selectList" parameterType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" resultMap="resultMapOutline">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>

        <if test="null!=examNo and ''!=examNo"> and `h`.`exam_no`=#{examNo}</if>
        <if test="null!=inpNo and ''!=inpNo"> and `h`.`inp_no`=#{inpNo}</if>
        <!-- 检查类型 -->
        <if test="null!=examModality and null!=examModality.dictValue and ''!=examModality.dictValue">
            <!-- and h.exam_modality_code=#{examModality.dictValue} -->
            and (h.exam_modality_code=#{examModality.dictValue}
            or exists (select 1 from v_sys_dict_data modc2 where modc2.dict_value=#{examModality.dictValue} and modc2.dict_code=rt.parent_dict_code))
        </if>
        <if test="null!=examModalitiesCodes">
            <trim prefix=" and (" suffix=")">
                <foreach collection="examModalitiesCodes" item="var" open="h.exam_modality_code in (" close=")" separator=",">
                    #{var}
                </foreach>

                <foreach collection="examModalitiesCodes" item="var" open="or exists (select 1 from v_sys_dict_data modc2 where modc2.dict_value in (" close=") and modc2.dict_code=rt.parent_dict_code)" separator=",">
                    #{var}
                </foreach>
            </trim>
        </if>
        <!-- 患者信息 -->
        <if test="null!=patientInfo">
            <if test="null!=patientInfo.name and ''!=patientInfo.name"> and `p`.`name` like replace(#{patientInfo.name},'*','%')</if>
            <if test="null!=patientInfo.medicalRecordNo and ''!=patientInfo.medicalRecordNo"> and `p`.`medical_record_no`=#{patientInfo.medicalRecordNo}</if>
            <if test="null!=patientInfo.registNo and ''!=patientInfo.registNo"> and `p`.`reg_no`=#{patientInfo.registNo}</if>
            <if test="null!=patientInfo.namePingyin and ''!=patientInfo.namePingyin"> and `p`.`name_pingyin`=#{patientInfo.namePingyin}</if>
            <if test="null!=patientInfo.gender and null!=patientInfo.gender.dictValue and ''!=patientInfo.gender.dictValue"> and `p`.`gender_code`=#{patientInfo.gender.dictValue}</if>
            <if test="null!=patientInfo.birthday"> and `p`.`birthday`=#{patientInfo.birthday}</if>
        </if>
        <!-- 检查项目 -->
        <if test="null!=examItemCodes">
            <foreach collection="examItemCodes" item="var" open=" and h.exam_item_code in (" close=")" separator=",">
                #{var}
            </foreach>
        </if>
        <!-- 诊前准备 -->
        <if test="null!=examPrerequire"> and h.exam_prerequire=#{examPrerequire}</if>
        <!-- 下午检查 -->
        <if test="null!=examAtPm"> and h.exam_at_pm=#{examAtPm}</if>
        <!-- 排除诊前准备 -->
        <if test="null!=examPrerequireExclude and examPrerequireExclude"> and (h.exam_prerequire is null or h.exam_prerequire=1)</if>
        <!-- 检查医生 -->
        <if test="null!=examDoctor">
            <if test="null!=examDoctor.nickName and ''!=examDoctor.nickName"> and h.exam_doctor_name like replace(#{examDoctor.nickName},'*','%')</if>
            <if test="null!=examDoctor.userName and ''!=examDoctor.userName"> and h.exam_doctor_code=#{examDoctor.userName}</if>
        </if>
        <if test="null!=examDoctorUserNames">
            <foreach collection="examDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.exam_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 工作状态 -->
        <if test="null!=resultStatus and null!=resultStatus.dictValue and ''!=resultStatus.dictValue">
            and h.result_status_code=#{resultStatus.dictValue}
        </if>
        <if test="null != resultStatusValues or (null!=resultStatusAsStatus and ''!=resultStatusAsStatus)">
            and(
            <trim prefixOverrides="or">
            <if test="null != resultStatusValues">
            <foreach collection="resultStatusValues" item="var" open=" or h.result_status_code in (" close=")" separator=",">#{var}</foreach>
            </if>
            <if test="(null!=resultStatusAsStatus and ''!=resultStatusAsStatus)"> or h.status=#{resultStatusAsStatus}</if>
            </trim>
            )
        </if>
        <!-- 登记时间 -->
        <if test="null!=createTimeGe"> and (h.create_time&gt;=#{createTimeGe} and h.appoint_time is null or h.appoint_time&gt;=#{createTimeGe})</if>
        <if test="null!=createTimeLt"> and (h.create_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY) and h.appoint_time is null or h.appoint_time&lt;date_add(#{createTimeLt},INTERVAL 1 DAY))</if>
        <!-- 预约时间 -->
        <if test="null!=appointTimeGe"> and h.appoint_time&gt;=#{appointTimeGe}</if>
        <if test="null!=appointTimeLt"> and h.appoint_time&lt;date_add(#{appointTimeLt},INTERVAL 1 DAY)</if>

        <!-- 预约检查日期 -->
        <if test="null!=appointExamDateGe"> and h.appoint_exam_date&gt;=#{appointExamDateGe}</if>
        <if test="null!=appointExamDateLt"> and h.appoint_exam_date&lt;date_add(#{appointExamDateLt},INTERVAL 1 DAY)</if>

        <!-- 当日检查的：当日登记+预约当日 -->
        <if test="appointWithCreated"> and(h.create_time&gt;=current_date() and h.appoint_exam_date is null
            or h.appoint_exam_date&gt;=current_date() and h.appoint_exam_date&lt;date_add(current_date(), interval 1 day))</if>

        <!-- 当日检查的：当日检查+预约检查当日 -->
<!--        <if test="appointWithCreated">-->
<!--         and (-->
<!--            h.appoint_exam_date&gt;=#{appointExamDateGe} and h.appoint_exam_date&lt;date_add(#{appointExamDateLt},INTERVAL 1 DAY)-->
<!--            or-->
<!--            h.exam_time&gt;=#{examTimeGe} and h.exam_time&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)-->
<!--        )-->
<!--        </if>-->

        <!-- 检查时间 -->
        <if test="null!=examTimeGe"> and h.exam_time&gt;=#{examTimeGe}</if>
        <if test="null!=examTimeLt"> and h.exam_time&lt;date_add(#{examTimeLt},INTERVAL 1 DAY)</if>
        <!-- 审核时间 -->
        <if test="null!=auditTimeGe"> and h.audit_time&gt;=#{auditTimeGe}</if>
        <if test="null!=auditTimeLt"> and h.audit_time&lt;date_add(#{auditTimeLt},INTERVAL 1 DAY)</if>
        <!-- 检查机房 -->
        <if test="null!=callInfo and null!=callInfo.callRoom and null!=callInfo.callRoom.roomCode and ''!=callInfo.callRoom.roomCode">
            and ci.call_room_code=#{callInfo.callRoom.roomCode}
        </if>
        <if test="null!=equipRoomsCode and equipRoomsCode.size()>0">
            <foreach collection="equipRoomsCode" item="var" open=" and ci.call_room_code in (" close=")" separator=",">#{var}</foreach>
        </if>
        <!-- 设备型号 -->
        <if test="null!=examDevicesCode and ''!=examDevicesCode">
            and cer.room_code in (select equip_room_code from r_dicominfo_equip_room rde,d_dicom_info dcmi where rde.equip_code=dcmi.id and dcmi.device_code in(
            <foreach collection="examDevicesCode" item="var" separator=",">#{var}</foreach>
            ))
        </if>
        <!-- 住院号 -->
        <if test="null!=inpNo and ''!=inpNo"> and h.inp_no=#{inpNo}</if>
        <!-- 报告医生 -->
        <if test="null!=reportDoctor">
            <if test="null!=reportDoctor.nickName and ''!=reportDoctor.nickName"> and h.report_doctor_name like replace(#{reportDoctor.nickName},'*','%')</if>
            <if test="null!=reportDoctor.userName and ''!=reportDoctor.userName"> and h.report_doctor_code=#{reportDoctor.userName}</if>
        </if>
        <if test="null!=reportDoctorUserNames">
            <foreach collection="reportDoctorUserNames" item="var" open=" and (" close=")" separator=" or ">concat(',',h.report_doctor_code,',') like concat('%,',#{var},',%')</foreach>
        </if>
        <!-- 审核医生 -->
        <if test="null!=auditDoctor">
            <if test="null!=auditDoctor.nickName and ''!=auditDoctor.nickName"> and h.audit_doctor_name like replace(#{auditDoctor.nickName},'*','%')</if>
            <if test="null!=auditDoctor.userName and ''!=auditDoctor.userName"> and h.audit_doctor_code=#{auditDoctor.userName}</if>
        </if>
        <!-- 检查所见 -->
        <if test='null != examDescSegm and examDescSegm.length > 0'>
            and (
            <foreach collection="examDescSegm" item="item" open="" separator=" and " close="">
                h.exam_conclusion like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 检查诊断 -->
        <if test='null != examDiagnosisSegm and examDiagnosisSegm.length > 0'>
            and (
            <foreach collection="examDiagnosisSegm" item="item" open="" separator=" and " close="">
                h.exam_diagnosis like '%${item}%'
            </foreach>
            )
        </if>
        <!-- 就诊类型 -->
        <if test="null!=inpType and null!=inpType.dictValue and ''!=inpType.dictValue">
            and h.inp_type_code=#{inpType.dictValue}
        </if>
        <if test="null!=inpTypeValues and inpTypeValues.length>0">
        <foreach collection="inpTypeValues" item="item" open=" and h.inp_type_code in (" close=")" separator=",">
          #{item}
        </foreach>
        </if>
        <!-- 状态 -->
        <if test="0==status"> and (h.status is null or h.status=0)</if>
        <if test="null!=status and 0!=status"> and h.status=#{status}</if>
        <!-- 就诊号 -->
        <if test="null!=outpNo and ''!=outpNo"> and h.outp_no=#{outpNo}</if>
        <!-- -->
        <if test="null!=statusOfSendReport"> and h.status_of_send_report=#{statusOfSendReport}</if>

        order by <if test="null!=orderBy and ''!=orderBy">${orderBy},</if>h.id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into `d_exam_info` (regist_way,exam_uid,exam_no
        , patient_id
        , exam_modality_code
        , inp_type_code
        , exam_item_code
        , equip_room_code
        , exam_parts_id
        , exam_dept_code, exam_dept_name
        , exam_doctor_code, exam_doctor_name
        , req_dept_code, req_dept_name
        , req_doctor_code, req_doctor_name
        , inp_ward_code,inp_room_code,exam_cost_type_code,cond_parting_code
        , exam_cost, green_channel_flag, appoint_time, clinic_diagnosis, allergy_history, clinic_disease,adm_no
        , operation_info,adm_series_num,inp_no,bed_no,apply_path,ord_id,ord_name,arcim_code,ord_bill_status
        , ord_priority_code,ord_priority,exam_purpose,is_emergency,ord_status,req_time,exam_time,result_status_code
        , exam_prerequire, reserved_no_used, exam_at_pm, note_info,origin_id,inp_times
        , regist_user_code,regist_user_name,regist_time
        , modality_code,device_code,operation_grade_code
        , anes_doctor_code,anes_doctor_name
        , oper_nurse_code,oper_nurse_name
        ,appoint_exam_date
        ,appoint_exam_time
        , status
        )
        values(#{registWay},#{examUid},#{examNo}
        ,<choose><when test="null!=patientInfo">#{patientInfo.patientId}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examModality">#{examModality.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=inpType">#{inpType.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examItem">#{examItem.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=equipRoom">#{equipRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,concat(<choose><when test="null!=examParts and examParts.size>0"><foreach collection="examParts" item="var" separator=",',',">#{var.id}</foreach></when><otherwise>null</otherwise></choose>)
        ,<choose><when test="null!=examDept">#{examDept.deptId},#{examDept.deptName}</when><otherwise>null,null</otherwise></choose>
        <!-- ,<choose><when test="null!=examDoctor">#{examDoctor.userName},#{examDoctor.nickName}</when><otherwise>null,null</otherwise></choose> -->
        ,#{examDoctorsCode},#{examDoctorsName}
        ,<choose><when test="null!=reqDept">#{reqDept.deptCode},#{reqDept.deptName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=reqDoctor">#{reqDoctor.userName},#{reqDoctor.nickName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=inpWard">#{inpWard.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=inpRoom">#{inpRoom.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=examCostType">#{examCostType.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=condParting">#{condParting.dictValue}</when><otherwise>null</otherwise></choose>
        ,#{examCost}, #{greenChannelFlag}, #{appointTime}, #{clinicDiagnosis}, #{allergyHistory}, #{clinicDisease},#{admNo}
        , #{operationInfo},#{admSeriesNum}, #{inpNo},#{bedNo},#{applyPath},#{ordId},#{ordName},#{arcimCode},#{ordBillStatus}
        ,#{ordPriorityCode},#{ordPriority},#{examPurpose},#{emergency},#{ordStatus},#{reqTime},#{examTime},0
        , #{examPrerequire}, #{reservedNoUsed}, #{examAtPm}, #{noteInfo},#{originId},#{inpTimes}
        ,<choose><when test="null!=creator">#{creator.userName},#{creator.nickName}</when><otherwise>null,null</otherwise></choose>,now()
        ,<choose><when test="null!=examDevice">#{examDevice.modalityCode},#{examDevice.deviceCode}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=operationGrade">#{operationGrade.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=anesDoctor">#{anesDoctor.userName}#{anesDoctor.nickName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=operNurse">#{operNurse.userName}#{operNurse.nickName}</when><otherwise>null,null</otherwise></choose>
        ,<choose><when test="null!=appointExamDate">#{appointExamDate}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=appointExamTime">#{appointExamTime}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=status">#{status}</when><otherwise>0</otherwise></choose>
        )
    </insert>
	 
    <update id="update">
        update `d_exam_info`
        set exam_no=#{examNo}
        ,patient_id=<choose><when test="null!=patientInfo">#{patientInfo.id}</when><otherwise>null</otherwise></choose>
        ,`exam_modality_code`=<choose><when test="null!=examModality">#{examModality.dictValue}</when><otherwise>null</otherwise></choose>
        ,`inp_type_code`=<choose><when test="null!=inpType">#{inpType.dictValue}</when><otherwise>null</otherwise></choose>
        ,`exam_item_code`=<choose><when test="null!=examItem">#{examItem.dictValue}</when><otherwise>null</otherwise></choose>
        ,`equip_room_code`=<choose><when test="null!=equipRoom">#{equipRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,`exam_parts_id`=concat(<choose><when test="null!=examParts and examParts.size>0"><foreach collection="examParts" item="var" separator=",',',">#{var.id}</foreach></when><otherwise>null</otherwise></choose>)
        ,exam_dept_code=<choose><when test="null!=examDept">#{examDept.deptId}</when><otherwise>null</otherwise></choose>
        ,exam_dept_name=<choose><when test="null!=examDept">#{examDept.deptName}</when><otherwise>null</otherwise></choose>
        <!-- ,exam_doctor_code=<choose><when test="null!=examDoctor">#{examDoctor.userName}</when><otherwise>null</otherwise></choose>
        ,exam_doctor_name=<choose><when test="null!=examDoctor">#{examDoctor.nickName}</when><otherwise>null</otherwise></choose> -->
        ,exam_doctor_code=#{examDoctorsCode},exam_doctor_name=#{examDoctorsName}
        ,req_dept_code=<choose><when test="null!=reqDept">#{reqDept.deptCode}</when><otherwise>null</otherwise></choose>
        ,req_dept_name=<choose><when test="null!=reqDept">#{reqDept.deptName}</when><otherwise>null</otherwise></choose>
        ,req_doctor_code=<choose><when test="null!=reqDoctor">#{reqDoctor.userName}</when><otherwise>null</otherwise></choose>
        ,req_doctor_name=<choose><when test="null!=reqDoctor">#{reqDoctor.nickName}</when><otherwise>null</otherwise></choose>
        ,inp_ward_code=<choose><when test="null!=inpWard">#{inpWard.dictValue}</when><otherwise>null</otherwise></choose>
        ,inp_room_code=<choose><when test="null!=inpRoom">#{inpRoom.dictValue}</when><otherwise>null</otherwise></choose>
        ,exam_cost_type_code=<choose><when test="null!=examCostType">#{examCostType.dictValue}</when><otherwise>null</otherwise></choose>
        ,cond_parting_code=<choose><when test="null!=condParting">#{condParting.dictValue}</when><otherwise>null</otherwise></choose>
        ,exam_cost=#{examCost}
        , green_channel_flag=#{greenChannelFlag}
        , appoint_time=#{appointTime}
        , clinic_diagnosis=#{clinicDiagnosis}
        , allergy_history=#{allergyHistory}
        , clinic_disease=#{clinicDisease}
        , adm_no=#{admNo}
        , operation_info=#{operationInfo}
        , adm_series_num=#{admSeriesNum}
        , inp_no=#{inpNo}
        , bed_no=#{bedNo}
        , apply_path=#{applyPath}
        ,ord_id=#{ordId},ord_name=#{ordName},arcim_code=#{arcimCode},ord_bill_status=#{ordBillStatus}
        ,ord_priority_code=#{ordPriorityCode},ord_priority=#{ordPriority},exam_purpose=#{examPurpose}
        ,is_emergency=#{emergency},ord_status=#{ordStatus},req_time=#{reqTime},exam_time=#{examTime}
        , exam_prerequire=#{examPrerequire}
        , reserved_no_used=#{reservedNoUsed}
        , exam_at_pm=#{examAtPm}
        , note_info=#{noteInfo}
        , inp_times=#{inpTimes}
        ,<choose><when test="null!=examDevice">modality_code=#{examDevice.modalityCode},device_code=#{examDevice.deviceCode}</when><otherwise>modality_code=null,device_code=null</otherwise></choose>
        ,operation_grade_code=<choose><when test="null!=operationGrade">#{operationGrade.dictValue}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=anesDoctor">anes_doctor_code=#{anesDoctor.userName},anes_doctor_name=#{anesDoctor.nickName}</when><otherwise>anes_doctor_code=null,anes_doctor_name=null</otherwise></choose>
        ,<choose><when test="null!=operNurse">oper_nurse_code=#{operNurse.userName},oper_nurse_name=#{operNurse.nickName}</when><otherwise>oper_nurse_code=null,oper_nurse_name=null</otherwise></choose>
        ,<choose><when test="null!=appointExamDate">appoint_exam_date=#{appointExamDate}</when><otherwise>appoint_exam_date=null</otherwise></choose>
        ,<choose><when test="null!=appointExamTime">appoint_exam_time=#{appointExamTime}</when><otherwise>appoint_exam_time=null</otherwise></choose>

        where (result_status_code is null or result_status_code in('0','1','2')) and `id`=#{id}
    </update>
    <!-- 检查工作进度 -->
    <update id="updateResultStatus">
        update d_exam_info set result_status_code=#{resultStatus.dictValue}
        <!-- 更新为已检查 -->
        <if test='"1"==resultStatus.dictValue'>
            ,exam_prerequire=case when 0=exam_prerequire then 1 else exam_prerequire end
            ,exam_time=now()
            <if test="null!=examDoctor">
            ,exam_doctor_name=#{examDoctor.nickName}
            ,exam_doctor_code=#{examDoctor.userName}
            </if>
            <if test="null!=examDoctorsName">
                ,exam_doctor_name=#{examDoctorsName}
            </if>
            <if test="null!=examDoctorsCode">
                ,exam_doctor_code=#{examDoctorsCode}
            </if>
        </if>

        where id=#{id}
    </update>
    <!-- 报告处于登记完成、一检查和已报告时可保存报告 -->
    <update id="saveReport">
        update d_exam_info
        set exam_conclusion=#{examDesc}
        , exam_diagnosis=#{examDiagnosis}
        , operation_suggestion=#{operationSuggestion}
        , exam_result_prop_code=<choose><when test="null!=examResultProp">#{examResultProp.dictValue}</when><otherwise>null</otherwise></choose>
        , operation_grade_code=<choose><when test="null!=operationGrade">#{operationGrade.dictValue}</when><otherwise>null</otherwise></choose>
        , report_date=ifnull(report_date,now())
        <choose><when test="null!=reportDoctor">
        ,report_doctor_code=#{reportDoctor.userName},report_doctor_name=#{reportDoctor.nickName}
        </when><otherwise>,report_doctor_code=null,report_doctor_name=null</otherwise></choose>
        <!-- 复审同时保存，不更新审核工作状态 -->
        , result_status_code=case when '3'=result_status_code then result_status_code else '2' end
        <if test="null!=examTime">,exam_time=ifnull(exam_time,#{examTime})</if>
        <!-- <if test="null!=examDoctor">,exam_doctor_code=ifnull(exam_doctor_code,#{examDoctor.userName}),exam_doctor_name=#{examDoctor.nickName}</if> -->
        <!-- -->
        ,exam_doctor_code=#{examDoctorsCode},exam_doctor_name=#{examDoctorsName}
        ,consultants_code=#{consultantsCode},consultants_name=#{consultantsName}
        ,recorders_code=#{recordersCode},recorders_name=#{recordersName}
        ,<choose><when test="null!=anesDoctor">anes_doctor_code=#{anesDoctor.userName},anes_doctor_name=#{anesDoctor.nickName}</when><otherwise>anes_doctor_code=null,anes_doctor_name=null</otherwise></choose>
        ,<choose><when test="null!=operNurse">oper_nurse_code=#{operNurse.userName},oper_nurse_name=#{operNurse.nickName}</when><otherwise>oper_nurse_code=null,oper_nurse_name=null</otherwise></choose>

        , pathology_diagnosis=#{pathologyDiagnosis}
        , pathology_treatment=#{pathologyTreatment}
        , anes_way_code=<choose><when test="null!=anesWay">#{anesWay.dictValue}</when><otherwise>null</otherwise></choose>
        , mirror_measure_code=<choose><when test="null!=mirrorMeasure">#{mirrorMeasure.dictValue}</when><otherwise>null</otherwise></choose>
        , breather_way_code=<choose><when test="null!=breatherWay">#{breatherWay.dictValue}</when><otherwise>null</otherwise></choose>
        , retain_specimen_flag=#{retainSpecimenFlag}
        , operation_complication_flag=#{operationComplicationFlag}
        , operation_complications_code=concat(<choose><when test="null!=operationComplicationsCode and operationComplicationsCode.size>0"><foreach collection="operationComplicationsCode" item="var" separator=",',',">#{var.dictValue}</foreach></when><otherwise>null</otherwise></choose>)
        , oc_treatment_measure=#{ocTreatmentMeasure}
        <!-- 工作状态为登记、检查、已报告、审核时允许保存报告 -->
        where (result_status_code is null or result_status_code in ('0','1','2','3')) and id=#{id}
    </update>
	<!-- 删除 -->
    <update id="delete">
        update `d_exam_info` set status=2 where `id`=#{id}
    </update>
    <update id="undoDelete">
        update `d_exam_info` set status=0 where status=2 and `id`=#{id}
    </update>
    <!-- 用医嘱查 -->
    <select id="selectByOrdId" resultMap="resultMapFull">
    select h.id,h.ord_id ordId, h.ord_name ordName
     from d_exam_info h
     where h.ord_id=#{ordId}
    </select>

    <!-- 诊前准备就绪 -->
    <update id="updateExamPrerequire">
        update d_exam_info
        set exam_prerequire=#{examPrerequire}
        where (result_status_code is null or result_status_code in('0','1'))

        <!-- <choose><when test="0==examPrerequire">
        and exam_prerequire is null
        </when><when test="1==examPrerequire">
        and exam_prerequire=0
        </when><otherwise> and 1=0</otherwise></choose> -->

        and (status is null or status=0) and id=#{id}
    </update>

    <!-- 下午/上午检查 -->
    <update id="updateExamPeriod">
        update d_exam_info
        set exam_at_pm=#{examAtPm}
        <!-- 只能更新工作状态为已登记和已检查 -->
        where (result_status_code is null or result_status_code in ('0','1')) and (status is null or status=0) and id=#{id}
    </update>

    <!-- 审核报告，参数的工作状态（resultStatus）必须时当前数据库中保存的值 -->
    <update id="auditReport">
        update d_exam_info
        <choose><when test='"2"==resultStatus.dictValue'>
            set result_status_code='3',audit_doctor_code=#{auditDoctor.userName},audit_doctor_name=#{auditDoctor.nickName},audit_time=now()
            where result_status_code='2'
            and (status is null or status=0) and id=#{id}
        </when><when test='"3"==resultStatus.dictValue'>
            set result_status_code='4',reaudit_doctor_code=#{reauditDoctor.userName},reaudit_doctor_name=#{reauditDoctor.nickName},reaudit_time=now()
            where result_status_code='3'
            and (status is null or status=0) and id=#{id}
        </when><otherwise>where 1=0</otherwise></choose>
    </update>

    <update id="reauditReport">
        update d_exam_info
        set result_status_code='4',reaudit_doctor_code=#{reauditDoctor.userName},reaudit_doctor_name=#{reauditDoctor.nickName},reaudit_time=now()
        ,consultants_name=<choose><when test="null!=consultantsName and ''!=consultantsName">#{consultantsName}</when><otherwise>#{reauditDoctor.nickName}</otherwise></choose>
        where result_status_code='3'
        and (status is null or status=0) and id=#{id}
    </update>

    <!-- 签字报告 -->
    <update id="signReport">
        update d_exam_info
        set sign_doctor_code=#{signDoctor.userName},sign_doctor_name=#{signDoctor.nickName},sign_time=now()
        where result_status_code in ('3','4')
        and sign_doctor_code is null
        and (status is null or status=0) and id=#{id}
    </update>

    <update id="saveDoc">
        update d_exam_info
        set report_url_pdf=#{reportUrlPdf}
        ,report_url_jpg=#{reportUrlJpg}
        ,report_url_password=#{reportUrlPassword}
        ,report_url_username=#{reportUrlUsername}
        where id=#{id}
    </update>

    <update id="sendReportStatus">
        update d_exam_info
        set status_of_send_report=#{statusOfSendReport}
        where id=#{id}
    </update>

    <!-- 更新激活状态 -->
    <update id="updateActiveStatus">
        update d_exam_info
        <choose><when test='"0"==status or "3"==status'>
            set status=#{status}
            where id=#{id}
        </when><otherwise>where 1=0</otherwise></choose>
    </update>

    <!-- 更新预约检查时间 -->
    <update id="updateAppointExamTime">
        update d_exam_info
        set appoint_exam_time=#{appointExamTime}
        where id=#{id}
    </update>
</mapper>