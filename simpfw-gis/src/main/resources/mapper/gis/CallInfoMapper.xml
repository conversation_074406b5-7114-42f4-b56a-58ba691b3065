<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.gis.mapper.CallInfoMapper">
    
    <resultMap id="baseResultMap" type="yyy.xxx.simpfw.module.pacs.vo.CallInfoVo" autoMapping="true">
        <!-- <association property="examInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo"
                     column="{id=exam_info_id}" select="yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper.selectOne" /> -->
        <association property="callRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom" autoMapping="true" columnPrefix="rm__" />
        <association property="doctor" javaType="SysUser" autoMapping="true" columnPrefix="doc__" />
    </resultMap>

    <resultMap id="oneResultMap" type="yyy.xxx.simpfw.module.pacs.vo.CallInfoVo" extends="baseResultMap" autoMapping="true">
        <association property="examInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo"
                     column="{id=exam_info_id}" select="yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper.selectOne" />
    </resultMap>

    <resultMap id="listResultMap" type="yyy.xxx.simpfw.module.pacs.vo.CallInfoVo" extends="baseResultMap" autoMapping="true">
        <association property="examInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo"
                     columnPrefix="ei__" resultMap="yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper.resultMapOutline" />
    </resultMap>

    <sql id="selectBase">
        select r.id,r.status,r.create_time createTime,r.update_time updateTime,r.note_info noteInfo
        ,r.exam_info_id,r.call_no callNo,r.queue_time queueTime,r.doctor_code,r.wait_time waitTime
        ,r.first_call_time firstCallTime,r.first_end_call_time firstEndCallTime,r.last_call_time lastCallTime
        ,r.last_end_call_time lastEndCallTime,r.calls_number callsNumber,r.modify_call_time modifyCallTime

        ,ei.id ei__id,ei.exam_at_pm ei__examAtPm,ei.patient_id EI__patient_id,ei.exam_parts_id EI__exam_parts_id,ei.create_time EI__createTime,ei.exam_uid ei__examUid
        ,ei.appoint_exam_date ei__appointExamDate,ei.appoint_exam_time ei__appointExamTime
        ,p.name ei__p__name,p.age ei__p__age,p.reg_no EI__p__registNo
        ,'gis_operation_complication' ei__operation_complication_dict_type,ei.operation_complications_code ei__operation_complications_dict_code
        ,exi.dict_label EI__ei__dictLabel
        ,sx.dict_label EI__p__gd__dictLabel
        ,res2.dict_value EI__res2__dictValue,res2.dict_label EI__res2__dictLabel
        ,ag.dict_value EI__p__ageu__dictValue,ag.dict_label EI__p__ageu__dictLabel
        ,rts.dict_code EI__rts__dictCode,rts.dict_value EI__rts__dictValue,rts.dict_label EI__rts__dictLabel

        ,rm.id rm__id,rm.room_code rm__roomCode,rm.room_name rm__roomName

        ,doc.user_id doc__userId,r.doctor_code doc__userName,doc.nick_name doc__nickName

        from `d_call_info` `r`
        join d_exam_info ei on ei.id=r.exam_info_id and (ei.status is null or ei.status=0)
        join d_patient p on p.patient_id=ei.patient_id

        left join d_equip_room rm on rm.room_code=r.call_room_code

        left join v_sys_user doc on doc.user_name=r.doctor_code

        left join v_sys_dict_data exi on exi.dict_type='uis_exam_item' and exi.dict_value=ei.exam_item_code
        left join v_sys_dict_data sx on sx.dict_type='uis_gender_type' and sx.dict_value=p.gender_code
        left join v_sys_dict_data res2 on res2.dict_type='uis_exam_result_status' and res2.dict_value=ei.result_status_code
        left join v_sys_dict_data ag on ag.dict_type='uis_age_unit' and ag.dict_value=p.age_unit_code
        left join v_sys_dict_data rts on rts.dict_type='uis_inp_type' and rts.dict_value=ei.inp_type_code
    </sql>

    <sql id="selectWhereKey">
        where 1=1
        <choose><when test="null!=id"> and `r`.`id`=#{id}</when>
        <!-- 检查 -->
        <when test="null!=examInfo and null!=examInfo.id"> and r.exam_info_id=#{examInfo.id}</when></choose>
    </sql>

    <select id="selectOne" resultMap="oneResultMap">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>
    </select>
    
    <select id="selectList" resultMap="listResultMap">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>

        <if test="null!=callNo and ''!=callNo">
          and r.call_no=#{callNo}
        </if>

        <if test="examInfo != null">
            <choose><when test="examInfo.id != null"> AND r.exam_info_id=#{examInfo.id}</when><otherwise>
            <if test="examInfo.examItem != null and examInfo.examItem.dictValue!=null and  examInfo.examItem.dictValue!=''"> AND ei.exam_item_code=#{examInfo.examItem.dictValue}</if>
            <if test="examInfo.examAtPm==0"> AND (ei.exam_at_pm is null or ei.exam_at_pm=0)</if>
            <if test="examInfo.examAtPm==1"> AND ei.exam_at_pm=1</if>
            <!-- <if test='null!=examInfo.resultStatus and examInfo.resultStatus.dictValue=="0"'> AND (ei.result_status_code is null or ei.result_status_code='0' or ei.result_status_code='1')</if> -->
            <if test="null!=examInfo.examNo and ''!=examInfo.examNo"> and ei.exam_no=#{examInfo.examNo}</if>
            <if test="null!=examInfo.inpNo and ''!=examInfo.inpNo"> and ei.inp_no=#{examInfo.inpNo}</if>
            <if test="null!=examInfo.patientInfo">
              <if test="null!=examInfo.patientInfo.name and ''!=examInfo.patientInfo.name"> and p.name like replace(#{examInfo.patientInfo.name}, '*', '%')</if>
              <if test="null!=examInfo.patientInfo.registNo and ''!=examInfo.patientInfo.registNo"> and p.reg_no=#{examInfo.patientInfo.registNo}</if>
            </if>
            </otherwise></choose>
        </if>
        <!-- 检查进度 -->
        <if test='null!=resultStatusCodes'><foreach collection="resultStatusCodes" item="var" open=" and (" close=")" separator=" or ">
        <choose><when test="'0'==var">ei.result_status_code is null or ei.result_status_code='0'</when><otherwise>ei.result_status_code=#{var}</otherwise></choose>
        </foreach></if>
        <!-- 检查项目 -->
        <if test='null!=examItemCodes'><foreach collection="examItemCodes" item="var" open=" and ei.exam_item_code in (" close=")" separator=",">#{var}</foreach></if>
        <!-- 登记时间/预约时间
        <if test="null!=createTimeGe"> and (r.create_time&gt;=#{createTimeGe})</if>
        <if test="null!=createTimeLt"> and (r.create_time&lt;date_add(#{createTimeLt},interval 1 day))</if> -->

        <!-- 未指定排队或检查，默认查询预约检查当日的排队 -->
        <if test="null == id and (null==examInfo or null==examInfo.id)">
        and (
         ei.create_time&gt;=current_date() and ei.appoint_exam_date is null and ei.create_time&lt;date_add(current_date(),interval 1 day)
--         or
--         ei.appoint_time&gt;=current_date() and ei.appoint_time&lt;date_add(current_date(),interval 1 day)
         or
        ei.appoint_exam_date&gt;=current_date() and ei.appoint_exam_date&lt;date_add(current_date(),interval 1 day)
        )
        </if>
        <!-- 是否已呼叫 -->
        <if test="0==callStatus">
            and (call_room_code is null or call_room_code='')
        </if>
        <if test="null!=examModalitiesCodes">
            <trim prefix=" and (" suffix=")">
                <foreach collection="examModalitiesCodes" item="var" open="ei.exam_modality_code in (" close=")" separator=",">
                    #{var}
                </foreach>

                <foreach collection="examModalitiesCodes" item="var" open="or exists (select 1 from v_sys_dict_data modc,v_sys_dict_data modc2 where modc2.dict_value in (" close=") and modc2.dict_code=modc.parent_dict_code and ei.exam_modality_code=modc.dict_value)" separator=",">
                    #{var}
                </foreach>
            </trim>
        </if>

        order by ei.id,r.id
    </select>

    <insert id="insert">
        insert into `d_call_info` (`status`,
        exam_info_id
        ,call_room_code
        ,doctor_code
        ,call_no,queue_time,wait_time
        ,first_call_time,first_end_call_time,last_call_time,last_end_call_time
        ,calls_number,modify_call_time)
        values(0,
        <choose><when test="null!=examInfo">#{examInfo.id}</when><otherwise>null</otherwise></choose>
        ,<choose><when test="null!=callRoom">#{callRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,null
        ,#{callNo},ifnull(#{queueTime},current_timestamp()),ifnull(#{waitTime},0)
        ,#{firstCallTime},#{firstEndCallTime},#{lastCallTime},#{lastEndCallTime}
        ,0,#{modifyCallTime})
    </insert>
	 
    <update id="update">
        update `d_call_info`
        set `call_room_code`=<choose><when test="null!=callRoom">#{callRoom.roomCode}</when><when test="null!=examInfo and null!=examInfo.equipRoom">#{examInfo.equipRoom.roomCode}</when><otherwise>null</otherwise></choose>
        ,`doctor_code`=<choose><when test="null!=doctor">#{doctor.userName}</when><otherwise>null</otherwise></choose>
        <!-- 首次呼叫填入 -->
        ,wait_time=case when first_call_time is null then TIMESTAMPDIFF(MINUTE,queue_time,current_timestamp()) else wait_time end
        <!-- 首次呼叫时间没有值表示首次呼叫 -->
        ,first_call_time=#{firstCallTime}
        <!-- 首次呼叫时间有值，最后呼叫时间没值时填写 -->
        ,first_end_call_time=#{firstEndCallTime}
        <!-- 首次呼叫结束时间有值时填写 -->
        ,last_call_time=#{lastCallTime}
        <!-- 最后呼叫结束时间有值时填写 -->
        ,last_end_call_time=#{lastEndCallTime}
        ,calls_number=ifnull(calls_number,0)+1,modify_call_time=#{modifyCallTime}
        <if test="null!=status">,status=#{status}</if>
        where `id`=#{id}
    </update>
	
    <delete id="delete">
        delete from `d_call_info`
        where `id`=#{id}
    </delete>

    <!-- 当日+预约当日检查 -->
    <sql id="selectBaseByRules">
        from d_call_info r,d_exam_info ei
        where r.exam_info_id=ei.id
        <!--and r.create_time&gt;=#{createTimeGe}-->
        <!-- 预约的检查 -->
        <choose><when test="null != appointDate"> and ei.appoint_time&gt;=date(#{appointDate}) and ei.appoint_time&lt;date_add(date(#{appointDate}), interval 1 day)</when>
        <!-- 登记当日检查加预约在当日检查 -->
        <when test="null != registDate"> and (ei.create_time&gt;=date(#{registDate}) and ei.create_time&lt;date_add(date(#{registDate}), interval 1 day) and ei.appoint_time is null
        or ei.appoint_time&gt;=date(#{registDate}) and ei.appoint_time&lt;date_add(date(#{registDate}), interval 1 day))</when>
        <otherwise>and (ei.create_time&gt;=current_date() and ei.create_time&lt;date_add(current_date(), interval 1 day))</otherwise></choose>
        <!-- 检查项目, [号码] -->
        <choose><when test="null!=examItem and ''!=examItem">
        and ei.exam_item_code=#{examItem}
        </when><when test="null!=examModality and ''!=examModality">
        <!-- 检查类型, [号码] -->
        and ei.exam_modality_code=#{examModality}
        </when><otherwise> and 1=0</otherwise></choose>
    </sql>
    <!-- 当日各检查项目已使用号码 -->
    <select id="selectByRules" resultType="String">
        select r.call_no
        <include refid="selectBaseByRules" />
        <choose><when test="null!=reservedNos and reservedNos.size() > 0">
        <foreach collection="reservedNos" item="p" open=" and r.call_no in (" close=")" separator=",">#{p}</foreach>
        </when><otherwise> and 1=0</otherwise></choose>
    </select>
    <!--  -->
    <select id="selectLastByRules" resultType="String">
        select max(r.call_no) call_no
        <include refid="selectBaseByRules" />
        <choose><when test="null!=reservedNos and reservedNos.size() > 0">
            <foreach collection="reservedNos" item="p" open=" and r.call_no not in (" close=")" separator=",">#{p}</foreach>
        </when><otherwise> and 1=0</otherwise></choose>
    </select>

    <!-- 检查 -->
    <select id="selectByExam" resultMap="oneResultMap">
        <include refid="selectBase"/>
        where r.exam_info_id=#{examId}
        order by r.id desc
        limit 1
    </select>

    <!-- 过号  -->
    <delete id="past">
        update `d_call_info`
        set `status`=#{status},call_room_code=null
        where `id`=#{id}
    </delete>

    <!-- 更改机房 -->
    <update id="changeEquipRoom">
        update d_call_info
        set call_room_code=#{toEquipRoomCode}
        <!--  -->
        where (status is null or status=0 or status=3)
        and (
        <if test="null!=fromEquipRoomCode and ''!=fromEquipRoomCode">equip_room_code=#{fromEquipRoomCode} or </if>id=#{fromId})
    </update>
</mapper>