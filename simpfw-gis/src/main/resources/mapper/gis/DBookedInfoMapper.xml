<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="yyy.xxx.simpfw.module.gis.mapper.DBookedInfoMapper">
    
    <resultMap id="DBookedInfoResult" type="yyy.xxx.simpfw.module.gis.entity.DBookedInfo" autoMapping="true" >
        <result property="id"    column="id"    />
        <result property="examInfo.examUid"    column="exam_uid"    />
        <result property="examNo"    column="exam_no"    />
        <result property="bookedId"    column="booked_id"    />
        <result property="mpi"    column="mpi"    />
        <result property="patientInfo.patientId"    column="patient_id"    />
        <result property="registNo"    column="regist_no"    />
        <result property="medicalRecordNo"    column="medical_record_no"    />

        <result property="name"    column="name"    />

        <result property="regNo"    column="reg_no"    />
        <result property="insuranceNo"    column="insurance_no"    />

        <result property="phone"    column="phone"    />

        <result property="examItemCode"    column="exam_item_code"    />

        <result property="examCost"    column="exam_cost"    />

        <result property="inpNo"    column="inp_no"    />

        <result property="inpRoomCode"    column="inp_room_code"    />
        <result property="bedNo"    column="bed_no"    />
        <result property="outpNo"    column="outp_no"    />
        <result property="admNo"    column="adm_no"    />
        <result property="admSeriesNum"    column="adm_series_num"    />
        <result property="ordId"    column="ord_id"    />
        <result property="ordPriorityCode"    column="ord_priority_Code"    />
        <result property="ordPriority"    column="ord_priority"    />
        <result property="ordName"    column="ord_name"    />
        <result property="ordBillStatus"    column="ord_bill_status"    />
        <result property="ordStatus"    column="ord_status"    />
        <result property="chargeStatus"    column="charge_status"    />
        <result property="admSerialNum"    column="adm_serial_num"    />
        <result property="diagnosisType"    column="diagnosis_type"    />
        <result property="appoint_time"    column="appointTime"    />
        <result property="appoint_exam_date"    column="appointExamDate"    />
        <result property="appoint_exam_time"    column="appointExamTime"    />

    </resultMap>

    <resultMap id="DBookedWeekInfoListVo" type="yyy.xxx.simpfw.module.gis.vo.DBookedWeekInfoListVo" >
        <result column="appointExamDate" property="appointExamDate" jdbcType="VARCHAR" />
        <result column="appointNumber" property="appointNumber" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="DBookedInfoByAppointDateVo" type="yyy.xxx.simpfw.module.gis.vo.DBookedInfoByAppointDateVo" >
        <result column="dictLabel" property="dictLabel" jdbcType="VARCHAR" />
        <result column="dictValue" property="dictValue" jdbcType="VARCHAR" />
        <result column="appointNumber" property="appointNumber" jdbcType="VARCHAR" />
    </resultMap>
<!--    <resultMap id="callInfoMap" type="yyy.xxx.simpfw.module.pacs.entity.CallInfo" autoMapping="true">-->
<!--        <association property="callRoom" javaType="yyy.xxx.simpfw.module.pacs.entity.EquipRoom">-->
<!--            <result property="roomCode" column="rm__roomCode"/>-->
<!--            <result property="roomName" column="rm__roomName"/>-->
<!--        </association>-->
<!--    </resultMap>-->
    <resultMap id="resultMapBase" type="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo" autoMapping="true">

    </resultMap>

    <!-- 检查完整信息 -->
    <resultMap id="resultMapOutline" type="yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo" extends="resultMapBase" autoMapping="true">
        <association property="examInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo"
                     column="{examUid=exam_uid}"
                     select="yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper.selectOne"/>
        <association property="patientInfo" javaType="yyy.xxx.simpfw.module.pacs.vo.PatientVo"
                     resultMap="yyy.xxx.simpfw.module.pacs.mapper.PatientMapper.resultMap" columnPrefix="p__" />
    </resultMap>

    <sql id="selectBase">
        select b.id,b.appoint_exam_date appointExamDate,b.appoint_exam_time appointExamTime, b.exam_uid,
        p.name p__name,p.name_pingyin p__namePingyin,p.age p__age,p.reg_no p__registNo,p.birthday p__birthday
        <!-- 检查单 -->
        from  `d_booked_info` `b`
        left join  d_exam_info h on b.exam_uid = h.exam_uid
        <!-- 患者信息 -->
        join d_patient p on p.patient_id=b.patient_id
        <!-- 排队信息 -->
        left join d_call_info ci on ci.exam_info_id=h.id

    </sql>

    <sql id="selectWhereKey">
        where 1=1
        and h.status != 2
        <if test="null!=id"> and `b`.`id`=#{id}</if>
        <if test="null!=examInfo and ''!=examInfo">
            <if test="null!=examInfo.examNo and ''!=examInfo.examNo" > and `h`.`exam_no`=#{examInfo.examNo}</if>
            <if test="null!=examInfo.examUid and ''!=examInfo.examUid"> and `b`.`exam_uid`=#{examInfo.examUid}</if>
        </if>
<!--        <if test="null!=params and null!=params.dataScope">${params.dataScope}</if>-->
    </sql>
<!--    预约列表-->
    <select id="selectList" parameterType="yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo" resultMap="resultMapOutline">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>

        <!--患者信息-->
        <if test="null!=examInfo and ''!=examInfo">
            <if test="null!=examInfo.appointExamDate"> and `h`.`appoint_exam_date`=#{examInfo.appointExamDate}</if>
            <if test="null!=examInfo.examItem and ''!=examInfo.examItem"> and `h`.`exam_item_code`=#{examInfo.examItem.dictValue}</if>
            <if test="null!=examInfo.inpNo and ''!=examInfo.inpNo"> and `h`.`inp_no`=#{examInfo.inpNo}</if>

            <if test="null!=examInfo.patientInfo and ''!=examInfo.patientInfo ">
                <if test="null!=examInfo.patientInfo.name and ''!=examInfo.patientInfo.name"> and `p`.`name` like replace(#{examInfo.patientInfo.name},'*','%')</if>
                <if test="null!=examInfo.patientInfo.medicalRecordNo and ''!=examInfo.patientInfo.medicalRecordNo"> and `p`.`medical_record_no`=#{examInfo.patientInfo.medicalRecordNo}</if>
                <if test="null!=examInfo.patientInfo.registNo and ''!=examInfo.patientInfo.registNo"> and `p`.`reg_no`=#{examInfo.patientInfo.registNo}</if>
                <if test="null!=examInfo.patientInfo.namePingyin and ''!=examInfo.patientInfo.namePingyin"> and `p`.`name_pingyin`=#{examInfo.patientInfo.namePingyin}</if>
                <if test="null!=examInfo.patientInfo.gender and null!=examInfo.patientInfo.gender.dictValue and ''!=patientInfo.gender.dictValue"> and `p`.`gender_code`=#{examInfo.patientInfo.gender.dictValue}</if>
                <if test="null!=examInfo.patientInfo.birthday"> and `p`.`birthday`=#{examInfo.patientInfo.birthday}</if>
            </if>

            <if test="null!=examInfo.callInfo and ''!=examInfo.callInfo ">
                <if test="null!=examInfo.callInfo.callNo and ''!=examInfo.callInfo.callNo">and `ci`.`call_no`=#{examInfo.callInfo.callNo}</if>
            </if>
        </if>

    </select>

<!--    <sql id="selectDBookedInfoVo">-->
<!--        select id, exam_uid, exam_no, booked_id, mpi, patient_id, regist_no, medical_record_no, name, reg_no, insurance_no, phone, exam_item_code, exam_cost, inp_no, inp_times, inp_ward_code, inp_room_code, bed_no, outp_no, adm_no, adm_series_num, ord_id, ord_priority_Code, ord_priority, ord_name, ord_bill_status, ord_status, charge_status, adm_serial_num, diagnosis_type, appoint_exam_date, appoint_exam_time, status, create_time, update_time from d_booked_info-->
<!--    </sql>-->

<!--    <select id="selectDBookedInfoList" parameterType="yyy.xxx.simpfw.module.gis.entity.DBookedInfo" resultMap="DBookedInfoResult">-->
<!--        <include refid="selectDBookedInfoVo"/>-->
<!--        <where>-->
<!--            <if test="examUid != null  and examUid != ''"> and exam_uid = #{examUid}</if>-->
<!--            <if test="examNo != null  and examNo != ''"> and exam_no = #{examNo}</if>-->
<!--            <if test="bookedId != null  and bookedId != ''"> and booked_id = #{bookedId}</if>-->
<!--            <if test="mpi != null  and mpi != ''"> and mpi = #{mpi}</if>-->
<!--            <if test="patientId != null  and patientId != ''"> and patient_id = #{patientId}</if>-->
<!--            <if test="registNo != null  and registNo != ''"> and regist_no = #{registNo}</if>-->
<!--            <if test="medicalRecordNo != null  and medicalRecordNo != ''"> and medical_record_no = #{medicalRecordNo}</if>-->
<!--            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>-->
<!--            <if test="regNo != null  and regNo != ''"> and reg_no = #{regNo}</if>-->
<!--            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>-->
<!--            <if test="examItemCode != null  and examItemCode != ''"> and exam_item_code = #{examItemCode}</if>-->
<!--            <if test="examCost != null "> and exam_cost = #{examCost}</if>-->
<!--            <if test="inpNo != null  and inpNo != ''"> and inp_no = #{inpNo}</if>-->
<!--            <if test="inpRoomCode != null  and inpRoomCode != ''"> and inp_room_code = #{inpRoomCode}</if>-->
<!--            <if test="bedNo != null  and bedNo != ''"> and bed_no = #{bedNo}</if>-->
<!--            <if test="outpNo != null  and outpNo != ''"> and outp_no = #{outpNo}</if>-->
<!--            <if test="admNo != null  and admNo != ''"> and adm_no = #{admNo}</if>-->
<!--            <if test="admSeriesNum != null  and admSeriesNum != ''"> and adm_series_num = #{admSeriesNum}</if>-->
<!--            <if test="ordId != null  and ordId != ''"> and ord_id = #{ordId}</if>-->
<!--            <if test="ordPriorityCode != null  and ordPriorityCode != ''"> and ord_priority_Code = #{ordPriorityCode}</if>-->
<!--            <if test="ordPriority != null  and ordPriority != ''"> and ord_priority = #{ordPriority}</if>-->
<!--            <if test="ordName != null  and ordName != ''"> and ord_name like concat('%', #{ordName}, '%')</if>-->
<!--            <if test="ordBillStatus != null  and ordBillStatus != ''"> and ord_bill_status = #{ordBillStatus}</if>-->
<!--            <if test="ordStatus != null  and ordStatus != ''"> and ord_status = #{ordStatus}</if>-->
<!--            <if test="chargeStatus != null  and chargeStatus != ''"> and charge_status = #{chargeStatus}</if>-->
<!--            <if test="admSerialNum != null  and admSerialNum != ''"> and adm_serial_num = #{admSerialNum}</if>-->
<!--            <if test="diagnosisType != null  and diagnosisType != ''"> and diagnosis_type = #{diagnosisType}</if>-->
<!--            <if test="appointTime != null "> and appoint_time = #{appointTime}</if>-->
<!--            <if test="appoint_exam_date != null "> and appoint_exam_date = #{appointExamDate}</if>-->
<!--            <if test="appoint_exam_time != null "> and appoint_exam_time = #{appointExamTime}</if>-->
<!--            </where>-->
<!--    </select>-->
<!--    -->

    <select id="selectOne" resultMap="resultMapOutline">
        <include refid="selectBase"/>
        <include refid="selectWhereKey"/>
    </select>
        
    <insert id="insertDBookedInfo" parameterType="yyy.xxx.simpfw.module.gis.entity.DBookedInfo" useGeneratedKeys="true" keyProperty="id">
        insert into d_booked_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="examInfo != null and examInfo != '' and examInfo.examUid != null and examInfo.examUid != ''">exam_uid,</if>
            <if test="examNo != null">exam_no,</if>
            <if test="bookedId != null and bookedId != ''">booked_id,</if>
            <if test="mpi != null">mpi,</if>
            <if test="patientInfo != null and patientInfo != '' and patientInfo.patientId != null and patientInfo.patientId != '' ">patient_id,</if>
            <if test="registNo != null">regist_no,</if>
            <if test="medicalRecordNo != null">medical_record_no,</if>

            <if test="name != null">name,</if>
            <if test="regNo != null">reg_no,</if>
            <if test="insuranceNo != null">insurance_no,</if>

            <if test="phone != null">phone,</if>

            <if test="examItemCode != null">exam_item_code,</if>

            <if test="examCost != null">exam_cost,</if>

            <if test="inpNo != null">inp_no,</if>

            <if test="inpRoomCode != null">inp_room_code,</if>
            <if test="bedNo != null">bed_no,</if>
            <if test="outpNo != null">outp_no,</if>
            <if test="admNo != null">adm_no,</if>
            <if test="admSeriesNum != null">adm_series_num,</if>
            <if test="ordId != null">ord_id,</if>
            <if test="ordPriorityCode != null">ord_priority_Code,</if>
            <if test="ordPriority != null">ord_priority,</if>
            <if test="ordName != null">ord_name,</if>
            <if test="ordBillStatus != null">ord_bill_status,</if>
            <if test="ordStatus != null">ord_status,</if>
            <if test="chargeStatus != null">charge_status,</if>
            <if test="admSerialNum != null">adm_serial_num,</if>
            <if test="diagnosisType != null">diagnosis_type,</if>

            <if test="appointTime != null">appoint_time,</if>
            <if test="appointExamDate != null">appoint_exam_date,</if>
            <if test="appointExamTime != null">appoint_exam_time,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="examInfo != null and examInfo != '' and examInfo.examUid != null and examInfo.examUid != ''">#{examInfo.examUid},</if>
            <if test="examNo != null">#{examNo},</if>
            <if test="bookedId != null and bookedId != ''">#{bookedId},</if>
            <if test="mpi != null">#{mpi},</if>
            <if test="patientInfo != null and patientInfo != '' and patientInfo.patientId != null and patientInfo.patientId != '' ">#{patientInfo.patientId},</if>
            <if test="registNo != null">#{registNo},</if>
            <if test="medicalRecordNo != null"> #{medicalRecordNo},</if>
            <if test="name != null">#{name},</if>
            <if test="regNo != null">#{regNo},</if>
            <if test="insuranceNo != null">#{insuranceNo},</if>
            <if test="phone != null">#{phone},</if>
            <if test="examItemCode != null">#{examItemCode},</if>
            <if test="examCost != null">#{examCost},</if>
            <if test="inpNo != null">#{inpNo},</if>
            <if test="inpRoomCode != null">#{inpRoomCode},</if>
            <if test="bedNo != null">#{bedNo},</if>
            <if test="outpNo != null">#{outpNo},</if>
            <if test="admNo != null">#{admNo},</if>
            <if test="admSeriesNum != null"> #{admSeriesNum},</if>
            <if test="ordId != null">#{ordId},</if>
            <if test="ordPriorityCode != null">#{ordPriorityCode},</if>
            <if test="ordPriority != null">#{ordPriority},</if>
            <if test="ordName != null">#{ordName},</if>
            <if test="ordBillStatus != null">#{ordBillStatus},</if>
            <if test="ordStatus != null">#{ordStatus},</if>
            <if test="chargeStatus != null">#{chargeStatus},</if>
            <if test="admSerialNum != null">= #{admSerialNum},</if>
            <if test="diagnosisType != null"> #{diagnosisType},</if>
            <if test="appointTime != null">#{appointTime},</if>
            <if test="appointExamDate != null">#{appointExamDate},</if>
            <if test="appointExamTime != null">#{appointExamTime},</if>
        </trim>
    </insert>

    <update id="updateDBookedInfo" parameterType="yyy.xxx.simpfw.module.gis.entity.DBookedInfo">
        update d_booked_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="examInfo != null and examInfo != '' and examInfo.examUid != null and examInfo.examUid != ''">exam_uid = #{examInfo.examUid},</if>
            <if test="examNo != null">exam_no = #{examNo},</if>
            <if test="bookedId != null and bookedId != ''">booked_id = #{bookedId},</if>
            <if test="mpi != null">mpi = #{mpi},</if>
            <if test="patientInfo != null and patientInfo != '' and patientInfo.patientId != null and patientInfo.patientId != '' ">patient_id = #{patientInfo.patientId},</if>
            <if test="registNo != null">regist_no = #{registNo},</if>
            <if test="medicalRecordNo != null">medical_record_no = #{medicalRecordNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="regNo != null">reg_no = #{regNo},</if>
            <if test="insuranceNo != null">insurance_no = #{insuranceNo},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="examItemCode != null">exam_item_code = #{examItemCode},</if>
            <if test="examCost != null">exam_cost = #{examCost},</if>
            <if test="inpNo != null">inp_no = #{inpNo},</if>
            <if test="inpRoomCode != null">inp_room_code = #{inpRoomCode},</if>
            <if test="bedNo != null">bed_no = #{bedNo},</if>
            <if test="outpNo != null">outp_no = #{outpNo},</if>
            <if test="admNo != null">adm_no = #{admNo},</if>
            <if test="admSeriesNum != null">adm_series_num = #{admSeriesNum},</if>
            <if test="ordId != null">ord_id = #{ordId},</if>
            <if test="ordPriorityCode != null">ord_priority_Code = #{ordPriorityCode},</if>
            <if test="ordPriority != null">ord_priority = #{ordPriority},</if>
            <if test="ordName != null">ord_name = #{ordName},</if>
            <if test="ordBillStatus != null">ord_bill_status = #{ordBillStatus},</if>
            <if test="ordStatus != null">ord_status = #{ordStatus},</if>
            <if test="chargeStatus != null">charge_status = #{chargeStatus},</if>
            <if test="admSerialNum != null">adm_serial_num = #{admSerialNum},</if>
            <if test="diagnosisType != null">diagnosis_type = #{diagnosisType},</if>
            <if test="appointTime != null">appoint_time = #{appointTime},</if>
            <if test="appointExamDate != null">appoint_exam_date = #{appointExamDate},</if>
            <if test="appointExamTime != null">appoint_exam_time = #{appointExamTime},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDBookedInfoById" parameterType="String">
        delete from d_booked_info where id = #{id}
    </delete>

    <update id="delete" >
        update `d_booked_info` set status=2 where `id`=#{id}
    </update>

<!--    <delete id="deleteDBookedInfoByIds" parameterType="String">-->
<!--        delete from d_booked_info where id in -->
<!--        <foreach item="id" collection="array" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </delete>-->

<!--    日期列表 病人数量-->
    <select id="selectWeekInfoList" resultMap="DBookedWeekInfoListVo">
        select DATE(d.weekday) as appointExamDate ,count(bh.id) as appointNumber from
            (SELECT  NOW() + INTERVAL (a.a + (10 * b.a) + (100 * c.a)) DAY AS weekday
            FROM (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS a
            CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS b
            CROSS JOIN (SELECT 0 AS a UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) AS c)   as d
            LEFT JOIN
            v_sys_dict_data ei on ei.dict_type='uis_exam_item'  and ei.`status` =0
            LEFT JOIN (SELECT h.appoint_exam_date, h.exam_item_code , h.id FROM d_booked_info b left JOIN  d_exam_info h on b.exam_uid = h.exam_uid and h.status != 2 ) as bh
            on  DATE_FORMAT(d.weekday,'%Y-%m-%d') = bh.appoint_exam_date and ei.dict_value=bh.exam_item_code

        where d.weekday BETWEEN NOW()  AND NOW()  + INTERVAL 1 WEEK
        -- and b.status=0
        GROUP BY DATE(d.weekday)
        ORDER BY DATE(d.weekday)
    </select>

<!--    预约时间段已预约人数-->
    <select id="selectBookInfoByAppointDate" resultMap="DBookedInfoByAppointDateVo">
        select
            count(b.id) as appointNumber, ei.dict_label as dictLabel  ,ei.dict_value as dictValue
        from v_sys_dict_data ei
            LEFT JOIN v_sys_dict_data ef on ef.dict_type='uis_exam_item' and ef.`status`=0
            left join  d_exam_info h on h.exam_item_code = ef.dict_value and ei.dict_label = h.appoint_exam_time
            and h.appoint_exam_date =#{examInfo.appointExamDate}
            LEFT JOIN d_booked_info b on b.exam_uid = h.exam_uid
            and (b.status=0 or b.status=3)
        WHERE
            ei.dict_type = 'gis_booked_time'
            and ei.status=0
        GROUP BY
            ei.dict_label,
            ei.dict_value,
            ei.dict_sort
        ORDER BY
            ei.dict_sort

    </select>
    
    <select id="findByExamUid" resultMap="DBookedInfoResult">
        select
            b.id, b.exam_uid, p.name,h.exam_item_code, h.appoint_exam_date, h.appoint_exam_time, h.create_time
        from d_booked_info b
        left join  d_exam_info h on b.exam_uid = h.exam_uid
        <!-- 患者信息 -->
        join d_patient p on p.patient_id=b.patient_id
        where h.status = 0 and b.exam_uid = #{examUid,jdbcType=VARCHAR}
    </select>

</mapper>