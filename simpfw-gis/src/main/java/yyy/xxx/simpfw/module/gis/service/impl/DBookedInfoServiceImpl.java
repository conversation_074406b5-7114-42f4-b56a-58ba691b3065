package yyy.xxx.simpfw.module.gis.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.module.gis.entity.DBookedInfo;
import yyy.xxx.simpfw.module.gis.mapper.CallInfoMapper;
import yyy.xxx.simpfw.module.gis.mapper.DBookedInfoMapper;
import yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper;
import yyy.xxx.simpfw.module.gis.service.EisExamInfoService;
import yyy.xxx.simpfw.module.gis.service.IDBookedInfoService;

import yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedInfoByAppointDateVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedWeekInfoListVo;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.mapper.PatientMapper;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.system.mapper.SysUserMapper;
import yyy.xxx.simpfw.system.service.ISysConfigService;

/**
 * 预约排队Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class DBookedInfoServiceImpl implements IDBookedInfoService
{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private DBookedInfoMapper dBookedInfoMapper;

    @Autowired
    private EisExamInfoMapper eisExamInfoMapper;

    @Autowired
    private PatientMapper patientMapper;

    @Autowired
    private CallInfoMapper callInfoMapper;

    @Autowired
    private ExamReportService reportService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CallInfoService callService;

    @Autowired private DicomStudyService studyService;

    @Autowired private EquipRoomService equipRoomService;
    @Autowired private SysUserMapper sysUserMapper;

    @Autowired private BridgeService bridgeService;

    @Autowired
    private EisExamInfoService eisExamInfoService;

    @Autowired private QueueInterService queueInterService;

    // -----------------
    // 20230425 rh-uis-queue 服务
    @Autowired
    private IRemoteQueueService remoteQueueService;

    /**
     * 激活
     *
     * @param dbBookedInfoVo 预约信息
     * @return 预约排队
     */
    @Transactional
    public AjaxResult updateActiveStatus(DBBookedInfoVo dbBookedInfoVo){
        ExamInfo entity = null;
        if(null!=dbBookedInfoVo.getExamInfo()) entity = eisExamInfoService.selectById(dbBookedInfoVo.getExamInfo().getId());
        if(null == entity) { return AjaxResult.error("检查不存在。"); }

        SysDictData resultStatus = entity.getResultStatus();
        //String resultStatusCode = null != resultStatus? resultStatus.getDictValue() : null;

        CallInfo callInfo = dbBookedInfoVo.getExamInfo().getCallInfo();
        if(null!=callInfo){
            callInfo.setExamInfo(entity);

            //CallInfoVo callInfo0 = callService.selectOne(callInfo);
            CallInfo callInfo0 =  callInfo;
            //无排队号，排队无效，删除
            if(null != callInfo0 && org.apache.commons.lang3.StringUtils.isBlank(callInfo0.getCallNo())) {
                callService.delete(callInfo0.getId());
                callInfo0 = null;
                callInfo.setId(null);
            }
            if(null != callInfo0) {
                callInfo0.setCallRoom(callInfo.getCallRoom());
                callService.insertOrUpdate(callInfo0);
            } else {
                callService.insertOrUpdate(callInfo);

                //entity.setCallInfo(callInfo);
                entity.getCallInfo().setCallNo(callInfo.getCallNo());
                //排队叫号接口 生成排队号时调用
                try {
                    // queueInterService.enqueue(entity.getCallInfo());
                    queueInterService.enqueueV2(entity.getCallInfo(), remoteQueueService.getQueueServiceBaseUrlKey());
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                }

            }
        }

        int res = eisExamInfoService.updateActiveStatus(dbBookedInfoVo.getExamInfo());
        if(1 == res) {
            //interfaceService.cancelFeeApp(entity, usr, null);
            return AjaxResult.success();
        }
        return AjaxResult.error(String.format("无法激活该检查，原因：检查进度为%s", (null != resultStatus? resultStatus.getDictLabel() : "未知")));
    }

    /**
     * 查询预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 预约排队
     */
    @Override
    public DBookedInfo selectOne(DBookedInfo dBookedInfo)
    {
        return dBookedInfoMapper.selectOne(dBookedInfo);
    }

//    /**
//     * 查询预约排队列表
//     *
//     * @param dBookedInfo 预约排队
//     * @return 预约排队
//     */
//    @Override
//    public List<DBookedInfo> selectDBookedInfoList(DBookedInfo dBookedInfo)
//    {
//        return dBookedInfoMapper.selectDBookedInfoList(dBookedInfo);
//    }

    @Override
    public List<DBookedWeekInfoListVo> selectWeekInfoList() {
        return dBookedInfoMapper.selectWeekInfoList();
    }

    @Override
    public List<DBookedInfoByAppointDateVo> selectBookInfoByAppointDate(DBookedInfo dBookedInfo) {
        return dBookedInfoMapper.selectBookInfoByAppointDate(dBookedInfo);
    }

    /**
     * 新增预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    @Override
    public int insertDBookedInfo(DBookedInfo dBookedInfo)
    {
        dBookedInfo.setCreateTime(DateUtils.getNowDate());
        return dBookedInfoMapper.insertDBookedInfo(dBookedInfo);
    }

    /**
     * 修改预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    @Override
    public int updateDBookedInfo(DBookedInfo dBookedInfo)
    {
        dBookedInfo.setUpdateTime(DateUtils.getNowDate());
        return dBookedInfoMapper.updateDBookedInfo(dBookedInfo);
    }

    /**
     * 修改预约检查时间
     *
     * @param list 预约
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAppointExamTime(List<DBookedInfo> list)
    {
        int res = 0;
        for(int i=0;i<list.size();i++){
            res = eisExamInfoMapper.updateAppointExamTime(list.get(i).getExamInfo());
        }
        return res;
    }

    /**
     * 批量删除预约排队
     * 
     * @param id 需要删除的预约排队主键
     * @return 结果
     */
//    @Override
//    public int deleteDBookedInfoByIds(String[] ids)
//    {
//        return dBookedInfoMapper.deleteDBookedInfoByIds(ids);
//    }

    @Override
    public int delete(Long id) {
        return dBookedInfoMapper.delete(id);
    }

    /**
     * 删除预约 预约表、检查表同步删除
     *
     * @param id 需要删除的预约排队主键集合
     * @return 结果
     */
    @Transactional
    public int deleteBooked(Long id){
        DBookedInfo dBookedInfoS = new DBookedInfo();
        dBookedInfoS.setId(id);
        DBookedInfo dBookedInfo = selectOne(dBookedInfoS);
        if(null == dBookedInfo) { return 0;}

        ExamInfo entity = eisExamInfoService.selectByExamUid(dBookedInfo.getExamInfo().getExamUid());
        if(null == entity) { return 0;}

        //删除预约表记录
        int res = dBookedInfoMapper.delete(id);
        if(0==res){
            throw new IllegalArgumentException("预约不存在");
        }

        //删除检查表记录
        res = eisExamInfoService.delete(entity.getId());
        if(0==res){
            throw new IllegalArgumentException("检查不存在");
        }

        return res;
    }

    /**
     * 删除预约排队信息
     * 
     * @param id 预约排队主键
     * @return 结果
     */
    @Override
    public int deleteDBookedInfoById(String id)
    {
        return dBookedInfoMapper.deleteDBookedInfoById(id);
    }

    @Override
    public List<DBBookedInfoVo> selectDBookedAndExamList(DBBookedInfoVo dbBookedInfoVo) {
        return dBookedInfoMapper.selectList(dbBookedInfoVo);
    }

	@Override
	public DBookedInfo findByExamUid(String examUid) {
		return dBookedInfoMapper.findByExamUid(examUid);
	}

    @Transactional
    public int saveOrUpdate(DBBookedInfoVo dbBookedInfoVo) {

        //if(StringUtils.isBlank(entity.getExamNo())) {
        //    entity.setExamNo(makeExamNo());
        //}
        //

        ExamInfo entity = dbBookedInfoVo.getExamInfo();

        int num = 0;
        Patient patient = entity.getPatientInfo();
        if(null != patient) {
            //登记方式
            patient.setRegistWay(entity.getRegistWay());
            //登记号去首位空白符号
            if(StringUtils.isNotBlank(patient.getRegistNo())) {
                patient.setRegistNo(patient.getRegistNo().trim());
            }
            //拼音
            /*if(StringUtils.isBlank(patient.getNamePingyin())) {
                try {

                } catch (Exception err) { log.error(err.getMessage()); }
            }*/
            //num += null == patient.getId()? patientMapper.insert(entity.getPatientInfo()) : patientMapper.update(entity.getPatientInfo());
            if(patient.isNew()) {
                Patient patientExist = patientMapper.selectOne(patient);
                if(null != patientExist) {
                    //更新已有
                    patient.setId(patientExist.getId());
                    patient.setPatientId(patientExist.getPatientId());
                    //使用已有病历号
                    patient.setMedicalRecordNo(patientExist.getMedicalRecordNo());
                    patientMapper.update(patient);
                } else {
                    //增加
                    patient.setPatientId(StringUtils.randomChars(16));//Long.MAX_VALUE - NumberUtil.randomInt(9999)
                    patientMapper.insert(patient);
                    //
                    patientMapper.currectPatientId(patient.getId());
                    patient.setPatientId(patient.getId().toString());
                }
            } else {
                patientMapper.update(patient);
            }

            if(null != dbBookedInfoVo.getPatientInfo()){
                dbBookedInfoVo.getPatientInfo().setPatientId(patient.getPatientId());
            }
        }

        //排队
        CallInfo callInfo = entity.getCallInfo();
        //房间信息->设备信息
        EquipRoom equipRoom = null != callInfo? callInfo.getCallRoom() : null;
        //房间号表示最新检查房间
        if(null != equipRoom && StringUtils.isNotBlank(equipRoom.getRoomCode())) {
            equipRoom.setId(null);
            equipRoom = equipRoomService.selectOne(equipRoom);
            //
            if(null != equipRoom && null != equipRoom.getDevice()) {
                entity.setExamDevice(equipRoom.getDevice());
            }
        } else {
            entity.setExamDevice(null);
        }
        //新增/更新检查
        boolean isNew = entity.isNew();
        if(isNew) {
            entity.setExamUid(StringUtils.uid());
        }
        num += isNew? eisExamInfoMapper.insert(entity) : eisExamInfoMapper.update(entity);

        if(null==callInfo||callInfo.isNew()) {
            //预约登记保存房间信息，不生成排队号
            if(null == callInfo) { callInfo = new CallInfoVo(); }
            //查询排队信息
            callInfo.setExamInfo(entity);
            callInfo.setCallNo(null);
            callInfoMapper.insert(callInfo);
        }
        else{
            callInfo.setExamInfo(entity);
            callInfoMapper.update(callInfo);
        }
//        else{
//            //更新/新增排队
//            if(null == callInfo) { callInfo = new CallInfoVo(); }
//            //查询排队信息
//            callInfo.setExamInfo(entity);
//            /*List<CallInfoVo> calls = callService.selectList(callInfo);
//            //如果没有排队号，重新排队
//            if(null == calls || calls.isEmpty() || StringUtils.isBlank(calls.get(0).getCallNo())) {
//                if(null != calls && !calls.isEmpty()) {
//                    callService.delete(calls.get(0).getId());
//                }
//                callService.insertOrUpdate(callInfo);
//            }*/
//            //
//            //CallInfoVo callInfo0 = null != callInfo.getId()? callService.selectOne(callInfo) : callService.selectByExam(entity.getId());
//            CallInfoVo callInfo0 = callService.selectOne(callInfo);
//            //无排队号，排队无效，删除
//            if(null != callInfo0 && StringUtils.isBlank(callInfo0.getCallNo())) {
//                callService.delete(callInfo0.getId());
//                callInfo0 = null;
//            }
//            if(null != callInfo0) {
//                callInfo0.setCallRoom(callInfo.getCallRoom());
//                callService.insertOrUpdate(callInfo0);
//            } else {
//                callService.insertOrUpdate(callInfo);
//            }
//            //循环嵌套
//            callInfo.setExamInfo(mapper.selectOne(entity));
//        }

        //dicomstudy
        DicomStudy study = new DicomStudy();
        study.setExamInfo(entity);
        if(null == studyService.selectOne(study)) {
            studyService.saveOrUpdate(study);
            entity.setDicomStudy(study);
        }

        if(dbBookedInfoVo.isNew()){
            insertDBookedInfo(dbBookedInfoVo);
        }else{
            updateDBookedInfo(dbBookedInfoVo);
        }
        return 1;
    }
	
}
