package yyy.xxx.simpfw.module.gis.service;

import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.module.gis.entity.DBookedInfo;

import yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedInfoByAppointDateVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedWeekInfoListVo;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;

import java.util.List;


/**
 * 预约排队Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
public interface IDBookedInfoService 
{
    /**
     * 激活
     *
     * @param dbBookedInfoVo 预约信息
     * @return 预约排队
     */
    public AjaxResult updateActiveStatus(DBBookedInfoVo dbBookedInfoVo);

    /**
     * 查询预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 预约排队
     */
    public DBookedInfo selectOne(DBookedInfo dBookedInfo);


    /**
     * 查询预约排队列表
     * 
     * @param dBookedInfo 预约排队
     * @return 预约排队集合
     */
//    public List<DBookedInfo> selectDBookedInfoList(DBookedInfo dBookedInfo);

    /**
     * 查询预约排队列表
     *
     *
     * @return 预约排队集合
     */
    public List<DBookedWeekInfoListVo> selectWeekInfoList( );

    /**
     * 查询预约排队列表
     *
     *
     * @return 预约排队集合
     */
    public List<DBookedInfoByAppointDateVo> selectBookInfoByAppointDate(DBookedInfo dBookedInfo );


    /**
     * 新增预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    public int insertDBookedInfo(DBookedInfo dBookedInfo);

    /**
     * 修改预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    public int updateDBookedInfo(DBookedInfo dBookedInfo);

    /**
     * 修改预约检查时间
     *
     * @param list 预约
     * @return 结果
     */
    public int updateAppointExamTime(List<DBookedInfo> list);

    /**
     * 批量删除预约排队
     * 
     * @param ids 需要删除的预约排队主键集合
     * @return 结果
     */
//    public int deleteDBookedInfoByIds(String[] ids);

    /**
     * 批量删除预约排队
     *
     * @param id 需要删除的预约排队主键集合
     * @return 结果
     */
    public int delete(Long id);

    /**
     * 删除预约 预约表、检查表同步删除
     *
     * @param id 需要删除的预约排队主键集合
     * @return 结果
     */
    public int deleteBooked(Long id);


    /**
     * 删除预约排队信息
     * 
     * @param id 预约排队主键
     * @return 结果
     */
    public int deleteDBookedInfoById(String id);

    List<DBBookedInfoVo> selectDBookedAndExamList(DBBookedInfoVo dbBookedInfoVo);
    
    /**
     * add@20230426
     * @param examUid
     * @return
     */
    DBookedInfo findByExamUid(String examUid);

    int saveOrUpdate(DBBookedInfoVo dbBookedInfoVo);
}
