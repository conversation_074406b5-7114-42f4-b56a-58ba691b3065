package yyy.xxx.simpfw.module.gis.vo;

import yyy.xxx.simpfw.module.pacs.entity.DicomImage;
import yyy.xxx.simpfw.module.pacs.entity.ExamAttachment;

public class DicomImageIllus {
	private ExamAttachment reportImage;
	private DicomImage dicomImage;

	private Boolean isNew = Boolean.FALSE;

	public ExamAttachment getReportImage() {
		return reportImage;
	}
	public void setReportImage(ExamAttachment reportImage) {
		this.reportImage = reportImage;
	}

	public DicomImage getDicomImage() {
		return dicomImage;
	}
	public void setDicomImage(DicomImage dicomImage) {
		this.dicomImage = dicomImage;
	}

	public Boolean getNew() {
		return isNew;
	}
	public void setNew(Boolean aNew) {
		isNew = aNew;
	}

	public DicomImageIllus(Boolean isNew, ExamAttachment atta, DicomImage img) {
		this.isNew = isNew;
		this.reportImage = atta;
		this.dicomImage = img;
	}
}
