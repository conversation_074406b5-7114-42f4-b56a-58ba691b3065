package yyy.xxx.simpfw.module.gis.service.impl;

import com.alibaba.fastjson.JSON;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.listener.IPdfTextLocation;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.pdfcleanup.PdfCleaner;
import com.itextpdf.pdfcleanup.autosweep.CompositeCleanupStrategy;
import com.itextpdf.pdfcleanup.autosweep.RegexBasedCleanupStrategy;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yyy.xxx.common.net.storage.AbsFile;
import yyy.xxx.common.net.storage.StorageInterface;
import yyy.xxx.common.net.storage.StorageInterfaceFactory;
import yyy.xxx.common.net.storage.utils.FileUtils;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.HttpRequestConfig;
import yyy.xxx.simpfw.common.core.domain.SimpleMap;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.JsonUtil;
import yyy.xxx.simpfw.common.utils.http.WebUtil;
import yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper;
import yyy.xxx.simpfw.module.gis.utils.BodypartsIllusUtil;
import yyy.xxx.simpfw.module.gis.vo.BodypartsIllus;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.dict.ExamEnum;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.utils.PdfUtil;
import yyy.xxx.simpfw.module.pacs.bo.StorageParam;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.mapper.ExamAttachmentMapper;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.DicomImageIndexServiceImpl;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.system.service.ISysConfigService;

import java.io.*;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class ExamReportServiceImpl implements ExamReportService {
    private static final Logger log = LoggerFactory.getLogger(ExamReportServiceImpl.class);

    @Autowired private EisExamInfoMapper mapper;

    @Autowired private ExamAttachmentMapper attachMapper;
    
    @Autowired private DicomStudyService dicomStudyService;

    @Autowired private CallInfoService callInfoService;

    @Autowired private BridgeService bridgeService;

    @Autowired private DicomImageService dicomImageService;

    @Autowired private HttpClientService httpClient;

    private final HttpRequestConfig httpRequestConfig = new HttpRequestConfig().setSocketTimeout(10 * 1000);

    private static final float PdfImageAltCellWidth = 40;

    /**
     * 保存报告相关的字段
     */
    @Transactional
    public int save(ExamInfo entity) {
        //检查时间
        if(null == entity.getExamTime()) {
            entity.setExamTime(DateUtils.getNowDate());
        }
        //医生
        //
        int num = mapper.saveReport(entity);
        //更新检查机房
        CallInfo callInfo = callInfoService.selectByExam(entity.getId());
        if(null == callInfo.getCallRoom() || StringUtils.isBlank(callInfo.getCallRoom().getRoomCode())) {
            callInfo.setExamInfo(entity);
            callInfoService.call(callInfo, entity.getEquipRoom().getRoomCode());
        }
        //保存图像
        saveImages(entity);
        //
//        DicomStudy study = entity.getDicomStudy();
//        if(null != study && org.apache.commons.lang3.StringUtils.isNotBlank(study.getStudyInstanceUid())) {
//            dicomStudySerice.saveOrUpdate(entity.getDicomStudy());
//        }
        
        return num;
    }
    /**
     * 保存报告的图片
     * @param entity 报告内容
     */
    @Transactional
    private void saveImages(ExamInfo entity) {

        List<ExamAttachment> exists = selectImages(entity);
        //
        List<ExamAttachment> images = entity.getImages();
        //删除
        for(ExamAttachment e : exists) {
            if(null != e.getId() && images.stream().noneMatch(i -> null != i.getId() && e.getId().longValue() == i.getId().longValue())) {
                attachMapper.delete(e.getId());
            }
        }
        //添加
        for(ExamAttachment i : images) {
            if(null == i.getId()) {
                i.setExamInfoId(entity.getId());
                i.setType(attTypeImage);
                attachMapper.insert(i);
            }
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<ExamAttachment> selectImages(ExamInfo ei) {
        ExamAttachment att = new ExamAttachment();
        att.setExamInfoId(ei.getId());
        att.setType(attTypeImage);
        att.setStatus(ExamAttachment.STATUS_NORMAL);
        List<ExamAttachment> items = attachMapper.selectList(att);
        //检查
        if(null != items) {
            for(int i = items.size() - 1; i >= 0; i --) {
                ExamAttachment item = items.get(i);
                String path = item.getPath();
                if(StringUtils.isBlank(path)) {
                    attachMapper.delete(item.getId());
                }

                if(Const.FILE_TYPE_NAME_DCM.equals(item.getFileType())) {
                    continue;
                }

                try {
                    String sop = path.split(Const.slash)[2];
                    if (null == dicomImageService.selectSop(sop)) {
                        log.warn("无效报告影像 exam.id={}, examNo={}, atta.id={}, atta.path={}"
                            , ei.getId(), ei.getExamNo(), item.getId(), path);
                        attachMapper.delete(item.getId());
                        items.remove(i);
                    }
                } catch (Exception err) { items.remove(i); }
            }
        }

        return items;
    }

    @Transactional
    public int audit(ExamInfo entity) {
        //先保存
        //mapper.saveReport(entity);
        save(entity);
        //再审核
        return mapper.auditReport(entity);
    }

    public int reaudit(ExamInfo param) {
        return mapper.reauditReport(param);
    }

    @Transactional
    public int sign(ExamInfo entity, String signImg) throws Exception {
        //将签字图片作为附件
        attachMapper.deleteByExam(entity.getId(), attTypeSign);

        ExamAttachment att = new ExamAttachment();
        att.setExamInfoId(entity.getId());
        att.setType(attTypeSign);
        att.setFileType("png");
        att.setData(signImg.getBytes(StandardCharsets.UTF_8));
        attachMapper.insert(att);
        //更新签字相关信息
        return mapper.signReport(entity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int saveDoc(ExamInfo report) {
        //获取报告存储
        String cfgVal;
        //检查项目是否有指定存储位置
        SysDictData examItem = bridgeService.getExamItem(report.getExamItem().getDictValue());
        if(null == examItem.getExtend() || StringUtils.isBlank(cfgVal = examItem.getExtend().getExtendS4())) {
            cfgVal = this.getSysConfig(reportStorageConfigKey);
        }
        if(StringUtils.isBlank(cfgVal)) { throw new IllegalArgumentException(String.format("未设置报告存放位置%s", reportStorageConfigKey)); }
        StorageParam storageParam = JSON.parseObject(cfgVal, StorageParam.TypeReference);
        if(null == storageParam || StringUtils.isBlank(storageParam.getLoc())) {
            throw new IllegalArgumentException(String.format("无法识别报告存放配置%s", cfgVal));
        }
        if(log.isDebugEnabled()) { log.debug("获取存储{}....", storageParam); }
        StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(storageParam.getLoc(), Const.STORAGE_SESSION_OPTION);
        if(log.isDebugEnabled()) { log.debug("取得存储."); }
        try {
            //年/月/日/检查号/
            String workPath = storage.getWorkPath();
            String folder = String.format("%1$tY/%1$tm/%1$td/%2$s/", System.currentTimeMillis(), report.getExamNo());
            folder = FileUtils.join(workPath, folder);
            String storageUserinfo = null;
            //pdf报告
            String pdfData = report.getReportUrlPdf();
            File pdfFile = null;
            byte[] pdfContent = null;
            //
            if(StringUtils.isBlank(pdfData) || !yyy.xxx.simpfw.common.utils.StringUtils.validateBase64(pdfData)) {
                //生成pdf
                try {
                    pdfFile = createReportPdf(report);
                } catch (Exception err) {
                    log.error("生成PDF报告错误：检查号={}", report.getExamNo());
                    throw new RuntimeException(err);
                }
            } else {
                //pdf 内容
                pdfContent = yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(pdfData);
            }

            try {
                //保存路径+文件名
                String filePath = String.format("%s%d.pdf", folder, report.getId());
                if(storage.fileExists(filePath)) {
                    storage.delete(filePath);
                }
                AbsFile file;
                if(null != pdfFile) {
                    file = storage.write(filePath, Files.newInputStream(pdfFile.toPath()));
                    if(!pdfFile.delete()) {
                        log.info("报告临时文件删除失败{}", pdfFile.getAbsolutePath());
                    }
                } else {
                    file = storage.write(filePath, pdfContent);
                }
                URI uri = file.toURI(true);
                report.setReportUrlPdf(URLDecoder.decode(uri.toString(), Const.charset_UTF_8));
                storageUserinfo = uri.getUserInfo();
            } catch (Exception err) {
                log.error(err.getMessage(), err);
                throw new RuntimeException(err);
            }

            //jpg报告
            String jpgData = report.getReportUrlJpg();
            if (StringUtils.isNotBlank(jpgData) && yyy.xxx.simpfw.common.utils.StringUtils.validateBase64(jpgData)) {
                byte[] bytes = yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(jpgData);
                try {
                    String filePath = String.format("%s%d.jpg", folder, report.getId());
                    if(storage.fileExists(filePath)) {
                        storage.delete(filePath);
                    }
                    AbsFile file = storage.write(filePath, bytes);
                    URI uri = file.toURI(true);
                    report.setReportUrlJpg(URLDecoder.decode(uri.toString(), Const.charset_UTF_8));
                    storageUserinfo = uri.getUserInfo();
                } catch (Exception err) {
                    log.error(err.getMessage(), err);
                    throw new RuntimeException(err);
                }
            }
            if (StringUtils.isNotBlank(storageUserinfo)) {
                String[] up = storageUserinfo.split(Const.SYMBOL_COLON);
                report.setReportUrlUsername(up[0]);
                if (up.length > 1) {
                    report.setReportUrlPassword(up[1]);
                }
            }
        } finally {
            storage.release();
        }

        return mapper.saveDoc(report);
    }

    @DataSource(value = DataSourceType.MASTER)
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    private String getSysConfig(String key) {
        return bridgeService.getSysConfigByKey(key);
    }

    /**
     * 生成报告文件
     * @param report
     * @return
     * @throws Exception
     */
    private File createReportPdf(ExamInfo report) throws Exception {
        //报告输出临时路径
        String destFileName = String.format("%d-%s-%d.pdf", report.getId(), report.getExamNo(), System.currentTimeMillis());
        Path destFilePath = FileUtils.getDir(String.format("temp/report/%s", DateUtils.getDate())).resolve(destFileName);
        //File destFile = new File(FileUtils.getDir(String.format("temp/report/%s", DateUtils.getDate())).toAbsolutePath().toFile(), destFileName);
        if(log.isInfoEnabled()) { log.info("检查号={}, 检查报告={}", report.getExamNo(), destFilePath); }
        //读取模板，输出报告
        createDoc(Files.newOutputStream(destFilePath), report, false);
        //程序退出时删除本地报告文件
        File destFile = destFilePath.toFile();
        destFile.deleteOnExit();

        return destFile;
    }

    /**
     * 读取报告图像
     * @param report 检查报告
     */
    private void readReportImages(ExamInfo report) {
        ExamAttachment attaParam = new ExamAttachment();
        attaParam.setExamInfoId(report.getId());
        attaParam.setType("report::image");
        List<ExamAttachment> list = attachMapper.selectList(attaParam);
        report.setImages(attachMapper.selectList(attaParam));
    }

    /**
     * 读取报告图像
     * @param atta
     * @return
     * @throws Exception
     */
    private byte[] readReportImageData(ExamAttachment atta) throws Exception {
        String imagePath = atta.getPath();
        if(null == imagePath) { return null; }
        //dicom
        // /ext/dicom-web/rs/studies/DCM..1.2.840.113663.1500.1.458618363.1.1.20221020.155919.49/series/1.2.840.113663.1500.1.458618363.2.1.20221020.155919.50/instances/1.2.840.113663.1500.1.458618363.3.5.20221020.160054.315/frames/1
        // /dic2png?SOPInstanceUID=1.2.840.113663.1500.1.458618363.3.5.20221020.160054.315
        if(Const.FILE_TYPE_NAME_DCM.equals((atta.getFileType()))) {
            String sopInstanceUid = imagePath.replaceAll("^.+/([^/]+)/frames/\\d+$", "$1");
            if(StringUtils.isBlank(sopInstanceUid) || sopInstanceUid.equals(imagePath)) { return null; }
            //调用dicom服务获取图像
            String imageIndexServer = getSysConfig(DicomImageIndexServiceImpl.CFG_KEY_INDEXSERVER);
            if(StringUtils.isNotBlank(imageIndexServer)) {
                try {
                    //获取jpg/png格式数据
                    String url = String.format("%s/dic2png?SOPInstanceUID=%s", imageIndexServer, sopInstanceUid);
                    if(log.isDebugEnabled()) { log.debug("读取报告图像 spath={}, tpath={}", imagePath, url); }
                    try (ByteArrayOutputStream stream = new ByteArrayOutputStream(8192);) {
                        Map<String, Object> headers = new SimpleMap().set("Content-Type", WebUtil.MINETYPE_STREAM);
                        httpClient.doGetForStream(url, null, headers, httpRequestConfig, stream);
                        return stream.toByteArray();
                    }
                } catch (Exception err) {
                    log.error(String.format("生成报告图像错误path=%s, ", imagePath), err);
                }
            }
        }
        //jpg路径为三段d_dicom_image表StudyInstanceUID/SeriesInstanceUID/SOPInstanceUID
        String[] segm;
        if((segm = imagePath.split(Const.slash)).length == 3) {
            DicomImage image = dicomImageService.selectSop(segm[2]);
            try(ByteArrayOutputStream stream = new ByteArrayOutputStream(8192);) {
                dicomImageService.retrieveImage(stream, image);
                return stream.toByteArray();
            }
        }
        //
        return null;
    }

    public void readSignImage(ExamInfo report) throws UnsupportedEncodingException {
        ExamAttachment attaParam = new ExamAttachment();
        attaParam.setExamInfoId(report.getId());
        attaParam.setType("report::sign");
        List<ExamAttachment> list = attachMapper.selectList(attaParam);
        if(null != list && !list.isEmpty()) {
            report.setSignImage(new String(list.get(0).getData(), StandardCharsets.UTF_8));
        }
    }

    //获取检查图像，检查所见，检查诊断table
    public Table getTable(ExamInfo report,int mainPos,float pageWidth ,float pageLpad,float pageRpad) throws Exception {

        //检查图像，检查所见，检查诊断
        Table table = new Table(UnitValue.createPercentArray(1)).useAllAvailableWidth();
        //table.setBackgroundColor(PdfUtil.dbColor);
        table.setMarginTop(mainPos);//表格顶部位置
        table.setMarginBottom(36);//超出部分不覆盖页脚
        table.setBorder(Border.NO_BORDER);
        //图像
        List<ExamAttachment> images = report.getImages();
        if(null == images) {
            readReportImages(report);
            images = report.getImages();
        }
        //图像数，图像行数
        int numImages = images.size(), numImagesPerLine = numImages <= 3 ? numImages : (4 == numImages ? 2 : 3);
        float imageStartX = 60, imageHoriSpace = 16, imageVertSpace = 10;
        //图像尺寸
        //float fixedImageWidth = Math.min(PdfUtil.imageWidthMax, (pageWidth - (2 * imageStartX) - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
        float fixedImageWidth = 170;//Math.min(PdfUtil.imageWidthMax, (pageWidth - numImagesPerLine * PdfImageAltCellWidth - (numImagesPerLine - 1) * imageHoriSpace) / numImagesPerLine);
        float fixedImageHeight = numImagesPerLine > 1? PdfUtil.imageHeightMax : -1;
        //
        Cell cell = new Cell();/* = new Cell();
                cell.setBorder(Border.NO_BORDER);
                if (numImagesPerLine == 1) {

                    createPdfImage(cell, images.get(0), fixedImageWidth, fixedImageHeight);
                } else if (numImagesPerLine > 1) {
                    Table nestTable = new Table(UnitValue.createPercentArray(numImagesPerLine)).useAllAvailableWidth();
                    for (int i = 0; i < numImages; i++) {
                        Cell c = new Cell();
                        c.setBorder(Border.NO_BORDER);
                        / *c.setVerticalAlignment(VerticalAlignment.MIDDLE);
                        c.setHorizontalAlignment(HorizontalAlignment.CENTER);

                        ExamAttachment img = images.get(i);
                        Image image = PdfUtil.createPdfImage(readReportImageData(img), fixedImageWidth, fixedImageHeight);
                        image.setHorizontalAlignment(HorizontalAlignment.CENTER);
                        c.add(image);* /
                        try {
                            createPdfImage(c, images.get(i), fixedImageWidth, fixedImageHeight);
                        } catch (Exception err) {
                            log.error(err.getMessage(), err);
                        }

                        nestTable.addCell(c);
                    }
                    nestTable.setBorder(Border.NO_BORDER);
                    cell.add(nestTable);
                }
                table.addCell(cell);*/
        ///if (numImagesPerLine == 1) {
        ///    createPdfImage(table, images.get(0), fixedImageWidth, fixedImageHeight);
        //} else if (numImagesPerLine > 1) {
        if (numImagesPerLine >= 1) {
            //Table nestTable = new Table(UnitValue.createPercentArray(numImagesPerLine));//.useAllAvailableWidth();
            Table nestTable = new Table(2 * numImagesPerLine + (numImagesPerLine - 1));//.useAllAvailableWidth();
            //nestTable.setBackgroundColor(PdfUtil.dbColor);
            nestTable.setBorder(Border.NO_BORDER);
            nestTable.setMarginTop(4);
            for (int i = 0; i < numImages; i++) {
                createPdfImage(nestTable, images.get(i), fixedImageWidth, fixedImageHeight);
                if((i + 1) % numImagesPerLine != 0) {
                    Cell spc = new Cell();
                    spc.setWidth(imageHoriSpace);
                    spc.setBorder(Border.NO_BORDER);
                    spc.setPadding(0);
                    nestTable.addCell(spc);
                }
            }

            cell = new Cell();
            cell.setBorder(Border.NO_BORDER);
            cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
            cell.setPadding(0);
            //居中。。
            if(numImagesPerLine < 3) {
                float marLeft = pageWidth - pageLpad - pageRpad
                        - numImagesPerLine * fixedImageWidth
                        - numImagesPerLine * PdfImageAltCellWidth
                        - (numImagesPerLine - 1) * imageHoriSpace;
                marLeft /= 2;
                nestTable.setMarginLeft(marLeft);
                //cell.setPaddingLeft(marLeft);
            }
            cell.add(nestTable);
            //cell.setBackgroundColor(DeviceRgb.BLUE);
            //cell.setTextAlignment(TextAlignment.CENTER);
            table.addCell(cell);
        }
        if(log.isDebugEnabled()) { log.debug("输出pdf 图像ok."); }                //
        float bodyLineHeight = 1.1f;
        //
//        cell = new Cell();
        cell.setBorder(Border.NO_BORDER);
        //检查所见
        Paragraph para = new Paragraph("检查所见：").setFontSize(PdfUtil.bodyHeadFontSize);
        para.setMarginTop(2f);
        para.setMarginBottom(2f);
        cell.add(para);
        //检查所见内容
        String examDesc = report.getExamDesc();
        para = new Paragraph(null != examDesc? examDesc : Const.SYMBOL_NL).setFontSize(PdfUtil.bodyFontSize);
        para.setMarginLeft(PdfUtil.bodyFontSize);
        para.setMultipliedLeading(bodyLineHeight);
        //检查部位示意图
        Image illusImage = null;
        List<ExamAttachment> attaIllus = images.stream().filter(i -> StringUtils.isNotBlank(i.getNoteInfo())).collect(Collectors.toList());
        Optional<ExamAttachment> attr1 = attaIllus.stream().filter(i -> i.getNoteInfo().contains("\"illus\":")).findFirst();
        if(attr1.isPresent()) {
            try {
                //{"parts":"腹腔","lesion":"正常","mark":{"illus":4,"x":63,"y":47,"w":130,"h":135}}
                BodypartsIllus illus = JsonUtil.toObject(attr1.get().getNoteInfo(), BodypartsIllus.class);
                if(null != illus && null != illus.getMark()) {
                    String illusName = illus.getMark().getIllus();
                    String illusFold = null;
                    int pos = illusName.indexOf(Const.slash);
                    if(-1 != pos) {
                        illusFold = illusName.substring(0, pos);
                        illusName = illusName.substring(pos + 1);
                    }
                    Path bodypartsIllus = BodypartsIllusUtil.illusPath(illusFold, illusName);
                    if(Files.isRegularFile(bodypartsIllus)) {
                        final int illusWidth = 140, illusHeight = -1;
                        byte[] imageData= BodypartsIllusUtil.reillus(bodypartsIllus, attaIllus, illusWidth, illusHeight);
                        if(null == imageData) {
                            imageData = FileUtils.readFile(bodypartsIllus);
                        }
                        illusImage = PdfUtil.createPdfImage(imageData, illusWidth, illusHeight);
                        //image.setFixedPosition(0, 0);
                    }
                }
            } catch (Exception err) {
                log.error(err.getMessage());
            }
        }
        //是否有部位示意图
        if(null == illusImage) {
            cell.add(para);
        } else {
            //
            Table descTable = new Table(2).useAllAvailableWidth();
            //
            Cell descCell = new Cell();
            descCell.setBorder(Border.NO_BORDER);
            descCell.setVerticalAlignment(VerticalAlignment.TOP);
            descCell.setHorizontalAlignment(HorizontalAlignment.LEFT);
            //para.setMarginRight(200);
            descCell.add(para);
            descCell.setWidth(1000);
            descTable.addCell(descCell);

            descCell = new Cell();
            descCell.setBorder(Border.NO_BORDER);
            descCell.add(illusImage);
            descTable.addCell(descCell);

            descTable.setBorder(Border.NO_BORDER);
            cell.add(descTable);
        }

        //检查诊断
        para = new Paragraph("检查诊断：").setFontSize(PdfUtil.bodyHeadFontSize);
        para.setMarginTop(2f);
        para.setMarginBottom(2f);
        cell.add(para);
        String examDiag = report.getExamDiagnosis();
        para = new Paragraph(null != examDiag? examDiag : Const.SYMBOL_NL).setFontSize(PdfUtil.bodyFontSize);
        para.setMarginLeft(PdfUtil.bodyFontSize);
        para.setMultipliedLeading(bodyLineHeight);
        cell.add(para);
        //
        table.addCell(cell);
        if(log.isDebugEnabled()) { log.debug("输出pdf 诊断ok."); }

        //术后医嘱/建议
        para = new Paragraph("建议：").setFontSize(PdfUtil.bodyHeadFontSize);
        para.setMarginTop(2f);
        para.setMarginBottom(2f);
        cell.add(para);
        String operSugg = report.getOperationSuggestion();
        para = new Paragraph(null != operSugg? operSugg : Const.SYMBOL_NL).setFontSize(PdfUtil.bodyFontSize);
        para.setMarginLeft(PdfUtil.bodyFontSize);
        para.setMultipliedLeading(bodyLineHeight);
        cell.add(para);
        //
        table.addCell(cell);
        return table;
    }

    //获取document占用页数
    public int getPageNum(Document document,ExamInfo report,int mainPos,float pageWidth ,float pageLpad,float pageRpad)  throws Exception {
        document.add(getTable(report,mainPos,pageWidth,pageLpad,pageRpad) );
        return document.getPdfDocument().getNumberOfPages();
    }

    //获取检查部位para
    public Paragraph getParagraph(Rectangle area,String rpl,int pPos,int firstLineIndent,int lineHeight,float pageLpad) throws Exception  {
        Paragraph para = new Paragraph(rpl).setFontSize(PdfUtil.metaFontSize)
                .setMargin(0f).setPadding(0f);
        para.setFont(PdfUtil.createFont())
                .setFixedLeading(lineHeight)
                .setFixedPosition(pageLpad, area.getBottom() - pPos, area.getWidth())
                .setFirstLineIndent(firstLineIndent);
        return para;
    }
    /**
     * 输出pdf
     * @param outStream 输出
     * @param report 报告内容
     * @param isInstant 完全使用报告图像、签名
     * @throws Exception
     */
    public void createDoc(OutputStream outStream, ExamInfo report, Boolean isInstant) throws Exception {
        //报告模板：report-MOD={设备类型}-INP={就诊类型}.pdf > report-MOD={设备类型}.pdf > report-INP={就诊类型}.pdf > report.pdf
        //适配报告模板：设备类型+就诊类型+检查项目>设备类型+检查项目>设备类型+就诊类型>设备类型>上级设备类型>就诊类型>通用
        SysDictData examModality = report.getExamModality(), inpType = report.getInpType(), examItem = report.getExamItem();
        String modalityCode = examModality.getDictValue(), examItemCode = examItem.getDictValue();
        //
        List<String> tplFilesName = new ArrayList<>();
        tplFilesName.add(String.format("report-MOD=%s-INP=%s-ITM=%s.pdf", modalityCode, inpType.getDictValue(), examItemCode));
        tplFilesName.add(String.format("report-MOD=%s-ITM=%s.pdf", modalityCode, examItemCode));
        tplFilesName.add(String.format("report-MOD=%s-INP=%s.pdf", modalityCode, inpType.getDictValue()));
        tplFilesName.add(String.format("report-MOD=%s.pdf", modalityCode));
        //上级分类
        examModality = bridgeService.getExamModality(modalityCode);
        if(null != examModality && null != examModality.getParent() && StringUtils.isNotBlank(examModality.getParent().getDictValue())) {
            tplFilesName.add(String.format("report-MOD=%s.pdf", examModality.getParent().getDictValue()));
        }
        tplFilesName.add(String.format("report-INP=%s.pdf", inpType.getDictValue()));
        tplFilesName.add("report.pdf");
        Path tplFileFold = FileUtils.getDir("resfiles/template");
        File tplFile = null;// = .resolve("report.pdf").toFile();
        for(int i = 0; i < tplFilesName.size(); i ++) {
            Path tplFile0 = tplFileFold.resolve(tplFilesName.get(i));
            if(Files.isRegularFile(tplFile0)) {
                tplFile = tplFile0.toFile();
                break;
            }
        }
        if(log.isInfoEnabled()) { log.info("检查报告模板={}", tplFile); }
        if(null == tplFile) { throw new FileNotFoundException("报告模板不存在。"); }
        if(log.isDebugEnabled()) { log.debug("输出pdf start...."); }
        //读取模板，输出报告
        try(PdfReader reader = new PdfReader(tplFile);PdfWriter writer = new PdfWriter(outStream);PdfDocument pdfDocument = new PdfDocument(reader, writer);) {
            if(log.isDebugEnabled()) { log.debug("输出pdf 获取占位...."); }            //
            PageSize pageSize = pdfDocument.getDefaultPageSize();
            float pageWidth = pageSize.getWidth(), pageLpad = 34, pageRpad = 18;
            //替换固定信息占位符
            String[] vars = new String[]{"\\{pi.nm\\}", "\\{pi.regNo\\}", "\\{pi.gd\\}"
                    , "\\{pi.ag\\}", "\\{inpNo\\}", "\\{examNo\\}", "\\{reqdp.nm\\}"
                    , "\\{examDt\\}", "\\{examTm\\}", "\\{examPrt\\}", "\\{bedNo\\}"
                    , "\\{rpDt\\}", "\\{reqd.nm\\}", "\\{adtd.sign\\}", "\\{devMod\\}"};
            //用白色刷掉被替换文字
            Color cvbg = ColorConstants.WHITE;
            CompositeCleanupStrategy strategy = new CompositeCleanupStrategy();
            for (String var : vars) {
                strategy.add(new RegexBasedCleanupStrategy(var).setRedactionColor(cvbg));
            }

            PdfCleaner.autoSweepCleanUp(pdfDocument, strategy);
            if(log.isDebugEnabled()) { log.debug("输出pdf 填充内容."); }            //
            try (Document document = new Document(pdfDocument);) {

                //临时Document,用于计算报告页数
                PdfWriter writerTTemp = new PdfWriter(new ByteArrayOutputStream());
                PdfDocument pdfDocumentTemp = new PdfDocument(writerTTemp);
                Document documentTemp= new Document(pdfDocumentTemp);
                documentTemp.setFont(PdfUtil.createFont()).setFontColor(PdfUtil.fontColor);

                //文字
                document.setFont(PdfUtil.createFont()).setFontColor(PdfUtil.fontColor);
                //当前内容顶部距离
                int mainPos = 102;
                //患者信息
                Patient pat = report.getPatientInfo();
                Date examTime = report.getExamTime();
                if(null == examTime && null != report.getReportTime()) {
                    examTime = report.getReportTime();
                }
                //替换固定信息
                for(int i=0;i<pdfDocument.getNumberOfPages();i++) {
                    for (IPdfTextLocation location : strategy.getResultantLocations()) {
                        final int pageNum = location.getPageNumber() + 1 + i;
                        PdfPage page = pdfDocument.getPage(pageNum);
                        PdfCanvas pdfCanvas = new PdfCanvas(page.newContentStreamAfter(), page.getResources(), page.getDocument());
                        Rectangle area = location.getRectangle();
                        area.setWidth(256);
                        area.setY(area.getY() + 1);
                        try (Canvas canvas = new Canvas(pdfCanvas, area);) {
                            boolean isExamPart = false;
                            //替换内容
                            String var = location.getText(), rpl;
                            if ("{pi.nm}".equals(var)) {            //患者姓名
                                rpl = pat.getName();
                            } else if ("{pi.regNo}".equals(var)) {  //登记号
                                rpl = report.getPatientInfo().getRegistNo();
                            } else if ("{pi.gd}".equals(var)) {     //性别
                                rpl = null != pat.getGender() ? pat.getGender().getDictLabel() : null;
                            } else if ("{pi.ag}".equals(var)) {     //年龄
                                rpl = pat.readage();
                            } else if ("{inpNo}".equals(var)) {     //住院号
                                rpl = report.getInpNo();
                            } else if ("{examNo}".equals(var)) {    //检查号
                                rpl = report.getExamNo();
                            } else if ("{reqdp.nm}".equals(var)) {  //申请科室
                                rpl = null != report.getReqDept() ? report.getReqDept().getDeptName() : null;
                            } else if ("{examDt}".equals(var)) {    //检查日期
                                rpl = null != examTime ? DateUtils.dateTime(examTime) : Const.EMPTY;
                            } else if ("{examTm}".equals(var)) {    //检查时间
                                rpl = null != examTime ? DateUtils.parseDateToStr(DateUtils.HH_MM_SS, examTime) : Const.EMPTY;
                            } else if ("{devMod}".equals(var)) {    //设备
                                rpl = null != report.getExamDevice() ? report.getExamDevice().getModality() : Const.EMPTY;
                            } else if ("{examPrt}".equals(var)) {   //检查部位
                                //换行处理
                                isExamPart = true;
                                final float width;// = pageWidth - pageLpad - pageRpad;//490;//pageWidth
                                if (ExamEnum.InpTypeBodyExam.equals(inpType.getDictValue())) {
                                    width = pageWidth - pageLpad - pageRpad;
                                } else {
                                    //默认模板
                                    width = pageWidth - pageLpad - pageRpad - 184;
                                }
                                area.setWidth(width);
                                rpl = StringUtils.join(report.getExamParts().stream().map(ExamParts::getPartsName).collect(Collectors.toList()), ",");
                            } else if ("{bedNo}".equals(var)) {     //床号
                                rpl = null != report.getBedNo() ? report.getBedNo() : null;
                            } else if ("{rpDt}".equals(var)) {      //报告时间
                                rpl = null != report.getReportTime() ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, report.getReportTime()) : Const.EMPTY;
                            } else if ("{reqd.nm}".equals(var)) {   //报告医生
                                //rpl = null != report.getReqDoctor()? report.getReqDoctor().getNickName() : null;
                                rpl = null != report.getReportDoctor() ? report.getReportDoctor().getNickName() : null;
                            } else if ("{adtd.sign}".equals(var)) { //签名图片
                                //签名图片
                                String signImageData = report.getSignImage();
                                //补发，附件取签名
                                if (!isInstant && StringUtils.isBlank(signImageData)) {
                                    readSignImage(report);
                                    signImageData = report.getSignImage();
                                }
                                //
                                if (StringUtils.isNotBlank(signImageData)) {
                                    int fixedHeight = 26;
                                    Image image = PdfUtil.createPdfImage(yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(signImageData), -1, fixedHeight)
                                            .setFixedPosition(pageNum, area.getX(), area.getTop() - fixedHeight);
                                    canvas.add(image);
                                    continue;
                                } else if (null != report.getAuditDoctor()) {
                                    rpl = report.getAuditDoctor().getNickName();
                                } else {
                                    continue;
                                }
                            } else {
                                continue;
                            }
                            if (null != rpl) {
                                Paragraph para = new Paragraph(rpl).setFontSize(PdfUtil.metaFontSize)
                                        .setMargin(0f).setPadding(0f);
                                //检查部位换行处理
                                if (isExamPart && (rpl.length() * PdfUtil.metaFontSize) > area.getWidth()) {
                                    int firstLineIndent = 5 * PdfUtil.metaFontSize, numLines = 0;
                                    String[] segm = rpl.split("[\r\n]+");
                                    for (String l : segm) {
                                        while (l.length() * PdfUtil.metaFontSize > area.getWidth()) {
                                            //首行缩进 5 * metaFontSize
                                            int indent = numLines == 0 ? firstLineIndent : 0;
                                            int pos = (int) Math.floor((area.getWidth() - indent) / PdfUtil.metaFontSize);
                                            ++numLines;
                                            String sl = yyy.xxx.simpfw.common.utils.StringUtils.substr(l, pos * 2, Const.CHARSET_ZH);
                                            l = l.substring(sl.length());
                                        }
                                        if (!l.isEmpty()) {
                                            ++numLines;
                                        }
                                    }

                                    int lineHeight = PdfUtil.metaFontSize, pPos = (numLines - 1) * lineHeight;
                                    //重画部位下横线
                                    PdfCanvas ln = new PdfCanvas(page);
                                    ln.setLineWidth(1.5F);
                                    ln.moveTo(pageLpad, area.getBottom() - pPos - 4).lineTo(pageWidth - pageRpad, area.getBottom() - pPos - 4);
                                    ln.closePathStroke();
                                    //擦掉部位下横线
                                    ln.setLineWidth(4F);
                                    ln.setStrokeColor(PdfUtil.strokeColor);
                                    ln.moveTo(0, area.getBottom() - 5).lineTo(pageWidth, area.getBottom() - 5);
                                    ln.closePathStroke();
                                    //
//                                    para.setFont(PdfUtil.createFont())
//                                            .setFixedLeading(lineHeight)
//                                            .setFixedPosition(pageLpad, area.getBottom() - pPos, area.getWidth())
//                                            .setFirstLineIndent(firstLineIndent);
                                    document.add(getParagraph(area,rpl,pPos,firstLineIndent,lineHeight,pageLpad));
                                    documentTemp.add(getParagraph(area,rpl,pPos,firstLineIndent,lineHeight,pageLpad));
                                    //
                                    mainPos += pPos;
                                    //
                                    continue;
                                }
                                //文本
                                canvas.setFont(PdfUtil.createFont()).setFontColor(PdfUtil.fontColor);
                                canvas.add(para);//
                            }
                        }
                    }
                }

                if(log.isDebugEnabled()) { log.debug("输出pdf 主内容...."); }
                //
                document.add(getTable(report,mainPos,pageWidth,pageLpad,pageRpad) );
                if(log.isDebugEnabled()) { log.debug("输出pdf 建议ok."); }
                //获取占用页数
                int pageNum = getPageNum(documentTemp,report,mainPos,pageWidth,pageLpad,pageRpad);

                //pdf页数
                int pdfPageNum  = document.getPdfDocument().getNumberOfPages();

                //移除掉多余页
                while(pdfPageNum>pageNum){
                    document.getPdfDocument().removePage(pdfPageNum--);
                }
            }
        }
        if(log.isDebugEnabled()) { log.debug("输出pdf end."); }
    }

    private void createPdfImage(Table table, ExamAttachment img, float width, float height) throws Exception {
        Cell cell = new Cell();
        //cell.setHeight(height);
        cell.setBorder(Border.NO_BORDER);
        cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
        cell.setHorizontalAlignment(HorizontalAlignment.CENTER);
        cell.setPadding(0);
        table.addCell(cell);

        Image image = PdfUtil.createPdfImage(readReportImageData(img), width, height);
        image.setHorizontalAlignment(HorizontalAlignment.CENTER);
        image.setTextAlignment(TextAlignment.CENTER);

        cell.add(image);

        //cell = new Cell();
        //cell.setWidth(PdfImageAltCellWidth);
        cell.setBorder(new SolidBorder(DeviceRgb.BLACK, 0.5f));
        cell.setVerticalAlignment(VerticalAlignment.TOP);
        cell.setHorizontalAlignment(HorizontalAlignment.LEFT);
        cell.setPadding(0);
        //cell.setMarginRight(50);
        table.addCell(cell);
        try {
            BodypartsIllus illus = JsonUtil.toObject(img.getNoteInfo(), BodypartsIllus.class);
            if(null == illus || (StringUtils.isBlank(illus.getParts()) && StringUtils.isBlank(illus.getLesion()))) {
                return ;
            }

            String partsLesion = "";
            //
            if(StringUtils.isNotBlank(illus.getParts())) {
                partsLesion += illus.getParts();
                //cell.add(new Paragraph(illus.getParts()).setFontSize(PdfUtil.imageAltFontSize));
            }
            if(StringUtils.isNotBlank(illus.getLesion())) {
                partsLesion += illus.getLesion();
                //cell.add(new Paragraph(illus.getLesion()).setFontSize(PdfUtil.imageAltFontSize));
            }
            cell.add(new Paragraph(partsLesion).setFontSize(PdfUtil.imageAltFontSize));
        } catch (Exception err) {
            log.error(err.getMessage(), err);
        }
    }
}
