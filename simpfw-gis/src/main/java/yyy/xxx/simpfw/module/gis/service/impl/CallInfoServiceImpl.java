package yyy.xxx.simpfw.module.gis.service.impl;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.module.gis.mapper.CallInfoMapper;
import yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.CallInfo;
import yyy.xxx.simpfw.module.pacs.entity.EquipRoom;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.entity.QueueNumberRule;
import yyy.xxx.simpfw.module.pacs.mapper.EquipRoomMapper;
import yyy.xxx.simpfw.module.pacs.mapper.EquipRoomSettingsMapper;
import yyy.xxx.simpfw.module.pacs.mapper.QueueNumberRuleMapper;
import yyy.xxx.simpfw.module.pacs.service.CallInfoService;
import yyy.xxx.simpfw.module.pacs.utils.CacheUtil;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.QueueNumberRuleVo;
import yyy.xxx.simpfw.system.mapper.SysConfigMapper;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class CallInfoServiceImpl implements CallInfoService {

    @Autowired
    private CallInfoMapper mapper;

    @Autowired
    private QueueNumberRuleMapper queueNumberRuleMapper;

    @Autowired
    private EquipRoomSettingsMapper equipRoomSettingsMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private EisExamInfoMapper examInfomapper;

    @Autowired private EquipRoomMapper equipRoomMapper;

    //生成排队号锁
    private static final String lockKeyName_callNo = CacheUtil.cacheKey("lock.callNo");

    public CallInfoVo selectOne(CallInfo param) {
        return mapper.selectOne(param);
    }

    public List<CallInfoVo> selectList(CallInfo param) {
        return mapper.selectList(param);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public int insertOrUpdate(CallInfo entity) {
        //生成排队号
        if(!entity.isNew()) {
            return mapper.update(entity);
        }
        //
        ExamInfo examInfo = entity.getExamInfo();
        //要求检查类型ES
        SysDictData examModality = examInfo.getExamModality();
        String examModalityCode = null != examModality? examModality.getDictValue() : null;
        if(StringUtils.isBlank(examModalityCode)) {
            throw new IllegalArgumentException("无法获取排队号规则。");
        }
        //预留号
        Set<Integer> reservedNo = new TreeSet<>();//new HashSet<>();
        //排队号设置{规则分类, 检查类型}，预留号码
        //List<String[]> rules = new ArrayList<>();
        Map<String, QueueNumberRule> rules = new HashMap<>();
        final String ruleKeyPattern = "%s-%s";
        //查询排队号设置：号码前缀、预留号码
        rules.put(String.format(ruleKeyPattern, QueueNumberRuleVo.RuleType.ExamModality.getCode(), examModalityCode), null);
        //检查类型
        for(String ruleKey : rules.keySet()) {
            String[] ruleKeys = ruleKey.split("\\-");
            QueueNumberRuleVo rule = queueNumberRuleMapper.getPriorRule(ruleKeys[0], ruleKeys[1]);
            if(null == rule) {
                continue;
            }
            rules.put(ruleKey, rule);
            //预留号
            String reservedNo0 = rule.getReservedNo();
            if(StringUtils.isNotBlank(reservedNo0)) {
                String[] reservedNo_ = reservedNo0.split(Const.comma);
                for(String no : reservedNo_) {
                    if(NumberUtils.isDigits(no)) { reservedNo.add(Integer.valueOf(no)); }
                }
            }
        }
        //房间设置检查人数
        //排序预约日期或登记日期号
        long millis = null != examInfo.getAppointTime()? examInfo.getAppointTime().getTime() : System.currentTimeMillis();
        String lockData = String.format(Const.SFMT_DATE, millis);
        if(null != redisCache.lock(lockKeyName_callNo, lockData)) {
            try {
                final char leadChar = '0';
                //当前适用规则
                QueueNumberRule ruleUsed = !rules.isEmpty()? rules.values().iterator().next() : null;
                String prefix = Const.EMPTY;//前缀
                int noLen = 3;//长度
                if(null != ruleUsed) {
                    prefix = StringUtils.isNotBlank(ruleUsed.getRulePrefix()) ? ruleUsed.getRulePrefix() : prefix;
                    noLen =  StringUtils.isNotBlank(ruleUsed.getRulePattern())? ruleUsed.getRulePattern().length() : noLen;
                }
                //号码占位部分，如"A00041"的"A000"
                final String noHeadPat = "^([^\\d]+)?0*";
                //启用预留号, 获取可用预留号，按小到大排序
                boolean isReservedNoUsed = null != examInfo.getReservedNoUsed() && 1 == examInfo.getReservedNoUsed().intValue();
                //已被占用预留号
                Collection<String> reservedNoFmted = null;
                if(!reservedNo.isEmpty()) {//isReservedNoUsed &&
                    final String prefix_ = prefix;
                    final int noLen_ = noLen;
                    //
                    reservedNoFmted = reservedNo.stream().map(no -> (prefix_ + StringUtils.leftPad(String.valueOf(no), noLen_, leadChar))).collect(Collectors.toSet());
                    //查询被用的预留号
                    if(isReservedNoUsed) {
                        List<String> usedNo = mapper.selectByRules(examInfo.getCreateTime(), examInfo.getAppointTime()
                        		, reservedNoFmted, null, examModalityCode);
                        if (null != usedNo && !usedNo.isEmpty()) {
                            Set<Integer> usedNo_ = usedNo.stream().map(n -> Integer.valueOf(n.replaceAll(noHeadPat, Const.EMPTY))).collect(Collectors.toSet());
                            reservedNo.removeAll(usedNo_);
                        }
                        //小到大排序
                        //reservedNo = reservedNo.stream().sorted().collect(Collectors.toSet());
                    }
                }
                //排队号键名，最新排队号吗
                String cacheKey = CacheUtil.cacheKey("callNo" + lockData);
                Map<String, Integer> lastCallNo = redisCache.getCacheMap(cacheKey);
                //每个检查项目一个队列
                Integer callNoValue = null != lastCallNo? lastCallNo.get(examModalityCode) : null;
                //
                if(null == lastCallNo) {
                    lastCallNo = new HashMap<>();
                }
                //没有排队记录，
                if(null == callNoValue) {
                    //读取当日/预约日排队号
                    if(null == examInfo.getAppointTime() && null == examInfo.getCreateTime()) {
                        examInfo.setCreateTime(DateUtils.getNowDate());
                    }
                    String lastNo = mapper.selectLastByRules(examInfo.getCreateTime(), examInfo.getAppointTime()
                    		, reservedNoFmted, null, examModalityCode);
                    if (StringUtils.isNotBlank(lastNo)) {
                        callNoValue = Integer.valueOf(lastNo.replaceAll(noHeadPat, Const.EMPTY));
                    } else if (null != ruleUsed && StringUtils.isNotBlank(ruleUsed.getRulePattern())) {
                        //是否设置第一个排队号
                        callNoValue = Integer.valueOf(ruleUsed.getRulePattern().replaceAll(noHeadPat, Const.EMPTY));
                    } else {
                        callNoValue = 0;
                    }
                }
                ++ callNoValue;
                //
                boolean updateCache = true;
                //
                if(isReservedNoUsed && !reservedNo.isEmpty()) {
                    //排队号和预留号较小值
                    Integer minReservedNo = reservedNo.iterator().next();
                    if(callNoValue > minReservedNo) {
                        callNoValue = minReservedNo;
                        //
                        updateCache = false;
                    }
                } else if(!reservedNo.isEmpty()) {
                    //排队号在预留号中
                    while(reservedNo.contains(callNoValue)) {
                        ++ callNoValue;
                    }
                }
                if(updateCache) {
                    lastCallNo.put(examModalityCode, callNoValue);
                    //
                    redisCache.setCacheMap(cacheKey, lastCallNo);
                    //过期半天
                    redisCache.expire(cacheKey, 24 * 60 * 60);
                }
                //排队好
                String callNo = callNoValue.toString();
                //格式，3位号码，不足前补0
                callNo = StringUtils.leftPad(callNo, noLen, leadChar);
                //前缀
                if (StringUtils.isNotBlank(prefix)) {
                    callNo = prefix + callNo;
                }
                //
                entity.setCallNo(callNo);
            } finally {
                redisCache.unlock(lockKeyName_callNo);
            }
        }

        return mapper.insert(entity);
    }

    public int delete(Long id) {
        return mapper.delete(id);
    }

    public CallInfoVo selectByExam(Long examId) {
        return mapper.selectByExam(examId);
    }

    /**
     * 更新呼叫信息
     * @param call0
     * @param callRoomCode
     */
    @Transactional(propagation = Propagation.SUPPORTS)
    public int call(CallInfo call0, String callRoomCode) {
        //当前时间
        Date now = DateUtils.getNowDate();
        //更新首次呼叫时间
        if(null == call0.getFirstCallTime()) {
            call0.setFirstCallTime(now);
        } else {
            //更新最近呼叫时间
            if(null == call0.getFirstEndCallTime()) {
                call0.setFirstEndCallTime(now);
            } else if(null != call0.getLastCallTime()) {
                call0.setLastEndCallTime(now);
            }
            call0.setLastCallTime(now);
        }
        //呼叫者
        call0.setDoctor(SecurityUtils.getLoginUser().getUser());
        //呼叫机房，应取呼叫所分配机房
        EquipRoom callRoom = null;
        //指定机房
        if(yyy.xxx.simpfw.common.utils.StringUtils.isNotBlank(callRoomCode)) {
            callRoom = new EquipRoom();
            callRoom.setRoomCode(callRoomCode);
            EquipRoom callRoom0 = equipRoomMapper.selectOne(callRoom);
            if(null != callRoom0) { callRoom.setRoomName(callRoom0.getRoomName()); }
        }
        callRoom = null != callRoom? callRoom : call0.getExamInfo().getEquipRoom();
        if(null == callRoom) {
            throw new IllegalArgumentException("请指定检查机房。");
        }
        call0.setCallRoom(callRoom);
        //执行呼叫信息更新
        //call0.setId(call0.getId());
        call0.setStatus(CallInfoVo.STATUS_NORMAL);
        return insertOrUpdate(call0);
    }

    @Transactional
    public int past(CallInfo callInfo) {
        //检查信息
        ExamInfo examInfo = callInfo.getExamInfo();
        //状态检查，当前工作状态不是检查中的不执行
        SysDictData resultStatus = examInfo.getResultStatus();
        if(null == resultStatus || !ResultStatus.REGIST.is(resultStatus.getDictValue()) && !ResultStatus.EXAM.is(resultStatus.getDictValue())) {
            throw new RuntimeException(String.format("不支持操作，原因：该检查已%s", resultStatus.getDictLabel()));
        }
        //工作进度恢复到已登记
        resultStatus = new SysDictData();
        resultStatus.setDictValue(ResultStatus.REGIST.getValue());
        examInfo.setResultStatus(resultStatus);
        examInfomapper.updateResultStatus(examInfo);
        //更新状态为过号
        callInfo.setStatus(CallInfoVo.STATUS_PAST);
        return mapper.past(callInfo);
    }

    public int changeEquipRoom(Long fromId, String fromEquipRoomCode, String toEquipRoomCode) {
        return mapper.changeEquipRoom(fromId, fromEquipRoomCode, toEquipRoomCode);
    }
}
