package yyy.xxx.simpfw.module.gis.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.JsonUtil;
import yyy.xxx.simpfw.module.gis.entity.DBookedInfo;
import yyy.xxx.simpfw.module.gis.service.EisExamInfoService;
import yyy.xxx.simpfw.module.gis.service.IDBookedInfoService;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.EquipRoom;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.entity.ExamParts;
import yyy.xxx.simpfw.module.pacs.service.EquipRoomService;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.vo.EquipRoomVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;

@RestController
@RequestMapping("/queue/ticket")
public class QueueSmallTicketController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(QueueSmallTicketController.class);
    
	//接口参数：预诊室，患者姓名，排队号，检查号，检查日期，排队时间，检查部位
	private static final String ticketApiParam = "{\"bizType\":\"usQueueTicket\",\"preExamRoom\":\"%1$s\",\"patientName\":\"%2$s\",\"callNo\":\"%3$s\",\"examNo\":\"%4$s\",\"examDate\":\"%5$tF\",\"watingTime\":\"%6$s\",\"examParts\":\"%7$s\"}";
   
	@Autowired 
    private EquipRoomService roomService;

    @Autowired 
    private BridgeService bridgeService;

    @Autowired 
    private HttpClientService httpService;
    
    @Autowired
    private IDBookedInfoService dBookedInfoService;
    
    @Autowired
    private EisExamInfoService eisExamInfoService;

    @RequestMapping(value = "/print")
    public AjaxResult print(@RequestBody ExamInfo param) {
        /*{
            "bizType": "usQueueTicket",                     // 超声科排队号，固定
            "preExamRoom": "2诊室、3诊室、4诊室、5诊室、6诊室",
            "patientName": "赵六",
            "callNo": "A019",
            "examNo": "202303100068",
            "examDate": "2023-03-10",
            "watingTime": "上午（8:00-9:00）",
            "examParts": "上腹部(肝.胆.胰.脾)，腹后腔/腹后腔肿块"
        }*/
        try {
            //打印服务地址
            final String cfgNam = "pacs.smallticketPrinter";
            String cfgVal = bridgeService.getSysConfigByKey(cfgNam);
            Map<?, ?> tickApi;
            String tickApiServer;
            if(StringUtils.isBlank(cfgVal) || null == (tickApi = JsonUtil.toObject(cfgVal, Map.class))
                    || StringUtils.isBlank(tickApiServer = ((String)tickApi.get("server")))) {
                final String errM = String.format("未配置打印接口或配置无效：%s。", cfgNam);
                //return AjaxResult.error(errM);
                return AjaxResult.success(errM);
            }
            //预诊科室：检查项目支持的诊室
            List<SysDictData> examItems = new ArrayList<>();
            examItems.add(param.getExamItem());
            EquipRoom roomParams = new EquipRoom();
            roomParams.setExamItems(examItems);
            List<EquipRoomVo> rooms = roomService.selectList(roomParams);
            String roomsNames = StringUtils.join(rooms.stream().map(EquipRoom::getRoomName).collect(Collectors.toList()), Const.comma);
            //
            String examPartsNames = StringUtils.join(param.getExamParts().stream().map(ExamParts::getPartsName).collect(Collectors.toList()), Const.comma);
            //打印服务地址
            String url = String.format("%s/printTicket", tickApiServer);
            String patientName = param.getPatientInfo().getName();
            String queueNo = param.getCallInfo().getCallNo();
            String examNo = param.getExamNo();
            Date examTime = param.getCreateTime();
            String waitingTime = DateUtils.meridiemMarker(examTime, true);
            waitingTime = String.format("%1$s（%2$tH:%2$tM）", waitingTime, examTime);

            String postData = String.format(ticketApiParam, roomsNames, patientName, queueNo, examNo, examTime, waitingTime, examPartsNames);
            String res = httpService.ajaxPost(url, postData, null, null);
            Map<?, ?> apiRes = StringUtils.isNotBlank(res)? JsonUtil.toObject(res, Map.class) : null;
            if(null == apiRes || !"0000".equals(apiRes.get("code"))) {
                String errM = null != apiRes? String.valueOf(apiRes.get("msg")) : null;
                throw new RuntimeException(errM);
            }

            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }
    
    
    /**
		<pre>
	     	超声小票信息：
		     {
		     	// 超声科排队号，固定
		        "bizType": "usQueueTicket",
		        "preExamRoom": "2诊室、3诊室、4诊室、5诊室、6诊室",
		        "patientName": "赵六",
		        "callNo": "A019",
		        "examNo": "202303100068",
		        "examDate": "2023-03-10",
		        "watingTime": "上午（8:00-9:00）",
		        "examParts": "上腹部(肝.胆.胰.脾)，腹后腔/腹后腔肿块"
			  }
    	</pre>
        
        <pre>
        	呼吸内镜小票信息，目前只有纤支镜：
        	{
    		  	// 呼吸内镜排队号，固定
                "bizType": "reQueueTicket",
                "patientName": "赵六",
                "callNo": "A019",
                "examDate": "2023年3月10日",
                "watingTime": "上午（8:00-9:00）", 
                "amOrPm": "AM|PM"
            }
        </pre>
     * @throws Exception 
    */
    @RequestMapping(value = "/getPrintInfo")
    public AjaxResult getPrintInfo(@RequestBody ExamInfo param) throws Exception {
    	if (param == null) {
    		return AjaxResult.error("param should not be NULL");
    	}
    	// System.out.println("---ExamInfo param: " + yyy.xxx.simpfw.module.pacs.utils.JsonUtil.writeAsPrettyJsonString(param));
    	SysDictData examModality = param.getExamModality();
    	if (examModality == null) {
    		return AjaxResult.error("设备类型为空");
    	}
    	
    	SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy年MM月dd日");
    	SimpleDateFormat sdf2 = new SimpleDateFormat("HH:mm");
    	Date noonTime = sdf2.parse("12:00");
    	String examModalityVal = examModality.getDictValue();
    	examModalityVal = examModalityVal.toUpperCase();
    	switch (examModalityVal) {
		case "EIS":					// 呼吸内镜
			SysDictData eisExamItem = param.getExamItem();
			logger.debug("获取检查项目[{}]的小票打印信息", eisExamItem.getDictValue());
			Map<String, Object> dataMap = new HashMap<String, Object>();
			dataMap.put("bizType", "reQueueTicket");
			dataMap.put("patientName", param.getPatientInfo().getName());
			dataMap.put("callNo", param.getCallInfo().getCallNo());

			// 如果预约表有记录，则使用预约表中的信息
			DBookedInfo bookedInfo = dBookedInfoService.findByExamUid(param.getExamUid());
			if (bookedInfo != null) {
				dataMap.put("examDate", sdf1.format(bookedInfo.getAppointExamDate()));

				String appointTime = bookedInfo.getAppointExamTime();
				String[] timeRange = appointTime.split("--");
				Date endTime = sdf2.parse(timeRange[1]);
				if (endTime.compareTo(noonTime) > 0) {
					dataMap.put("amOrPm", "PM");
					dataMap.put("watingTime", String.format("下午（%s-%s）", timeRange[0], timeRange[1]));
				} else {
					dataMap.put("amOrPm", "AM");
					dataMap.put("watingTime", String.format("上午（%s-%s）", timeRange[0], timeRange[1]));
				}

			} // end if
			else {
				ExamInfoVo examInfoVo = eisExamInfoService.selectByExamUid(param.getExamUid());
				if (examInfoVo != null) {
					Date appointTime = examInfoVo.getAppointTime();
					if (appointTime == null) {
						appointTime = examInfoVo.getCreateTime();
					}
					dataMap.put("examDate", sdf1.format(appointTime));

					String startHourStr = "0";
					String endHourStr = "0";
					String startMinuteStr = "00";
					String endMinuteStr = "00";
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(appointTime);
					int whichHour = calendar.get(Calendar.HOUR_OF_DAY);
					int whichMinute = calendar.get(Calendar.MINUTE);

					String endTimeStr = sdf2.format(appointTime);
					Date endTime = sdf2.parse(endTimeStr);
					if (endTime.compareTo(noonTime) > 0) {
						startHourStr = whichHour + "";
						endHourStr = ((whichHour + 1) >= 24) ? "00" : ((whichHour + 1) + "");
						if (whichMinute >= 30) {
							startMinuteStr = "30";
							endMinuteStr = "30";
						} else {
							startMinuteStr = "00";
							endMinuteStr = "00";
						}
						dataMap.put("watingTime", String.format("下午（%s-%s）", startHourStr + ":" + startMinuteStr,
								endHourStr + ":" + endMinuteStr));
						dataMap.put("amOrPm", "PM");

					} else {
						startHourStr = whichHour + "";
						endHourStr = ((whichHour + 1) >= 12) ? "12" : ((whichHour + 1) + "");
						if (whichMinute >= 30) {
							startMinuteStr = "30";
							endMinuteStr = "30";
						} else {
							startMinuteStr = "00";
							endMinuteStr = "00";
						}
						dataMap.put("watingTime", String.format("上午（%s-%s）", startHourStr + ":" + startMinuteStr,
								endHourStr + ":" + endMinuteStr));
						dataMap.put("amOrPm", "AM");
					}
				}
			} // end else
			return AjaxResult.success(dataMap);
			
		case "UIS":					// 超声
			try {
				//预诊科室：检查项目支持的诊室
				List<SysDictData> examItems = new ArrayList<>();
				examItems.add(param.getExamItem());
				EquipRoom roomParams = new EquipRoom();
				roomParams.setExamItems(examItems);
				List<EquipRoomVo> rooms = roomService.selectList(roomParams);
				String roomsNames = StringUtils.join(rooms.stream().map(EquipRoom::getRoomName).collect(Collectors.toList()), Const.comma);
				//
				String examPartsNames = StringUtils.join(param.getExamParts().stream().map(ExamParts::getPartsName).collect(Collectors.toList()), Const.comma);
				String patientName = param.getPatientInfo().getName();
				String queueNo = param.getCallInfo().getCallNo();
				String examNo = StringUtils.trimToEmpty(param.getExamNo());
				// Date examTime = param.getCreateTime();
				Date examTime = param.getCallInfo().getExamInfo().getCreateTime();
				String waitingTime = DateUtils.meridiemMarker(examTime, true);
				waitingTime = String.format("%1$s（%2$tH:%2$tM）", waitingTime, examTime);
				
				String postData = String.format(ticketApiParam, roomsNames, patientName, queueNo, examNo, examTime, waitingTime, examPartsNames);
				return AjaxResult.success((Object)postData);
				
			} catch (Exception err) {
				logger.error(err.getMessage(), err);
			}
			break ;
			
		default:
			logger.warn("暂不支持获取设备类型[{}]的小票打印信息", examModalityVal);
			break;
		}
    	return AjaxResult.success();
    	// return AjaxResult.error("无法获取检查信息打印排队小票");
    }
    
    
}
