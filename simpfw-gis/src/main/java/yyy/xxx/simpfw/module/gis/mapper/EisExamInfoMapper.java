package yyy.xxx.simpfw.module.gis.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;

public interface EisExamInfoMapper extends ExamInfoMapper {
    int updateActiveStatus(ExamInfo entity);
    int updateAppointExamTime(ExamInfo entity);
}
