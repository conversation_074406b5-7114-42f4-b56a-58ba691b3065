package yyy.xxx.simpfw.module.gis.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import yyy.xxx.common.net.storage.utils.FileUtils;
import yyy.xxx.simpfw.common.utils.JsonUtil;
import yyy.xxx.simpfw.common.utils.file.ImageUtils;
import yyy.xxx.simpfw.module.gis.vo.BodypartsIllus;
import yyy.xxx.simpfw.module.pacs.entity.ExamAttachment;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

public class BodypartsIllusUtil {
    private static final Logger log = LoggerFactory.getLogger(BodypartsIllusUtil.class);
    //部位图标记号字体
    private static final Font markFont = new Font("宋体", Font.PLAIN, 10);
    //部位标记颜色
    private static final Color[] markColor = new Color[]{Color.DARK_GRAY, new Color(41, 170, 81), Color.RED, Color.BLUE, Color.CYAN};
    //通用示意图文件夹
    public static final String COMMON_FOLD = "com";

    /**
     * 部位示意图上标点
     * @param illusFile 示意图路径
     * @param marks 标点信息
     * @param scaleWidth 输出宽度
     * @param scaleHeight 输出高度
     * @return 部位示意图数据
     */
    public static byte[] reillus(Path illusFile, List<ExamAttachment> marks, int scaleWidth, int scaleHeight) throws IOException {
        if(null == marks || marks.isEmpty()) {
            return null;
        }

        final int markWidth = 6, markHeight = 10, fontSize = markFont.getSize();

        BufferedImage img = null;
        Graphics2D g = null;

        for(int i = 0; i < marks.size(); i ++) {
            ExamAttachment m = marks.get(i);
            if(StringUtils.isBlank(m.getNoteInfo())) {
                continue;
            }
            BodypartsIllus illus;
            try {
                illus = JsonUtil.toObject(m.getNoteInfo(), BodypartsIllus.class);
                if(null == illus || !illus.validate()) {
                    continue;
                }
                //读取部位示意图，准备标注
                if(null == g) {
                    img = ImageIO.read(illusFile.toFile());
                    g = img.createGraphics();
                    g.setFont(markFont);
                }
                //标注信息
                BodypartsIllus.Mark mark = illus.getMark();
                int x = mark.getX().intValue(), y = mark.getY().intValue();
                double r;
                if(scaleWidth > 0) {
                    r = scaleWidth / mark.getW();
                    x = (int)(r * x);
                }
                if(scaleHeight > 0) {
                    r = scaleHeight / mark.getH();
                    y = (int)(r * y);
                }
                //标记
                g.setColor(markColor[i]);
                //g.fillOval(x, y, markSize, markSize);
                g.fillRect(x, y, markWidth, markHeight);
                //序号
                g.setColor(Color.WHITE);
                g.drawString(String.valueOf(1 + i), x + 1, y + fontSize - 2);
            } catch (Exception err) {
                log.error(err.getMessage());
            }
        }

        if(null != g) {
            g.dispose();
            return ImageUtils.getData(img);
        }

        return null;
    }

    /**
     * 获取部位示意图目录
     * @param sfold 所在文件夹，未指定取通用文件夹
     * @return 部位示意图目录
     */
    public static Path illusFolder(String ... sfold) throws IOException {
        final String fmt = "resfiles/bodyparts-illustration/%s";
        String fld = null != sfold && sfold.length == 1 && null != sfold[0]? sfold[0] : COMMON_FOLD;
        return FileUtils.getDir(String.format(fmt, fld), false);
    }

    /**
     * 获取指定路径部位示意图
     * @param name 图片名字
     * @return 部位示意图路径
     */
    public static Path illusPath(String sfold, String name) throws IOException {
        return illusFolder(sfold).resolve(String.format("%s.png", name));
    }
}
