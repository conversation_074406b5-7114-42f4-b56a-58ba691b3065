package yyy.xxx.simpfw.module.gis.service.impl;

import java.io.File;
import java.net.URI;
import java.net.URLDecoder;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import yyy.xxx.common.net.storage.AbsFile;
import yyy.xxx.common.net.storage.StorageInterface;
import yyy.xxx.common.net.storage.StorageInterfaceFactory;
import yyy.xxx.common.net.storage.impl.ftp.FtpStorage;
import yyy.xxx.common.net.storage.impl.local.LocalStorage;
import yyy.xxx.common.net.storage.impl.smb.SmbStorage;
import yyy.xxx.common.net.storage.utils.FileUtils;
import yyy.xxx.common.net.storage.utils.WebUtil;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.module.gis.service.DicomImageIllusService;
import yyy.xxx.simpfw.module.gis.vo.DicomImageIllus;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.DicomImage;
import yyy.xxx.simpfw.module.pacs.entity.ExamAttachment;
import yyy.xxx.simpfw.module.pacs.mapper.DicomImageMapper;
import yyy.xxx.simpfw.module.pacs.mapper.ExamAttachmentMapper;
import yyy.xxx.simpfw.module.pacs.service.ExamReportService;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class DicomImageIllusServiceImpl implements DicomImageIllusService {
	
	private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired private DicomImageMapper imageMapper;

    @Autowired private ExamAttachmentMapper attaMapper;

	@Override
    public DicomImageIllus save(ExamAttachment atta) throws Exception {
    	String path = atta.getPath();
    	String[] parts;
    	if(StringUtils.isBlank(path) || 3 != (parts = path.split(Const.SYMBOL_SLASH)).length) {
    		throw new IllegalArgumentException(String.format("无效 path=%s", path));
    	}
    	String sopInstanceUid = parts[2];
    	
    	DicomImage image = imageMapper.selectSop(sopInstanceUid);
    	if(null == image) {
    		throw new RuntimeException(String.format("影像文件不存在 path=%s", path));
    	}
    	String fileUrl = image.getFileUrl();
    	String fileName = image.getFileName();
    	String urlUser = image.getUsername(), urlPasswd = image.getPassword();
    	fileUrl = WebUtil.putUserinfo(fileUrl, urlUser, urlPasswd);
    	//
		if(logger.isDebugEnabled()) { logger.debug("获取存储...."); }
    	StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(fileUrl, Const.STORAGE_SESSION_OPTION);
    	if(logger.isDebugEnabled()) { logger.debug("取得存储."); }
    	try {
    		//标识该影像为编辑图像，非原图
			//反斜杠转斜杠，方便处理
    		String fileUrl0 = fileUrl.replace(Const.backslash, Const.slash);
			//截取路径//
    		int pos = fileUrl0.indexOf(Const.slash2);
			//斜杠位置
    		pos = fileUrl0.indexOf(Const.slash, pos + 2);

			if(SmbStorage.handle(fileUrl)) {
				//smb去掉共享目录
				pos = fileUrl0.indexOf(Const.slash, pos + 1);
			}

    		String filePath = fileUrl.substring(pos + 1);
    		//截取不含文件名
    		pos = filePath.lastIndexOf(fileName);
    		if(!fileName.contains(SOURCE_SIGN_ILLUS)) {
    			String extName = FileUtils.getExtName(fileName);
    			fileName = fileName.replace(extName, SOURCE_SIGN_ILLUS + extName);
    			filePath = filePath.substring(0, pos ) + fileName;
    		}else{
    			if(SmbStorage.handle(fileUrl)) {
    				//smb旧文件改名
					String extName = FileUtils.getExtName(fileName);
					String newFileName = fileName.replace(extName, System.currentTimeMillis() + extName);
					String newFilePath = filePath.substring(0, pos ) + newFileName;
					storage.rename(filePath, newFilePath);
				}
			}

			if(logger.isDebugEnabled()) { logger.debug("影像数据...."); }
        	byte[] imageData = atta.getData();//yyy.xxx.simpfw.common.utils.StringUtils.base64ToBytes(base64Data);
			if(logger.isDebugEnabled()) { logger.debug("写入文件....{}", filePath); }
        	AbsFile sFile = storage.write(filePath, imageData);
			if(logger.isDebugEnabled()) { logger.debug("写入完成."); }
			URI fileUri = sFile.toURI(false);
			fileUrl = URLDecoder.decode(fileUri.toString(), Const.charset_UTF_8);
    	} finally {
    		storage.release();
    	}
    	
    	//更新图像
        image.setFileUrl(fileUrl);
        image.setFileName(fileName);
    	
        imageMapper.updateMeta(image);
        
        //更新报告影像
        if(atta.isNew()) {
			ExamAttachment p = new ExamAttachment();
			p.setPath(atta.getPath());
			p.setType(atta.getType());
			p.setExamInfoId(atta.getExamInfoId());
			p.setStatus(0);
        	List<ExamAttachment> rows = attaMapper.selectList(p);
        	if(null != rows && !rows.isEmpty() && path.equals(rows.get(0).getPath())) {
        		atta.setId(rows.get(0).getId());
        	}
        }
        
        atta.setData(null);
		boolean isNew = atta.isNew();
        if(isNew) {
        	atta.setType(ExamReportService.attTypeImage);
        	atta.setFileType(Const.FILE_TYPE_NAME_JPG);
        	attaMapper.insert(atta);
        } else {
        	attaMapper.update(atta);
			atta = attaMapper.selectById(atta.getId());
        }
        
        return new DicomImageIllus(isNew, atta, image);
    }
}
