package yyy.xxx.simpfw.module.gis.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.module.gis.mapper.CallIngBoardMapper;
import yyy.xxx.simpfw.module.pacs.service.CallIngBoardService;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class CallIngBoardServiceImpl implements CallIngBoardService {

    @Autowired
    private CallIngBoardMapper mapper;

    public List<CallInfoVo> selectList() {
        return mapper.selectList();
    }
}
