package yyy.xxx.simpfw.module.gis.vo;

import org.apache.commons.lang3.StringUtils;

public class BodypartsIllus {
    public static class Mark {
        private String illus;
        private Double x, y, w, h;

        public String getIllus() {
            return illus;
        }
        public void setIllus(String illus) {
            this.illus = illus;
        }

        public Double getX() {
            return x;
        }
        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }
        public void setY(Double y) {
            this.y = y;
        }

        public Double getW() {
            return w;
        }
        public void setW(Double w) {
            this.w = w;
        }

        public Double getH() {
            return h;
        }
        public void setH(Double h) {
            this.h = h;
        }
    }
    private String parts, lesion;

    private Mark mark;

    public String getParts() {
        return parts;
    }
    public void setParts(String parts) {
        this.parts = parts;
    }

    public String getLesion() {
        return lesion;
    }
    public void setLesion(String lesion) {
        this.lesion = lesion;
    }

    public Mark getMark() {
        return mark;
    }
    public void setMark(Mark mark) {
        this.mark = mark;
    }

    public boolean validate() {
        return null != mark && StringUtils.isNotBlank(mark.illus)
                && null != mark.w && mark.w > 0
                && null != mark.h && mark.h > 0
                && null != mark.x && mark.x > 0
                && null != mark.y && mark.y > 0;
    }
}
