package yyy.xxx.simpfw.module.gis.service.impl;

import java.lang.reflect.Method;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.entity.SysConfig;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.module.gis.mapper.CallInfoMapper;
import yyy.xxx.simpfw.module.gis.mapper.EisExamInfoMapper;
import yyy.xxx.simpfw.module.gis.service.EisExamInfoService;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.mapper.PatientMapper;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.utils.CacheUtil;
import yyy.xxx.simpfw.module.pacs.utils.ExamNoUtil;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.system.mapper.SysUserMapper;
import yyy.xxx.simpfw.system.service.ISysConfigService;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class ExamInfoServiceImpl implements EisExamInfoService {
    private static final Logger log = LoggerFactory.getLogger(ExamInfoServiceImpl.class);

    @Autowired
    private EisExamInfoMapper mapper;

    @Autowired
    private PatientMapper patientMapper;

    @Autowired
    private CallInfoMapper callInfoMapper;

    @Autowired
    private ExamReportService reportService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CallInfoService callService;
    
    @Autowired private DicomStudyService studyService;

    @Autowired private EquipRoomService equipRoomService;
    @Autowired private SysUserMapper sysUserMapper;

    @Autowired private BridgeService bridgeService;

    //检查号全局配置
    private static final String LOCKKEY_LASTEXAMNO = CacheUtil.cacheKey(FMT_LOCKKEY_LASTEXAMNO, Const.SYS_MODALITY)
            , CONFIGKEY_LASTEXAMNO = String.format(FMT_CONFIGKEY_LASTEXAMNO, Const.SYS_MODALITY)
            , CONFIGKEY_EXAMNOPATTERN = String.format(FMT_CONFIGKEY_EXAMNOPATTERN, Const.SYS_MODALITY);

    public ExamInfoVo selectOne(ExamInfo param) {
        ExamInfoVo entity = mapper.selectOne(param);
        entity.setImages(reportService.selectImages(entity));

        return entity;
    }

    public ExamInfoVo selectById(Long id) {
        ExamInfo param = new ExamInfo();
        param.setId(id);
        return selectOne(param);
    }

    public ExamInfoVo selectByExamUid(String examUid) {
        ExamInfo param = new ExamInfo();
        param.setExamUid(examUid);
        return selectOne(param);
    }

    //@DataScope(deptAlias="")
    public List<ExamInfoVo> selectList(ExamInfo param) {
        return mapper.selectList(param);
    }

    @Transactional
    public int saveOrUpdate(ExamInfo entity) {
        //if(StringUtils.isBlank(entity.getExamNo())) {
        //    entity.setExamNo(makeExamNo());
        //}
        //
        int num = 0;
        Patient patient = entity.getPatientInfo();
        if(null != patient) {
            //登记方式
            patient.setRegistWay(entity.getRegistWay());
            //登记号去首位空白符号
            if(StringUtils.isNotBlank(patient.getRegistNo())) {
                patient.setRegistNo(patient.getRegistNo().trim());
            }
            //拼音
            /*if(StringUtils.isBlank(patient.getNamePingyin())) {
                try {

                } catch (Exception err) { log.error(err.getMessage()); }
            }*/
            //num += null == patient.getId()? patientMapper.insert(entity.getPatientInfo()) : patientMapper.update(entity.getPatientInfo());
            if(patient.isNew()) {
                Patient patientExist = patientMapper.selectOne(patient);
                if(null != patientExist) {
                    //更新已有
                    patient.setId(patientExist.getId());
                    patient.setPatientId(patientExist.getPatientId());
                    //使用已有病历号
                    patient.setMedicalRecordNo(patientExist.getMedicalRecordNo());
                    patientMapper.update(patient);
                } else {
                    //增加
                    patient.setPatientId(StringUtils.randomChars(16));//Long.MAX_VALUE - NumberUtil.randomInt(9999)
                    patientMapper.insert(patient);
                    //
                    patientMapper.currectPatientId(patient.getId());
                    patient.setPatientId(patient.getId().toString());
                }
            } else {
                patientMapper.update(patient);
            }
        }
        //排队
        CallInfo callInfo = entity.getCallInfo();
        //房间信息->设备信息
        EquipRoom equipRoom = null != callInfo? callInfo.getCallRoom() : null;
        //房间号表示最新检查房间
        if(null != equipRoom && StringUtils.isNotBlank(equipRoom.getRoomCode())) {
            equipRoom.setId(null);
            equipRoom = equipRoomService.selectOne(equipRoom);
            //
            if(null != equipRoom && null != equipRoom.getDevice()) {
                entity.setExamDevice(equipRoom.getDevice());
            }
        } else {
            entity.setExamDevice(null);
        }
        //新增/更新检查
        boolean isNew = entity.isNew();
        if(isNew) {
            entity.setExamUid(StringUtils.uid());
        }
        num += isNew? mapper.insert(entity) : mapper.update(entity);
        //更新/新增排队
        if(null == callInfo) { callInfo = new CallInfoVo(); }
        //查询排队信息
        callInfo.setExamInfo(entity);
        /*List<CallInfoVo> calls = callService.selectList(callInfo);
        //如果没有排队号，重新排队
        if(null == calls || calls.isEmpty() || StringUtils.isBlank(calls.get(0).getCallNo())) {
            if(null != calls && !calls.isEmpty()) {
                callService.delete(calls.get(0).getId());
            }
            callService.insertOrUpdate(callInfo);
        }*/
        //
        //CallInfoVo callInfo0 = null != callInfo.getId()? callService.selectOne(callInfo) : callService.selectByExam(entity.getId());
        CallInfoVo callInfo0 = callService.selectOne(callInfo);
        //无排队号，排队无效，删除
        if(null != callInfo0 && StringUtils.isBlank(callInfo0.getCallNo())) {
            callService.delete(callInfo0.getId());
        }
        if(null != callInfo0) {
            callInfo0.setCallRoom(callInfo.getCallRoom());
            callService.insertOrUpdate(callInfo0);
        } else {
            callService.insertOrUpdate(callInfo);
        }
        //循环嵌套
        callInfo.setExamInfo(mapper.selectOne(entity));
        //dicomstudy
        DicomStudy study = new DicomStudy();
        study.setExamInfo(entity);
        if(null == studyService.selectOne(study)) {
        	studyService.saveOrUpdate(study);
        	entity.setDicomStudy(study);
        }

        return num;
    }

    //@DataSource(DataSourceType.MASTER)
    public String makeExamNo(String examItemCode) {
        //String datetime = String.format(UisConst.SFMT_MIN, System.currentTimeMillis());
        //String date = datetime.substring(0, 10);
        //读取字典获取检查号格式配置
        SysDictData dict = bridgeService.getExamItem(examItemCode);
        //分布式锁
        String lockKeyName_lastExamNo;
        //检查号格式
        String examNoPattern;////configService.selectConfigByKey(configKeyName_examNoPattern);
        ///
        String configKey_lastExamNo;
        //字典是否有配置
            if(null != dict && null != dict.getExtend() && StringUtils.isNotBlank(dict.getExtend().getExtendS2())) {
            //锁当前检查类型
            lockKeyName_lastExamNo = CacheUtil.cacheKey(FMT_LOCKKEY_LASTEXAMNO, examItemCode);
            //当前检查类型检查号格式
            examNoPattern = dict.getExtend().getExtendS2();
            //
            configKey_lastExamNo = String.format(FMT_CONFIGKEY_LASTEXAMNO, examItemCode);
        } else {
            //锁全局
            lockKeyName_lastExamNo = LOCKKEY_LASTEXAMNO;
            //全局检查类型检查号格式
            examNoPattern = bridgeService.getSysConfigByKey(CONFIGKEY_EXAMNOPATTERN);
            //
            configKey_lastExamNo = CONFIGKEY_LASTEXAMNO;
        }
        //默认格式
        if(StringUtils.isBlank(examNoPattern)) {
            examNoPattern = DEF_EXAMNOPATTERN;
        }
        //锁
        if(null != redisCache.lock(lockKeyName_lastExamNo, lockKeyName_lastExamNo)) {
            try {
                //从系统参数获取最后检查号
                SysConfig cfg = new SysConfig();
                cfg.setConfigKey(configKey_lastExamNo);
                SysConfig cfg0 = configService.selectConfig(cfg);
                String lastExamNo = null != cfg0? cfg0.getConfigValue() : null;
                //
                lastExamNo = ExamNoUtil.make(lastExamNo, examNoPattern);
                //存在-更新
                String lastExamNoMem = String.format(ExamNoUtil.examNoHistFmt, examNoPattern, lastExamNo);
                if(null != cfg0) {
                    cfg0.setConfigValue(lastExamNoMem);
                    cfg0.setStatus(1);
                    configService.updateConfig(cfg0);
                } else {
                    cfg.setConfigValue(lastExamNoMem);
                    cfg.setConfigName("当前最新检查号");
                    cfg.setConfigType("N");
                    cfg.setStatus(1);
                    configService.insertConfig(cfg);
                }
                //
                return lastExamNo;
            } catch (Exception err) {
                log.error(err.getMessage(), err);
                throw err;
            } finally {
                redisCache.unlock(lockKeyName_lastExamNo);
            }
        }
        return null;
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public int delete(Long id) {
       return mapper.delete(id);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public int updateActiveStatus(ExamInfo entity) {
        return mapper.updateActiveStatus(entity);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public int undoDelete(Long id) {
        return mapper.undoDelete(id);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public int updateResultStatus(ExamInfo entity) {
        /*在调用时检查SysDictData resultStatus;
        String resultStatusCode;
        if(null == entity.getId()
                || null == (resultStatus = entity.getResultStatus())
                || !NumberUtils.isDigits(resultStatusCode = resultStatus.getDictValue())) {
            throw new RuntimeException("请提供检查id和检查状态。");
        }
        //读取检查信息，1验证检查是否存在，2-对比当前状态和更新的状态
        ExamInfo entity0 = selectOne(entity);
        if(null == entity0) {
            throw new RuntimeException("该检查不存在。");
        }

        //状态按状态升序走
        //登记为0，已检查为1，已报告为2，已审核为3，复审状态为4，已打印为5，已完成为6，已取消为10

        SysDictData resultStatus0 = entity0.getResultStatus();
        String resultStatusCode0 = null;
        //状态代码按检查执行顺序一致：0-登记完成，1-开始检查，2-报告书写完成。。。，只能往后更新
        //10-取消检查，可以恢复取消的检查
        if(null != resultStatus0 && NumberUtils.isDigits(resultStatusCode0 = resultStatus0.getDictValue())) {
            int resultStatusCancel = Integer.valueOf(ExamInfoVo.ResultStatus.CANCEL.getValue())
                    , resultStatusRegist = Integer.valueOf(ExamInfoVo.ResultStatus.REGIST.getValue())
                    , resultStatusValue = Integer.valueOf(resultStatusCode)
                    , resultStatusValue0 = Integer.valueOf(resultStatusCode0);
            //状态不变
            if(resultStatusValue == resultStatusValue0) {
                return 0;
            }
            //
            if(resultStatusRegist == resultStatusValue && resultStatusCancel != resultStatusValue0
                    || resultStatusRegist != resultStatusValue && resultStatusValue0 > resultStatusValue) {
                String dictLabel = DictUtils.getDictLabel(UisConst.DICT_TYPE_RESULT_STATUS, resultStatusCode0);
                throw new RuntimeException(String.format("无法更新为请求的状态，原因：该检查状态为%s。", dictLabel));
            }
        }*/
        //更新为检查状态，当前登录医生为检查医生
        //if(null != resultStatusCode0 && ExamInfoVo.ResultStatus.EXAM.is(resultStatusCode0)) {
        //    if(null == entity.getExamDoctorsCode()) {
        //
        //    }
        //}
        //排队信息
        /*CallInfoVo callInfo = callService.selectByExam(entity.getId());
        if(null != callInfo && (null != callInfo.getFirstCallTime() || null != callInfo.getLastCallTime())) {
            Date now = DateUtils.getNowDate();
            if(null == callInfo.getFirstEndCallTime()) {
                callInfo.setFirstEndCallTime(now);
            } else {
                callInfo.setLastEndCallTime(now);
            }
            callService.insertOrUpdate(callInfo);
        }*/

        return mapper.updateResultStatus(entity);
    }

    public int updateExamPrerequire(ExamInfo entity){
        return mapper.updateExamPrerequire(entity);
    }

    public int updateExamPeriod(ExamInfo entity) {
        return mapper.updateExamPeriod(entity);
    }

    /**
     * 医生姓名和工号对不上，清除工号
     * @param examInfo
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @DataSource(value = DataSourceType.MASTER)
    public void theDoctors(ExamInfo examInfo) {
        if(null == examInfo) {
            return;
        }
        Class<ExamInfo> clz = ExamInfo.class;
        String[] docsField = new String[]{"reqDoctor", "examDoctorsName", "consultantsName", "recordersName", "reportDoctor", "anesDoctor", "operNurse"};
        for(String fld : docsField) {
            try {
                Method fx = clz.getMethod("get".concat(fld.substring(0, 1).toUpperCase().concat(fld.substring(1))));
                //
                Class<?> fldClz = fx.getReturnType();
                if(fldClz == SysUser.class) {
                    SysUser su = (SysUser)fx.invoke(examInfo);
                    String userName, nickName;
                    if(null == su || org.apache.commons.lang3.StringUtils.isBlank(userName = su.getUserName())) {
                        continue;
                    }
                    if(org.apache.commons.lang3.StringUtils.isBlank(nickName = su.getNickName())) {
                        su.setUserName(null);
                        continue;
                    }
                    //工号和姓名是否对上
                    SysUser u = sysUserMapper.selectUserByUserName(userName);
                    if(null == u || !nickName.equals(u.getNickName())) {
                        su.setUserName(null);
                    }
                } else if(fldClz == String.class) {
                    //工号getter
                    Method fxc = clz.getMethod(fx.getName().replace("Name", "Code"));
                    String userName = (String)fxc.invoke(examInfo);
                    if(org.apache.commons.lang3.StringUtils.isBlank(userName)) {
                        continue;
                    }
                    //工号setter
                    Method fxcs = clz.getMethod("set" + fxc.getName().substring(3), String.class);
                    //没有姓名
                    String nickName = (String)fx.invoke(examInfo);
                    if(org.apache.commons.lang3.StringUtils.isBlank(nickName)) {
                        fxcs.invoke(examInfo, new String[]{null});
                        continue;
                    }
                    //工号姓名数不一样
                    String[] userNames = userName.split(","), nickNames = nickName.split("\\s*,\\s*");
                    if(userNames.length != nickNames.length) {
                        fxcs.invoke(examInfo, new String[]{null});
                        continue;
                    }
                    //工号姓名是否对上
                    for(int i = 0; i < userNames.length; i ++) {
                        String un = userNames[i];
                        SysUser u = sysUserMapper.selectUserByUserName(un);
                        if(!nickNames[i].equals(u.getNickName())) {
                            fxcs.invoke(examInfo, new String[]{null});
                            break;
                        }
                    }
                }
            } catch (Exception err) {
                log.error(err.getMessage(), err);
            }
        }
    }

    public int sendReportStatus(Long id, Integer statusOfSendReport) {
        return mapper.sendReportStatus(id, statusOfSendReport);
    }
}
