package yyy.xxx.simpfw.module.gis.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import yyy.xxx.simpfw.common.annotation.Excel;
import yyy.xxx.simpfw.module.pacs.entity.BaseEntity;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.entity.Patient;

import java.util.Date;

/**
 * 预约排队对象 d_booked_info
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
public class DBookedInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private ExamInfo examInfo;

    /** 检查号 */
    @Excel(name = "检查号")
    private String examNo;

    /** 预约ID */
    @Excel(name = "预约ID")
    private String bookedId;

    /** 患者主索引 */
    @Excel(name = "患者主索引")
    private String mpi;

    private Patient patientInfo;

    /** 暂时不用 */
    @Excel(name = "暂时不用")
    private String registNo;

    /** 病历号 */
    @Excel(name = "病历号")
    private String medicalRecordNo;


    /** 姓名	 */
    @Excel(name = "姓名	")
    private String name;

    /** 登记号	 */
    @Excel(name = "登记号	")
    private String regNo;

    /** 医保号	 */
    @Excel(name = "医保号	")
    private String insuranceNo;


    /** 联系电话	 */
    @Excel(name = "联系电话	")
    private String phone;



    /** 检查项目代码 */
    @Excel(name = "检查项目代码")
    private String examItemCode;


    /** 检查费用	 */
    @Excel(name = "检查费用	")
    private Double examCost;


    /** 住院号	 */
    @Excel(name = "住院号	")
    private String inpNo;


    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String inpRoomCode;

    /** 床号	 */
    @Excel(name = "床号	")
    private String bedNo;

    /** 门诊号	 */
    @Excel(name = "门诊号	")
    private String outpNo;

    /** 就诊号	 */
    @Excel(name = "就诊号	")
    private String admNo;

    /** 就诊流水号 */
    @Excel(name = "就诊流水号")
    private String admSeriesNum;

    /** 医嘱号	 */
    @Excel(name = "医嘱号	")
    private String ordId;

    /** 医嘱类型代码	 */
    @Excel(name = "医嘱类型代码	")
    private String ordPriorityCode;

    /** 医嘱类型 */
    @Excel(name = "医嘱类型")
    private String ordPriority;

    /** 医嘱名称	 */
    @Excel(name = "医嘱名称	")
    private String ordName;

    /** 医嘱状态	 */
    @Excel(name = "医嘱状态	")
    private String ordBillStatus;

    /** 医嘱状态 */
    @Excel(name = "医嘱状态")
    private String ordStatus;

    /** 收费状态	 */
    @Excel(name = "收费状态	")
    private String chargeStatus;

    /** 就诊流水号	 */
    @Excel(name = "就诊流水号	")
    private String admSerialNum;

    /** 就诊类型	有就诊字典表 */
    @Excel(name = "就诊类型	有就诊字典表")
    private String diagnosisType;


    private Date appointTime;

    /** 预约日期 */
    @Excel(name = "预约日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date appointExamDate;



    /** 预约时间 */
    @Excel(name = "预约时间")
    private String appointExamTime;

    public String getExamNo() {
        return examNo;
    }

    public void setExamNo(String examNo) {
        this.examNo = examNo;
    }

    public String getBookedId() {
        return bookedId;
    }

    public void setBookedId(String bookedId) {
        this.bookedId = bookedId;
    }

    public String getMpi() {
        return mpi;
    }

    public void setMpi(String mpi) {
        this.mpi = mpi;
    }

    public String getRegistNo() {
        return registNo;
    }

    public void setRegistNo(String registNo) {
        this.registNo = registNo;
    }

    public String getMedicalRecordNo() {
        return medicalRecordNo;
    }

    public void setMedicalRecordNo(String medicalRecordNo) {
        this.medicalRecordNo = medicalRecordNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    public String getInsuranceNo() {
        return insuranceNo;
    }

    public void setInsuranceNo(String insuranceNo) {
        this.insuranceNo = insuranceNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getExamItemCode() {
        return examItemCode;
    }

    public void setExamItemCode(String examItemCode) {
        this.examItemCode = examItemCode;
    }

    public Double getExamCost() {
        return examCost;
    }

    public void setExamCost(Double examCost) {
        this.examCost = examCost;
    }

    public String getInpNo() {
        return inpNo;
    }

    public void setInpNo(String inpNo) {
        this.inpNo = inpNo;
    }

    public String getInpRoomCode() {
        return inpRoomCode;
    }

    public void setInpRoomCode(String inpRoomCode) {
        this.inpRoomCode = inpRoomCode;
    }

    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public String getOutpNo() {
        return outpNo;
    }

    public void setOutpNo(String outpNo) {
        this.outpNo = outpNo;
    }

    public String getAdmNo() {
        return admNo;
    }

    public void setAdmNo(String admNo) {
        this.admNo = admNo;
    }

    public String getAdmSeriesNum() {
        return admSeriesNum;
    }

    public void setAdmSeriesNum(String admSeriesNum) {
        this.admSeriesNum = admSeriesNum;
    }

    public String getOrdId() {
        return ordId;
    }

    public void setOrdId(String ordId) {
        this.ordId = ordId;
    }

    public String getOrdPriorityCode() {
        return ordPriorityCode;
    }

    public void setOrdPriorityCode(String ordPriorityCode) {
        this.ordPriorityCode = ordPriorityCode;
    }

    public String getOrdPriority() {
        return ordPriority;
    }

    public void setOrdPriority(String ordPriority) {
        this.ordPriority = ordPriority;
    }

    public String getOrdName() {
        return ordName;
    }

    public void setOrdName(String ordName) {
        this.ordName = ordName;
    }

    public String getOrdBillStatus() {
        return ordBillStatus;
    }

    public void setOrdBillStatus(String ordBillStatus) {
        this.ordBillStatus = ordBillStatus;
    }

    public String getOrdStatus() {
        return ordStatus;
    }

    public void setOrdStatus(String ordStatus) {
        this.ordStatus = ordStatus;
    }

    public String getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    public String getAdmSerialNum() {
        return admSerialNum;
    }

    public void setAdmSerialNum(String admSerialNum) {
        this.admSerialNum = admSerialNum;
    }

    public String getDiagnosisType() {
        return diagnosisType;
    }

    public void setDiagnosisType(String diagnosisType) {
        this.diagnosisType = diagnosisType;
    }

    public Date getAppointTime() {
        return appointTime;
    }

    public void setAppointTime(Date appointTime) {
        this.appointTime = appointTime;
    }

    public Date getAppointExamDate() {
        return appointExamDate;
    }

    public void setAppointExamDate(Date appointExamDate) {
        this.appointExamDate = appointExamDate;
    }

    public String getAppointExamTime() {
        return appointExamTime;
    }

    public void setAppointExamTime(String appointExamTime) {
        this.appointExamTime = appointExamTime;
    }

    public ExamInfo getExamInfo() {
        return examInfo;
    }

    public void setExamInfo(ExamInfo examInfo) {
        this.examInfo = examInfo;
    }

    public Patient getPatientInfo() {
        return patientInfo;
    }

    public void setPatientInfo(Patient patientInfo) {
        this.patientInfo = patientInfo;
    }
}
