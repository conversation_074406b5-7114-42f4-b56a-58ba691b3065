package yyy.xxx.simpfw.module.gis.web;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.http.conn.HttpHostConnectException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;

import yyy.xxx.simpfw.common.annotation.Log;
import yyy.xxx.simpfw.common.constant.HttpStatus;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.HttpRequestConfig;
import yyy.xxx.simpfw.common.core.domain.HttpResult;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.BusinessType;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.bo.CallingServiceProp;
import yyy.xxx.simpfw.module.pacs.entity.CallInfo;
import yyy.xxx.simpfw.module.pacs.service.CallInfoService;
import yyy.xxx.simpfw.module.pacs.service.CallInfoV2Service;
import yyy.xxx.simpfw.module.pacs.service.EquipRoomService;
import yyy.xxx.simpfw.module.pacs.service.ExamInfoService;
import yyy.xxx.simpfw.module.pacs.service.IRemoteQueueService;
import yyy.xxx.simpfw.module.pacs.service.QueueInterService;
import yyy.xxx.simpfw.module.pacs.service.WorklistService;
import yyy.xxx.simpfw.module.pacs.utils.JsonUtil;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.system.service.ISysConfigService;

@RestController
@RequestMapping("/exammanagement/queue")
public class CallInfoController extends BaseController {

    @Autowired
    private CallInfoService service;

    @Autowired
    private ExamInfoService examInfoService;

    @Autowired
    private IRemoteQueueService remoteQueueService;
    
    @Autowired
    private HttpClientService httpClientService;
    //
    @Autowired private RedisCache redisCache;
    //机房信息
    @Autowired private EquipRoomService roomService;
    //worklist
    @Autowired private WorklistService worklistService;

    @Autowired private QueueInterService queueInterService;
    //呼叫服务配置
    private CallingServiceProp callingService;

    @RequestMapping("/list")
    public TableDataInfo list(CallInfoVo param) {
        startPage();
        List<CallInfoVo> list = service.selectList(param);
        return getDataTable(list);
    }

    @GetMapping(value = "/get")
    public AjaxResult get(CallInfo param) {
        return AjaxResult.success(service.selectOne(param));
    }

    /**
     * 新增
     */
    @PostMapping(value = "/save")
    public AjaxResult save(@RequestBody CallInfo entity) {

        return toAjax(service.insertOrUpdate(entity));
    }

    /**
     * 修改
     */
    @PutMapping(value = "/update")
    public AjaxResult update(@RequestBody CallInfo entity) {
        return toAjax(service.insertOrUpdate(entity));
    }

    /**
     * 删除
     */
    @DeleteMapping("/del/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        return toAjax(service.delete(id));
    }

    /**
     * 调用呼叫服务
     * 生成worklist
     */
    @Log(title = "检查呼叫", businessType = BusinessType.UPDATE)
    @GetMapping("/call")
    public AjaxResult call(Long examInfoId, String callRoomCode) {

        try {
            if(null == examInfoId) {
                return AjaxResult.error("请提供呼叫的检查。");
            }
            //是否配置呼叫服务
            checkCallingService();
            //获取检查信息
            ExamInfoVo examInfo = examInfoService.selectById(examInfoId);
            if(null == examInfo) {
                return AjaxResult.error("无法获取该检查信息。");
            }
            //是否指定机房
            //if(null == examInfo.getEquipRoom()) {
            //    return AjaxResult.error("未分配检查机房。");
            //}
            //获取排队信息
            CallInfoVo callInfoParam = new CallInfoVo();
            callInfoParam.setExamInfo(examInfo);
            List<CallInfoVo> calls = service.selectList(callInfoParam);
            CallInfo call0 = null != calls && !calls.isEmpty()? calls.get(0) : null;
            if(null == call0) {
                return AjaxResult.error("无法获取该检查排队信息。");
            }
            call0.setExamInfo(examInfo);
            //获取呼叫中检查，避免重复呼叫
            Set<ExamInfoVo> callingItems = redisCache.getCacheSet(CallInfoService.callingExamCacheKey);
            if(logger.isDebugEnabled()) { logger.debug("呼叫中检查: {} > {}", examInfoId, JSON.toJSONString(callingItems)); }
            if(null != callingItems && callingItems.stream().anyMatch(e -> e.getId().equals(examInfoId))) {
                return AjaxResult.success().put("errM", "该检查正在呼叫，请稍后再呼或呼叫其它检查。");
            }
            updateCallInfo(call0, callRoomCode);
            //生成worklist
            //addToWorklist(examInfo);
            //更新检查工作状态为检查中
            //SysDictData resultStatus = new SysDictData();
            //resultStatus.setDictValue(ExamInfoVo.ResultStatus.EXAM.getValue());
            //examInfo.setResultStatus(resultStatus);
            //examInfoService.updateResultStatus(examInfo);
            //调用呼叫服务
            invokeCalling(call0);
            //返回最新排队信息
            callInfoParam.setId(call0.getId());
            call0 = service.selectOne(callInfoParam);
            return AjaxResult.success(call0);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            if(err instanceof HttpHostConnectException) {
                errM = "无法连接叫号服务。";
            }
            return AjaxResult.error(errM);
        }
    }
    
    // --------------------------------------------
    // --------------------------------------------
    // --------------------------------------------
    
	@Autowired
	private CallInfoV2Service callInfoV2Service;
	
	// 参数表 sys_config
	@Autowired
	private ISysConfigService configService;
    
    /**
     * 调用 rh-uis-queue 服务进行叫号
     * @date 20230418
     */
    @Log(title = "检查呼叫", businessType = BusinessType.UPDATE)
    @GetMapping("/call2")
    public AjaxResult call2(Long examInfoId, String callRoomCode) {
    	// 获取检查信息
        ExamInfoVo examInfo = examInfoService.selectById(examInfoId);
        if(null == examInfo) {
            return AjaxResult.error("无法获取该检查信息。");
        }
        
        // 获取排队信息
        CallInfoVo callInfoParam = new CallInfoVo();
        callInfoParam.setExamInfo(examInfo);
        List<CallInfoVo> calls = service.selectList(callInfoParam);
        CallInfo call0 = null != calls && !calls.isEmpty()? calls.get(0) : null;
        if (null == call0) {
            return AjaxResult.error("无法获取该检查排队信息。");
        }
        
        callRoomCode = StringUtils.trim(callRoomCode);
		if (StringUtils.isBlank(callRoomCode)) {
			return AjaxResult.error("请选择检查房间。");
		}
		String callNo = call0.getCallNo();
		
		String url = String.format("%s%s", getQueueServiceBaseUrl(), "/v1/queue/selectCall");
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("equipRoomCode", callRoomCode);
		paramMap.put("callNo", callNo);
		paramMap.put("doctorCode", SecurityUtils.getLoginUser().getUser().getUserName());
		String params = JsonUtil.writeAsJsonString(paramMap);
		logger.debug("选呼参数: {}", params);
		Map<String, Object> dataMap = callInfoV2Service.executeJsonRestRequest(url, new HashMap<String, String>(), params, false);
		
		// 返回最新排队信息
        callInfoParam.setId(call0.getId());
        call0 = service.selectOne(callInfoParam);
        return AjaxResult.success(call0);
        
		// return AjaxResult.success(dataMap);
        
    }
    
	private String getQueueServiceBaseUrl() {
		String queueServiceBaseUrl = configService.selectConfigByKey(remoteQueueService.getQueueServiceBaseUrlKey());
		if (StringUtils.isBlank(queueServiceBaseUrl)) {
			logger.warn("请在参数表中配置排队叫号服务的baseUrl, 参数key: {}", queueServiceBaseUrl);
			throw new RuntimeException("暂未配置排队叫号服务的baseUrl");
		}
		return queueServiceBaseUrl;
	}
	
	// --------------------------------------------
	// --------------------------------------------
    // --------------------------------------------
    
    /**
     * 更新呼叫信息
     * @param call0
     * @param callRoomCode
     */
    private int updateCallInfo(CallInfo call0, String callRoomCode) {
        return service.call(call0, callRoomCode);
    }
    /**
     * 将检查信息放进工作列表
     */
    private void addToWorklist(ExamInfoVo examInfo) {
    	worklistService.make(examInfo);
    }
    /**
     * 检查呼叫服务配置
     */
    private void checkCallingService() {
        if(null == callingService) {
            //呼叫系统服务
            callingService = queueInterService.getInterInfo();
        }
    }
    /**
     * 调用呼叫服务
     * @throws Exception 
     */
    private void invokeCalling(CallInfo call0) throws Exception {
    	Set<ExamInfoVo> callingItems = redisCache.getCacheSet(CallInfoService.callingExamCacheKey);
    	ExamInfoVo examInfo = (ExamInfoVo)call0.getExamInfo();
    	//
        String ver = null != callingService.getVer()? callingService.getVer().getRest() : null;
        if(StringUtils.isNotBlank(ver)) { ver = Const.slash + ver; }
    	String url = String.format("http://%s%s/queue/call", callingService.getServer(), ver);
        //账号
        Map<String, Object> headers = new HashMap<>(1);
        String auth = callingService.getAuth();
        headers.put("X-Auth", auth);
        //调用超时时间
        HttpRequestConfig requestConfig = new HttpRequestConfig().setSocketTimeout(30 * 1000);
        //呼叫内容
        Map<String, Object> params = new HashMap<>(1);
        String sentence = String.format("请-%s号%s到%s检查。"
                , call0.getCallNo(), examInfo.getPatientInfo().getName(), call0.getCallRoom().getRoomName());
        params.put("sentence", sentence);
        //
        if(null == callingItems) {
            callingItems = new HashSet<>();
        }
        if(logger.isDebugEnabled()) { logger.debug("添加前: {} > {}", examInfo.getId(), JSON.toJSONString(callingItems)); }
        if(null != examInfo.getCallInfo()) {
            examInfo.getCallInfo().setCallRoom(call0.getCallRoom());
        }
        callingItems.add(examInfo);
        if(logger.isDebugEnabled()) { logger.debug("添加后: {} > {}", examInfo.getId(), JSON.toJSONString(callingItems)); }
        try {
            cacheCalling(callingItems);
            //调用呼叫服务
            HttpResult res = httpClientService.doPost(url, params, headers, requestConfig);
            if (HttpStatus.SUCCESS != res.getCode()) {
                String errM = res.getBody();
                logger.error("调用较好服务错误{}={}", res.getCode(), errM);
                //return new AjaxResult(res.getCode(), errM);
                throw new RuntimeException(errM);
            }
        } finally {
            //重新读取缓存，避免呼叫进行中有呼叫假如造成堵塞
            callingItems = redisCache.getCacheSet(CallInfoService.callingExamCacheKey);
            if(logger.isDebugEnabled()) { logger.debug("删除前: {} > {}", examInfo.getId(), JSON.toJSONString(callingItems)); }
            callingItems.remove(examInfo);
            if(logger.isDebugEnabled()) { logger.debug("删除后: {} > {}", examInfo.getId(), JSON.toJSONString(callingItems)); }
            //
            cacheCalling(callingItems);
        }
    }
    /**
     * 正在呼叫的检查
     * @param callingItems
     */
    private void cacheCalling(Set<ExamInfoVo> callingItems) {
        //先清除
        boolean v = redisCache.deleteObject(CallInfoService.callingExamCacheKey);
        if(logger.isDebugEnabled()) { logger.debug("删除结果={}", v); }
        if(null != callingItems && callingItems.size() > 0) {
            redisCache.setCacheSet(CallInfoService.callingExamCacheKey, callingItems);
            redisCache.expire(CallInfoService.callingExamCacheKey, 5, TimeUnit.MINUTES);
        }
    }

    /**
     * 过号
     * @param id
     * @return
     */
    @GetMapping("/past/{id}")
    public AjaxResult past(@PathVariable Long id) {
        CallInfo callInfo = new CallInfo();
        callInfo.setId(id);
        callInfo = service.selectOne(callInfo);
        //worklistService.remove(callInfo.getExamInfo());
        try {
        	// 原来的处理方法
            // return toAjax(service.past(callInfo));
            
        	// update@20230420；调用 rh-uis-queue服务进行恢复排队
        	String roomCode = callInfo.getCallRoom().getRoomCode();
    		String callNo = callInfo.getCallNo();
    		String url = String.format("%s%s", getQueueServiceBaseUrl(), "/v1/queue/resetCall");
    		Map<String, String> paramMap = new HashMap<String, String>();
    		paramMap.put("equipRoomCode", roomCode);
    		paramMap.put("callNo", callNo);
    		paramMap.put("doctorCode", SecurityUtils.getLoginUser().getUser().getUserName());
    		String params = JsonUtil.writeAsJsonString(paramMap);
    		Map<String, Object> dataMap = callInfoV2Service.executeJsonRestRequest(url, new HashMap<String, String>(), params, false);
    		logger.debug("恢复排队，返回结果：{}", dataMap);
    		return toAjax(1);
    		
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 更改机房
     * @param fromId
     * @param fromEquipRoomCode
     * @param toEquipRoomCode
     * @return
     */
    @Log(title = "患者检查", businessType = BusinessType.UPDATE)
    @RequestMapping("/changeEquipRoom")
    public AjaxResult changeEquipRoom(Long fromId, String fromEquipRoomCode, String toEquipRoomCode) {
        try {
            service.changeEquipRoom(fromId, fromEquipRoomCode, toEquipRoomCode);
            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

}
