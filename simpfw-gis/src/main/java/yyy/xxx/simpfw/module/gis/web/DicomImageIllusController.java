package yyy.xxx.simpfw.module.gis.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import yyy.xxx.common.net.storage.utils.FileUtils;
import yyy.xxx.common.net.storage.utils.StreamUtils;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.utils.http.WebUtil;
import yyy.xxx.simpfw.module.gis.service.DicomImageIllusService;
import yyy.xxx.simpfw.module.gis.utils.BodypartsIllusUtil;
import yyy.xxx.simpfw.module.gis.vo.DicomImageIllus;
import yyy.xxx.simpfw.module.pacs.entity.ExamAttachment;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Controller
@RequestMapping("/dicomImage/illus")
public class DicomImageIllusController extends BaseController {

	@Autowired
	private DicomImageIllusService service;

	/**
	 * 保存图像修改
	 * @param examInfoId 检查记录id
	 * @param path 附件对应影像文件信息：“{StudyInstanceUID}/{SeriesInstanceUID}/{SOPInstanceUID}”
	 * @param noteInfo 示意图信息：”{illus:示意图名字,mark:位置标志信息}“
	 * @param file 图像文件数据
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/save")
	@ResponseBody
	public AjaxResult save(@RequestParam("examInfoId") Long examInfoId
			, @RequestParam("path") String path
			, @RequestParam("noteInfo") String noteInfo
			, @RequestParam("data") MultipartFile file) throws Exception {

		ExamAttachment atta = new ExamAttachment();
		atta.setExamInfoId(examInfoId);
		atta.setPath(path);
		atta.setData(StreamUtils.readBytes(file.getInputStream()));
		atta.setNoteInfo(noteInfo);
		DicomImageIllus illus = service.save(atta);

		AjaxResult r = AjaxResult.success(illus);

		return r;
	}

	/**
	 * 已有部位示意图列表
	 * @param examItem 检查项目
	 */
	@RequestMapping(value = "/bodyparts/list")
	@ResponseBody
	public AjaxResult listIllus(SysDictData examItem) {
		try {
			//部位示意图目录
			Path folder = BodypartsIllusUtil.illusFolder(examItem.getDictValue());
			if(!Files.isDirectory(folder)) {
				folder = BodypartsIllusUtil.illusFolder();
			}
			//读取文件列表
			try(Stream<Path> s = Files.list(folder)) {
				final String folderName = folder.getFileName().toString(), fileNameExt = ".png";
				List<String> filesName = new ArrayList<>();
				s.forEach(e -> {
					String name = e.getFileName().toString();
					if(name.toLowerCase().endsWith(fileNameExt)) {
						name = name.substring(0, name.length() - fileNameExt.length());
						name = String.format("%s/%s", folderName, name);
						filesName.add(name);
					}
				});
				return AjaxResult.success(filesName);
			}
		} catch (Exception err) {
			logger.error(err.getMessage(), err);
			return AjaxResult.error(err.getMessage());
		}
	}

	/**
	 * 读取部位示意图
	 * @param request 请求
	 * @param response 返回
	 * @param name 示意图文件名
	 */
	@RequestMapping(value = "/bodyparts/locate/{fold}/{name}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
	public void locateIllus(HttpServletRequest request, HttpServletResponse response
			, @PathVariable("fold") String fold
			, @PathVariable("name") String name) {
		try {
			WebUtil.setStreamContent(response, null);
			StreamUtils.write(FileUtils.openFile(BodypartsIllusUtil.illusPath(fold, name)), response.getOutputStream());
		} catch (Exception err) {
			logger.error(err.getMessage(), err);
		}
	}
}
