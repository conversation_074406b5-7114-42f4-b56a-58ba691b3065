package yyy.xxx.simpfw.module.gis.web;

import java.lang.reflect.Field;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yyy.xxx.simpfw.common.annotation.Log;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysDept;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.enums.BusinessType;

import yyy.xxx.simpfw.common.utils.poi.ExcelUtil;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.framework.web.service.PermissionService;
import yyy.xxx.simpfw.module.gis.entity.DBookedInfo;
import yyy.xxx.simpfw.module.gis.service.EisExamInfoService;
import yyy.xxx.simpfw.module.gis.service.IDBookedInfoService;
import yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.dict.ExamEnum;
import yyy.xxx.simpfw.module.pacs.dict.RegistWay;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.entity.CallInfo;
import yyy.xxx.simpfw.module.pacs.entity.ExamInfo;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.BridgeService;
import yyy.xxx.simpfw.module.pacs.vo.CallInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.system.service.*;

/**
 * 预约排队Controller
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@RestController
@RequestMapping("/exammanagement/booked")
public class DBookedInfoController extends BaseController
{
    @Autowired
    private IDBookedInfoService dBookedInfoService;

    @Autowired
    private EisExamInfoService eisExamInfoService;

    @Autowired private PatientService patientService;

    @Autowired private OrdInterService interfaceService;

    @Autowired private ISysDeptService deptService;

    @Autowired private QueueInterService queueInterService;

    @Autowired private CallInfoService callService;

    // -----------------
    // 20230425 rh-uis-queue 服务
    @Autowired
    private IRemoteQueueService remoteQueueService;

    /**
     * 查询预约排队列表
     */
    @PostMapping("/list")
    public TableDataInfo list(DBBookedInfoVo dbBookedInfoVo)
    {
        startPage();
        List<DBBookedInfoVo> list = dBookedInfoService.selectDBookedAndExamList(dbBookedInfoVo);
       // List<DBookedInfo> list = dBookedInfoService.selectDBookedInfoList(dBookedInfo);
        return getDataTable(list);
    }

    /**
     * 导出预约排队列表
     */
//    @Log(title = "预约排队", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DBookedInfo dBookedInfo)
//    {
//        List<DBookedInfo> list = dBookedInfoService.selectDBookedInfoList(dBookedInfo);
//        ExcelUtil<DBookedInfo> util = new ExcelUtil<DBookedInfo>(DBookedInfo.class);
//        util.exportExcel(response, list, "预约排队数据");
//    }

    /**
     * 获取预约排队详细信息
     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return AjaxResult.success(dBookedInfoService.selectDBookedInfoById(id));
//    }

    @GetMapping(value = "/weekInfoList")
    public AjaxResult getWeekInfoList()
    {
        return AjaxResult.success(dBookedInfoService.selectWeekInfoList());
    }

    /**
     * 新增预约排队
     */
    @PostMapping(value = "/save")
    @PreAuthorize("@ss.hasAnyPermi('exam-info:edit')")
    public AjaxResult add(@RequestBody DBBookedInfoVo dbBookedInfoVo) {

        ExamInfo entity = dbBookedInfoVo.getExamInfo();

        try {
            //
            boolean isNew = entity.isNew();
            //状态
            if(!isNew) {
                ExamInfo exists = eisExamInfoService.selectById(entity.getId());
                if(null == exists) {
                    return AjaxResult.error("数据不存在。");
                }
                //状态为登记、检查和报告时可修改
                String resultStatusCode;
                if(null != exists.getResultStatus()
                        && StringUtils.isNotBlank(resultStatusCode = exists.getResultStatus().getDictValue())
                        && !ResultStatus.REGIST.is(resultStatusCode)
                        && !ResultStatus.EXAM.is(resultStatusCode)
                        && !ResultStatus.REPORT.is(resultStatusCode)) {
                    return AjaxResult.error("无法更新检查信息，原因：当前检查状态为%s", entity.getResultStatus().getDictLabel());
                }
                //医嘱退费
                String existsOrdId = exists.getOrdId(), ordId = entity.getOrdId();
                if(logger.isDebugEnabled()) {
                    if(StringUtils.isNotBlank(existsOrdId)) {
                        logger.debug("医嘱 {} {} => {}", entity.getExamNo(), existsOrdId, ordId);
                    }
                }
                if(StringUtils.isNotBlank(existsOrdId) && !existsOrdId.equals(ordId)) {
                    String[] existsOrdsId = existsOrdId.split(Const.at)
                            , ordsId = StringUtils.isNotBlank(ordId)? ordId.split(Const.at) : null;
                    //找出退费的医嘱
                    for(String eoi : existsOrdsId) {
                        if(null == ordsId || !ArrayUtils.contains(ordsId, eoi)) {
                            interfaceService.cancelFeeApp(exists, getLoginUser().getUser(), eoi);
                        }
                    }
                }
            } else {
                entity.setCreator(getLoginUser().getUser());
            }
            //生成检查号
            if(StringUtils.isBlank(entity.getExamNo())) {
                //生成检查号
                entity.setExamNo(eisExamInfoService.makeExamNo(entity.getExamItem().getDictValue()));
            } else {
                //检查号是否存在
                ExamInfo p = new ExamInfoVo();
                p.setExamNo(entity.getExamNo());
                List<ExamInfoVo> rows = eisExamInfoService.selectList(p);
                if(null != rows && !rows.isEmpty()) {
                    if(isNew || !entity.getId().equals(rows.get(0).getId())) {
                        return AjaxResult.error("该检查号已存在。");
                    }
                }
            }
            //病历号
            if(StringUtils.isBlank(entity.getPatientInfo().getMedicalRecordNo())) {
                entity.getPatientInfo().setMedicalRecordNo(patientService.makeMedicalRecordNo());
            }
            //
            eisExamInfoService.theDoctors(entity);
            //申请科室编码和名称对不上，认为是手输，不保存编码
            SysDept reqDept = entity.getReqDept();
            String reqDeptCode = null;
            if(null != reqDept) {
                if(StringUtils.isNotBlank(reqDept.getDeptCode())) {
                    String reqDeptName = reqDept.getDeptName();
                    if (StringUtils.isNotBlank(reqDeptName)) {
                        SysDept dept = deptService.selectDeptByCode(reqDept.getDeptCode());
                        if (null != dept && reqDeptName.equals(dept.getDeptName())) {
                            reqDeptCode = reqDept.getDeptCode();
                        }
                    }
                }
                reqDept.setDeptCode(reqDeptCode);
            }
            //保存登记信息
            dBookedInfoService.saveOrUpdate(dbBookedInfoVo);
            //回写接口服务医嘱状态
            /*if(logger.isInfoEnabled() && null != entity.getCreator() && "TJauto".equalsIgnoreCase(entity.getCreator().getUserName())) {
                logger.debug("isNew={}, registWay={}", isNew, entity.getRegistWay());
            }*/
            //就诊类别不是体检的向his登记
            if(isNew && RegistWay.INTER.is(entity.getRegistWay())
                    && (null == entity.getInpType() || !ExamEnum.InpTypeBodyExam.equals(entity.getInpType().getDictValue()))) {
                interfaceService.regOrd(entity);
            }
            //排队叫号接口 生成排队号时调用
//            try {
//                // queueInterService.enqueue(entity.getCallInfo());
//                queueInterService.enqueueV2(entity.getCallInfo(), remoteQueueService.getQueueServiceBaseUrlKey());
//            } catch (Exception err) {
//                logger.error(err.getMessage(), err);
//            }

            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 修改预约排队
     */
    @PostMapping(value = "/update")
    @PreAuthorize("@ss.hasAnyPermi('exam-info:edit')")
    public AjaxResult edit(@RequestBody DBBookedInfoVo dbBookedInfoVo)
    {
        return add(dbBookedInfoVo);
//        return toAjax(dBookedInfoService.updateDBookedInfo(dBookedInfo));
    }

    /**
     * 更新预约时间
     */
    @Log(title = "更新预约时间", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/updateAppointTimes")
    public AjaxResult updateAppointExamTime(@RequestBody List<DBookedInfo> list)
    {
        return AjaxResult.success(dBookedInfoService.updateAppointExamTime(list));
    }

    /**
     * 删除预约排队
     */
//    @Log(title = "预约排队", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(dBookedInfoService.deleteDBookedInfoByIds(ids));
//    }


    /**
     * 删除预约排队
     */
    @Log(title = "预约排队", businessType = BusinessType.DELETE)
    @PutMapping("/del/{id}")
    public AjaxResult delete(@PathVariable Long id)
    {
        int res = dBookedInfoService.deleteBooked(id);
        return toAjax(res);
    }


    @PostMapping(value = "/appointList")
    public AjaxResult getBookedInfoListByAppoint( DBookedInfo dBookedInfo)
    {
        return AjaxResult.success(dBookedInfoService.selectBookInfoByAppointDate(dBookedInfo));
    }

    /**
     * 激活
     */
    @Log(title = "患者检查", businessType = BusinessType.DELETE)
    @PutMapping("/updateActiveStatus")
    public AjaxResult updateActiveStatus(@RequestBody DBBookedInfoVo dbBookedInfoVo) {
        return dBookedInfoService.updateActiveStatus(dbBookedInfoVo);
    }
}
