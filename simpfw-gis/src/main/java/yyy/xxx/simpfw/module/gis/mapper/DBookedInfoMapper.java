package yyy.xxx.simpfw.module.gis.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import yyy.xxx.simpfw.module.gis.entity.DBookedInfo;
import yyy.xxx.simpfw.module.gis.vo.DBBookedInfoVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedInfoByAppointDateVo;
import yyy.xxx.simpfw.module.gis.vo.DBookedWeekInfoListVo;


/**
 * 预约排队Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
public interface DBookedInfoMapper 
{


    List<DBBookedInfoVo> selectList(DBBookedInfoVo dbBookedInfoVo);

    /**
     * 查询预约人数
     *
     * @return 预约排队数量
     */
     List<DBookedWeekInfoListVo> selectWeekInfoList();

    /**
     * 查询预约人数
     *
     * @return 预约排队数量
     */
    List<DBookedInfoByAppointDateVo> selectBookInfoByAppointDate(DBookedInfo dBookedInfo);


    /**
     * 查询预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 预约排队
     */
    public DBookedInfo selectOne(DBookedInfo dBookedInfo);

    /**
     * 查询预约排队列表
     * 
     * @param dBookedInfo 预约排队
     * @return 预约排队集合
     */
//    public List<DBookedInfo> selectDBookedInfoList(DBookedInfo dBookedInfo);

    /**
     * 新增预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    public int insertDBookedInfo(DBookedInfo dBookedInfo);

    /**
     * 修改预约排队
     * 
     * @param dBookedInfo 预约排队
     * @return 结果
     */
    public int updateDBookedInfo(DBookedInfo dBookedInfo);

    /**
     * 删除预约排队
     * 
     * @param id 预约排队主键
     * @return 结果
     */
    public int delete(Long id);

    /**
     * 删除预约排队
     *
     * @param id 预约排队主键
     * @return 结果
     */
    public int deleteDBookedInfoById(String id);


    /**
     * 批量删除预约排队
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
//    public int deleteDBookedInfoByIds(String[] ids);

    /**
     * add@20230426
     */
    DBookedInfo findByExamUid(@Param("examUid") String examUid);
}
